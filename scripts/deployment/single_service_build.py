import os
import sys
import urllib.parse
import requests

def url_encode(s):
    return urllib.parse.quote(s, safe='')

def build(branch_raw, project_raw):
    branch = url_encode(branch_raw)
    project = url_encode(project_raw)

    print(f"branch is {branch}")
    print(f"project is {project}")

    jenkins_username = os.getenv('JENKINS_USER')
    jenkins_key = os.getenv('JENKINS_KEY')

    if not jenkins_key or not jenkins_username:
        print("You need to set JENKINS_KEY and JENKINS_USER environment variables")
        sys.exit(1)

    url = f"https://{jenkins_username}:{jenkins_key}@jenkins-mgmt.nexla.com/job/backend-connectors-single-service-build/build?delay=0sec"

    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'accept-language': 'en-US,en;q=0.9',
        'cache-control': 'max-age=0',
        'content-type': 'application/x-www-form-urlencoded',
        'origin': 'https://jenkins-mgmt.nexla.com',
        'referer': 'https://jenkins-mgmt.nexla.com/job/backend-connectors-single-service-build/build?delay=0sec',
        'sec-ch-ua': '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '0',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    # TODO this is taken from the curl and contains auth params we likely don't need, since we use the username@pass auth (but I got 500 when I tried to remove them)
    data_raw = f"name=gitUrl&value=git%40github.com%3Anexla%2Fbackend-connectors.git&name=branch&value={branch}&name=TAG&value=&name=RELEASE&value=&name=DOCKER_REGISTRY&value=ECR&name=NEXLA_PARALLELISM&value=1&name=NEXLA_PROFILE&value=-P+build-connector&name=NEXLA_TESTS&value=-Dmaven.test.skip%3Dtrue&name=NEXLA_SUBPROJECT&value={project}&name=CODE_ARTIFACT_TOKEN&value=&statusCode=303&redirectTo=.&Jenkins-Crumb=&json=%7B%22parameter%22%3A%5B%7B%22name%22%3A%22gitUrl%22%2C%22value%22%3A%22git%40github.com%3Anexla%2Fbackend-connectors.git%22%7D%2C%7B%22name%22%3A%22branch%22%2C%22value%22%3A%22{branch}%22%7D%2C%7B%22name%22%3A%22TAG%22%2C%22value%22%3A%22%22%7D%2C%7B%22name%22%3A%22RELEASE%22%2C%22value%22%3A%22%22%7D%2C%7B%22name%22%3A%22DOCKER_REGISTRY%22%2C%22value%22%3A%22ECR%22%7D%2C%7B%22name%22%3A%22NEXLA_PARALLELISM%22%2C%22value%22%3A%221%22%7D%2C%7B%22name%22%3A%22NEXLA_PROFILE%22%2C%22value%22%3A%22-P+build-connector%22%7D%2C%7B%22name%22%3A%22NEXLA_TESTS%22%2C%22value%22%3A%22-Dmaven.test.skip%3Dtrue%22%7D%2C%7B%22name%22%3A%22NEXLA_SUBPROJECT%22%2C%22value%22%3A%22{project}%22%7D%2C%7B%22name%22%3A%22CODE_ARTIFACT_TOKEN%22%2C%22value%22%3A%22%22%7D%5D%2C%22statusCode%22%3A%22303%22%2C%22redirectTo%22%3A%22.%22%2C%22%22%3A%22%22%2C%22Jenkins-Crumb%22%3A%22%22%7D"

    status_code = requests.post(url, headers=headers, data=data_raw).status_code
    if status_code == 403:
        print(f"Response status code 403! Did you log into Perimeter81 VPN?")
    else:
        print(f"Response status code: {status_code}")
    return status_code

if __name__ == '__main__':
    if len(sys.argv) != 3:
        print(f"Usage: {sys.argv[0]} <branch> <project>")
        sys.exit(1)
    branch = sys.argv[1]
    project =  sys.argv[2]
    build(branch, project)
