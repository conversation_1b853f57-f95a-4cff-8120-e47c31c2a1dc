<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.nexla</groupId>
		<artifactId>backend-connectors</artifactId>
		<version>3.3.0-SNAPSHOT</version>
	</parent>

	<artifactId>box-service</artifactId>

	<dependencies>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>file-service</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common</artifactId>
			<version>${nexla-backend-common.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>connector-properties</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-library</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- Enable JSON logging -->
		<dependency>
			<groupId>net.logstash.logback</groupId>
			<artifactId>logstash-logback-encoder</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>

		<dependency>
			<groupId>com.box</groupId>
			<artifactId>box-java-sdk</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15on</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcpkix-jdk15on</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

	</dependencies>

</project>
