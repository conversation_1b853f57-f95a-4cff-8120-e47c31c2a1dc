# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands

This is a Maven-based Kafka Connect Iceberg sink connector project. Use these commands for development:

```bash
# Build the project
mvn clean compile

# Run tests
mvn test

# Package the connector (with shaded JAR)
mvn clean package -P build-connector

# Install to local repository
mvn clean install
```

## Architecture Overview

This connector implements a Kafka Connect sink for writing data to Apache Iceberg tables, supporting two distinct operating modes:

### Key Components

- **ApacheIcebergSinkConnector**: Main connector class extending `BaseSinkConnector`
- **IcebergSinkTask**: FlexFlow mode implementation using single-node Spark runtime with Delta table staging
- **IcebergCdcSinkTask**: CDC mode implementation using modified Databricks Iceberg connector
- **IcebergSinkStrategy**: Factory pattern for choosing between FlexFlow and CDC strategies
- **BaseIcebergSinkTask**: Common base class providing shared functionality

### Operating Modes

**FlexFlow Mode** (`IcebergSinkTask`):
- Uses single-node Spark runtime with in-memory/local Delta table staging
- Supports both INSERT and UPSERT operations
- Applies PostponedFlush logic for data maturity
- <PERSON><PERSON> deduplication and MERGE INTO operations for UPSERTs
- Limited to one task per JVM due to Spark single SparkContext constraint

**CDC Mode** (`IcebergCdcSinkTask`):
- Integrates with modified Databricks Iceberg connector
- Supports schema and partition evolution
- Handles DML and DDL operations from CDC streams
- Uses Nexla PostponedFlush logic via custom coordinator

### Configuration

Key configuration properties defined in `IcebergSinkConnectorConfig`:
- `iceberg.warehouse.dir`: S3 path for Iceberg warehouse
- `iceberg.table.name`: Target table name
- `iceberg.insert.mode`: INSERT or UPSERT mode
- `iceberg.id-fields`: Columns for UPSERT matching
- `cdc.enabled`: Enable CDC mode vs FlexFlow mode
- `iceberg.partition-keys`: Table partitioning configuration

### Dependencies

- Apache Iceberg 1.x with Spark 3.4 runtime
- Apache Spark SQL and Core for data processing
- Hadoop AWS for S3 integration
- Tabular.io Iceberg Kafka Connect for CDC mode
- Nexla common connector framework

## Important Notes

- Due to Spark's single SparkContext limitation, connector must run isolated (one task per pod)
- FlexFlow mode is recommended for most use cases
- CDC mode should only be used when schema/partition evolution is required
- The connector requires specific S3 permissions for read/write operations
- All configuration follows Nexla's connector patterns and integrates with telemetry

## Development Guidelines

### Code Structure
- Follow Nexla's connector patterns and conventions
- Use the existing base classes for common functionality
- Implement proper error handling and logging
- Add comprehensive unit tests for new features

### Testing
- Write unit tests for all new functionality
- Include integration tests for end-to-end scenarios
- Test both FlexFlow and CDC modes
- Validate configuration validation logic

### Documentation
- Update README.md with new features
- Add configuration examples for new properties
- Document any breaking changes

## Product Requirements

For detailed functional requirements, feature roadmap, and implementation priorities, see **[PRD.md](./PRD.md)**.

The PRD contains:
- P0, P1, P2, P3 prioritized requirements
- Acceptance criteria and technical notes
- Implementation roadmap and success metrics
- Risk assessment and dependencies

## Quick Reference

### Current Status
- ✅ FlexFlow mode implemented
- ✅ CDC mode implemented  
- ✅ Basic S3 integration
- ✅ UPSERT operations
- 🔄 P0 requirements in progress (see PRD.md)

### Next Steps
1. Review PRD.md for current priorities
2. Implement P0 requirements first
3. Follow the implementation roadmap
4. Update this file as features are completed