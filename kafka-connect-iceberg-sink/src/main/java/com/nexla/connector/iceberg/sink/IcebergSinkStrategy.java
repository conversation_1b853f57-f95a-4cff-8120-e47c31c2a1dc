package com.nexla.connector.iceberg.sink;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;

/**
 * Strategy interface for handling data operations in the Iceberg sink connector.
 * Implementations define different approaches for buffering, processing, and
 * writing data to Iceberg tables.
 */
public interface IcebergSinkStrategy {

  /**
   * Initializes the strategy, setting up any required resources.
   * This method should be called before any other operations.
   */
  void initialize();

  /**
   * Returns the current dataset being managed by this strategy.
   *
   * @return The current Spark dataset, or null if no data is available
   */
  Dataset<Row> dataset();

  /**
   * Performs a complete cleanup of all resources used by this strategy.
   * Should be called when the connector is stopping.
   */
  void cleanup();

  /**
   * Cleans up temporary resources used for intermediate processing.
   * Unlike {@link #cleanup()}, this may preserve some state between operations.
   */
  void cleanupTemp();

  /**
   * Adds new data to be processed by this strategy.
   * Different implementations may handle the data differently (e.g., caching in memory,
   * writing to temporary tables).
   *
   * @param sourceDataFrame The Spark DataFrame containing new records to process
   */
  void upsertData(Dataset<Row> sourceDataFrame);


  /**
   * Returns the name of the temporary table or view used by this strategy
   * for SQL operations.
   *
   * @return The name of the temporary table or view
   */
  String tempTableOrViewName();
}