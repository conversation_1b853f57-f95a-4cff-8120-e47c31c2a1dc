package com.nexla.connector.iceberg.sink;

import static com.nexla.connector.iceberg.sink.IcebergSinkTask.normalizePath;

import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.InstanceProfileCredentialsProvider;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.GetCallerIdentityResponse;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.admin.client.DataSink;
import com.nexla.common.exception.NexlaQuarantineMessage;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.connect.common.PostponedFlush;
import com.nexla.connect.common.cdc.DebeziumConstants;
import com.nexla.connect.common.cdc.DebeziumData;
import com.nexla.connect.common.cdc.OperationType;
import com.nexla.connect.common.offsets.OffsetsTracker;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig;
import com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig.InsertMode;
import com.nexla.connector.config.iceberg.IcebergSparkCatalog;
import com.nexla.connector.config.iceberg.IcebergConnectorConfig;
import com.nexla.probe.iceberg.IcebergS3TablesConnectorService;
import com.nexla.probe.iceberg.IcebergWriteException;
import io.tabular.iceberg.connect.IcebergSinkConfig;
import io.tabular.iceberg.connect.channel.IcebergCdcWorker;
import io.tabular.iceberg.connect.channel.Task;
import io.tabular.iceberg.connect.data.Operation;
import io.tabular.iceberg.connect.data.Utilities;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider;
import org.apache.hadoop.fs.s3a.auth.AssumedRoleCredentialProvider;
import org.apache.iceberg.catalog.Catalog;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.connect.data.Schema;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTask;
import org.apache.spark.sql.SparkSession;
import org.slf4j.Logger;

/**
 * Wrapper for the Tabular IcebergSinkTask which will skip committing offsets in the usual kafka-connect way
 */
public class IcebergCdcSinkTask extends SinkTask {

  private static final String NEXLA_CDC_FIELD = "__nexla_cdc_op";
  private static final String NEXLA_CDC_FIELD_TABLE = "__nexla_cdc_table";
  public static final String NEXLA_CDC_PRIMARY_KEY = "__nexla_cdc_primary_key";
  private static final String INVALID_PRIMARY_KEY = "Primary key definition is mandatory for Iceberg tables!";
  private static final String RECORD_NOT_FOUND = "Record not found in Iceberg table ";
  public static final String SPARK_CATALOG_TABLE_PREFIX = "spark_catalog.";
  private final Logger log;
  private final IcebergSinkConnectorConfig nexlaConfig;
  private final Optional<OffsetsTracker> offsetsTracker;
  private final PostponedFlush postponedFlush;

  private final SparkSession sparkSession;
  private IcebergSinkConfig config;
  private Task task;
  private DataSink dataSink;
  private Supplier<Long> runIdSupplier;
  private ControlMessageProducer controlMessageProducer;
  private Catalog catalog;
  private IcebergCdcTableManager tableManager;
  private final Runnable postCommitCallback;
  private Optional<IcebergS3TablesConnectorService> s3TablesProbe;

  public IcebergCdcSinkTask(IcebergSinkConnectorConfig nexlaConfig, DataSink dataSink, Supplier<Long> runIdSupplier,
                            ControlMessageProducer controlMessageProducer, Optional<OffsetsTracker> offsetsTracker, PostponedFlush postponedFlush,
                            SparkSession sparkSession, Logger logger, Runnable postCommitCallback, Optional<IcebergS3TablesConnectorService> s3TablesProbe) {
    this.nexlaConfig = nexlaConfig;
    this.offsetsTracker = offsetsTracker;
    this.dataSink = dataSink;
    this.runIdSupplier = runIdSupplier;
    this.controlMessageProducer = controlMessageProducer;
    this.postponedFlush = postponedFlush;
    this.log = logger;
    this.sparkSession = sparkSession;
    this.postCommitCallback = postCommitCallback;
    this.s3TablesProbe = s3TablesProbe;
  }

  @Override
  public String version() {
    return IcebergSinkConfig.version();
  }

  @Override
  public void start(Map<String, String> props) {
    this.config = createTabularSinkConfig();
  }

  @Override
  public void open(Collection<TopicPartition> partitions) {
    this.clearObjectState();
    this.catalog = Utilities.loadCatalog(this.config);
    this.tableManager = new IcebergCdcTableManager(this.nexlaConfig, this.catalog, this.sparkSession, this.log);
    this.task = new IcebergCdcWorker(this.context, this.nexlaConfig, this.config, this.runIdSupplier, this.dataSink,
        this.controlMessageProducer, this.offsetsTracker, this.postponedFlush, this.log, this.catalog, this.postCommitCallback);
  }

  /**
   * Immediately writes sink records to their final destination
   * @param sinkRecords the set of records to send
   */
  @Override
  public void put(Collection<SinkRecord> sinkRecords) {
    if (this.task != null) {
      this.task.put(sinkRecords);
    }
  }

  public void commit() {
    this.task.put(Collections.emptyList());
  }

  @Override
  public void stop() {
    this.close();
  }

  @Override
  public void close(Collection<TopicPartition> partitions) {
    super.close(partitions);
  }

  public Map<TopicPartition, OffsetAndMetadata> preCommit(Map<TopicPartition, OffsetAndMetadata> currentOffsets) {
    return ImmutableMap.of(); // opt-out of default offset commit, we're committing our offsets to the control plane
  }

  public List<Pair<String, RecordMetric>> doCdcPut(StreamEx<NexlaMessageContext> messages) {
    List<TableRecords> tableRecords = messages.groupingBy(nexla -> (String) nexla.getOriginal().getRawMessage().get(DebeziumConstants.NEXLA_OPERATION_TABLE))
        .entrySet()
        .stream()
        .map(entry -> extractTableRecords(entry.getKey(), entry.getValue()))
        .collect(Collectors.toList());

    return tableRecords
        .stream()
        .map(tableRecord -> {
          String fullTableName = tableRecord.getTableName();
          RecordMetric recordMetric = new RecordMetric();
          try {
            List<SinkRecord> sinkRecords = tableRecord.getRecords();
            if (CollectionUtils.isNotEmpty(sinkRecords)) {
              log.info("M=doCdcPut, processing {} records for table {}", sinkRecords.size(), fullTableName);
              tableManager.processTable(fullTableName, tableRecord.getDebeziumData(), s3TablesProbe);
              put(sinkRecords);
            }
            recordErrors(recordMetric, tableRecord.getInvalidPrimaryKeyRecords(), INVALID_PRIMARY_KEY);
            recordErrors(recordMetric, tableRecord.getNotFoundRecords(), RECORD_NOT_FOUND + fullTableName);
          } catch (Exception e) {
            log.error("M=doCdcPut, error caught while processing table " + fullTableName, e);
            recordErrors(recordMetric, tableRecord.getNexlaMessages(), e.getMessage());
          }
          return Pair.of(fullTableName, recordMetric);
        })
        .collect(Collectors.toList());
  }

  private Operation mapNexlaOperationTypeToTabularOperationType(OperationType op) {
    switch (op) {
      case READ:
      case CREATE:
        return Operation.INSERT;
      case UPDATE:
        return Operation.UPDATE;
      case DELETE:
      case TRUNCATE:
        return Operation.DELETE;
      default:
        throw new RuntimeException(String.format("Couldn't map %s to a CDC operation type", op.name()));
    }
  }

  private void close() {
    this.clearObjectState();
  }

  private void clearObjectState() {
    Utilities.close(this.task);
    this.task = null;
  }

  private IcebergSinkConfig createTabularSinkConfig() {
    if (this.nexlaConfig == null) {
      throw new RuntimeException("Nexla sink config can't be null");
    }
    AWSAuthConfig authConfig = (AWSAuthConfig) this.nexlaConfig.getAuthConfig();
    Map<String, String> props = nexlaConfig.originalsStrings();

    props.put("name", "sink-" + this.nexlaConfig.sinkId);
    props.put("iceberg.control.topic", "control-iceberg-sink-" + this.nexlaConfig.sinkId);
    props.put("iceberg.coordinator.transactional.suffix", String.valueOf(this.nexlaConfig.sinkId));
    props.put("iceberg.control.commit.interval-ms", String.valueOf(this.nexlaConfig.commitIntervalMs));
    props.put("iceberg.control.commit.timeout-ms", String.valueOf(this.nexlaConfig.commitPollTimeoutMs));
    props.put("iceberg.kafka.bootstrap.servers", this.nexlaConfig.dataKafkaContext.bootstrapServer);
    props.put("iceberg.kafka.auto.offset.reset", "earliest");
    props.put("iceberg.tables.cdc-field", NEXLA_CDC_FIELD);
    props.put("iceberg.tables.upsert-mode-enabled", this.nexlaConfig.insertMode.equals(InsertMode.UPSERT) ? "true" : "false");
    props.put("iceberg.tables.evolve-schema-enabled", "false");
    props.put("iceberg.tables.auto-create-enabled", "false");
    props.put("iceberg.tables.dynamic-enabled", "true");
    props.put("iceberg.tables.route-field", NEXLA_CDC_FIELD_TABLE);
    props.computeIfAbsent("iceberg.tables.default-partition-by",
        (k) -> this.nexlaConfig.partitionKeys.orElse(null));


    final String warehouseDir = "s3a://" + normalizePath(this.nexlaConfig.getPath());
    final String buckerArn = ((IcebergConnectorConfig) this.nexlaConfig).getBucketArn();
    final IcebergSparkCatalog catalog = ((IcebergConnectorConfig) this.nexlaConfig).getCatalog();
    String catalogPrefix = "iceberg.catalog";
    if (IcebergSparkCatalog.GLUE.equals(catalog)) {
        props.put(catalogPrefix, catalog.getCatalogClass());
        props.put(catalogPrefix + ".warehouse", warehouseDir);
        props.put(catalogPrefix + ".catalog-impl", catalog.getCatalogImpl());
        props.put(catalogPrefix + ".io-impl", catalog.getCatalogIoImpl());
    } else if (IcebergSparkCatalog.S3_TABLES.equals(catalog)) {
        props.put(catalogPrefix, catalog.getCatalogClass());
        props.put(catalogPrefix + ".warehouse", buckerArn);
        props.put(catalogPrefix + ".catalog-impl", catalog.getCatalogImpl());
        props.put(catalogPrefix + ".io-impl", catalog.getCatalogIoImpl());
    } else if (IcebergSparkCatalog.SPARK_CATALOG.equals(catalog)) {
        props.put(catalogPrefix, catalog.getCatalogClass());
        props.put(catalogPrefix + ".type", "hadoop");
        props.put(catalogPrefix + ".warehouse", warehouseDir);
    }
    props.put("iceberg.hadoop.fs.s3a.path.style.access", "true");
    props.put("iceberg.hadoop.fs.file.impl", org.apache.hadoop.fs.LocalFileSystem.class.getName());

    if (StringUtils.isNotBlank(authConfig.region)) {
      System.setProperty("aws.region", authConfig.region); 
      props.put(catalogPrefix + ".client.region", authConfig.region);
      props.put("iceberg.hadoop.fs.s3a.endpoint.region", authConfig.region);
    }

    if (StringUtils.isNotBlank(authConfig.arn)) {
      props = enrichTabularConnectorPropsAssumedRoleCredentials(props, authConfig);
    } else {
      props = enrichTabularConnectorPropsSimpleCredentials(props, authConfig, catalogPrefix);
    }
    if (nexlaConfig.getConnectorConfig().logVerbose) {
        log.info("Tabular Sink Config: {}", props);
    }
    return new IcebergSinkConfig(props);
  }

  private Map<String, String> enrichTabularConnectorPropsSimpleCredentials(Map<String, String> props,
      AWSAuthConfig authConfig, String catalogPrefix) {
    System.setProperty("aws.accessKeyId", authConfig.accessKeyId);
    System.setProperty("aws.secretAccessKey", authConfig.secretKey);
    props.put(catalogPrefix + ".s3.access-key-id", authConfig.accessKeyId);
    props.put(catalogPrefix + ".s3.secret-access-key", authConfig.secretKey);
    props.put("iceberg.hadoop.fs.s3a.access.key", authConfig.accessKeyId);
    props.put("iceberg.hadoop.fs.s3a.secret.key", authConfig.secretKey);
    props.put("iceberg.hadoop.fs.s3a.endpoint.region", authConfig.region);
    authConfig.serviceEndpoint.ifPresent(ep -> props.put("iceberg.hadoop.fs.s3a.endpoint", ep));
    props.put("iceberg.hadoop.fs.s3a.aws.credentials.provider", SimpleAWSCredentialsProvider.NAME);
    return props;
  }

  private Map<String, String> enrichTabularConnectorPropsAssumedRoleCredentials(Map<String, String> props,
      AWSAuthConfig authConfig) {
    log.info("M=enrichTabularConnectorPropsAssumedRoleCredentials, configuredArnPresent={}", StringUtils.isNotBlank(authConfig.arn));
    
    // Check if we're running in EKS with IRSA by examining current AWS identity
    String currentIdentity = getCurrentAwsIdentity();
    log.info("M=enrichTabularConnectorPropsAssumedRoleCredentials, currentIdentityType={}", 
             currentIdentity != null ? (currentIdentity.contains("assumed-role") ? "assumed-role" : "other") : "null");
    
    // If current identity is already an assumed role and matches the configured ARN, 
    // we're in EKS with IRSA - use DefaultCredentialsProvider directly
    if (currentIdentity != null && currentIdentity.contains("assumed-role") && 
        isCurrentIdentityMatchingConfiguredRole(currentIdentity, authConfig.arn)) {
      log.info("M=enrichTabularConnectorPropsAssumedRoleCredentials, detected EKS IRSA environment, using DefaultCredentialsProvider");
      props.put("iceberg.hadoop.fs.s3a.aws.credentials.provider", DefaultCredentialsProvider.class.getName());
      return props;
    }
    
    // Traditional role assumption for non-EKS environments
    log.info("M=enrichTabularConnectorPropsAssumedRoleCredentials, using traditional role assumption");
    props.put("iceberg.hadoop.fs.s3a.assumed.role.arn", authConfig.arn);
    props.put("iceberg.hadoop.fs.s3a.aws.credentials.provider", AssumedRoleCredentialProvider.NAME);
    props.put("iceberg.hadoop.fs.s3a.assumed.role.credentials.provider",
            String.join(",",
                    InstanceProfileCredentialsProvider.class.getName(),
                    DefaultCredentialsProvider.class.getName()));
    return props;
  }

  private TableRecords extractTableRecords(String tableName, List<NexlaMessageContext> messages) {
    List<NexlaMessageContext> invalidPrimaryKeyRecords = Lists.newLinkedList();
    List<NexlaMessageContext> notFoundRecords = Lists.newLinkedList();
    AtomicReference<DebeziumData> debeziumData = new AtomicReference<>();

    String finalTableName = getFinalTableName(tableName);
    List<SinkRecord> sinkRecords = messages
        .stream()
        .map(msg -> {
          OperationType op = OperationType.asEnum(msg.getOriginal().getRawMessage().get(DebeziumConstants.NEXLA_OPERATION).toString());
          DebeziumData dData = DebeziumData.newDebeziumData(msg.getOriginal(), nexlaConfig.sinkId);

          if (dData == null) {
            throw new IcebergWriteException(String.format("Expected debezium record but failed to parse it from message %s",
                msg.getOriginal()));
          }

          LinkedHashMap<String, Object> afterData = dData.getAfterData();
          Operation mappedOperation = mapNexlaOperationTypeToTabularOperationType(op);

          // We must enforce primary keys usage as iceberg needs it for equality delete/updates
          if (CollectionUtils.isEmpty(dData.getPrimaryKey())){
            if (!nexlaConfig.allowNoPkTables) {
              invalidPrimaryKeyRecords.add(msg);
              return null;
            } else {
              String primaryKey = tableManager.getRecordPrimaryKey(finalTableName, mappedOperation, dData.getBeforeData(), afterData);
              if (Objects.isNull(primaryKey)) {
                notFoundRecords.add(msg);
                return null;
              }
              afterData.put(NEXLA_CDC_PRIMARY_KEY, primaryKey);
              dData.getPrimaryKey().add(NEXLA_CDC_PRIMARY_KEY);
              Map<String, String> schemaType = Maps.newHashMap();
              schemaType.put("STRING", null);
              dData.getSchema().put(NEXLA_CDC_PRIMARY_KEY, schemaType);
            }
          }
          afterData.put(NEXLA_CDC_FIELD, mappedOperation.name());
          afterData.put(NEXLA_CDC_FIELD_TABLE, finalTableName);
          // Let's consider last schema message as the most updated one
          debeziumData.set(dData);

          return new SinkRecord(
              msg.getTopicPartition().topic,
              msg.getTopicPartition().partition,
              Schema.STRING_SCHEMA,
              msg.getOriginal().getNexlaMetaData().getSourceKey(),
              null,
              afterData,
              msg.getKafkaOffset());
        })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());

    return TableRecords.builder()
        .tableName(finalTableName)
        .invalidPrimaryKeyRecords(invalidPrimaryKeyRecords)
        .records(sinkRecords)
        .debeziumData(debeziumData.get())
        .nexlaMessages(messages)
        .notFoundRecords(notFoundRecords)
        .build();
  }

  private void recordErrors(RecordMetric recordMetric, List<NexlaMessageContext> messages, String errorMessage) {
    recordMetric.getErrorRecords().addAndGet(messages.size());
    List<NexlaQuarantineMessage> quarantineMessages = messages
        .stream()
        .map(it -> RecordMetric.quarantineMessage(it.getOriginal(), errorMessage))
        .collect(Collectors.toList());
    recordMetric.getQuarantineMessages().addAll(quarantineMessages);
  }

  private String getFinalTableName(String baseTableName) {
    String tableName = this.nexlaConfig
        .mappingConfig
        .map(it -> String.format("%s%s%s", stringOrEmpty(it.getTableNamePrefix()), baseTableName, stringOrEmpty(it.getTableNameSuffix())))
        .orElse(baseTableName);
    // enforce lower case names as iceberg doesn't accept upper case table names
    boolean hasUpperCaseLetter = tableName.chars().anyMatch(Character::isUpperCase);
    if (hasUpperCaseLetter) {
      log.warn("M=getFinalTableName, detected upper case letters in the table name {}", tableName);
    }
    return ((IcebergConnectorConfig) nexlaConfig).getFullTableName(tableName).toLowerCase();
  }

  private String stringOrEmpty(String s) {
    return Optional.ofNullable(s).filter(StringUtils::isNotBlank).orElse("");
  }

  private String getCurrentAwsIdentity() {
    try (StsClient stsClient = StsClient.builder()
        .credentialsProvider(DefaultCredentialsProvider.create())
        .build()) {
      
      GetCallerIdentityResponse response = stsClient.getCallerIdentity();
      String arn = response.arn();
      log.debug("M=getCurrentAwsIdentity, retrieved AWS identity type: {}", 
                arn != null ? (arn.contains("assumed-role") ? "assumed-role" : "other") : "null");
      return arn;
    } catch (Exception e) {
      log.warn("M=getCurrentAwsIdentity, failed to get current AWS identity", e);
      return null;
    }
  }

  private boolean isCurrentIdentityMatchingConfiguredRole(String currentIdentity, String configuredArn) {
    if (currentIdentity == null || configuredArn == null) {
      return false;
    }
    
    // Extract role name from current identity (assumed-role format)
    // Current identity: arn:aws:sts::account:assumed-role/role-name/session-name
    // Configured ARN: arn:aws:iam::account:role/role-name
    try {
      String[] currentParts = currentIdentity.split("/");
      String[] configuredParts = configuredArn.split("/");
      
      if (currentParts.length >= 2 && configuredParts.length >= 2) {
        String currentRoleName = currentParts[1]; // role-name from assumed-role
        String configuredRoleName = configuredParts[1]; // role-name from iam role
        
        boolean matches = currentRoleName.equals(configuredRoleName);
        log.debug("M=isCurrentIdentityMatchingConfiguredRole, currentRole={}, configuredRole={}, matches={}", 
                  currentRoleName, configuredRoleName, matches);
        return matches;
      }
    } catch (Exception e) {
      log.warn("M=isCurrentIdentityMatchingConfiguredRole, failed to parse role names", e);
    }
    
    return false;
  }

  @Getter
  @Builder
  @AllArgsConstructor
  private static class TableRecords {
    private final String tableName;
    private final List<SinkRecord> records;
    private final List<NexlaMessageContext> invalidPrimaryKeyRecords;
    private final DebeziumData debeziumData;
    private final List<NexlaMessageContext> nexlaMessages;
    private final List<NexlaMessageContext> notFoundRecords;
  }
}
