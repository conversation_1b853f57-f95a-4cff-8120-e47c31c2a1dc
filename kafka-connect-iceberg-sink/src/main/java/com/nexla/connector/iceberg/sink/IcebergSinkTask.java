package com.nexla.connector.iceberg.sink;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connect.common.OffsetsCoordinationClient;
import com.nexla.connect.common.PostponedFlush;
import com.nexla.connect.common.ReadyToFlush;
import com.nexla.connect.common.postponedFlush.KafkaProgressTracker;
import com.nexla.connect.common.postponedFlush.PipelineProgressTracker;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig;
import com.nexla.connector.config.iceberg.IcebergSparkCatalog;
import com.nexla.probe.iceberg.IcebergConnectorService;
import com.nexla.probe.iceberg.IcebergS3TablesConnectorService;
import com.nexla.probe.iceberg.SparkUtils;
import com.nexla.telemetry.utils.NexlaStopwatch;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.spark.scheduler.SparkListener;
import org.apache.spark.scheduler.SparkListenerTaskEnd;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.CreateTableWriter;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Encoders;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.expressions.Window;
import org.apache.spark.sql.expressions.WindowSpec;
import org.apache.spark.sql.functions;
import org.apache.spark.util.LongAccumulator;
import scala.collection.JavaConverters;
import scala.collection.Seq;
import scala.collection.Seq$;

import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity.INFO;
import static com.nexla.common.notify.monitoring.NexlaMonitoringLogType.LOG;
import static com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix;
import static com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig.InsertMode.UPSERT;
import static java.util.Optional.ofNullable;

public class IcebergSinkTask extends BaseSinkTask<IcebergSinkConnectorConfig> {

  private final ObjectMapper objectMapper = new ObjectMapper()
      .setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL)
      .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
      .registerModule(new Jdk8Module())
      .registerModule(new JavaTimeModule());
  private final AtomicLong lastProcessedTimestamp = new AtomicLong(0L);
  private final Map<TopicPartition, Long> offsets = new ConcurrentHashMap<>();
  private final AtomicLong recordsToAppendCounter = new AtomicLong(0L);
  private IcebergConnectorService probe;
  private Optional<IcebergS3TablesConnectorService> s3TablesProbe = Optional.empty();
  private SparkSession spark;
  private Optional<IcebergCdcSinkTask> cdcSinkTask = Optional.empty();
  private SparkUtils sparkUtils;

  private IcebergSinkStrategy sinkTaskStrategy;
  private final IcebergSinkStrategyFactory icebergSinkStrategyFactory = new IcebergSinkStrategyFactory();

  @Override
  public void doStart() {
    final NexlaStopwatch stopwatch = new NexlaStopwatch("doStart");
    stopwatch.start("setup");
    this.probe = new IcebergConnectorService();
    this.spark = probe.getSparkSession(config);
    this.sparkUtils = new SparkUtils(logger, spark, config);
    final String tempTableOrViewName = "delta_temp_" + UUID.randomUUID().toString().replace("-", "_");
    this.sinkTaskStrategy = icebergSinkStrategyFactory.createStrategy(config.tempDataHandlingType, spark, tempTableOrViewName);
    if (IcebergSparkCatalog.S3_TABLES.equals(config.getCatalog())) {
      s3TablesProbe = Optional.of(new IcebergS3TablesConnectorService(config, config.getAuthConfig().asAWS()));
    }

    if (config.cdcEnabled) {
      Runnable postCommitCallback = () -> {
        if (postponedFlush.readyToFlush().reportFlushed) {
          onPipelineFlushed();
          stopSinkConnectorIfDedicated();
        }
      };
      stopwatch.stop();

      stopwatch.start("initSinkTask");
      // runId is updated dynamically during message put, closure allows lower level classes to get the updated value
      IcebergCdcSinkTask sinkTask = new IcebergCdcSinkTask(config, dataSink, () -> runId,
          controlMessageProducer, offsetsSender, postponedFlush, spark, logger, postCommitCallback, s3TablesProbe);
      sinkTask.initialize(context);
      sinkTask.start(config.originalsStrings());
      cdcSinkTask = Optional.of(sinkTask);
      stopwatch.stop();
    }
    sinkTaskStrategy.initialize();
    logger.info("Time breakdown: {}", stopwatch);
  }

  @Override
  protected PostponedFlush postponedFlush() {
    PipelineProgressTracker ppt = new KafkaProgressTracker(getKafkaLagCalculator(), adminApiClient, ctrlClient, logger);
    return new PostponedFlush(config, adminApiClient, ppt, logger) {

      @Override
      protected boolean dataMatureMs(long delayMs) {
        long inactivityTimeIntervalMs = System.currentTimeMillis() - lastProcessedTimestamp.get();
        return recordsToAppendCounter.get() > 0 && inactivityTimeIntervalMs > delayMs;
      }

      @Override
      protected boolean noDataMatureMs(long delayMs) {
        return !hasAccumulatedRecords() && (System.currentTimeMillis() - getLastRecordsPutTs()) > delayMs;
      }

      @Override
      protected boolean hasAccumulatedRecords() {
        return recordsToAppendCounter.get() > 0L;
      }

      @Override
      protected long intermediateFlushBatchSize() {
        return recordsToAppendCounter.get();
      }
    };
  }

  @Override
  public void open(Collection<org.apache.kafka.common.TopicPartition> partitions) {
    super.open(partitions);
    cdcSinkTask.ifPresent(cdc -> cdc.open(partitions));

    if (postponedFlush != null) {
      postponedFlush.resetFirstFlushTsThreshold();
    }
  }

  @SneakyThrows
  @Override
  public void stop() {
    // avoid SparkSession#stop as it will stop the SparkContext (singular per JVM) and affect other workers
    SparkSession.clearActiveSession();
    probe.close();
    sinkTaskStrategy.cleanup();
    cdcSinkTask.ifPresent(IcebergCdcSinkTask::stop);
    super.stop();
  }


  @Override
  public boolean doFlush(ReadyToFlush readyToFlush) {
    final NexlaStopwatch stopwatch = new NexlaStopwatch("doFlush");
    try {
      if (config.cdcEnabled && readyToFlush.flush) {
        stopwatch.start("cdcDoFlush");
        cdcSinkTask.ifPresent(IcebergCdcSinkTask::commit);
        stopwatch.stop();
        return false;
      }

      try {
        if (readyToFlush.flush) {
          stopwatch.start("createDataframe");
          this.postponedFlush.resetFailures();
          final String tableName = config.tableName;
          final String tableNameWithCatalog = config.getFullTableName();
          final AWSAuthConfig.BucketPrefix bucketPrefix = toBucketPrefix(config.getPath(), true);
          final String path = "s3://" + normalizePath(Paths.get(bucketPrefix.bucket, bucketPrefix.prefix).toString());
          final LongAccumulator countAccumulator = spark.sparkContext().longAccumulator("record-count");
          final LongAccumulator bytesAccumulator = spark.sparkContext().longAccumulator("byte-count");
          final SparkListener accumulatorListener = new SparkListener() {
            @Override
            public void onTaskEnd(SparkListenerTaskEnd taskEnd) {
              var metrics = taskEnd.taskMetrics();
              if (metrics != null && metrics.outputMetrics() != null) {
                countAccumulator.add(metrics.outputMetrics().recordsWritten());
                bytesAccumulator.add(metrics.outputMetrics().bytesWritten());
              }
            }
          };
          spark.sparkContext().addSparkListener(accumulatorListener);
          Dataset<Row> dataset = sinkTaskStrategy.dataset();
          if (dataset == null || dataset.isEmpty()) {
            logger.info("No data to flush");
            stopwatch.stop(); // make sure to stop the stopwatch
            return false;
          }
          // we have data to flush, stopping stopwatch to capture createDataframe time
          stopwatch.stop(); // createDataframe

          stopwatch.start("extractPartitionKeys");
          Seq<Column> partitionKeys = extractPartitionKeys(config, dataset);
          stopwatch.stop();

          if (!partitionKeys.isEmpty()) {
            stopwatch.start("sortAndRepartition");
            // spark version < 3.5.x and with partitioning: we need to handle pre-sorting the dataset ourselves
            dataset = dataset.repartition(partitionKeys).sortWithinPartitions(partitionKeys);
            stopwatch.stop();
          }

          if (config.insertMode.equals(UPSERT)) {
            stopwatch.start("deduplicateKeys");
            dataset = deduplicateKeys(dataset);
            stopwatch.stop();
          }

          if (!spark.catalog().tableExists(tableNameWithCatalog)) {
            stopwatch.start("createWriter");
            publishMonitoringLog(String.format("Table '%s' doesn't exist. It will be created.", tableNameWithCatalog), LOG, INFO);
            CreateTableWriter<Row> writer = dataset.writeTo(tableNameWithCatalog)
                .using("iceberg")
                .option("mergeSchema", "true")
                .tableProperty("write.spark.accept-any-schema", "true")
                .tableProperty("write.format.default", "parquet")
                .tableProperty("write.parquet.compression-codec", "zstd")
                .tableProperty("write.distribution-mode", "hash")
                .tableProperty("write.metadata.compression-codec", "gzip")
                .tableProperty("write.metadata.delete-after-commit.enabled", "true");
            if (!IcebergSparkCatalog.S3_TABLES.equals(config.getCatalog())) {
              String s3Location = SparkUtils.getS3Folder(probe.getDataLakePath(config.getPath()), config.databaseName,
                  tableName);
              writer = writer.tableProperty("location", s3Location);
            }
            stopwatch.stop();

            if (!partitionKeys.isEmpty()) {
              stopwatch.start("partitionData");
              publishMonitoringLog(String.format("Partition spec is %s", partitionKeys.mkString(",")), LOG, INFO);
              writer.partitionedBy(partitionKeys.head(), partitionKeys.tail().toSeq());
              stopwatch.stop();
            }

            publishMonitoringLog(String.format("Creating table '%s'.", tableNameWithCatalog), LOG, INFO);
            stopwatch.start("createS3TableBucketIfNotExists");
            s3TablesProbe.ifPresent(IcebergS3TablesConnectorService::createS3TableBucketAndNamespaceIfNotExists);
            stopwatch.stop();

            stopwatch.start("createTable");
            writer.createOrReplace();
            stopwatch.stop();
          } else {
            final String tempTableOrViewName = sinkTaskStrategy.tempTableOrViewName();
            // TODO: support partition evolution
            publishMonitoringLog(
                String.format("Appending temp view %s to table %s at %s", tempTableOrViewName, tableName, path), LOG, INFO);

            if (config.alterTableAcceptAnySchema) {
              stopwatch.start("alterTable");
              sparkUtils.execSparkSql(String.format("ALTER TABLE %s SET TBLPROPERTIES ('write.spark.accept-any-schema'='true')",
                  tableNameWithCatalog));
              stopwatch.stop();
            }

            String matchCondition = "1 = 0"; //For inserts, doing merge with non match condition in order to use schema casting from merge
            if (config.insertMode.equals(UPSERT)) {
              matchCondition = String.join(" AND ",
                  Arrays.stream(config.idFields.split(","))
                      .map(col -> "target." + esc(col.trim()) + " = source." + esc(col.trim()))
                      .toArray(String[]::new));
            }
            stopwatch.start("createOrReplaceTempView");
            dataset.createOrReplaceTempView(tempTableOrViewName);
            stopwatch.stop();

            String query = String.format(
                "MERGE INTO %s AS target "
                    + "USING %s AS source "
                    + "ON %s WHEN MATCHED "
                    + "THEN UPDATE SET * "
                    + "WHEN NOT MATCHED "
                    + "THEN INSERT *",
                tableNameWithCatalog, tempTableOrViewName, matchCondition);
            stopwatch.start("executeMergeQuery");
            sparkUtils.execSparkSql(query);
            stopwatch.stop();

            stopwatch.start("dropTempView");
            spark.catalog().dropTempView(tempTableOrViewName);
            stopwatch.stop();
            publishMonitoringLog(String.format("Dropping temp view %s", tempTableOrViewName), LOG, INFO);
          }

          stopwatch.start("cleanupTemp");
          sinkTaskStrategy.cleanupTemp();
          stopwatch.stop();

          logger.info("M=flush recordsToAppendCounter={}, countAccumulatorCount={}, countAccumulatorSum={}, bytesAccumulatorCount={}, bytesAccumulatorSum={}",
              recordsToAppendCounter.get(),
              countAccumulator.count(),
              countAccumulator.sum(),
              bytesAccumulator.count(),
              bytesAccumulator.sum());

          stopwatch.start("sendMetric");
          sendMetric(String.format("%s%s", config.tableName, runId > 1L ? " (" + runId + ")" : ""),
              Optional.empty(), recordsToAppendCounter.get(), bytesAccumulator.sum(), 0L);
          stopwatch.stop();

          offsetsSender.ifPresent(this::commitOffsets);
          recordsToAppendCounter.set(0L);
          countAccumulator.reset();
          bytesAccumulator.count();
          sinkTaskStrategy.cleanupTemp();
          return readyToFlush.reportFlushed;
        }
        return false;
      } catch (Exception exc) {
        throw new RuntimeException(exc);
      }
    } finally {
      logger.info("Time breakdown: {}", stopwatch);
    }
  }

  private Dataset<Row> deduplicateKeys(Dataset<Row> dataset) {
    final WindowSpec windowSpec = Window.partitionBy(functions.col(esc(config.idFields)))
        .orderBy(functions.monotonically_increasing_id());
    return dataset
        .withColumn("row_num", functions.row_number().over(windowSpec))
        .filter("row_num = 1")
        .drop("row_num");
  }

  @Override
  protected ConfigDef configDef() {
    return IcebergSinkConnectorConfig.configDef();
  }

  @Override
  protected IcebergSinkConnectorConfig parseConfig(Map<String, String> props) {
    return new IcebergSinkConnectorConfig(props);
  }

  @Override
  protected void doPut(StreamEx<NexlaMessageContext> messages, int streamSize) {
    final NexlaStopwatch stopwatch = new NexlaStopwatch("doPut");
    try {
      if (config.cdcEnabled && this.cdcSinkTask.isPresent()) {
        stopwatch.start("doCdcPut");
        List<Pair<String, RecordMetric>> errorByTable = this.cdcSinkTask.get().doCdcPut(messages);
        long totalErrors = errorByTable
            .stream()
            .map(Pair::getRight)
            .mapToLong(it -> it.getErrorRecords().get())
            .sum();
        if (totalErrors < streamSize) {
          recordsToAppendCounter.addAndGet(streamSize);
          lastProcessedTimestamp.set(System.currentTimeMillis());
        }
        errorByTable.forEach(pair -> {
          String name = String.format("%s%s", pair.getLeft(), runId > 1L ? " (" + runId + ")" : "");
          RecordMetric recordMetric = pair.getRight();
          sendQuarantineMessage(name, recordMetric.getQuarantineMessages());
          sendMetric(name, Optional.empty(), 0, 0, recordMetric.errorRecords.get());
        });
        stopwatch.stop(); // doCdcPut
        return;
      }

      stopwatch.start("createDataframe");
      // Convert raw messages to JSON strings (avoid deserialization/serialization overhead)
      final List<String> jsonStrings = messages.map(ctx -> {
        offsets.put(ctx.topicPartition, ctx.kafkaOffset);
        try {
          return objectMapper.writeValueAsString(ctx.getOriginal().getRawMessage());
        } catch (final JsonProcessingException e) {
          throw new RuntimeException(e);
        }
      }).collect(Collectors.toList());

      // Create DataFrame directly from JSON strings
      Dataset<Row> df = spark.read().json(
          spark.createDataset(jsonStrings, Encoders.STRING())
      );
      stopwatch.stop();

      stopwatch.start("extractPartitionKeys");
      Seq<Column> partitionKeys = extractPartitionKeys(config, df);
      stopwatch.stop();

      if (!partitionKeys.isEmpty()) {
        stopwatch.start("sortAndRepartition");
        // spark version < 3.5.x and with partitioning: we need to handle pre-sorting the dataset ourselves
        df = df.sort(partitionKeys).repartition(partitionKeys);
        stopwatch.stop();
      }

      boolean isUpsert = UPSERT.equals(config.insertMode);
      if (isUpsert) {
        if (StringUtils.isEmpty(config.idFields)) {
          throw new IllegalArgumentException("Identity fields property must be defined and non-empty in upsert mode");
        }
        stopwatch.start("deduplicateKeys");
        df = deduplicateKeys(df);
        stopwatch.stop();
      }

      stopwatch.start("upsertTempTable");
      sinkTaskStrategy.upsertData(df);
      stopwatch.stop();

      stopwatch.start("publishMonitoringLog");
      publishMonitoringLog(String.format("Put batch of %d records to temp table", streamSize), LOG, INFO);
      stopwatch.stop();

      recordsToAppendCounter.addAndGet(streamSize);
      lastProcessedTimestamp.set(System.currentTimeMillis());
    } finally {
      logger.info("Time breakdown: {}", stopwatch);
    }
  }


  private void commitOffsets(final OffsetsCoordinationClient os) {
    if (offsets.isEmpty()) {
      return;
    }
    logger.info("Update offsets: {}", offsets);
    os.updateSinkOffsets(config.sinkId, offsets);
    offsets.clear();
  }

  private Seq<Column> extractPartitionKeys(IcebergSinkConnectorConfig config, Dataset<Row> dataset) {
    if (config.partitionKeys.isPresent()
        && !config.partitionKeys.get().isEmpty()) {
      List<Column> partitionKeys = Arrays.stream(
              config.partitionKeys.get().split("(?<!\\\\),"))
          .map(s -> s.replace("\\,", ",").trim())
          .map(dataset::col)
          .collect(Collectors.toList());

      return JavaConverters
          .asScalaIteratorConverter(partitionKeys.iterator())
          .asScala()
          .toSeq();
    } else {
      // noinspection unchecked
      return (Seq<Column>) Seq$.MODULE$.<Column>empty();
    }
  }

  public static String normalizePath(String prefix) {
    return opt(prefix).filter("/"::equals)
        .orElseGet(() ->
            ofNullable(prefix)
                .map(p -> Paths.get(p).toString() + "/")
                .orElse("/"));
  }

  private String esc(String col) {
    if (col.startsWith("`") && col.endsWith("`")) {
      return col;
    }

    return "`" + col + "`";
  }
}
