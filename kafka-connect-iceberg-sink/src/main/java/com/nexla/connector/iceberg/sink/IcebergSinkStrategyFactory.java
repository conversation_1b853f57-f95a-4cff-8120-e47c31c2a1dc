package com.nexla.connector.iceberg.sink;

import com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig;
import org.apache.spark.sql.SparkSession;

public class IcebergSinkStrategyFactory {

    public IcebergSinkStrategy createStrategy(
            final IcebergSinkConnectorConfig.TempDataHandlingType handlingType, SparkSession spark, String tempTableOrViewName
    ) {

        switch (handlingType) {
            case CACHED_DATASET:
                return new IcebergSinkTaskCachedDataset(spark, tempTableOrViewName);
            case LOCAL_FILE_SYSTEM:
                return new IcebergSinkTaskLocalFileSystem(spark, tempTableOrViewName);
            case DELTA_LAKE:
            default:
                return new IcebergSinkTaskDeltaTables(spark, tempTableOrViewName);
        }
    }
}