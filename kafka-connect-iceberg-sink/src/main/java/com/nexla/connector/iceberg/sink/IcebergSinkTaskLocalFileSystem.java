package com.nexla.connector.iceberg.sink;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;


/**
 * Implementation of {@link IcebergSinkStrategy} that uses the local file system
 * ({@link com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig.TempDataHandlingType#LOCAL_FILE_SYSTEM}).
 * for storing temporary data as Parquet files
 * <p>
 * This strategy writes incoming data to Parquet files in a temporary directory on the local
 * file system. These files are then read back when needed for processing operations.
 * The temporary files are automatically managed and cleaned up after use.
 */
public class IcebergSinkTaskLocalFileSystem extends BaseIcebergSinkTask {

  private String tempDirPath;
  private File tempDir;
  private final AtomicLong fileCounter = new AtomicLong(0);
  private final List<String> pendingFiles = Collections.synchronizedList(new ArrayList<>());
  private final Object fileWriteLock = new Object();
  private FileSystem fileSystem;

  public IcebergSinkTaskLocalFileSystem(final SparkSession spark, String tempTableOrViewName) {
    super(spark, tempTableOrViewName);
  }

  @Override
  public void initialize() {
    try {
      this.fileSystem = org.apache.hadoop.fs.FileSystem.get(spark.sparkContext().hadoopConfiguration());
    } catch (IOException e) {
      throw new RuntimeException("Failed to initialize HDFS", e);
    }

    // Initialize local file system storage with unique directory
    tempDirPath = BASE_TEMP_DIR + UUID.randomUUID().toString().replace("-", "_") + "/";
    tempDir = new File(tempDirPath);
    if (!tempDir.exists() && !tempDir.mkdirs()) {
      throw new RuntimeException("Failed to create temp directory: " + tempDirPath);
    }
    logger.info("Created temporary directory for Parquet files: {}", tempDirPath);
  }

  @Override
  public Dataset<Row> dataset() {

    // Create a list of files to process and clear the pending files list
    List<String> filesToProcess;
    synchronized (fileWriteLock) {
      filesToProcess = new ArrayList<>(pendingFiles);
      pendingFiles.clear();
    }
    if (CollectionUtils.isEmpty(filesToProcess)) {
      logger.info("No files to process in temp directory: {}", tempDirPath);
      return null;
    }

    // Read the parquet files into a Dataset
    final Dataset<Row> dataset = readParquetFiles(filesToProcess);

    // Delete processed files after they've been read successfully
    for (String file : filesToProcess) {
      try {
        fileSystem.delete(new org.apache.hadoop.fs.Path(file), false);
      } catch (Exception e) {
        logger.warn("Failed to delete temp parquet file: {}", file, e);
      }
    }
    return dataset;
  }

  @Override
  public void cleanup() {
    try {
      if (tempDir != null && tempDir.exists()) {
        FileUtils.deleteDirectory(tempDir);
        logger.info("Temp directory was cleaned up. path: {}", tempDirPath);
      }
    } catch (Exception e) {
      logger.error("Failed to cleanup temp directory: {}", tempDirPath, e);
    }

  }

  @Override
  public void cleanupTemp() {
    // do nothing here, as temp cleanup is handled in dataset() method
  }

  @Override
  public void upsertData(final Dataset<Row> sourceDataFrame) {
    // Write to parquet file
    final String filePath = tempDirPath + "batch_" + fileCounter.getAndIncrement() + ".parquet";
    sourceDataFrame.write().parquet(filePath);

    // Add file to pending files list
    synchronized (fileWriteLock) {
      pendingFiles.add(filePath);
    }
  }

  @Override
  public String tempTableOrViewName() {
    return tempTableOrViewName;
  }

  private boolean checkTempDeltaExists(String tempTablePath) {
    try {
      final FileSystem fs = FileSystem.get(spark.sparkContext().hadoopConfiguration());
      return fs.exists(new Path(tempTablePath + "/_delta_log"));
    } catch (IOException e) {
      logger.error("Failed to check if temp table exists.", e);
      throw new RuntimeException(e);
    }
  }

  private Dataset<Row> readParquetFiles(List<String> files) {
    if (files.isEmpty()) {
      return spark.emptyDataFrame();
    }
    return spark.read().parquet(files.toArray(new String[0]));
  }
}
