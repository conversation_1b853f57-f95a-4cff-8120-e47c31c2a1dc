package com.nexla.connector.iceberg.sink;

import org.apache.hadoop.fs.FileSystem;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.conf.Configuration;
import org.apache.spark.sql.SparkSession;

import java.io.IOException;

/**
 * Implementation of {@link IcebergSinkStrategy} that uses Delta Lake tables
 * for temporary data storage,
 * i.e. {@link com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig.TempDataHandlingType#DELTA_LAKE}.
 * <p>
 * This strategy writes incoming data to Delta tables in a designated temporary
 * directory. The Delta format provides transactional guarantees and schema
 * evolution capabilities during the buffering phase before data is written
 * to the target Iceberg tables.
 * <p>
 * Data is appended to the temporary Delta table for each batch and read back
 * when needed for processing operations. The temporary Delta tables are
 * automatically managed and cleaned up after use.
 */
public class IcebergSinkTaskDeltaTables extends BaseIcebergSinkTask {

  public IcebergSinkTaskDeltaTables(final SparkSession spark, String tempTableOrViewName) {
    super(spark, tempTableOrViewName);
  }

  @Override
  public void initialize() {

  }

  @Override
  public Dataset<Row> dataset() {
    return spark.read().format("delta").load(getTempTablePath());
  }

  @Override
  public void cleanup() {
    cleanupTemp();

  }

  @Override
  public void cleanupTemp() {
    final String path = getTempTablePath();
    try {
      final FileSystem fs = FileSystem.get(
          new Configuration());
      fs.delete(new Path(path), true);
      logger.info("Temp delta storage was cleaned up. path: {}", path);
    } catch (final Exception e) {
      logger.error("Failed to cleanup temp delta storage. path: {}", path, e);
    }
  }

  @Override
  public void upsertData(Dataset<Row> sourceDataFrame) {
    String tempTablePath = getTempTablePath();
    if (!checkTempDeltaExists(tempTablePath)) {
      sourceDataFrame.write().format("delta").mode("overwrite").save(tempTablePath);
      logger.info("Delta table created & data inserted: {}", tempTablePath);
    } else {
      sourceDataFrame.write().format("delta").mode("append").option("mergeSchema", "true").save(tempTablePath);
    }
  }

  @Override
  public String tempTableOrViewName() {
    return tempTableOrViewName;
  }

  private boolean checkTempDeltaExists(String tempTablePath) {
    try {
      final FileSystem fs = org.apache.hadoop.fs.FileSystem.get(spark.sparkContext().hadoopConfiguration());
      return fs.exists(new org.apache.hadoop.fs.Path(tempTablePath + "/_delta_log"));
    } catch (IOException e) {
      logger.error("Failed to check if temp table exists.", e);
      throw new RuntimeException(e);
    }
  }

  private String getTempTablePath() {
    return BASE_TEMP_DIR + tempTableOrViewName;
  }
}
