package com.nexla.connector.iceberg.sink;

import com.nexla.common.logging.NexlaLogger;
import org.apache.spark.sql.SparkSession;
import org.slf4j.LoggerFactory;

/**
 * Abstract base implementation of the {@link IcebergSinkStrategy} interface that provides
 * common functionality for different data handling strategies in the Iceberg sink connector.
 * <p>
 * Subclasses should implement the specific data handling mechanisms according to their
 * strategy type (e.g., local file system, Delta tables, or in-memory caching).
 */
public abstract class BaseIcebergSinkTask implements IcebergSinkStrategy {

  protected static final String BASE_TEMP_DIR = "/tmp/iceberg_sink/";

  protected final NexlaLogger logger = new NexlaLogger(LoggerFactory.getLogger(this.getClass()));

  protected final SparkSession spark;
  protected final String tempTableOrViewName;

  protected BaseIcebergSinkTask(SparkSession spark, String tempTableOrViewName) {
    this.spark = spark;
    this.tempTableOrViewName = tempTableOrViewName;
  }
}
