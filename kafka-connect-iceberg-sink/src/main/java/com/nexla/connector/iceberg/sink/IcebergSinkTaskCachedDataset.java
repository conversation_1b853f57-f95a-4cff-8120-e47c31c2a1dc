package com.nexla.connector.iceberg.sink;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

/**
 * Implementation of {@link IcebergSinkStrategy} that uses Spark's in-memory caching
 * mechanism for temporary data storage,
 * i.e. {@link com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig.TempDataHandlingType#CACHED_DATASET}.
 * <p>
 * This strategy maintains a cached dataset in Spark's memory (with disk overflow
 * capabilities) instead of writing to temporary files or tables. New data is
 * unioned with the existing cached data. This approach minimizes I/O operations
 * and provides faster processing for workloads that can fit in memory.
 * <p>
 * The cached dataset is stored with MEMORY_AND_DISK persistence level, allowing
 * Spark to spill to disk if necessary while keeping as much data in memory as
 * possible for optimal performance. The cached data is properly unpersisted
 * during cleanup to free memory resources.
 */
public class IcebergSinkTaskCachedDataset extends BaseIcebergSinkTask {

  // With this optimized approach using in-memory caching
  private Dataset<Row> cachedDataset = null;

  public IcebergSinkTaskCachedDataset(SparkSession spark, String tempTableOrViewName) {
    super(spark, tempTableOrViewName);
  }


  @Override
  public void initialize() {
    // Nothing do here
  }

  @Override
  public Dataset<Row> dataset() {
    return cachedDataset;
  }

  @Override
  public void upsertData(final Dataset<Row> sourceDataFrame) {
    // Use Spark's native caching mechanism instead of Delta tables
    if (cachedDataset == null) {
      cachedDataset = sourceDataFrame.persist(org.apache.spark.storage.StorageLevel.MEMORY_AND_DISK());
      logger.info("Created new cached dataset for batch processing");
    } else {
      // Union with existing data
      cachedDataset = cachedDataset.union(sourceDataFrame)
          .persist(org.apache.spark.storage.StorageLevel.MEMORY_AND_DISK());
      logger.info("Appended to cached dataset for batch processing");
    }
  }

  @Override
  public String tempTableOrViewName() {
    return tempTableOrViewName;
  }

  @Override
  public void cleanup() {
    cleanupTemp();
  }

  @Override
  public void cleanupTemp() {
    // Simply unpersist the cached dataset instead of filesystem cleanup
    if (cachedDataset != null) {
      cachedDataset.unpersist();
      cachedDataset = null;
    }
  }
}
