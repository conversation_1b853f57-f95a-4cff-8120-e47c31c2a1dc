package com.nexla.connector.iceberg.sink;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.Fixtures;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaConstants;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connect.common.PostponedFlush;
import com.nexla.connect.common.ReadyToFlush;
import com.nexla.connect.common.offsets.OffsetsTracker;
import com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig;
import com.nexla.listing.client.ListingClient;
import com.nexla.probe.iceberg.IcebergConnectorService;
import com.nexla.test.IntegrationTests;
import lombok.SneakyThrows;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.ClassRule;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.mockito.Mockito;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.utility.DockerImageName;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.NexlaConstants.CREDENTIALS_DECRYPT_KEY;
import static com.nexla.common.NexlaConstants.CREDS_ENC;
import static com.nexla.common.NexlaConstants.CREDS_ENC_IV;
import static com.nexla.common.NexlaConstants.LISTING_ENABLED;
import static com.nexla.common.NexlaConstants.MONITOR_POLL_MS;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.common.NexlaConstants.SINK_TYPE;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.file.AWSAuthConfig.SERVICE_ENDPOINT;
import static com.nexla.connector.config.file.FileSinkConnectorConfig.SPARK_SESSION_CONFIGS;
import static com.nexla.connector.config.file.S3Constants.ACCESS_KEY_ID;
import static com.nexla.connector.config.file.S3Constants.REGION;
import static com.nexla.connector.config.file.S3Constants.SECRET_KEY;
import static com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig.TABLE_NAME;
import static com.nexla.connector.iceberg.sink.LocalStackContainerAWSSDK2.Service.S3;
import static com.nexla.connector.properties.FileConfigAccessor.FILE_NAME_PREFIX;
import static com.nexla.connector.properties.FileConfigAccessor.MAX_FILE_SIZE_MB;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;

@Category(IntegrationTests.class)
public class IcebergSinkTaskLocalFileSystemTest extends BaseKafkaTest {
  public static KafkaContainer kafka = new KafkaContainer("7.2.11");
  static DockerImageName localstackImage = DockerImageName.parse("localstack/localstack:4.1.1");

  private IcebergSinkTask task;

  @ClassRule
  public static LocalStackContainerAWSSDK2 localstack = new LocalStackContainerAWSSDK2(localstackImage)
      .withServices(
          LocalStackContainerAWSSDK2.Service.S3,
          LocalStackContainerAWSSDK2.EnabledService.named("glue")
      );

  @SneakyThrows
  @BeforeClass
  public static void setup() {
    localstack.execInContainer("awslocal", "s3", "mb", "s3://nexla-data-lake");
    localstack.execInContainer("awslocal", "glue", "create-database", "--database-input", "{\"Name\":\"db1\"}");
    kafka.start();
    init(kafka);
  }

  @AfterClass
  public static void teardown() {
    SparkSession.active().close();
  }

  @Before
  public void onBefore() {
    AdminApiClient adminApiClient = Fixtures.adminApiWithSourceSinkDataset(1);
    AdminApiClientBuilder.INSTANCE = adminApiClient;

    this.task = new IcebergSinkTask() {
      @Override
      public void doStart() {
        super.doStart();
        this.listingClient = Mockito.mock(ListingClient.class);
        this.offsetsSender = Optional.of(Mockito.mock(OffsetsTracker.class));
        this.postponedFlush = Mockito.mock(PostponedFlush.class);
        Mockito.when(this.postponedFlush.readyToFlush()).thenReturn(ReadyToFlush.FLUSH);
      }
    };

    SinkTaskContext taskContext = Mockito.mock(SinkTaskContext.class);
    Mockito.when(taskContext.assignment()).thenReturn(Collections.emptySet());
    task.initialize(taskContext);
  }

  @After
  @SneakyThrows
  public void after() {
    if (task != null
        && task.getControlMessageProducer() != null) {
      task.getControlMessageProducer().close();
      task.stop();
    }
  }

  @Test
  public void appendInS3UsingDefaultCatalog() {
    var connectorConfig = getSinkConnectorConfigProps("appendInS3UsingDefaultCatalog");
    task.start(connectorConfig);
    testInsertTestCase(connectorConfig);
  }

  @Test
  public void appendInS3UsingNonDefaultDbAndDefaultCatalog() {
    var connectorConfig = getSinkConnectorConfigProps("appendInS3UsingNonDefaultDbAndDefaultCatalog");
    connectorConfig.put("iceberg.database.name", "db1");
    task.start(connectorConfig);
    testInsertTestCase(connectorConfig);
  }

  @Test
  public void upsertInS3UsingDefaultCatalog() {
    var connectorConfig = getSinkConnectorConfigProps("upsertInS3UsingDefaultCatalog");
    connectorConfig.put("iceberg.database.name", "db1");
    connectorConfig.put("iceberg.insert.mode", "upsert");
    connectorConfig.put("iceberg.id-fields", "title");
    task.start(connectorConfig);
    testUpsertTestCase(connectorConfig);
  }

  @Test
  public void upsertInS3UsingNonDefaultDbAndDefaultCatalog() {
    var connectorConfig = getSinkConnectorConfigProps("upsertInS3UsingNonDefaultDbAndDefaultCatalog");
    connectorConfig.put("iceberg.insert.mode", "upsert");
    connectorConfig.put("iceberg.id-fields", "title");
    task.start(connectorConfig);
    testUpsertTestCase(connectorConfig);
  }

  @Test
  public void castLongToString() {
    var connectorConfig = getSinkConnectorConfigProps("cast.long.to.string");
    task.start(connectorConfig);

    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null,
        JsonUtils.toJsonString(createMessage(new LinkedHashMap<>(Map.of("person", "sue1", "age", "32")))), 1)));
    task.doFlush(ReadyToFlush.FLUSH);

    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null,
        JsonUtils.toJsonString(createMessage(new LinkedHashMap<>(Map.of("person", "sue2", "age", 40)))), 1)));
    task.doFlush(ReadyToFlush.FLUSH);

    var rows = getRows(connectorConfig);

    Assert.assertEquals(2, rows.size());
    Row row1 = rows.stream().filter(r -> r.getAs("person").equals("sue1")).findFirst().get();
    Assert.assertEquals("32", row1.<String>getAs("age"));
    Row row2 = rows.stream().filter(r -> r.getAs("person").equals("sue2")).findFirst().get();
    Assert.assertEquals("40", row2.<String>getAs("age"));
  }

  @Test
  public void castStringToLong() {
    var connectorConfig = getSinkConnectorConfigProps("cast_long_to_string");
    task.start(connectorConfig);

    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null,
        JsonUtils.toJsonString(createMessage(new LinkedHashMap<>(Map.of("person", "sue1", "age", 32)))), 1)));
    task.doFlush(ReadyToFlush.FLUSH);

    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null,
        JsonUtils.toJsonString(createMessage(new LinkedHashMap<>(Map.of("person", "sue2", "age", "40")))), 1)));
    Throwable thrown = assertThrows(RuntimeException.class, () -> task.doFlush(ReadyToFlush.FLUSH));
    assertTrue(thrown.getMessage().contains("Cannot safely cast 'age': string to bigint"));

    var rows = getRows(connectorConfig);
    Assert.assertEquals(1, rows.size());
    Row row1 = rows.stream().filter(r -> r.getAs("person").equals("sue1")).findFirst().get();
    Assert.assertEquals(Long.valueOf(32), row1.<Long>getAs("age"));
  }

  @Test
  public void testDoPutWithStringRawMessage() {
    // Setup
    var connectorConfig = getSinkConnectorConfigProps("test_do_put_string_raw");
    task.start(connectorConfig);

    // Use exactly the same pattern as the working tests - create using Map.of and wrap in LinkedHashMap
    LinkedHashMap<String, Object> rawMessage = new LinkedHashMap<>(Map.of(
        "person", "test_person",
        "age", "25"
    ));

    // Create the message and convert to JSON string exactly like other working tests
    task.put(Collections.singletonList(new SinkRecord("test-topic", 0, null, null, null,
        JsonUtils.toJsonString(createMessage(rawMessage)), 0)));

    // Force flush to verify data was processed
    task.doFlush(ReadyToFlush.FLUSH);

    // Verify data was written
    var rows = getRows(connectorConfig);
    Assert.assertEquals(1, rows.size());
    Row row = rows.get(0);
    Assert.assertEquals("test_person", row.getAs("person"));
    Assert.assertEquals("25", row.getAs("age"));
  }

  @Test
  public void testDoPutWithMapRawMessage() {
    // Setup
    var connectorConfig = getSinkConnectorConfigProps("test_do_put_map_raw");
    task.start(connectorConfig);

    // Create message with Map.of pattern used in working tests
    LinkedHashMap<String, Object> rawMessage = new LinkedHashMap<>(Map.of(
        "person", "map_person",
        "age", "30"
    ));

    // Don't pass the raw map directly - convert to NexlaMessage and then to JSON string
    NexlaMessage message = createMessage(rawMessage);
    String jsonMessage = JsonUtils.toJsonString(message);

    // Put the message as a JSON string
    task.put(Collections.singletonList(new SinkRecord("test-topic", 0, null, null, null, jsonMessage, 0)));

    // Force flush
    task.doFlush(ReadyToFlush.FLUSH);

    // Verify data was written
    var rows = getRows(connectorConfig);
    Assert.assertEquals(1, rows.size());
    Row row = rows.get(0);
    Assert.assertEquals("map_person", row.getAs("person"));
    Assert.assertEquals("30", row.getAs("age"));
  }

  private Map<String, String> getSinkConnectorConfigProps(String tableName) {
    return new HashMap<>() {{
      put(NexlaConstants.CONTROL_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
      put(NexlaConstants.DATA_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
      put(NexlaConstants.CONTROL_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);
      put(NexlaConstants.DATA_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);
      put(UNIT_TEST, "true");
      put(SINK_ID, "1");
      put(CREDS_ENC, "1");
      put(CREDS_ENC_IV, "1");
      put(CREDENTIALS_DECRYPT_KEY, "1");
      put(LISTING_ENABLED, "false");
      put(SINK_TYPE, ConnectionType.S3_ICEBERG.name());
      put(ACCESS_KEY_ID, localstack.getAccessKey());
      put(SECRET_KEY, localstack.getSecretKey());
      put(REGION, localstack.getRegion());
      put(SERVICE_ENDPOINT, localstack.getEndpointOverride(S3).toString());
      put(TABLE_NAME, tableName);
      put("iceberg.warehouse.dir", "nexla-data-lake");
      put(SPARK_SESSION_CONFIGS, "spark.driver.bindAddress=127.0.0.1");
      put(MAX_FILE_SIZE_MB, "0");
      put(FILE_NAME_PREFIX, "my-dataset");
      put(MONITOR_POLL_MS, "1");
      put("log.verbose", "true");
      put("temp.data.handling.type", "LOCAL_FILE_SYSTEM");
    }};
  }

  static NexlaMessage createMessage(LinkedHashMap<String, Object> rawMessage) {
    NexlaMessage message = new NexlaMessage(rawMessage);
    NexlaMetaData nexlaMetaData = new NexlaMetaData();
    nexlaMetaData.setRunId(1L);
    nexlaMetaData.setIngestTime(System.currentTimeMillis());
    message.setNexlaMetaData(nexlaMetaData);
    return message;
  }

  private void testInsertTestCase(Map<String, String> connectorConfig) {
    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null,
        JsonUtils.toJsonString(createMessage(new LinkedHashMap<>(Map.of("title", "World of Warcraft 1", "release_date", "2004-11-23")))), 1)));
    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null,
        JsonUtils.toJsonString(createMessage(new LinkedHashMap<>(Map.of("title", "World of Warcraft 2", "release_date", "2004-11-24")))), 1)));
    task.doFlush(ReadyToFlush.FLUSH);

    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null,
        JsonUtils.toJsonString(createMessage(new LinkedHashMap<>(Map.of("title", "World of Warcraft 2", "release_date", "2004-11-25")))), 1)));
    task.doFlush(ReadyToFlush.FLUSH);

    List<Row> rows = getRows(connectorConfig);
    Assert.assertEquals(3, rows.size());
    Row row1 = rows.stream().filter(r -> r.getAs("title").equals("World of Warcraft 1")).findFirst().get();
    Assert.assertEquals("2004-11-23", row1.getAs("release_date"));
    long count2 = rows.stream().filter(r -> r.getAs("title").equals("World of Warcraft 2")).count();
    Assert.assertEquals(2, count2);
  }

  private void testUpsertTestCase(Map<String, String> connectorConfig) {
    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null,
        JsonUtils.toJsonString(createMessage(new LinkedHashMap<>(Map.of("title", "World of Warcraft 1", "release_date", "2004-11-23")))), 1)));
    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null,
        JsonUtils.toJsonString(createMessage(new LinkedHashMap<>(Map.of("title", "World of Warcraft 2", "release_date", "2004-11-24")))), 1)));
    task.doFlush(ReadyToFlush.FLUSH);

    task.put(Collections.singletonList(new SinkRecord("test-topic", 1, null, null, null,
        JsonUtils.toJsonString(createMessage(new LinkedHashMap<>(Map.of("title", "World of Warcraft 2", "release_date", "2004-11-25")))), 1)));
    task.doFlush(ReadyToFlush.FLUSH);

    List<Row> rows = getRows(connectorConfig);
    Assert.assertEquals(2, rows.size());
    Row row1 = rows.stream().filter(r -> r.getAs("title").equals("World of Warcraft 1")).findFirst().get();
    Assert.assertEquals("2004-11-23", row1.getAs("release_date"));
    Row row2 = rows.stream().filter(r -> r.getAs("title").equals("World of Warcraft 2")).findFirst().get();
    Assert.assertEquals("2004-11-25", row2.getAs("release_date"));
  }

  private static List<Row> getRows(Map<String, String> connectorConfig) {
    var probe = new IcebergConnectorService();
    var icebergSinkConnectorConfig = new IcebergSinkConnectorConfig(connectorConfig);
    SparkSession spark = probe.getSparkSession(icebergSinkConnectorConfig, Collections.emptyMap());
    var df = spark.table(icebergSinkConnectorConfig.getFullTableName());
    var rows = df.sqlContext().sql(String.format("SELECT * FROM %s", icebergSinkConnectorConfig.getFullTableName())).collectAsList();
    return rows;
  }
}
