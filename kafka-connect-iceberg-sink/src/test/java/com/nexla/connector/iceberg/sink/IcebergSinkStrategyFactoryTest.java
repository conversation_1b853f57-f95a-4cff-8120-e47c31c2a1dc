package com.nexla.connector.iceberg.sink;

import com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig;
import com.nexla.test.UnitTests;
import junit.framework.TestCase;
import lombok.val;
import org.apache.spark.sql.SparkSession;
import org.junit.experimental.categories.Category;
import org.mockito.Mockito;

import java.util.UUID;

@Category(UnitTests.class)
public class IcebergSinkStrategyFactoryTest extends TestCase {

  private final SparkSession spark = Mockito.mock(SparkSession.class);
  private final String tempTableOrViewName = "test_temp_table_" + UUID.randomUUID();

  public void testCreateStrategy() {
    val factory = new IcebergSinkStrategyFactory();

    val deltaTables = factory.createStrategy(
        IcebergSinkConnectorConfig.TempDataHandlingType.DELTA_LAKE, spark, tempTableOrViewName
    );
    assertTrue(deltaTables instanceof IcebergSinkTaskDeltaTables);

    val cachedDateset = factory.createStrategy(
        IcebergSinkConnectorConfig.TempDataHandlingType.CACHED_DATASET, spark, tempTableOrViewName
    );
    assertTrue(cachedDateset instanceof IcebergSinkTaskCachedDataset);

    val localFileSystem = factory.createStrategy(
        IcebergSinkConnectorConfig.TempDataHandlingType.LOCAL_FILE_SYSTEM, spark, tempTableOrViewName
    );
    assertTrue(localFileSystem instanceof IcebergSinkTaskLocalFileSystem);

  }
}