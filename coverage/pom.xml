<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.nexla</groupId>
        <artifactId>backend-connectors</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>

    <artifactId>coverage</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>base-connector-source-agent</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>base-connector-sink-agent</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>common-connector</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>file-source-sink</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-jdbc-source</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-jdbc-sink</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-documentdb-sink</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-redis-sink</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-rest-source</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-rest-sink</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-soap-source</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-soap-sink</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-bigquery-source</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-bigquery-sink</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-spreadsheets-sink</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-file-source</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-file-sink</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-documentdb-source</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-iceberg-source</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-iceberg-sink</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-vectordb-sink</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-vectordb-source</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>kafka-connect-api-streams-source</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>file-service</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>inmemory-connector-common</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>inmemory-connector</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>connector-properties</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>connector-test</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>parsers</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>parser-properties</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>replication-connector</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>cloud-job-connector</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>nexla-spark-agent</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>ingestion-service</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>webdav-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>sql-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>ftp-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>dropbox-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>one-drive-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>bigquery-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>redis-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>sharepoint-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>gdrive-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>azure-blob-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>delta-lake-service</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>gcs-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>kafka-probe-service</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>probe-http</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>box-service</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>vectordb-service</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>documentdb-service</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>s3-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>file-service-utils</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>iceberg-probe</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>http-probe</artifactId>
            <version>${version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>verify</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                        <configuration>
                            <dataFileIncludes>
                                <dataFileInclude>**/jacoco.exec</dataFileInclude>
                            </dataFileIncludes>
                            <outputDirectory>${project.reporting.outputDirectory}/jacoco-aggregate</outputDirectory>
                            <excludes>
                                <exclude>*$$anonfun$*</exclude>
                                <exclude>*$lambda$*</exclude>
                                <exclude>*$anon$*</exclude>
                                <exclude>*.access$*</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>