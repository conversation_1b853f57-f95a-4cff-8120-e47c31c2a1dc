package com.nexla.coverage;

import static org.junit.Assert.fail;

import com.google.common.collect.Sets;
import java.io.File;
import java.io.FileReader;
import java.util.*;
import java.util.stream.Collectors;
import lombok.val;
import org.apache.maven.model.Dependency;
import org.apache.maven.model.io.xpp3.MavenXpp3Reader;
import org.junit.Test;

/**
 * This test class ensures that all modules listed in the root `pom.xml` are added as dependencies
 * in the `coverage/pom.xml`. This is required for accurate reporting of coverage numbers.
 */
public class ModuleDependencyTest {

  /**
   * Test method to verify that all modules listed in the root `pom.xml` (excluding the `coverage`
   * module) are included as dependencies in the `coverage/pom.xml`. If any module is missing, the
   * test fails with a message listing the missing dependencies.
   *
   * @throws Exception if an error occurs while reading the POM files.
   */
  @Test
  public void test() throws Exception {
    val rootDir = getRootDirectory();

    val modules = getModulesFromPom(new File(rootDir, "pom.xml"));
    modules.remove("coverage");

    val dependencies = getDependenciesFromPom(new File(rootDir, "coverage/pom.xml"));

    val missingDependencies = Sets.difference(new HashSet<>(modules.values()), dependencies);

    if (!missingDependencies.isEmpty()) {
      fail("Add dependencies in coverage/pom.xml: " + String.join(", ", missingDependencies));
    }
  }


  /**
   * Retrieves a map of module names to artifact IDs defined in the given `pom.xml` file.
   * This method reads the `pom.xml` file to extract the list of modules, then iterates through
   * each module to locate its corresponding `pom.xml` file and retrieve its artifact ID.
   *
   * @param pomFile the `pom.xml` file to read.
   * @return a map where the key is the module name and the value is the artifact ID, or an empty map if no modules are found.
   * @throws Exception if an error occurs while reading the `pom.xml` file or any module's `pom.xml` file.
   */
  private Map<String, String> getModulesFromPom(File pomFile) throws Exception {
    val reader = new MavenXpp3Reader();
    val model = reader.read(new FileReader(pomFile));
    final Set<String> modules = model.getModules() != null ? new HashSet<>(model.getModules()) : Collections.emptySet();
    val map = new HashMap<String, String>();
    for (val module : modules) {
      // Normalize module names to remove any leading or trailing slashes
      File modulePomFile = new File(pomFile.getParent(), module + "/pom.xml");
      if (modulePomFile.exists()) {
        val moduleModel = reader.read(new FileReader(modulePomFile));
        val artifactId = moduleModel.getArtifactId();
        map.put(module, artifactId);
      }
    }
    return map;
  }

  /**
   * Retrieves the set of dependencies from the given `pom.xml` file that belong to the group
   * `com.nexla`.
   *
   * @param pomFile the `pom.xml` file to read.
   * @return a set of artifact IDs for dependencies belonging to the group `com.nexla`.
   * @throws Exception if an error occurs while reading the `pom.xml` file.
   */
  private Set<String> getDependenciesFromPom(File pomFile) throws Exception {
    return new MavenXpp3Reader()
        .read(new FileReader(pomFile)).getDependencies().stream()
            .map(Dependency::getArtifactId)
            .collect(Collectors.toSet());
  }

  /**
   * Retrieves the root directory of the project. If the current directory ends with `coverage`, the
   * parent directory is returned; otherwise, the current directory is returned.
   *
   * @return the root directory of the project.
   */
  private String getRootDirectory() {
    val rootDir = System.getProperty("user.dir");
    return rootDir.endsWith("coverage") ? new File(rootDir).getParent() : rootDir;
  }
}
