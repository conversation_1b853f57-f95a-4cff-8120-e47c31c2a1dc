package com.nexla.connector.file.source;

import com.nexla.common.exception.ParseError;
import com.nexla.common.metrics.Metric;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;

import java.util.Map;
import java.util.Optional;

public class NoopFileSourceNotificationSender extends FileSourceNotificationSender {
    public NoopFileSourceNotificationSender() {
        super(null, null, null, null, null);
    }

    public void publishExceptionMessage(ReadingContext ctx, long numberOfRowsReadInBatch, String errorMessage, Exception exception, Optional<Long> runId) {
    }

    public void sendQuarantineRecord(ReadingContext readingContext, Throwable exp, ParseError error, Optional<Long> runId) {
    }

    public void sendSuccessMessage(ReadingContext ctx, Map<Integer, Metric> datasetMetrics, boolean fileEofReached, Optional<Long> runId) {
    }

    public void publishDownloadMessage(String log, String displayPath, Integer orgId, NexlaMonitoringLogSeverity severity) {
    }

    public void publishMonitoringMessage(String log, Integer orgId, NexlaMonitoringLogType logType, NexlaMonitoringLogSeverity severity) {
    }

    public void publishMonitoringMetrics(Integer orgId, long records, long size, long errors, String fileName) {
    }
}