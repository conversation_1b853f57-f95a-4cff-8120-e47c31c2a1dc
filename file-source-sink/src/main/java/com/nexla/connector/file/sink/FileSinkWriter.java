package com.nexla.connector.file.sink;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ibm.icu.impl.Pair;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSet;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.NotificationUtils;
import com.nexla.common.Resource;
import com.nexla.common.ResourceType;
import com.nexla.common.exception.AuthFailException;
import com.nexla.common.exception.NexlaErrorMessage;
import com.nexla.common.exception.NexlaException;
import com.nexla.common.exception.NexlaExceptionChecker;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.sink.TopicPartition;
import com.nexla.common.transform.Flattener;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import com.nexla.connector.file.sink.names.FileNamingGenerator;
import com.nexla.connector.file.sink.names.FileNaming;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import com.nexla.client.JavaJobSchedulerClient;
import com.nexla.parser.ASCEnryptUtils;
import com.nexla.parser.JsonUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.kafka.connect.errors.ConnectException;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;


import static com.google.common.base.MoreObjects.firstNonNull;
import static com.nexla.common.Resource.sink;
import static com.nexla.common.StreamUtils.zipWithIndices;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.time.VarUtils.VarInfo;
import static com.nexla.common.time.VarUtils.getFolderStructure;
import static com.nexla.common.time.VarUtils.replaceVars;
import static com.nexla.connector.config.file.AWSAuthConfig.getDirectoryPath;
import static com.nexla.connector.config.file.FileSinkConnectorConfig.RECORDS_PER_FILE_UNLIMITED;
import static java.util.Collections.emptyMap;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;
import static org.joda.time.Minutes.minutesBetween;

public class FileSinkWriter {

	private static final Map<String, String> DEFAULT_PARTITION = emptyMap();
	private static ScheduledExecutorService SCHEDULED_POOL = Executors.newScheduledThreadPool(1);

	private final PollingWriter pollingWriter;
	private final FileSinkConnectorConfig config;
	private final FileSinkNotificationSender notificationSender;
	private final NexlaLogger logger;
	private final String localDir;
	private final Optional<String> identifier;
	private final AdminApiClient adminApiClient;

	/**
	 * LRU cache for handling max open files per task
	 */
	private final Map<String, FileWriter> filesCache = new HashMap<>();
	/**
	 * Map of
	 * -> key-values defining the group (i.e. "City=NY, vehicleType=car")
	 * -> file writers for this group
	 */
	public final Map<
		Map<String, String>, //  "City=NY, vehicleType=car"
		LinkedList<FileWriter>
		> writers = new HashMap<>();

	private final ExecutorService executorService;
	private final Optional<JavaJobSchedulerClient> jobSchedulerClient;
	private final FileNamingGenerator fileNamingGenerator;
	@Setter
	private Collection<TopicPartition> topicPartitions = Lists.newArrayList();
	@Getter
	private Optional<Long> runId;

	public final Set<Long> heartbeatRunIds = Sets.newHashSet();

	private final boolean storeOffsets;

	public FileSinkWriter(
		Optional<String> identifier,
		PollingWriter pollingWriter,
		FileSinkConnectorConfig config,
		FileSinkNotificationSender notificationSender,
		String localDir,
		NexlaLogger logger,
		boolean storeOffsets,
		Optional<JavaJobSchedulerClient> jobSchedulerClient,
		FileConnectorService probe,
        AdminApiClient adminApiClient,
		FileNamingGenerator fileNamingGenerator
	) {
		this.identifier = identifier.map(e -> "-" + e);
		this.pollingWriter = pollingWriter;
		this.config = config;
		this.notificationSender = notificationSender;
		this.logger = logger;
		this.localDir = localDir;
		this.storeOffsets = storeOffsets;
		this.executorService = Executors.newWorkStealingPool(probe.calculateSinkParallelism(config));
		this.jobSchedulerClient = jobSchedulerClient;
		this.adminApiClient = adminApiClient;
		this.fileNamingGenerator = fileNamingGenerator;
	}

	public boolean containsTopicPartition(TopicPartition partition) {
		return topicPartitions.contains(partition);
	}

	@Deprecated
	public void write(NexlaMessageContext message, Optional<Long> runId) {
		this.write(message, runId, Optional.empty());
	}

	public void write(NexlaMessageContext message, Optional<Long> runId, Optional<MappingConfig> mappingConfig) {
		FileWriter writer = null;
		try {
			this.runId = runId;
			this.runId.ifPresent(this.heartbeatRunIds::add);

			LinkedHashMap<String, Object> mappingRawMessage = new LinkedHashMap<>(message.getOriginal().getRawMessage());
			mappingRawMessage.putAll(message.getMapped().getRawMessage());
			NexlaMessage mappingMessage = new NexlaMessage(mappingRawMessage, message.getOriginal().getNexlaMetaData());

			Map<String, String> messageKey = getMessageKey(mappingMessage);
			FileNaming fileNaming = fileNamingGenerator.createFileNaming(messageKey, message.getTopicPartition().topic);
			writer = getOrCreateWriter(message, fileNaming, messageKey);
			writer.write(message, mappingConfig);
		} catch (Exception e) {
			logger.error("error while writing from topic {}, exception {}", message.getTopicPartition().topic, e);
			if (writer != null) {
				Optional<DataSet> dataSet = adminApiClient.getDataSet(config.datasetId);
				notificationSender.publishMetrics(writer.dataFilePath, Optional.empty(), 0L, 0L,
					1L, runId, dataSet, Optional.of(new NexlaErrorMessage(e.getMessage(), NotificationUtils.getStacktraceForDb(e))));
				notificationSender.publishExceptionMessage(writer.dataFilePath, 0L,
					new ConnectException("Error writing record in Sink connector with ID=" + config.sinkId, e), 1, runId.orElse(0L));
			}
			notificationSender.sendQuarantineMessage(sink(config.sinkId), message.getMapped(), e);
			throw new RuntimeException(e);
		}
	}

	public void close() {

		writers.values().stream().flatMap(Collection::stream).forEach(writer -> {

			if (!writer.isClosed()) {
				try {
					writer.close();
				} catch (Exception e) {
					logger.warn("Failed to close writer: [{}], [{}]",
						writer.getDataFilePath(),
						indexFilePaths(topicPartitions, writer));
				}
			}

			try {
				writer.delete();
			} catch (Exception ioe) {
				logger.warn("Failed to delete temp files: [{}], [{}]",
					writer.getDataFilePath(),
					indexFilePaths(topicPartitions, writer));
			}
		});

		writers.clear();
		filesCache.clear();
		topicPartitions.clear();
		heartbeatRunIds.clear();
	}

	public boolean writersReady(long maxInactivityMin) {
		return !writers.isEmpty() && writers
			.values()
			.stream()
			.flatMap(Collection::stream)
			.noneMatch(writer -> {
				int fileInactivityMin = minutesBetween(writer.getLastModified(), nowUTC()).getMinutes();
				return fileInactivityMin < maxInactivityMin;
			});
	}

	public long getTotalRecordsNum() {
		return writers
				.values()
				.stream()
				.flatMap(Collection::stream)
				.mapToLong(fileWriter -> fileWriter.numRecords)
				.sum();
	}

	@SneakyThrows
	public boolean flush() {
		return this.flush(ignored -> {});
	}

	/**
	 * @param progressConsumer - consumer for the number of flushed records, used to report current flush operation progress
	 */
	@SneakyThrows
	public boolean flush(Consumer<Long> progressConsumer) {
		if (!writers.isEmpty()) {
			DateTime nowInUserTimeZone = getNowInUserTimeZone(nowUTC());

			List<Pair<Map<String, String>, FileWriter>> inlineWriters = StreamEx.of(writers.entrySet())
					.filter(x -> !x.getValue().isEmpty())
					.flatMap(x -> x.getValue().stream().map(xx -> Pair.of(x.getKey(), xx)))
					.filter(x -> x.second.flushAndFinalizeStats())
					.toList();

			int batchSize = Math.max(inlineWriters.size() / config.sinkParallelism, 1);
			List<List<Pair<Map<String, String>, FileWriter>>> listWriters = Lists.partition(inlineWriters, batchSize);

			if (inlineWriters.size() > 0) {
				doFlushWriters(nowInUserTimeZone, inlineWriters, listWriters, progressConsumer);
				writers.clear();
				return true;
			}

		}
		return false;
	}

	private FileWriter getOrCreateWriter(NexlaMessageContext messageContext, FileNaming fileNaming, Map<String, String> messageKey) {
		// get list of files for given partition key
		LinkedList<FileWriter> keyWriters = writers.computeIfAbsent(messageKey, key -> new LinkedList<>());

		// if there are files for partition key
		if (!keyWriters.isEmpty()) {

			// take latest file and look if we can append to it
			FileWriter writer = keyWriters.getLast();

			// if file size is less than maximum file size or number of records is greater than max
			boolean maxFileSizeReached = writer.rawBytes >= config.maxFileSize;
			boolean unlimitedRecords = config.maxRecordsPerFile == RECORDS_PER_FILE_UNLIMITED;
			boolean maxRecordsReached = writer.numRecords >= config.maxRecordsPerFile;
			if (!maxFileSizeReached && (unlimitedRecords || !maxRecordsReached)) {
				// if file was closed before, for instance, by being evicted from open files cache
				if (writer.isClosed()) {

					// check if there is place for new open file in cache, if not, evict least recently used file
					if (filesCache.size() == config.maxOpenFiles) {
						removeOldestFile().ifPresent(FileWriter::close);
					}

					// open file to append
					writer.createNexlaFileWriter(identifier, messageContext);
					// populate cache with it
					filesCache.put(writer.dataFilePath, writer);
				}

				return writer;

			} else {
				// if max file size or record count is reached, close it and evict from cache
				if (!writer.isClosed()) {
					writer.close();
				}
				filesCache.remove(writer.dataFilePath);
			}
		}

		// for all cases above check for space in open files cache again and free one slot if necessary
		if (filesCache.size() == config.maxOpenFiles) {
			removeOldestFile().ifPresent(FileWriter::close);
		}
		FileWriter writer = new FileWriter(fileNaming, config.originals(), config.mappingConfig, logger.getPrefix(), config.bufferedWriterSize);

		keyWriters.add(writer);
		writer.createNexlaFileWriter(identifier, messageContext);

		// populate cache with the new file
		filesCache.put(writer.dataFilePath, writer);

		return writer;
	}

	private void doFlushWriters(DateTime nowInUserTimeZone, List<Pair<Map<String, String>, FileWriter>> inlineWriters, List<List<Pair<Map<String, String>, FileWriter>>> listWriters, Consumer<Long> progressConsumer) throws InterruptedException {
		logger.info("Starting flush {} writers in {} batches", inlineWriters.size(), listWriters.size());

		AtomicBoolean killSwitch = new AtomicBoolean(false);
		StreamEx<Callable<FileWriterFlushResult>> callables = zipWithIndices(StreamEx.of(listWriters))
			.mapKeys(x -> x + 1)
			.map(task ->
				() -> {
					List<Pair<Map<String, String>, FileWriter>> writers = task.getValue();
					logger.info("Flushing task #{} [0/{}] writers", task.getKey(), writers.size());

					var readyWriters = new ConcurrentSkipListSet<Integer>();

					return zipWithIndices(StreamEx.of(writers))
						.mapKeys(x -> x + 1)
						.filter(k -> {
							if(killSwitch.get()) {
								logger.warn("stream is going to shutdown due to 'killSwitch' property: file {}", k.getValue().second.getDataFilePath());
								return false;
							}
							return true;
						})
						.map(kw -> {
							FileWriterFlushResult fileWriterFlushResult = flushFileWriter(nowInUserTimeZone, kw.getValue());
							setKillSwitch(fileWriterFlushResult, killSwitch);
							readyWriters.add(kw.getKey());
							logger.info("Flushing task #{} [{}/{}]", task.getKey(), readyWriters.size(), writers.size());
							progressConsumer.accept(kw.getValue().second.numRecords);
							return fileWriterFlushResult;
						})
						.reduce(this::mergeResults)
						.get();
				});

		List<Future<FileWriterFlushResult>> result = executorService.invokeAll(callables.toList());

		StreamEx.of(result)
			.map(this::getResult)
			.reduce(this::mergeResults)
			.ifPresent(this::processFlushedWriter);
	}

	private void setKillSwitch(FileWriterFlushResult fileWriterFlushResult, AtomicBoolean killSwitch){
		boolean anyMatch = fileWriterFlushResult.exceptions.stream()
				.anyMatch(pair -> NexlaExceptionChecker.checkParentException(pair.first, AuthFailException.class));

		if(anyMatch) {
			killSwitch.set(true);
		}
	}

	@SneakyThrows
	private void processFlushedWriter(FileWriterFlushResult writerDetails) {
		if (!writerDetails.exceptions.isEmpty()) {
			String normPath = getDirectoryPath(config.sinkType, config.path);

			Set<String> fileNamesList = StreamEx.of(writerDetails.exceptions)
				.flatMap(x -> {
					String folderStructure = x.second;
					String filePath = normPath + "/" + folderStructure;

					Path path = Paths.get(filePath);
					return StreamEx.of(Optional.ofNullable(path.getFileName()).map(Objects::toString));
				}).toSet();

			if (fileNamesList.isEmpty()) {
				fileNamesList.add("/");
			}

			Exception e = writerDetails.exceptions.get(0).first;
			String fileNames = String.join(",", fileNamesList);

			long numRecords = writerDetails.numRecords.get();
			long errorCount = writerDetails.fileWriterRecords;
			Optional<DataSet> dataSet = adminApiClient.getDataSet(config.datasetId);
			notificationSender.publishMetrics(fileNames, Optional.empty(), numRecords,
				0L, errorCount, runId, dataSet, Optional.of(new NexlaErrorMessage(e.getMessage(), NotificationUtils.getStacktraceForDb(e))));
			notificationSender.publishExceptionMessage(fileNames, numRecords, e, errorCount, runId.orElse(0L));

			// Added for delta lake sinks: NEX-9099
			if (e instanceof NexlaException) {
				Optional<Object> records = ((NexlaException) e).getData("records");
				logger.info("M=processFlushedWriter, recordsPresent={}", records.isPresent());
				if (records.isPresent()) {
					List<String> data = (List<String>) records.get();
					data.forEach(record -> {
						LinkedHashMap<String, Object> message = JsonUtils.convertToMap(record);
						NexlaMessage nexlaMessage = new NexlaMessage(message);
						Resource resource = new Resource(config.sinkId, ResourceType.SINK);
						notificationSender.sendQuarantineMessage(resource, nexlaMessage, e);
					});
				}
			}

			throw e;
		}

		if (isFilePartitioningUsed() && (writerDetails.numRecords.get() > 0)) {

			if (storeOffsets) {
				EntryStream.of(writerDetails.delayedMetricsMap).forKeyValue((tp, delayed) -> {
					FileStats stats = new FileStats(delayed.rawBytes, delayed.minFirstOffset, delayed.maxLastOffset, writerDetails.numRecords.get());
					if (config.listingEnabled) {
						pollingWriter.updateOffset(tp, stats.lastOffset);
					} else {
						String indexFile = pollingWriter.uploadIndex(delayed.fileNamingOfMaxChunk, delayed.folderStructureOfMaxChunk, stats, tp).getPath();
						pollingWriter.updateCursorFile(indexFile, tp);
					}
				});
			}

			// publishing metrics after cursor file updated
			EntryStream.of(writerDetails.delayedMetrics)
				.forKeyValue((dataFileKey, writer) -> {
					Optional<DataSet> dataSet = adminApiClient.getDataSet(config.datasetId);
					notificationSender.publishMetrics(dataFileKey.getPath(), dataFileKey.getDisplayPath(),
							writer.numRecords, writer.messageBytes, 0L, runId, dataSet, Optional.empty());
				});
		}

		cleanup(writerDetails.processedWriters);
	}

	private FileWriterFlushResult flushFileWriter(DateTime nowInUserTimeZone, Pair<Map<String, String>, FileWriter> kw) {
		String folderStructure = getFolderStructure(nowInUserTimeZone, kw.first, config.outputDirNamePattern.map(VarInfo::getTemplate), config.datetimePadding);
		FileWriterFlushResult writerDetails = new FileWriterFlushResult();

		try {
			FileWriter writer = kw.second;

			if (!writer.isClosed()) {
				writer.close();
			}

			List<TopicPartition> activePartitions = getActivePartitions(writer);
			encryptIfRequired(writer);

			writerDetails.fileWriterRecords += writer.numRecords;

			FileDetails dataFile = config.appendModeEnabled
					? pollingWriter.appendDataToFile(writer, folderStructure, config)
					: pollingWriter.uploadDataFile(writer, folderStructure, config.gzip);

			if (!storeOffsets) {

				if (!isFilePartitioningUsed()) {
					Optional<DataSet> dataSet = adminApiClient.getDataSet(config.datasetId);
					notificationSender.publishMetrics(dataFile.getPath(), dataFile.getDisplayPath(),
						writer.numRecords, writer.rawBytes, 0L, runId, dataSet, Optional.empty());
				} else {
					// delaying publishing metrics until cursor file is updated
					writerDetails.delayedMetrics.put(dataFile, new FileWriterFlushResult.DelayedMetricsByFile(writer));

					activePartitions.forEach(tp ->
						computeDelayedTopicPartitionMetrics(writerDetails.delayedMetricsMap, writerDetails.numRecords, folderStructure, writer, tp));
				}

			} else if (config.listingEnabled) {
				if (!isFilePartitioningUsed()) {
					activePartitions.forEach(tp -> {
						long lastOffset = writer.getFileStats(tp).lastOffset;
						pollingWriter.updateOffset(tp, lastOffset);
					});
					Optional<DataSet> dataSet = adminApiClient.getDataSet(config.datasetId);
					notificationSender.publishMetrics(dataFile.getPath(), dataFile.getDisplayPath(),
						writer.numRecords, writer.rawBytes, 0L, runId, dataSet, Optional.empty());
				} else {
					// delaying publishing metrics until cursor file is updated
					writerDetails.delayedMetrics.put(dataFile, new FileWriterFlushResult.DelayedMetricsByFile(writer));

					activePartitions.forEach(tp ->
						computeDelayedTopicPartitionMetrics(writerDetails.delayedMetricsMap, writerDetails.numRecords, folderStructure, writer, tp));
				}
			} else {

				if (!isFilePartitioningUsed()) {
					activePartitions.forEach(tp -> {
						FileStats fileStats = writer.getFileStats(tp);
						String indexFile = pollingWriter.uploadIndex(writer.fileNaming, folderStructure, fileStats, tp).getPath();
						pollingWriter.updateCursorFile(indexFile, tp);
					});

					Optional<DataSet> dataSet = adminApiClient.getDataSet(config.datasetId);
					notificationSender.publishMetrics(dataFile.getPath(), dataFile.getDisplayPath(),
						writer.numRecords, writer.rawBytes, 0L, runId, dataSet, Optional.empty());

				} else {

					// delaying publishing metrics until cursor file is updated
					writerDetails.delayedMetrics.put(dataFile, new FileWriterFlushResult.DelayedMetricsByFile(writer));

					activePartitions.forEach(tp ->
						computeDelayedTopicPartitionMetrics(writerDetails.delayedMetricsMap, writerDetails.numRecords, folderStructure, writer, tp));
				}
			}

			writerDetails.processedWriters.add(writer.dataFilePath);

			notificationSender.publishMonitoringMessage(
					String.format("File %s written successfully", dataFile.getDisplayPath().orElse(writer.dataFilePath)),
					0,
					runId.orElse(0L),
					NexlaMonitoringLogType.LOG,
					NexlaMonitoringLogSeverity.INFO
			);
		} catch (Exception e) {
			String filePath = folderStructure;
			if (e instanceof NexlaException){
				Optional<FileDetails> fileDetails = ((NexlaException) e).getData("fileDetails");
				if (fileDetails.isPresent()){
					filePath = fileDetails.get().getPath();
				}
			}
			writerDetails.exceptions.add(Pair.of(e, filePath));
		}

		return writerDetails;
	}

	@SneakyThrows
	private FileWriterFlushResult getResult(Future<FileWriterFlushResult> x) {
		return x.get();
	}

	private FileWriterFlushResult mergeResults(FileWriterFlushResult res1, FileWriterFlushResult res2) {
		Set<String> processedWriters = Sets.newHashSet(res1.processedWriters);
		processedWriters.addAll(res2.processedWriters);

		Map<FileDetails, FileWriterFlushResult.DelayedMetricsByFile> delayedMetrics = Maps.newHashMap(res1.delayedMetrics);
		res2.delayedMetrics.forEach(
			(key, value) -> delayedMetrics.merge(key, value, FileWriterFlushResult.DelayedMetricsByFile::new));

		Map<TopicPartition, DelayedMetrics> delayedMetricsMap = Maps.newHashMap(res1.delayedMetricsMap);
		res2.delayedMetricsMap.forEach(
			(key, value) -> delayedMetricsMap.merge(key, value, DelayedMetrics::new));

		List<Pair<Exception, String>> exceptionList = Lists.newArrayList(res1.exceptions);
		exceptionList.addAll(res2.exceptions);

		return new FileWriterFlushResult(
			processedWriters,
			res1.fileWriterRecords + res2.fileWriterRecords,
			delayedMetrics,
			delayedMetricsMap,
			new AtomicLong(res1.numRecords.get() + res2.numRecords.get()),
			exceptionList
		);
	}

	private DateTime getNowInUserTimeZone(DateTime nowUtc) {
		return nowUtc.withZone(DateTimeZone.forID(config.timezone));
	}

	private void cleanup(Set<String> processedWriters) {
		writers.values().forEach(keyWriters -> {
			Iterator<FileWriter> iter = keyWriters.iterator();
			while (iter.hasNext()) {
				FileWriter keyWriter = iter.next();
				if (processedWriters.contains(keyWriter.dataFilePath)) {
					iter.remove();
					keyWriter.delete();
					filesCache.remove(keyWriter.dataFilePath);
				}
			}
		});

		writers.entrySet().removeIf(e -> e.getValue().isEmpty());
	}

	private void encryptIfRequired(FileWriter writer) {
		config.fileEncryptConfig.ifPresent(fileEncryptConfig -> {
			String encryptedFileName = ASCEnryptUtils.encrypt(writer.dataFilePath, fileEncryptConfig);
			writer.delete();
			writer.dataFilePath = encryptedFileName;
		});
	}

	private List<TopicPartition> getActivePartitions(FileWriter writer) {
		return topicPartitions.stream()
			.filter(writer::containsFileStats)
			.collect(toList());
	}

	private void computeDelayedTopicPartitionMetrics(
		Map<TopicPartition, DelayedMetrics> delayed,
		AtomicLong numRecords,
		String folderStructure,
		FileWriter writer,
		TopicPartition tp
	) {
		DelayedMetrics metrics = delayed.computeIfAbsent(tp, t -> new DelayedMetrics());

		FileStats stats = writer.getFileStats(tp);
		metrics.numRecords += stats.numRecords;
		numRecords.addAndGet(stats.numRecords);

		metrics.rawBytes += stats.rawBytes;

		long firstOffset = stats.firstOffset;
		metrics.minFirstOffset = Math.min(firstNonNull(metrics.minFirstOffset, firstOffset), firstOffset);

		long lastOffset = stats.lastOffset;
		if ((metrics.maxLastOffset == null) || (lastOffset > metrics.maxLastOffset)) {
			metrics.maxLastOffset = lastOffset;
			metrics.folderStructureOfMaxChunk = folderStructure;
			metrics.fileNamingOfMaxChunk = writer.fileNaming;
		}
	}

	private boolean isFilePartitioningUsed() {
		return !config.groupPartitionKeys.isEmpty();
	}

	private Optional<FileWriter> removeOldestFile() {
		return StreamEx
			.of(filesCache.values())
			.minBy(FileWriter::getLastModified)
			.map(oldestFile -> filesCache.remove(oldestFile.dataFilePath));
	}

	private String indexFilePaths(Collection<TopicPartition> topicPartitions, FileWriter writer) {
		return StreamEx.of(topicPartitions)
			.filter(writer::containsFileStats)
			.map(tp -> writer.fileNaming.getIndexFilePath(tp, writer.getFileStats(tp).firstOffset))
			.collect(joining(","));
	}

	private Map<String, String> getMessageKey(NexlaMessage message) {
		if (!isFilePartitioningUsed()) {
			return DEFAULT_PARTITION;
		} else {
			Map<String, Object> partitionKeys = Maps.newHashMap(message.getRawMessage());

			NexlaMetaData metaData = message.getNexlaMetaData();
			ofNullable(metaData.getSourceType()).ifPresent(st -> partitionKeys.put("meta.connection_type", st.toString()));
			ofNullable(metaData.getIngestTime()).ifPresent(st -> partitionKeys.put("meta.ingest_time", st.toString()));
			ofNullable(metaData.getSourceOffset()).ifPresent(st -> partitionKeys.put("meta.source_offset", st.toString()));
			ofNullable(metaData.getSourceKey()).ifPresent(st -> partitionKeys.put("meta.source_key", st));
			ofNullable(metaData.isEof()).ifPresent(st -> partitionKeys.put("meta.is_eof", st.toString()));
			ofNullable(metaData.getLastModified()).ifPresent(st -> partitionKeys.put("meta.last_modified", st.toString()));
			ofNullable(metaData.getRunId()).ifPresent(st -> partitionKeys.put("meta.run_id", st.toString()));
			ofNullable(message.getNexlaMetaData().getTags()).ifPresent(tags ->
				EntryStream.of(tags).forKeyValue((t, v) -> partitionKeys.put("meta.tag." + t, v)));

			partitionKeys.putAll(Flattener.INSTANCE.flatten(message.getRawMessage()));

			Map<String, String> key = new HashMap<>(config.groupPartitionKeys.size());
			for (String partitionKey : config.groupPartitionKeys) {
				Object value = partitionKeys.get(partitionKey);
				if (value == null) {
					value = config.groupPartitionDefaults.get(partitionKey);
				}
				key.put(partitionKey, Objects.toString(value));
			}
			return key;
		}
	}

	public Map<String, FileWriter> getFilesCache() {
		return filesCache;
	}

	public Map<Map<String, String>, LinkedList<FileWriter>> getWriters() {
		return writers;
	}

}
