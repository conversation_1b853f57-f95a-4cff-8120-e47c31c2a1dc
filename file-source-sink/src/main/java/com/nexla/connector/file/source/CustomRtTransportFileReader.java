package com.nexla.connector.file.source;

import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSet;
import com.nexla.common.NexlaFile;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.ResourceType;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.parse.NexlaParser;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.CustomRtEmissionMode;
import com.nexla.connector.config.file.CustomRtMode;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.file.service.FileConnectorService;
import com.nexla.listing.client.ListingClient;
import com.nexla.probe.s3.S3ClientFactory;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.kafka.connect.storage.OffsetStorageReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.transfer.s3.S3TransferManager;
import software.amazon.awssdk.transfer.s3.model.CompletedFileUpload;
import software.amazon.awssdk.transfer.s3.model.UploadFileRequest;

import static com.nexla.common.NexlaNamingUtils.nameDataSetTopic;
import static com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix;
import static com.nexla.probe.s3.S3ConnectorService.createS3ClientFromCreds;

public class CustomRtTransportFileReader extends TransportFileReader {
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomRtTransportFileReader.class);
    public static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private static final long MULTIPART_UPLOAD_THRESHOLD_BYTES = 1L << 28;
    private static final long MINIMUM_UPLOAD_PART_SIZE_BYTES = 1L << 27;

    /*
    Only one of these will be set, depending on the configuration.
     */
    protected final AWSAuthConfig.BucketPrefix uploadBase;
    protected final AWSAuthConfig.BucketPrefix uploadExact;

    private final List<TransportFile> processedFiles = new ArrayList<>();
    private boolean isFullIngestionCompleted = false;

    private final CustomRtMode mode;

    public CustomRtTransportFileReader(FileSourceNotificationSender notificationSender, FileConnectorService probeService, SchemaDetectionUtils schemaDetection, FileSourceOffsetWriter offsetWriter, MessageGrouper messageGrouper, NexlaLogger logger, Optional<OffsetStorageReader> offsetReader, Optional<ListingClient> listingClient, Optional<Consumer<TransportFile>> onFileConsumed, FileSourceContext fileSourceContext, Optional<Runnable> heartbeatCallback, boolean listingEnabled, CustomRtMode mode) {
        super(notificationSender, probeService, schemaDetection, offsetWriter, messageGrouper, logger, offsetReader, listingClient, onFileConsumed, fileSourceContext, heartbeatCallback, listingEnabled);

        FileSourceConnectorConfig fscc = this.fileSourceContext.config;

        if ((fscc.customRtDestination == null)  == (fscc.customRtExactDestination == null)) {
            throw new IllegalArgumentException("Exactly one of customRtDestination or customRtExactDestination must be set");
        }

        uploadBase = fscc.customRtDestination != null
                ? toBucketPrefix(fscc.customRtDestination + "/" + fscc.sourceId + "/" + UUID.randomUUID(), true)
                : null;

        uploadExact = fscc.customRtExactDestination != null
                ? toBucketPrefix(fscc.customRtExactDestination, false)
                : null;

        this.firstTime.set(false);

        this.mode = mode;
    }

    @SneakyThrows
    @Override
    protected void initTransportFile(TransportFile transportFile, AdminApiClient adminApiClient, boolean isNoopMessageConsumer) {
        transportFile.ctx.nexlaFile = transportFile.nexlaFile;
        transportFile.ctx.skippingParserErrors = true;
        transportFile.ctx.messageNumber = transportFile.lastMessageNumber;

        transportFile.ctx.state = ReadingContext.ReadingContextState.INITIALIZED;

        if (!isDryRunMode()) {
            Optional<DataSet> optDataset = adminApiClient.getAllDataSetsForSourceWithSamples(fileSourceContext.config.sourceId)
                    .stream()
                    .findFirst();

            if (optDataset.isPresent()) {
                logger.info("Found dataset: {}", optDataset.get().getId());
                Integer dataSetId = optDataset.get().getId();

                transportFile.ctx.schema = new SchemaContext();
                transportFile.ctx.schema.dataSetTopic = nameDataSetTopic(dataSetId);
                transportFile.ctx.schema.dataSetId = dataSetId;
            } else {
                DetectionResult detectionResult = detectSchema(transportFile.ctx, null, List.of(Optional.of(new NexlaMessage(
                        new LinkedHashMap<>(newSample())
                ))).iterator());

                logger.info("Detected schema: {}", detectionResult.schema);

                transportFile.ctx.schema = detectionResult.schema;
            }
        } else {
            logger.info("Dry run mode, skipping schema detection");
        }

        detectParserIfNeeded(transportFile.ctx, adminApiClient);
    }

    private boolean isDryRunMode() {
        return fileSourceContext.config.customRtDryRun;
    }

    private Map<String, Object> newSample() {
        if (isFullIngestion()) {
            return Map.of(
                    "directory_file_path", "<directory_file_path>",
                    "files_metadata", List.of(Map.of(
                            "id", 0,
                            "path", "<file_path>",
                            "size", 0
                    ))
            );
        }

        return Map.of(
                "id", 0,
                "file_name", "<file_name>",
                "file_path", "<file_path>",
                "size", 0
        );
    }

    private void detectParserIfNeeded(ReadingContext ctx, AdminApiClient adminApiClient) throws IOException {
        if (this.mode == CustomRtMode.PARSING) {
            getNexlaParser(ctx, adminApiClient)
                    .ifPresent(parser -> {
                        logger.info("Detected parser: {}", parser.getClass().getSimpleName());
                        ctx.parser = parser;
                    });
        }
    }

    @Override
    public <T> ReadBatchResult<T> readNextBatch(FileReadResult<T> consumer, AdminApiClient adminApiClient) {
        if (isFullIngestion()) {
            if (isFullIngestionCompleted) {
                return new ReadBatchResult<>(Collections.emptyList(), null, true, true);
            }

            ReadBatchResult<T> batchResult = super.readNextBatch(consumer, adminApiClient);
            if (batchResult.messages != null) {
                return batchResult;
            }

            if (fileObjects.isEmpty() && (!fileSourceContext.config.listingEnabled || batchResult.listingIsEmpty)) {
                if (this.processedFiles.isEmpty()) {
                    logger.info("No files to process && lastTransportFile is null");
                    return new ReadBatchResult<>(Collections.emptyList(), null, true, true);
                }

                TransportFile tf = this.processedFiles.get(0);
                consumer.acceptMessage(tf.ctx.schema, tf.ctx, new NexlaMessage(new LinkedHashMap<>(Map.of(
                        "directory_file_path", uploadBase.bucket + "/" + uploadBase.prefix,
                        "files_metadata", processedFiles.stream().filter(f -> !f.error).map(f -> Map.of(
                                "id", f.nexlaFile.getId() == null ? 0 : f.nexlaFile.getId(),
                                "path", f.nexlaFile.getFullPath(),
                                "size", f.nexlaFile.getSize()
                        )).collect(Collectors.toList())
                ))), true, 1, new HashMap<>());

                this.isFullIngestionCompleted = true;

                return new ReadBatchResult<>(consumer.removeResult(), tf.ctx.schema, false, true);
            }
            return batchResult;
        } else {
            return super.readNextBatch(consumer, adminApiClient);
        }
    }

    private boolean isFullIngestion() {
        return fileSourceContext.config.customRtEmissionMode == CustomRtEmissionMode.FULL_INGESTION;
    }

    @Data
    @AllArgsConstructor
    private static class FileWithName {
        final File file;
        final String fullPath;
    }

    @SneakyThrows
    @Override
    protected <T> List<T> doReadNextBatch(TransportFile tf, FileReadResult<T> messageConsumer) {
        this.processedFiles.add(tf);

        try {
            if (this.mode == CustomRtMode.PARSING) {
                return doReadNextBatchParsing(tf, messageConsumer);
            } else {
                return doReadNextBatchReplication(tf, messageConsumer);
            }
        } catch (Throwable e) {
            logger.error("Error processing file: {}", tf.nexlaFile.getFullPath(), e);

            return onError(tf, messageConsumer);
        }
    }

    private <T> List<T> onError(TransportFile tf, FileReadResult<T> messageConsumer) {
        messageConsumer.acceptMessage(
                tf.ctx.schema,
                tf.ctx,
                errorToNexlaMessage(tf.nexlaFile),
                true,
                1,
                new HashMap<>()
        );

        tf.error = true;
        finishReading(tf.ctx, false /* ignored */);

        return messageConsumer.removeResult();
    }

    private <T> List<T> doReadNextBatchReplication(TransportFile tf, FileReadResult<T> messageConsumer) {
        Optional<FileWithName> optFileWithExtension = extractIfNeeded(tf);

        FileWithName file = optFileWithExtension
                .orElse(new FileWithName(tf.ctx.localFile, tf.nexlaFile.getFullPath()));

        UploadResult upload = upload(
                file.getFile(),
                file.getFullPath()
        );

        optFileWithExtension
                .ifPresent(f -> {
                    try {
                        Files.delete(f.getFile().toPath());
                    } catch (IOException e) {
                        logger.error("Error deleting file: {}", f.getFile(), e);
                    }
                });

        messageConsumer.acceptMessage(
                tf.ctx.schema,
                tf.ctx,
                toNexlaMessage(upload, tf.nexlaFile),
                true,
                1,
                new HashMap<>()
        );

        finishReading(tf.ctx, false /* ignored */);

        return messageConsumer.removeResult();
    }

    @SneakyThrows
    private Optional<FileWithName> extractIfNeeded(TransportFile tf) {
        if (!isGz(tf.ctx.localFile)) {
            return Optional.empty();
        }

        File extracted = File.createTempFile("extracted", ".tmp");

        try (GZIPInputStream gzip = new GZIPInputStream(new FileInputStream(tf.ctx.localFile));
             FileOutputStream out = new FileOutputStream(extracted)) {
            IOUtils.copy(gzip, out);
        }

        return Optional.of(new FileWithName(extracted, Path.of(
                new File(tf.nexlaFile.getFullPath()).getParent(),
                FilenameUtils.getBaseName(tf.nexlaFile.getFullPath())
        ).toString()));
    }

    private static boolean isGz(File file) {
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] signature = new byte[2];
            int read = fis.read(signature);
            return read == 2 && signature[0] == (byte) 0x1F && signature[1] == (byte) 0x8B;
        } catch (IOException e) {
            return false;
        }
    }

    @SneakyThrows
    private <T> List<T> doReadNextBatchParsing(TransportFile tf, FileReadResult<T> messageConsumer) {
        Optional<FileWithName> optFile = parseToFile(tf);

        if (optFile.isEmpty()) {
            return Collections.emptyList();
        }

        FileWithName fileWithName = optFile.get();

        UploadResult upload = upload(
                fileWithName.getFile(),
                fileWithName.getFullPath()
        );

        Files.delete(fileWithName.getFile().toPath());

        messageConsumer.acceptMessage(
                tf.ctx.schema,
                tf.ctx,
                toNexlaMessage(upload, tf.nexlaFile),
                true,
                1,
                new HashMap<>()
        );

        finishReading(tf.ctx, false /* ignored */);

        return messageConsumer.removeResult();
    }

    private Optional<FileWithName> parseToFile(TransportFile tf) throws IOException {
        if (tf.ctx.parser == null) {
            logger.info("Can't parse file: {}, parser is not initialized", tf.nexlaFile.getFullPath());
            return Optional.empty();
        }

        NexlaParser parser = tf.ctx.parser;
        StreamEx<Optional<NexlaMessage>> optMessages = parser.parseMessages(() -> {
            try {
                return Files.newInputStream(tf.ctx.localFile.toPath());
            } catch (IOException e) {
                logger.error("Error reading file: {}", tf.ctx.localFile, e);
                throw new RuntimeException(e);
            }
        }, true);

        try (optMessages) {
            List<Optional<NexlaMessage>> optMessagesList = optMessages.toList();

            List<NexlaMessage> messages = new ArrayList<>();
            for (int offset = 0; offset < optMessagesList.size(); offset++) {
                Optional<NexlaMessage> optMessage = optMessagesList.get(offset);
                if (optMessage.isPresent()) {
                    NexlaMessage message = optMessage.get();
                    message.setNexlaMetaData(createNexlaMetaData(tf, offset, this.fileSourceContext.config));
                    messages.add(message);
                }
            }

            logger.info("Parsed messages: {}", messages.size());

            File parsed = File.createTempFile("parsed", ".json");

            OBJECT_MAPPER.writeValue(parsed, messages);
            String baseName = FilenameUtils.getBaseName(tf.nexlaFile.getFullPath());
            String extension = "json";
            String path = new File(tf.nexlaFile.getFullPath()).getParent();

            return Optional.of(new FileWithName(parsed, Path.of(path, baseName + "." + extension).toString()));
        }
    }

    private static NexlaMetaData createNexlaMetaData(TransportFile tf, long offset, FileSourceConnectorConfig fscc) {
        NexlaMetaData metadata = new NexlaMetaData(
                fscc.sourceType,
                System.currentTimeMillis(),
                offset,
                tf.nexlaFile.getFullPath(),
                null,
                ResourceType.SOURCE, fscc.sourceId,
                true,
                null, null
        );

        tf.nexlaFile.getMetadata().ifPresent((fileTags) -> {
            metadata.setTags(new LinkedHashMap<>(fileTags));
        });

        metadata.setLastModified(tf.nexlaFile.getLastModified());

        return metadata;
    }

    private UploadResult upload(File localFile, String fullPath) {


        try(S3AsyncClient s3Client = createS3Client();
            S3TransferManager tm = S3TransferManager.builder()
                .s3Client(s3Client)
                .build()) {
            String uploadFullPath = toUploadPath(fullPath);

          LOGGER.info("Uploading file: {}", uploadFullPath);

          UploadFileRequest uploadFileRequest = UploadFileRequest.builder()
              .source(localFile)
              .putObjectRequest(PutObjectRequest.builder()
                  .bucket(destinationBucket())
                  .key(uploadFullPath)
              .build())
          .build();
          CompletedFileUpload completedFileUpload = tm
              .uploadFile(uploadFileRequest)
              .completionFuture()
              .join();

          LOGGER.info("Uploaded file: {}", completedFileUpload);

            return new UploadResult(uploadFullPath);
        } catch (Throwable e) {
            LOGGER.error("Error uploading file to S3", e);
            throw e;
        }
    }

    private String destinationBucket() {
        if (uploadExact != null) {
            return uploadExact.bucket;
        }

        return uploadBase.bucket;
    }

    protected String toUploadPath(String fullPath) {
        if (uploadExact != null) {
            return uploadExact.prefix;
        }

        AWSAuthConfig.BucketPrefix bucketPrefix = toBucketPrefix(fileSourceContext.config.getPath(), true);
        String relativePath = getRelativeS3Path(fullPath, bucketPrefix.prefix);

        return Path.of(uploadBase.prefix, relativePath).toString();
    }

    private String getRelativeS3Path(String relativize, String upper) {
        try {
            Path relativizePath = Paths.get(relativize);
            Path upperPath = Paths.get(upper);

            return upperPath.relativize(relativizePath).toString();
        } catch (Exception e) {
            logger.error("Error getting relative path for {} and {}", relativize, upper, e);
            return relativize;
        }
    }

    private S3AsyncClient createS3Client() {
        return S3ClientFactory.createAsyncClient(this.fileSourceContext.config.customRtAuthConfig, MULTIPART_UPLOAD_THRESHOLD_BYTES, MINIMUM_UPLOAD_PART_SIZE_BYTES);
    }

    @Override
    protected void finishReading(ReadingContext ctx, boolean done) {
        fileLock.lock();
        try {
            TransportFile tf = fileObjects.poll();
            onFileConsumed.ifPresent(c -> c.accept(tf));

            logger.info("[{}] FULL FILE TIME={}, path={}", ctx.nexlaFile.getId(), ctx.timer, ctx.nexlaFile.getFullPath());
            ctx.close();
        } finally {
            fileLock.unlock();
        }
    }

    private NexlaMessage toNexlaMessage(UploadResult uploadResult, NexlaFile file) {
        logger.info("Uploaded file: {}, original: {}, path: {}, size: {}", uploadResult, file.getId(), file.getFullPath(), file.getSize());

        return new NexlaMessage(new LinkedHashMap<>() {{
            put("id", file.getId());
            put("file_name", new File(uploadResult.relativePath).getName());
            put("file_path", destinationBucket() + "/" + uploadResult.relativePath);
            put("size", file.getSize());
        }});
    }

    private NexlaMessage errorToNexlaMessage(NexlaFile file) {
        return new NexlaMessage(new LinkedHashMap<>() {{
            put("id", file.getId());
            put("file_path", file.getFullPath());
            put("error", true);
        }});
    }

    @Data
    private static class UploadResult {
        final String relativePath;
    }
}
