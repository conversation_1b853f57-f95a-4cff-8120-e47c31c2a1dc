package com.nexla.connector.file.source;

import com.nexla.common.NexlaMetaData;
import com.nexla.common.NotificationEventType;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.exception.NexlaError;
import com.nexla.common.exception.ParseError;
import com.nexla.common.metrics.Metric;
import com.nexla.common.metrics.MetricWithErrors;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.common.notify.monitoring.*;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.common.notify.transport.DataMessageProducer;
import com.nexla.connect.common.NexlaConnectorUtils;
import com.nexla.connector.config.FlowType;
import com.nexla.sc.metric.MetadataBuilder;
import one.util.streamex.EntryStream;
import org.slf4j.Logger;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConverterUtils.toLong;
import static com.nexla.common.NexlaConstants.EXCEPTION_TRACE;
import static com.nexla.common.NexlaMetaData.createSourceMetadata;
import static com.nexla.common.NotificationUtils.updateErrorContext;
import static com.nexla.common.Resource.source;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.StreamUtils.lhm;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.exception.NexlaError.getErrorDetails;
import static com.nexla.common.probe.ProbeControllerConstants.DISPLAY_PATH;
import static com.nexla.connect.common.NexlaConnectorUtils.sendNexlaNotificationEvent;

public class FileSourceNotificationSender {

	final ControlMessageProducer controlMessageProducer;
	final DataMessageProducer dataMessageProducer;
	final FlowType flowType;
	private final FileSourceContext context;
	private final Logger logger;

	public FileSourceNotificationSender(
			ControlMessageProducer controlMessageProducer,
			DataMessageProducer dataMessageProducer,
			FlowType flowType,
			FileSourceContext context,
			Logger logger) {
		this.controlMessageProducer = controlMessageProducer;
		this.dataMessageProducer = dataMessageProducer;
		this.flowType = flowType;
		this.context = context;
		this.logger = logger;
	}

	public void publishExceptionMessage(
		ReadingContext ctx,
		long numberOfRowsReadInBatch,
		String errorMessage,
		Exception exception,
		Optional<Long> runId
	) {
		String filePath = ctx.nexlaFile.getFullPath();
		NexlaError nexlaError = getErrorDetails(exception, Optional.empty());
		Optional<Integer> dataSetId = Optional.ofNullable(ctx.schema).map(x -> x.dataSetId);
		NexlaRawMetric metric = createFileDescriptionMetric(ctx, false, context.config.sourceId, 0L, 0L, dataSetId, runId);
		updateErrorContext(nexlaError, metric);

		controlMessageProducer.publishMetrics(metric);
		publishMonitoringMessage(
				"Error while reading file. Reason: " + errorMessage,
				context.orgId,
				NexlaMonitoringLogType.LOG,
				NexlaMonitoringLogSeverity.ERROR);

		sendNexlaNotificationEvent(
			controlMessageProducer,
			NotificationEventType.ERROR,
			runId.orElse(0L),
			SOURCE,
			context.config.sourceId,
			filePath,
			numberOfRowsReadInBatch,
			nexlaError.getMessage());

		sendQuarantineRecord(
			ctx,
			exception,
			new ParseError(null, Optional.empty(), errorMessage),
			runId);
	}

	public void sendQuarantineRecord(
		ReadingContext readingContext,
		Throwable exp,
		ParseError error,
		Optional<Long> runId
	) {
		NexlaMetaData nexlaMetaData = createSourceMetadata(
			context.config.sourceType,
			context.config.sourceId,
			context.config.version,
			readingContext.nexlaFile,
			false,
			toLong(error.getMessageNumber()),
			readingContext.schema != null ? readingContext.schema.dataSetTopic : null,
			runId.orElse(null),
			null,
			readingContext.schema != null ? readingContext.schema.dataSetId : null);

		String messageContent = calcQuarantineMessage(readingContext, error);
		LinkedHashMap<String, Object> message = lhm("message", messageContent);
		logger.debug("Sending failed message to quarantine topic for resourceType={}, resourceId={}", SOURCE, context.config.sourceId);
		dataMessageProducer.sendQuarantineMessage(source(context.config.sourceId), nexlaMetaData, message, exp, error.getMessageNumber());
	}

	public void sendSuccessMessage(ReadingContext ctx, Map<Integer, Metric> datasetMetrics, boolean fileEofReached, Optional<Long> runId) {
		EntryStream
			.of(datasetMetrics)
			.forKeyValue((dsId, metric) -> {
				String fullPath = ctx.nexlaFile.getFullPath();
				NexlaConnectorUtils.publishMetrics(
						controlMessageProducer,
						SOURCE,
						context.config.sourceId,
						fullPath,
						metric.getRecords(),
						metric.getSize(),
						ctx.errorCount,
						DateTimeUtils.nowUTC().getMillis(),
						runId,
						Optional.of(fileEofReached),
						Optional.ofNullable(ctx.nexlaFile.getMd5()),
						Optional.empty(),
						Optional.ofNullable(dsId),
						Optional.ofNullable(ctx.nexlaFile.getLastModified()),
						Optional.of(ctx.messageNumber), Optional.empty(),
						Optional.of(fullPath),
                    	flowType,
						context.orgId,
						context.ownerId);

				String fileNameToDisplay = ctx.nexlaFile.getMetadata()
						.map(meta -> meta.get(DISPLAY_PATH))
						.map(Object::toString)
						.orElse(fullPath);
				publishMonitoringMetrics(context.orgId, metric.getRecords(), metric.getSize(), ctx.errorCount, fileNameToDisplay);
			});

		// send empty metrics if file is empty
		if (datasetMetrics.isEmpty() && fileEofReached) {
			String fullPath = ctx.nexlaFile.getFullPath();
			NexlaConnectorUtils.publishMetrics(controlMessageProducer, SOURCE, context.config.sourceId, fullPath,
					0, 0, ctx.errorCount, DateTimeUtils.nowUTC().getMillis(), runId,
					Optional.of(fileEofReached), Optional.ofNullable(ctx.nexlaFile.getMd5()), Optional.empty(),
					Optional.empty(), Optional.ofNullable(ctx.nexlaFile.getLastModified()), Optional.of(ctx.messageNumber), Optional.empty(),
					Optional.of(fullPath), flowType, context.orgId, context.ownerId);

			publishMonitoringMessage(
					String.format("File %s is empty", ctx.nexlaFile.getMetadata()
							.map(meta -> meta.get(DISPLAY_PATH))
							.map(Object::toString)
							.orElse(fullPath)),
					context.orgId,
					NexlaMonitoringLogType.LOG,
					NexlaMonitoringLogSeverity.WARNING
			);
		}

		if (ctx.errorCount > 0) {
			var batchRecords = EntryStream
				.of(datasetMetrics)
				.values()
				.mapToLong(Metric::getRecords)
				.sum();

			sendNexlaNotificationEvent(
				controlMessageProducer,
				NotificationEventType.ERROR,
				runId.orElse(0L),
				SOURCE,
				context.config.sourceId,
				ctx.nexlaFile.getFullPath(),
				batchRecords,
				ctx.errorMessages.toString());

			publishMonitoringMessage(
					String.format("%d records failed. Reason for failure 1 of %d records is: %s",
							ctx.errorCount,
							ctx.errorCount,
							ctx.errorMessages.stream()
									.findFirst()
									.orElse("")),
					context.orgId,
					NexlaMonitoringLogType.LOG,
					NexlaMonitoringLogSeverity.ERROR
			);
		}
	}

	public void publishDownloadMessage(String log, String displayPath, Integer orgId, NexlaMonitoringLogSeverity severity) {
		publishMonitoringMessage(String.format(log, displayPath), orgId, NexlaMonitoringLogType.LOG, severity);
	}

	public void publishMonitoringMessage(
			String log,
			Integer orgId,
			NexlaMonitoringLogType logType,
			NexlaMonitoringLogSeverity severity) {
		NexlaMonitoringLogEvent monitoringLogEvent = NexlaMonitoringLogEvent.of(
				orgId,
				context.runId,
				context.config.sourceId,
				SOURCE,
				log,
				logType,
				severity,
				System.currentTimeMillis());
		controlMessageProducer.publishMonitoringLog(monitoringLogEvent);
	}

	public void publishMonitoringMetrics(
			Integer orgId,
			long records,
			long size,
			long errors,
			String fileName) {
		NexlaMonitoringLogMetricsProducer.publishMonitoringLogMetrics(
				controlMessageProducer,
				orgId,
				context.runId,
				context.config.sourceId,
				SOURCE,
				fileName,
				new MetricWithErrors(records, size, errors)
		);
	}


	private String calcQuarantineMessage(ReadingContext readingContext, ParseError error) {

		if (readingContext.lineNavigator.isPresent()) {
			FileLineNavigator navigator = readingContext.lineNavigator.get();
			if (error.getLineNumber().isPresent()) {

				try {
					Optional<String> lineContent = navigator.moveToLine(error.getLineNumber().get());
					if (lineContent.isPresent()) {
						return lineContent.get();
					}
				} catch (Exception e) {
					logger.error("Error while trying to read the lineContent for quarantine record, returning error message to quarantine", e);
				}
			}
		}
		return error.getMessage();
	}

	private NexlaRawMetric createFileDescriptionMetric(
		ReadingContext ctx,
		boolean eofReached,
		int sourceId,
		long batchRecords,
		long batchSizeBytes,
		Optional<Integer> dataSetId,
		Optional<Long> runId
	) {
		MetadataBuilder metadataBuilder = new MetadataBuilder().withName(ctx.nexlaFile.getFullPath());

		NexlaRawMetric metric = NexlaRawMetric.create(SOURCE, sourceId,
			batchRecords,
			batchSizeBytes,
			ctx.errorCount,
			nowUTC().getMillis(),
			runId,
			Optional.of(eofReached),
			Optional.ofNullable(ctx.nexlaFile.getMd5()),
			dataSetId,
			Optional.ofNullable(ctx.nexlaFile.getLastModified()),
			Optional.of(ctx.messageNumber),
			Optional.of(metadataBuilder.build()),
            flowType, Optional.empty(), Optional.empty(), Optional.empty(),
			context.orgId , context.ownerId);

		Map<String, Object> fields = metric.getFields();
		if (ctx.errorCount > 0) {
			fields.put(EXCEPTION_TRACE, String.join("\n", ctx.errorMessages));
		}

		return metric;
	}

}