package com.nexla.sinkagent

import com.bazaarvoice.jolt.JsonUtils
import com.google.common.collect.Maps
import com.nexla.admin.client.{AdminApiClient, AdminApiClientBuilder}
import com.nexla.admin.client.config.SinkConfigUtils
import com.nexla.client.SinkUtils
import com.nexla.common.NexlaConstants._
import com.nexla.common.NexlaNamingUtils.nameDataSetTopic
import com.nexla.connector.config.BaseConnectorConfig.SKIP_CTRL_NODE_CHECK
import com.nexla.connector.config.SinkInstanceConfig._
import com.nexla.control.message.ConnectorConfig
import com.nexla.kafka.control.listener.ControlConsumerConstants.TOPICS
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import org.apache.commons.codec.binary.Base64
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod.{DELETE, GET}
import org.springframework.http.{HttpEntity, HttpHeaders, MediaType}
import org.springframework.web.client.{HttpClientErrorException, RestTemplate}

import java.util
import java.util.Collections.singletonList
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters._
import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}

class SinkConnectorClient(props: AppProps, restTemplate: RestTemplate)
                         (implicit ec: ExecutionContext) extends StrictNexlaLogging
  with WithLogging {

  val CONNECTORS_URL = "http://localhost:8083/connectors"
  val headers = {
    val h = new HttpHeaders()
    h.setContentType(MediaType.APPLICATION_JSON)
    h.setAccept(singletonList(MediaType.APPLICATION_JSON))
    h
  }

  def getConnectors() = {
    val responseType = new ParameterizedTypeReference[util.List[String]]() {}
    val request = new HttpEntity("", headers)
    restTemplate.exchange(CONNECTORS_URL, GET, request, responseType)
      .getBody
      .asScala
  }

  def deleteAllConnectors() = {
    getConnectors()
      .toStream
      .foreach(connectorName => {
        logger.warn(s"Removing connector $connectorName from node")
        val response = restTemplate.exchange(CONNECTORS_URL + "/" + connectorName, DELETE, new HttpEntity("", headers), classOf[Object])
        if (!response.getStatusCode.is2xxSuccessful) {
          throw new Exception(s"Failed to delete connector $connectorName from node, responce: $response")
        }
      })
  }

  def startDedicatedSinkTask(sinkId: Int) = {
    val adminClient = new AdminApiClientBuilder()
      .setAppName(s"dedicated-sink-register-app-$sinkId")
      .setDataPlaneUid(props.dataplaneUid)
      .setEnrichmentUrl(props.credEnrichmentUrl)
      .setNoCache(true)
      .create(props.apiCredentialsServer, props.apiAccessKey, restTemplate)

    adminClient.getDataSink(sinkId).asScala match {
      case Some(dataSink) =>
        val sinkConfig = SinkConfigUtils.enrichSinkWithCtrl(dataSink, props.sinkParams)
        val connectorConfig = new ConnectorConfig(s"sink-$sinkId", sinkConfig)

        sinkConfig.put(CONNECTOR_CLASS, SinkUtils.getConnectorClass(dataSink))
        sinkConfig.put(TOPICS, nameDataSetTopic(dataSink.getDataSet.getId))
        sinkConfig.put(DATASET_ID, dataSink.getDataSetId.toString)
        sinkConfig.put(SKIP_CTRL_NODE_CHECK, "true")
        sinkConfig.put(DEDICATED_NODE, "true")
        sinkConfig.put(TASK_TYPE, props.taskType.get)

        sinkConfig.put(CONFIG_PARAMS_64, Base64.encodeBase64String(JsonUtils.toJsonString(sinkConfig).getBytes()))
        logger.debug(s"Dedicated sink connector $sinkId config: $sinkConfig")
        restTemplate.postForObject(CONNECTORS_URL, new HttpEntity[ConnectorConfig](connectorConfig, headers), classOf[util.Map[_, _]])
        logger.info(s"Dedicated sink connector $sinkId started")
      case None => throw new Exception(s"Sink $sinkId was not found")
    }
  }

  def restartSinkAgent() = {
    val sinkConfig: util.Map[String, String] = Maps.newHashMap()

    sinkConfig.put(DEDICATED_NODE, "false")
    sinkConfig.put(RECEIVE_TASKS_CRON, props.receiveTasksCron)
    sinkConfig.put(APP_VERSION, props.appVersion)
    sinkConfig.put(NODE_TAGS, props.nodeTags.mkString(","))
    sinkConfig.put(TASK_TYPE, props.taskType.get)

    sinkConfig.put(CONNECTOR_CLASS, "com.nexla.connect.common.connector.SinkAgentConnector")
    sinkConfig.put(TOPICS, SINK_AGENT)

    sinkConfig.put("name", SINK_AGENT)
    sinkConfig.put(CREDENTIALS_SOURCE, props.credentialsSource)
    sinkConfig.put(SINK_ID, "0")

    val connectorConfig = new ConnectorConfig(SINK_AGENT, sinkConfig)
    val allParams64 = Base64.encodeBase64String(JsonUtils.toJsonString(sinkConfig).getBytes())
    sinkConfig.put(CONFIG_PARAMS_64, allParams64)

    logger.info(s"sinkConfig = ${JsonUtils.toJsonString(sinkConfig)}")

    Try(restTemplate.postForObject(CONNECTORS_URL, new HttpEntity[ConnectorConfig](connectorConfig, headers), classOf[util.Map[_, _]]))
      .recover {
        case e: HttpClientErrorException.Conflict =>
          logger.warn("Sink agent connector already exists ({}). Update existing connector configuration", e.getMessage)
          val connectorUpdateUrl = CONNECTORS_URL + "/" + connectorConfig.name + "/config"
          restTemplate.put(connectorUpdateUrl, new HttpEntity[util.Map[String, String]](connectorConfig.config, headers))
        case e => throw e
      } match {
      case Success(_) => logger.info("Sink agent connector started")
      case Failure(e) => throw e
    }
  }

}
