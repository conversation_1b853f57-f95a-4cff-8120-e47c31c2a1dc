package com.nexla;

import static java.util.concurrent.TimeUnit.MILLISECONDS;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.Mockito.*;

import com.nexla.common.NexlaScheduledAccumulator;
import com.nexla.test.UnitTests;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import org.junit.experimental.categories.Category;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

@Category(UnitTests.class)
class NexlaScheduledAccumulatorTest {

  private ScheduledExecutorService executorService;
  private BiFunction<Integer, Integer, Integer> accumulatorFunction;
  private Consumer<Integer> onScheduleCallback;
  private NexlaScheduledAccumulator<Integer> accumulator;

  @BeforeEach
  void setUp() {
    executorService = Executors.newSingleThreadScheduledExecutor();
    accumulatorFunction = (x, y) -> x + y; // Simple accumulator function for testing
    onScheduleCallback = mock(Consumer.class);
    accumulator =
        new NexlaScheduledAccumulator<>(
            accumulatorFunction,
            onScheduleCallback,
            100L, // Fire every 100ms for testing
            executorService,
            false);
  }

  @AfterEach
  void tearDown() {
    executorService.shutdownNow();
  }

  @Test
  void whenAccumulateIsCalled_thenShouldAccumulateData() throws InterruptedException {
    accumulator.accumulate(1);
    accumulator.accumulate(2);

    MILLISECONDS.sleep(150); // Wait for the scheduler to fire

    ArgumentCaptor<Integer> argumentCaptor = ArgumentCaptor.forClass(Integer.class);
    verify(onScheduleCallback).accept(argumentCaptor.capture());
    Integer capturedValue = argumentCaptor.getValue();

    assert capturedValue == 3 : "Accumulated value should be 3";
  }

  @Test
  void whenCloseIsCalled_thenShouldFlushDataImmediately() {
    accumulator.accumulate(1);
    accumulator.close();

    verify(onScheduleCallback).accept(1);
    verifyNoMoreInteractions(onScheduleCallback);
  }

  @Test
  void whenAccumulateAfterClose_thenShouldThrowException() {
    accumulator.close();
    assertThrows(IllegalStateException.class, () -> accumulator.accumulate(1));
  }

  @Test
  void whenConcurrentlyAccumulating_thenCorrectlyAccumulatesResults() throws InterruptedException {
    int numberOfThreads = 10;
    int numberOfIncrements = 1000;
    AtomicInteger result = new AtomicInteger(0);
    Consumer<Integer> mockConsumer = result::addAndGet;
    BiFunction<Integer, Integer, Integer> accumulatorFunction = Integer::sum;

    ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
    NexlaScheduledAccumulator<Integer> accumulator =
        new NexlaScheduledAccumulator<>(
            accumulatorFunction,
            mockConsumer,
            10L, // 10ms for fast accumulation
            executorService,
            false);

    CountDownLatch latch = new CountDownLatch(numberOfThreads);
    ExecutorService pool = Executors.newFixedThreadPool(numberOfThreads);

    for (int i = 0; i < numberOfThreads; i++) {
      pool.execute(
          () -> {
            for (int j = 0; j < numberOfIncrements; j++) {
              accumulator.accumulate(1);
            }
            latch.countDown();
          });
    }

    // Wait for all threads to finish
    latch.await();
    // Wait for the scheduled task to pick up the last accumulation
    Thread.sleep(50);

    accumulator.close();
    pool.shutdown();
    executorService.shutdown();

    assertEquals(numberOfThreads * numberOfIncrements, result.get());
  }
}
