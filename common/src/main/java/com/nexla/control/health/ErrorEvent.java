package com.nexla.control.health;

import java.util.HashMap;
import java.util.Map;
import lombok.Data;

@Data
public class ErrorEvent implements HealthMessage {

  @Override
  public HealthMessageType getMessageType() {
    return HealthMessageType.ERROR;
  }

  private final long timestamp;
  private final String serviceName;
  private final String podName;

  private final String metricId;
  private final String message;
  private Map<String, String> context = new HashMap<>();

  private final Long ttl;
  private final Long createdAt = System.currentTimeMillis();

  public ErrorEvent putContext(String key, String value) {
    context.put(key, value);
    return this;
  }
}
