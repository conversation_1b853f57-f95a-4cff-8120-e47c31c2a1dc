package com.nexla.connector.config;

import java.util.Map;
import lombok.SneakyThrows;
import org.apache.kafka.common.config.AbstractConfig;

public abstract class AbstractNoLoggingConfig extends AbstractConfig implements Cloneable {

  public AbstractNoLoggingConfig(NexlaConfigDef definition, Map<?, ?> originals) {
    super(definition, originals, false);
  }

  @SneakyThrows
  @Override
  public Object clone() {
    return super.clone();
  }
}
