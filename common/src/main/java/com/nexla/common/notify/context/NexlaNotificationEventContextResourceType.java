package com.nexla.common.notify.context;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes({
  @JsonSubTypes.Type(value = NexlaNotificationEventContextResourceTypeUser.class, name = "USER"),
  @JsonSubTypes.Type(value = NexlaNotificationEventContextResourceTypeOrg.class, name = "ORG"),
  @JsonSubTypes.Type(value = NexlaNotificationEventContextResourceTypeTeam.class, name = "TEAM")
})
@JsonIgnoreProperties(ignoreUnknown = true)
public interface NexlaNotificationEventContextResourceType {
  String getDisplayName();
}
