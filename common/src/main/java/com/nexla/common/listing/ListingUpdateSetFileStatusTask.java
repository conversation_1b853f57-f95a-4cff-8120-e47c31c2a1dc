package com.nexla.common.listing;

import java.util.Optional;
import lombok.Data;

@Data
public class ListingUpdateSetFileStatusTask implements ListingUpdateMessage {
  @Override
  public ListingUpdateTaskType getTaskType() {
    return ListingUpdateTaskType.LISTING_UPDATE_SET_FILE_STATUS;
  }

  private final String messageId;
  private final Long fileId;
  private final String status;
  private final Optional<Long> lastMessageOffset;
  private final Optional<Boolean> schemaDetectionAttempted;
  private final Optional<String> message;
  private final Optional<Long> sourceId;
  private final Long ttl;

  private final Long createdAt = System.currentTimeMillis();

  @Override
  public String key() {
    return fileId.toString();
  }
}
