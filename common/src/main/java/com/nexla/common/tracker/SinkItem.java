package com.nexla.common.tracker;

import static com.nexla.common.tracker.Tracker.SEP;

import com.nexla.common.tracker.Tracker.TrackerMode;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
public class SinkItem {

  private int id;
  private int version;
  private Long offsetFromIngest;

  public SinkItem deepCopy() {
    SinkItem si = new SinkItem();
    si.setId(this.id);
    si.setVersion(this.version);
    si.setOffsetFromIngest(this.offsetFromIngest);
    return si;
  }

  public SinkItem(int id, int version, Long offsetFromIngest) {
    this.id = id;
    this.version = version;
    this.offsetFromIngest = offsetFromIngest;
  }

  public static SinkItem parse(String text) {
    String[] values = text.split(SEP);
    SinkItem item = new SinkItem();
    item.setId(Integer.parseInt(values[0]));
    item.setVersion(Integer.parseInt(values[1]));
    item.setOffsetFromIngest(
        values[2] != null && !values[2].isEmpty() ? Long.parseLong(values[2]) : null);
    return item;
  }

  public void renderTo(StringBuilder sb, TrackerMode trackerMode) {
    switch (trackerMode) {
      case NONE:
        break;
      case FILE:
      case RECORD:
      case FULL:
        sb.append(id)
            .append(SEP)
            .append(version)
            .append(SEP)
            .append(offsetFromIngest != null ? offsetFromIngest : "");
        break;
    }
  }
}
