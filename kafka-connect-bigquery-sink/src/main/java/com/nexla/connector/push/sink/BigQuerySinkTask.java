package com.nexla.connector.push.sink;

import com.bazaarvoice.jolt.JsonUtils;
import com.github.rholder.retry.*;
import com.google.cloud.bigquery.*;
import com.google.cloud.bigquery.Field.Mode;
import com.google.cloud.bigquery.JobStatistics.LoadStatistics;
import com.google.cloud.bigquery.storage.v1.BigQueryWriteClient;
import com.google.cloud.bigquery.storage.v1.TableName;
import com.google.common.collect.Iterables;
import com.google.common.collect.Maps;
import com.nexla.common.*;
import com.nexla.common.exception.NexlaError;
import com.nexla.common.logging.TimeLogger;
import com.nexla.common.notify.NexlaNotificationEvent;
import com.nexla.common.notify.context.NexlaNotificationEventContext;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.sink.TopicPartition;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.*;
import com.nexla.connect.common.ReadyToFlush;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connect.common.postponedFlush.KafkaProgressTracker;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.big_query.BigQuerySinkConnectorConfig;
import com.nexla.connector.config.big_query.BigQuerySinkConnectorConfig.LoadMode;
import com.nexla.connector.push.sink.strategy.LoadStrategy;
import com.nexla.connector.push.sink.strategy.StreamingLoadStrategyImpl;
import com.nexla.probe.bigquery.BigQueryClientService;
import com.nexla.probe.bigquery.BigQueryConnectorService;
import com.nexla.probe.bigquery.BigQueryDialect;
import com.nexla.probe.sql.AutomaticBindingFactory;
import connect.jdbc.sink.dialect.AutomaticBinding;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigInteger;
import java.nio.channels.Channels;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.nexla.common.NotificationEventType.ERROR;
import static com.nexla.common.NotificationUtils.notifyErrorMessage;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.exception.NexlaError.getErrorDetails;
import static com.nexla.connector.config.MappingConfig.MODE_AUTO;
import static com.nexla.connector.config.MappingConfig.MODE_MANUAL;
import static com.nexla.connector.config.SinkConnectorConfig.DEFAULT_MAPPING;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static io.jsonwebtoken.lang.Collections.isEmpty;
import static java.util.stream.Collectors.toList;

public class BigQuerySinkTask extends BaseSinkTask<BigQuerySinkConnectorConfig> {
	// https://cloud.google.com/bigquery/quotas#table_limits
	private static final int SINGLE_TABLE_MODIFICATIONS_LIMIT = 1000;

	private static final int FAILED_UPLOAD_FILE_MILLIS = 3 * 24 * 60 * 60 * 1000;
	private static final String ERROR_JSON_PARSING_AT_POSITION = "Error while reading data, error message: JSON parsing error in row starting at position ";
	private static final Pattern ERROR_FIELD_MAPPING_PATTERN = Pattern.compile("^Invalid \\w+ value: .+ Field: (\\w+); Value: (.+)$");
	public static final String ERROR_FILE_PREFIX = "error-gbq-";
	public static final String NON_VALID_CHARS = "[^a-zA-Z0-9_]";

	private File bufferFile;
	private FileWriter writer;
	private TableId tableId;
	private BigQueryConnectorService service;
	private boolean manualCreateTable = false;
	private String mode;
	private Map<TopicPartition, Long> offsets = new ConcurrentHashMap<>();
	private FieldNameMapper fieldMapper;
	private Optional<LoadStrategy> streaming;

	private Pattern temporaryTablePattern;
	private String temporaryTableName;

	// Used to track the number of inserts into temporary table in Batch UPSERT mode.
	// We may also need to track the number of inserts for INSERT mode
	private AtomicInteger tableUpdatesCounter = new AtomicInteger(0);

	private final AtomicLong recordsToMergeCounter = new AtomicLong(0);
	// Becomes true if in case of BATCH UPSERT mode and the first batch was inserted directly
	private final AtomicBoolean executedDirectInsert = new AtomicBoolean(false);

	private final Set<Long> runIdsToHeartbeat = ConcurrentHashMap.newKeySet();
	private final AtomicLong lastProcessedMessageTs = new AtomicLong(0);

	@Override
	public void doStart() {
		this.sinkTelemetryReporter =
				Optional.of(new ConnectorTelemetryReporter(config.sinkId, ConnectionType.BIGQUERY.name(), SINK, isDedicatedNode));

		recreateFileWriter();
		this.tableId = TableId.of(config.dataset, config.table);
		this.service = new BigQueryConnectorService(adminApiClient, listingClient, new BigQueryClientService(adminApiClient, config.decryptKey));
		this.mode = config
				.mappingConfig
				.map(MappingConfig::getMode)
				.orElse(MODE_AUTO);
		this.temporaryTablePattern = Pattern.compile("^temp_" + config.sinkId + "_" + config.table + "_" + "\\d+$");

		withClientRetriable(this::getBigQueryClient, (bigQuery) -> {
			Table table = bigQuery.getTable(tableId);

			boolean tableExists = table != null && table.exists();
			this.manualCreateTable = MODE_MANUAL.equals(mode) && !tableExists;

			if (tableExists) {
				this.fieldMapper = new SchemaAwareFieldNameMapper(table.getDefinition().getSchema());
			} else if (MODE_AUTO.equals(mode)) {
				this.fieldMapper = new DynamicFieldNameMapper();
			} else {
				// for mode manual, we will create the table and the field mapper will be created there if needed
				this.fieldMapper = FieldNameMapper.NOOP;
			}

			if (config.loadMode == LoadMode.STREAMING) {
				this.streaming = newStreamingLoadStrategy(bigQuery);
			}
			if (config.cleanupTempTables) {
				cleanUpTemporaryTables(bigQuery);
			}
		});
	}

	protected PostponedFlush postponedFlush() {
		KafkaProgressTracker kafkaProgressTracker = new KafkaProgressTracker(getKafkaLagCalculator(), adminApiClient, ctrlClient, logger);
		return new PostponedFlush(this, kafkaProgressTracker, logger) {
			protected boolean dataMatureMs(long delayMs) {
				long inactivityTimeIntervalMs = System.currentTimeMillis() - lastProcessedMessageTs.get();
				return recordsToMergeCounter.get() > 0 && inactivityTimeIntervalMs > delayMs;
			}

			@Override
			protected boolean hasAccumulatedRecords() {
				return recordsToMergeCounter.get() > 0;
			}

			@Override
			protected boolean noDataMatureMs(long delayMs) {
				return !hasAccumulatedRecords() && System.currentTimeMillis() - getLastRecordsPutTs() > delayMs;
			}

			@Override
			protected boolean preventFlushTrigger() {
				if (config.loadMode != LoadMode.BATCH || config.insertMode != UPSERT) {
					return true;
				}
				if (recordsToMergeCounter.get() == 0L) {
					logger.debug("No records to merge from temporary table");
					return true;
				}
				return false;
			}

			@Override
			protected boolean customIntermediateFlushTrigger() {
				if (tableUpdatesCounter.get() >= SINGLE_TABLE_MODIFICATIONS_LIMIT) {
					logger.debug("Single table modifications limit reached ({})", tableUpdatesCounter.get());
					return true;
				}
				if (config.loadMode == LoadMode.STREAMING && streaming.isEmpty()){
					return true;
				}
				return false;
			}
		};
	}

	@Override
	public void open(Collection<org.apache.kafka.common.TopicPartition> partitions) {
		super.open(partitions);
		postponedFlush.resetFirstFlushTsThreshold();
	}

	@Override
	protected ConfigDef configDef() {
		return BigQuerySinkConnectorConfig.configDef();
	}

	private StreamingLoadStrategyImpl newStreamingLoadStrategyForExistingTable() {
		logger.debug("Creating streaming for existing table");
		BigQueryWriteClient bqWriteClient = this.service.getBigQueryService().getStreamingService(this.config.authConfig);
		return new StreamingLoadStrategyImpl(
				logger,
				bqWriteClient,
				TableName.of(config.authConfig.projectId, config.dataset, config.table),
				() -> {
					bqWriteClient.shutdown();
					int terminaionChecks = 0;
					while (terminaionChecks++ < 3) {
						try {
							if (bqWriteClient.awaitTermination(60, TimeUnit.SECONDS)) {
								break;
							}
						} catch (InterruptedException e) {
							break;
						}
					}

					bqWriteClient.close();
				}
		);
	}

	private AtomicLong processedMessagesCount = new AtomicLong();

	private Optional<LoadStrategy> newStreamingLoadStrategy(BigQuery bigQuery) {
		Table table = bigQuery.getTable(tableId);
		boolean tableExists = table != null && table.exists();

		if (tableExists) {
			return Optional.of(newStreamingLoadStrategyForExistingTable());
		}

		if (MODE_AUTO.equals(mode)) {
			// StreamingLoadStrategyImpl cannot be created for non-existing table, since BQ doesn't support schema detection for streaming mode.
			// To work around this limitation, first batch will be inserted in batch mode. StreamingLoadStrategyImpl will be created after first batch flush.
			return Optional.empty();
		} else if (MODE_MANUAL.equals(mode)) {
			createTable(config, bigQuery);
			return Optional.of(newStreamingLoadStrategyForExistingTable());
		}

		throw new RuntimeException("Unknown mode.");
	}

	@Override
	protected BigQuerySinkConnectorConfig parseConfig(Map<String, String> props) {
		return new BigQuerySinkConnectorConfig(props);
	}

	private static final int BQ_SCHEMA_DETECTION_MESSAGE_COUNT = 500;

	@Override
	@SneakyThrows
	public void doPut(StreamEx<NexlaMessageContext> messages, int streamSize) {
		long total = processedMessagesCount.addAndGet(streamSize);
		if (config.loadMode == LoadMode.STREAMING) {
			if (streaming.isEmpty()) {
				putBatchMode(messages);

				if (total >= BQ_SCHEMA_DETECTION_MESSAGE_COUNT) {
					doFlush(ReadyToFlush.FLUSH);
				}

				return;
			}

			List<NexlaMessageContext> contexts = messages.toList();
			if (contexts.isEmpty()) {
				logger.warn("Batch is empty, skipping.");
				return;
			}

			if (streaming.filter(LoadStrategy::isClosed).isPresent()) {
				logger.debug("Recreating closed streaming stretagy...");
				streaming = Optional.of(newStreamingLoadStrategyForExistingTable());
			}

			LoadStrategy streamingLoadStrategy = streaming.get();

			NexlaMessageContext last = Iterables.getLast(contexts);

			List<LinkedHashMap<String, Object>> mappedMessages = contexts
					.stream()
					.map(c -> fieldMapper.map(getRawMessage(manualCreateTable, c.mapped.getRawMessage())))
					.collect(toList());

			streamingLoadStrategy.append(mappedMessages, new LoadStrategy.BatchObserver() {
				@Override
				public void error(LinkedHashMap<String, Object> x, Throwable t) {
					logger.error("Streaming message failed", t);
					sendMetric(config.dataset + "/" + config.table, Optional.empty(), 0, 0, 1);
					dataMessageProducer.sendQuarantineMessage(new Resource(config.sinkId, SINK), x, null, t);
				}

				@Override
				public void error(Throwable t) {
					logger.error("Streaming micro-batch failed", t);
					sendMetric(config.dataset + "/" + config.table, Optional.empty(), 0, 0, contexts.size());
					quarantineMessages(contexts.stream().map(NexlaMessageContext::getMapped), new Resource(config.sinkId, SINK), t);
				}

				@Override
				public void success() {
					int sz = mappedMessages.stream().map(JsonUtils::toJsonString).mapToInt(o -> o.getBytes().length).sum();
					sendMetric(config.dataset + "/" + config.table, Optional.empty(), contexts.size(), sz, 0);
					offsets.put(last.topicPartition, last.kafkaOffset);
				}
			});
			return;
		}

		putBatchMode(messages);
		lastProcessedMessageTs.set(System.currentTimeMillis());        // no need to update this in case of streaming mode
	}

	private void putBatchMode(StreamEx<NexlaMessageContext> messagesStream) {
		List<NexlaMessageContext> messages = messagesStream.collect(toList());
		List<NexlaMessageContext> uniqueMessages = deduplicate(StreamEx.of(messages)).collect(toList());
		int duplicatesCount = messages.size() - uniqueMessages.size();
		if (duplicatesCount != 0) {
			logger.debug("Filtered out {} duplicate messages by primary key", duplicatesCount);
		}
		uniqueMessages.forEach(m -> {
			try {
				offsets.put(m.topicPartition, m.kafkaOffset);
				writer.write(StreamUtils.jsonUtil().toJsonString(
						fieldMapper.map(getRawMessage(manualCreateTable, m.mapped.getRawMessage()))
				) + "\n");
				if (config.loadMode == LoadMode.BATCH && config.insertMode == UPSERT
						&& m.getMapped() != null && m.getMapped().getNexlaMetaData() != null) {
					// Heartbeat messages runID until they are not delivered to the target table
					runIdsToHeartbeat.add(m.getMapped().getNexlaMetaData().getRunId());
				}
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
		});
	}

	@Override
	@SneakyThrows
	public boolean doFlush(ReadyToFlush readyToFlush) {
		if (config.loadMode == LoadMode.STREAMING && streaming.isPresent()) {
			streaming.ifPresent(LoadStrategy::flush);
			offsetsSender.ifPresent(this::commitOffsets);
			return false;
		} else {
			removeOldErrorFiles();

			try {
				writer.flush();
				if (hasData(bufferFile)) {
					File localTempFile = bufferFile;
					recreateFileWriter();
					uploadFile(localTempFile);
				}
				this.postponedFlush.resetFailures();
			} catch (IOException e) {
				logger.warn("Exception during flush of sink task/file upload", e);
				this.postponedFlush.incrementFailures(e);
			}

			boolean tempTableMerged = readyToFlush.flush && mergeTempTable(clientRetriable(this::getBigQueryClient));
			boolean executedDirectInsert = this.executedDirectInsert.getAndSet(false);

			if (config.insertMode == UPSERT && !tempTableMerged && !executedDirectInsert) {
				return false;
			}

			offsetsSender.ifPresent(this::commitOffsets);

			if (config.loadMode == LoadMode.STREAMING && streaming.isEmpty()) {
				streaming = Optional.of(newStreamingLoadStrategyForExistingTable());
				logger.debug("Created streaming after table auto-creation using first batch");
				return readyToFlush.reportFlushed;
			}

			// TODO should also return true for INSERT mode after pipeline is fully flushed in order to trigger Flushed Coordination event (once)
			return config.insertMode == UPSERT && readyToFlush.reportFlushed && tempTableMerged;
		}
	}

	@Override
	protected Set<Long> getActiveRunIds() {
		return config.loadMode == LoadMode.BATCH && config.insertMode == UPSERT
				? runIdsToHeartbeat
				: super.getActiveRunIds();
	}

	@Override
	protected Optional<Set<Long>> getAndResetFlushedRunIds() {
		if (config.loadMode != LoadMode.BATCH || config.insertMode != UPSERT) {
			return Optional.empty();
		}
		Set<Long> heartbeatRunIds = new HashSet<>(runIdsToHeartbeat);
		runIdsToHeartbeat.clear();
		return Optional.of(heartbeatRunIds);
	}

	@Override
	protected void refreshPipelineStatus() {
		super.refreshPipelineStatus();
		pipelineStatusReport.ifPresent(status -> status.bufferedRecords.set(recordsToMergeCounter.get()));
	}

	@Override
	public void stop() {
		if (config.loadMode == LoadMode.BATCH && config.insertMode == UPSERT) {
			logger.warn("{} records were not delivered to the target table. Data will be reprocessed at future sink task connector run", recordsToMergeCounter.get());
			if (temporaryTableName != null) {
				withClientRetriable(this::getBigQueryClient, bigQuery -> deleteTable(bigQuery, TableId.of(config.dataset, temporaryTableName)));
				temporaryTableName = null;
				tableUpdatesCounter.set(0);
				runIdsToHeartbeat.clear();
			}
		}
		super.stop();
	}

	private void commitOffsets(final OffsetsCoordinationClient os) {
		if (offsets.isEmpty()) {
			return;
		}
		logger.info("Update offsets: {}", offsets);
		os.updateSinkOffsets(config.sinkId, offsets);
		offsets.clear();
	}

	public void uploadFile(File readyFile) {
		withClientRetriable(this::getBigQueryClient, (client) -> uploadFile(client, readyFile, FormatOptions.json()));
	}

	public void uploadFileFastConnector(File readyFile) {
		withClientRetriable(this::getBigQueryClient, (client) -> uploadFile(client, readyFile, toFormatOptions(readyFile)));
		if (config.insertMode == UPSERT) {
			mergeTempTable(clientRetriable(this::getBigQueryClient));
		}
	}

	private StreamEx<NexlaMessageContext> deduplicate(StreamEx<NexlaMessageContext> dataStream) {
		if (config.insertMode == UPSERT) {
			return dedupByPk(dataStream, new ArrayList<>(config.idFields));
		} else {
			return dataStream;
		}
	}

	private StreamEx<NexlaMessageContext> dedupByPk(StreamEx<NexlaMessageContext> dataStream, List<String> keyFields) {
		LinkedHashMap<List<Object>, NexlaMessageContext> dedupMap = new LinkedHashMap<>();
		dataStream.forEach(value -> {
			List<Object> pkValue = extractPrimaryKey(keyFields, value.mapped.getRawMessage());
			dedupMap.put(pkValue, value);
		});
		return StreamEx.of(dedupMap.values());
	}

	private List<Object> extractPrimaryKey(List<String> keyFields, Map<String, Object> rawMessage) {
		return keyFields.stream().map(rawMessage::get).collect(toList());
	}

	private void removeOldErrorFiles() {
		File[] files = new File(System.getProperty("java.io.tmpdir"))
				.listFiles((dir, name) -> name.toLowerCase().startsWith(ERROR_FILE_PREFIX));

		StreamEx
				.of(files)
				.filter(x -> x.lastModified() < System.currentTimeMillis() - FAILED_UPLOAD_FILE_MILLIS)
				.forEach(File::delete);
	}

	protected void onRunIdChanged(Long prevRunId, Long currRunId) {
		logger.debug("Run id changed from {} to {}", prevRunId, currRunId);
		if (config.truncOnLoad) {
			truncateProcessedData();
			return;
		}
		if (config.loadMode == LoadMode.BATCH && config.insertMode == UPSERT) {
			// data is still might not be delivered to the target table
			runIdsToHeartbeat.add(currRunId);
			logger.debug("runIdsToHeartbeat: {}", runIdsToHeartbeat);
		}
	}

	private void truncateProcessedData() {
		withClientRetriable(this::getBigQueryClient, bigQuery -> {
			// Step 1. Truncate destination table
			try {
				Table table = bigQuery.getTable(tableId);

				boolean tableExists = table != null && table.exists();
				if (tableExists) {
					TableDefinition tableDefinition = table.getDefinition();
					TableInfo tableInfo = TableInfo.newBuilder(tableId, tableDefinition).build();
					logger.info("Truncating table {}", tableId);
					bigQuery.delete(tableId);
					bigQuery.create(tableInfo);
				}
			} catch (Exception e) {
				logger.error("Failed trunc destination table", e);
			}

			// Step 2. Delete local files (if any)
			if (writer != null) {
				logger.info("Deleting local data files");
				try {
					writer.flush();
					bufferFile.delete();
					removeOldErrorFiles();
					recreateFileWriter();
				} catch (Exception e) {
					logger.error("Failed to delete local files", e);
				}
			}

			// Step 3. Delete temporary table (if any)
			if (temporaryTableName != null) {
				try {
					deleteTable(bigQuery, TableId.of(config.dataset, temporaryTableName));
					temporaryTableName = null;
					tableUpdatesCounter.set(0);
				} catch (Exception e) {
					logger.error("Failed to delete temporary table {}", temporaryTableName, e);
				}
			}

			// Step 4. Reset counters
			recordsToMergeCounter.set(0);
			runIdsToHeartbeat.clear();
		});
	}

	private void cleanUpTemporaryTables(BigQuery client) {
		try (TimeLogger ignored = new TimeLogger(logger, "Cleanup temporary tables")) {
			client.listTables(DatasetId.of(config.dataset)).iterateAll().forEach(table -> {
				Matcher matcher = temporaryTablePattern.matcher(table.getTableId().getTable());
				if (matcher.matches()) {
					deleteTable(client, table.getTableId());
				}
			});
		} catch (Exception e) {
			logger.error("Failed to cleanup temporary tables", e);
		}
	}

	@SneakyThrows
	public void uploadFile(BigQuery bigQuery, File localTempFile, FormatOptions formatOptions) {
		Resource resource = new Resource(this.config.sinkId, SINK);

		Retryer<BQJobResult> insertRetryer = RetryerBuilder.<BQJobResult>newBuilder()
				.retryIfException()
				.withWaitStrategy(WaitStrategies.randomWait(30, TimeUnit.SECONDS))
				.withStopStrategy(StopStrategies.stopAfterAttempt(3))
				.build();

		Callable<BQJobResult> callInsert = () -> insert(bigQuery, localTempFile, tableId, formatOptions);

		try {
			Table table = bigQuery.getTable(tableId);
			boolean tableExists = table != null && table.exists();
			if (manualCreateTable && !tableExists) {
				createTable(config, bigQuery);
				table = bigQuery.getTable(tableId);
			}
			if (localTempFile.exists() && localTempFile.length() > 0) {
				if (tableExists && config.insertMode == UPSERT && table.getNumRows().compareTo(BigInteger.ZERO) > 0) {
					upsert(bigQuery, localTempFile, resource, formatOptions);
				} else {
					try {
						BQJobResult insertJob = insertRetryer.call(callInsert);
						if (insertJob.isFailed()) {
							onFailure(insertJob, localTempFile, resource);
							publishMonitoringLog("Error during job execution", NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
						} else {
							onInsertSuccess(insertJob, localTempFile, resource);
							executedDirectInsert.set(true);
						}
					} catch (RetryException e) {
						// unpack exception to make sure we get the original cause
						throw e.getCause();
					}
				}
			}
		} catch (Exception e) {
			logger.error("", e);
			long rowsCount = numOfLines(localTempFile);
			sendMetric(config.dataset + "/" + config.table, Optional.empty(),
					0, 0, rowsCount);
			NexlaError error = getErrorDetails(e, Optional.of(rowsCount));
			NexlaConnectorUtils.sendNexlaNotificationEvent(
				controlMessageProducer, ERROR, runId, SINK, config.sinkId, e.getMessage(), 0L, notifyErrorMessage(error));
			quarantineFile(localTempFile, resource, e);
			publishMonitoringLog(e.getMessage(), NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
		} finally {
			localTempFile.delete();
		}
	}

	@Data
	@AllArgsConstructor
	private static class BQJobResult {
		private Job job;
		private Exception exception;

		boolean isFailed() {
			return exception != null || job == null || job.getStatus().getError() != null;
		}

		private String getErrorMessage() {
			if (job != null) {
				if (job.getStatus().getExecutionErrors() != null && job.getStatus().getExecutionErrors().size() > 1) {
					return job.getStatus().getExecutionErrors()
							.stream()
							.map(BigQueryError::getMessage)
							.filter(x -> !x.contains("Please look into the errors[]"))
							.collect(Collectors.joining("; "));
				} else if (job.getStatus().getError() != null) {
					return job.getStatus().getError().getMessage();
				}
			}

			if (exception != null) {
				return exception.getMessage();
			} else {
				return null;
			}
		}
	}

	@SneakyThrows
	private BQJobResult insert(BigQuery bigQuery, File localTempFile, TableId tableId, FormatOptions formatOptions) {
		WriteChannelConfiguration writeChannelConfiguration = getWriteChannel(bigQuery, tableId, mode, formatOptions);
		JobId jobId = JobId.newBuilder().build();
		try (TimeLogger ignored = new TimeLogger(logger, String.format("Insert local file %s (%d) into %s", localTempFile, localTempFile.length(), tableId));
			 TableDataWriteChannel bigQueryWriter = bigQuery.writer(jobId, writeChannelConfiguration)) {
			try (OutputStream stream = Channels.newOutputStream(bigQueryWriter)) {
				Files.copy(localTempFile.toPath(), stream);
			}

			try {
				return new BQJobResult(bigQueryWriter.getJob().waitFor(), null);
			} catch (BigQueryException exception) {
				logger.error("Failed to insert data into table " + tableId, exception);
				Job job;
				try {
					job = bigQuery.getJob(bigQueryWriter.getJob().getJobId());
				} catch (Exception e) {
					logger.error("Cannot get BQ Job", e);
					job = bigQueryWriter.getJob();
				}

				return new BQJobResult(job, exception);
			}
		}
	}

	/**
	 * Uploads file with records to a temporary table (not the target one).
	 * If there are multiple sink tasks running, every task should write into its own temporary table.
	 */
	@SneakyThrows
	private void upsert(BigQuery client, File localTempFile, Resource resource, FormatOptions formatOptions) {
		if (temporaryTableName == null) {
			this.temporaryTableName = "temp_" + config.sinkId + "_" + config.table + "_" + System.currentTimeMillis();
			logger.debug("Initializing the new temporary table: {}", temporaryTableName);
			executeQuery(client, String.format("CREATE TABLE IF NOT EXISTS %s LIKE %s",
					table(config.authConfig.projectId, config.dataset, temporaryTableName),
					table(config.authConfig.projectId, config.dataset, config.table)));
			tableUpdatesCounter.set(0);
		}
		try (TimeLogger ignored = new TimeLogger(logger, String.format("Upload local file to temporary table %s", temporaryTableName))) {
			TableId tempTableId = TableId.of(config.dataset, temporaryTableName);
			BQJobResult insertJob = insert(client, localTempFile, tempTableId, formatOptions);
			tableUpdatesCounter.incrementAndGet();
			if (insertJob.isFailed()) {
				onFailure(insertJob, localTempFile, resource);
				return;
			}
			logger.info("Insert Job into temporary table {} succeeded (total table updates: {}), file: {}, job: {}", temporaryTableName, tableUpdatesCounter.get(), localTempFile, insertJob.job);
			LoadStatistics stats = insertJob.job.getStatistics();
			recordsToMergeCounter.addAndGet(stats.getOutputRows());
			quarantineErroneousMessagesIfNeeded(localTempFile, resource, insertJob.job);
			// Report only errors, as records will be delivered to the target table later
			sendMetric(config.dataset + "/" + config.table, Optional.empty(), 0, 0, stats.getBadRecords());
		}
	}


	@SneakyThrows
	private boolean mergeTempTable(Supplier<BigQuery> clientSupplier) {
		pipelineStatusReport.ifPresent(status -> status.reportPipelineFlushStarted(1));
		long recordsToMerge = recordsToMergeCounter.get();
		if (recordsToMerge == 0L) {
			logger.debug("No records to merge from temporary table");
			runIdsToHeartbeat.clear();
			return false;
		}
		BigQuery client = clientSupplier.get();
		try (TimeLogger ignored = new TimeLogger(logger, String.format("Merge temporary table %s with %d records into target %s", temporaryTableName, recordsToMerge, config.table))) {
			List<String> columns = getColumns(client, config.authConfig.projectId, config.dataset, config.table);
			String mergeStmt = String.format(
					"MERGE %s T\n" +
							" USING (SELECT * FROM (SELECT *, ROW_NUMBER() OVER(PARTITION BY %s) AS DEDUP_ROW_NR FROM %s) INTERM WHERE INTERM.DEDUP_ROW_NR = 1) S\n" +
							" ON %s\n" +
							" WHEN MATCHED THEN\n" +
							"  UPDATE SET %s\n" +
							" WHEN NOT MATCHED THEN\n" +
							"  INSERT %s",
					table(config.authConfig.projectId, config.dataset, config.table),
					StreamEx.of(config.idFields).map(s -> String.format("`%s`", s)).joining(", "),
					table(config.authConfig.projectId, config.dataset, temporaryTableName),
					keyCondition(config.idFields),
					updateStmt(config.idFields, columns),
					insertStmt(columns));
			BQJobResult mergeJob = executeJob(client, mergeStmt);
			Resource resource = new Resource(this.config.sinkId, SINK);
			if (mergeJob.isFailed()) {
				String errorMessage = String.format("Merge job %s failed, %s records are not merged from temporary table %s: %s",
						mergeJob.getJob().getJobId(), recordsToMerge, temporaryTableName, mergeJob.getErrorMessage());
				logger.error("{}: Job: {}", errorMessage, mergeJob.job);
				publishNotification(resource, errorMessage);
				publishMonitoringLog(errorMessage, NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
				sendMetric(config.dataset + "/" + config.table, Optional.empty(), 0, 0, recordsToMerge);
				return false;
			} else {
				JobStatistics.QueryStatistics stats = mergeJob.job.getStatistics();
				logger.info("Merge Job {} succeeded, total records: {} records, status: {}, totalBytesProcessed: {}, totalBytesBilled: {}",
						mergeJob.job.getJobId(), recordsToMerge, mergeJob.job.getStatus(), stats.getTotalBytesProcessed(), stats.getTotalBytesBilled());
				// Generally, we should not have errors here, all the data was already uploaded to BigQuery before the merge operation
				sendMetric(config.dataset + "/" + config.table, Optional.empty(), recordsToMerge, stats.getTotalBytesProcessed(), 0);
				handleOtherErrors(resource, mergeJob.getJob().getStatus().getExecutionErrors());
				deleteTable(client, TableId.of(config.dataset, temporaryTableName));
				temporaryTableName = null;
				recordsToMergeCounter.set(0);
				tableUpdatesCounter.set(0);
				return true;
			}
		}
	}

	private static String table(String p, String d, String t) {
		return String.format("`%s.%s.%s`", p, d, t);
	}

	@SneakyThrows
	private void onInsertSuccess(BQJobResult handle, File localTempFile, Resource resource) {
		logger.info("Insert Job succeeded, file: {}, status: {}", localTempFile, handle.job.getStatus());

		LoadStatistics stats = handle.job.getStatistics();

		if (stats.getBadRecords() != 0) {
			String errorMessage = "Failed to handle " + stats.getBadRecords() + " records";
			publishNotification(resource, errorMessage);
			publishMonitoringLog(errorMessage, NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
		}

		quarantineErroneousMessagesIfNeeded(localTempFile, resource, handle.job);

		// subtracting errors from # of rows
		sendMetric(config.dataset + "/" + config.table, Optional.empty(),
				stats.getOutputRows(), stats.getOutputBytes(), stats.getBadRecords());
	}

	@SneakyThrows
	private void onFailure(BQJobResult handle, File localTempFile, Resource resource) {
		logger.error("Job failed, file: {}, with errors: {}", localTempFile.getName(), handle.job.getStatus());
		String errorMessage = handle.getErrorMessage();
		boolean quarantined = quarantineErroneousMessagesIfNeeded(localTempFile, resource, handle.job);
		if (!quarantined) {
			quarantineFile(
					localTempFile,
					resource,
					handle.exception != null ? handle.exception : new RuntimeException(errorMessage)
			);
		}

		publishNotification(resource, errorMessage);
		long rowsCount = numOfLines(localTempFile);
		// All records failed so total records updated is 0
		sendMetric(config.dataset + "/" + config.table, Optional.empty(), 0L, 0L, rowsCount);
		saveErrorFile(localTempFile);
	}

	@SneakyThrows
	private void quarantineFile(File localTempFile, Resource resource, Exception exception) {
		try (Stream<String> stream = Files.lines(localTempFile.toPath())) {
			Stream<NexlaMessage> nexlaMessageStream = stream
					.map(x -> StreamUtils.jsonUtil().jsonToMap(x))
					.map(LinkedHashMap::new)
					.map(NexlaMessage::new);

			quarantineMessages(nexlaMessageStream, resource, exception);
		}
	}

	@SneakyThrows
	private void quarantineMessages(Stream<NexlaMessage> stream, Resource resource, Throwable exception) {
		stream.forEach(x -> {
			this.dataMessageProducer.sendQuarantineMessage(resource, x, exception);
		});
	}


	protected void saveErrorFile(File localTempFile) throws IOException {
		String errorFileName = ERROR_FILE_PREFIX + config.sinkId + "-" + config.dataset + "-" + config.table + "-" + runId;
		File tempFile = File.createTempFile(errorFileName, null);
		FileUtils.copyFile(localTempFile, tempFile);
		tempFile.deleteOnExit();
		logger.error("Error on file upload. Saved output to temp file " + tempFile);
	}

	private void publishNotification(Resource resource, String errorMessage) {
		NexlaNotificationEvent notification = new NexlaNotificationEvent();
		notification.setEventSource(resource.type.name());
		notification.setEventType(NotificationEventType.ERROR);
		notification.setEventTimeMillis(nowUTC().getMillis());
		notification.setResourceId(resource.id);
		notification.setResourceType(resource.type);
		NexlaNotificationEventContext notificationEventContext = new NexlaNotificationEventContext();
		notificationEventContext.setErrorMessage(StringUtils.left(errorMessage, 250));
		notificationEventContext.setErrorDetail(errorMessage);
		notificationEventContext.setRunId(runId);
		notification.setContext(notificationEventContext);
		this.controlMessageProducer.publishNotification(notification);
	}

	private boolean quarantineErroneousMessagesIfNeeded(File localTempFile, Resource resource, Job job) {
		if (isEmpty(job.getStatus().getExecutionErrors())) {
			return false;
		}

		// Caution! job.getStatus().getExecutionErrors() does not return all the errors, despite the say so in documentation
		// https://cloud.google.com/java/docs/reference/google-cloud-bigquery/latest/com.google.cloud.bigquery.JobStatus#com_google_cloud_bigquery_JobStatus_getExecutionErrors__

		// Step 1. Separate different error types
		List<BigQueryError> jsonParseErrors = new ArrayList<>();
		List<BigQueryError> valueMappingErrors = new ArrayList<>();
		List<BigQueryError> otherErrors = new ArrayList<>();

		for (BigQueryError bigQueryError : job.getStatus().getExecutionErrors()) {
			boolean isJsonParseError = bigQueryError.getMessage().startsWith(ERROR_JSON_PARSING_AT_POSITION);
			if (isJsonParseError) {
				jsonParseErrors.add(bigQueryError);
				continue;
			}
			boolean isValueMappingError = ERROR_FIELD_MAPPING_PATTERN.matcher(bigQueryError.getMessage()).matches();
			if (isValueMappingError) {
				valueMappingErrors.add(bigQueryError);
				continue;
			}
			otherErrors.add(bigQueryError);
		}

		// Step 2. Handle Json parse errors
		try {
			handleJsonParseErrors(localTempFile, resource, jsonParseErrors);
		} catch (Exception e) {
			logger.error("Failed to handle {} BigQuery json parse errors: {}", jsonParseErrors.size(), jsonParseErrors, e);
		}

		// Step 3. Handle value mapping errors
		try {
			handleValueMappingErrors(localTempFile, resource, valueMappingErrors);
		} catch (Exception e) {
			logger.error("Failed to handle {} BigQuery value mapping errors: {}", valueMappingErrors.size(), valueMappingErrors, e);
		}

		// Step 4. Handle other errors
		try {
			handleOtherErrors(resource, otherErrors);
		} catch (Exception e) {
			logger.error("Failed to handle {} BigQuery other errors: {}", otherErrors.size(), otherErrors, e);
		}

		return !isEmpty(job.getStatus().getExecutionErrors());
	}

	private void handleJsonParseErrors(File localTempFile, Resource resource, List<BigQueryError> jsonParseErrors) throws IOException {
		if (jsonParseErrors.isEmpty()) {
			return;
		}
		Map<Integer, String> errors =
				StreamEx.of(jsonParseErrors)
						.map(x -> x.getMessage().substring(ERROR_JSON_PARSING_AT_POSITION.length()))
						.mapToEntry(
								x -> Integer.parseInt(x.split(":")[0]),
								x -> x.substring(x.split(":")[0].length() + 1).trim())
						.toMap();
		String error = StreamEx.of(errors.values()).limit(2).joining(", ");
		String errorStr = errors.size() > 2 ? error + ", ... " : error;
		publishNotification(resource, errorStr);
		publishMonitoringLog(
				"1 of the errors is: " + errors.values().stream().findFirst().orElse(""),
				NexlaMonitoringLogType.LOG,
				NexlaMonitoringLogSeverity.ERROR
		);

		List<Integer> charPositions = errors
				.keySet()
				.stream()
				.sorted()
				.collect(toList());

		AtomicInteger charNumberRead = new AtomicInteger(0);
		try (StreamEx<String> stream = StreamEx.of(Files.lines(localTempFile.toPath()))) {
			stream
					.takeWhile(x -> !charPositions.isEmpty())
					.forEach(x -> {
						Integer pos = charPositions.get(0);
						if (charNumberRead.get() <= pos && pos <= charNumberRead.get() + x.length()) {
							Map<String, Object> m = StreamUtils.jsonUtil().jsonToMap(x);
							this.dataMessageProducer.sendQuarantineMessage(resource, new NexlaMessage(new LinkedHashMap<>(m)), new RuntimeException(errors.get(pos)));
							charPositions.remove(0);
						}
						charNumberRead.addAndGet(x.length());
					});
		}
	}

	private void handleValueMappingErrors(File localTempFile, Resource resource, List<BigQueryError> errors) throws IOException {
		if (errors.isEmpty()) {
			return;
		}

		String errorMessage = "Failed to map record values: " + errors.stream().findFirst().get().getMessage() + (errors.size() > 1 ? ", ..." : "");
		publishNotification(resource, errorMessage);
		publishMonitoringLog(errorMessage, NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);

		List<String> fileLines = Files.readAllLines(localTempFile.toPath());
		// Match records from file by field name and field value.
		// Keep track of the last matched message number in order to not send the same message twice (if there are several records with the same failed value)
		int lastLineIdx = -1;
		for (BigQueryError bqError : errors) {
			Matcher matcher = ERROR_FIELD_MAPPING_PATTERN.matcher(bqError.getMessage());
			if (!matcher.find()) { // Should not happen
				logger.error("Wrong logic: Failed to match ERROR_FIELD_MAPPING_PATTERN to {}", bqError.getMessage());
				continue;
			}
			String fieldName = matcher.group(1);
			String fieldValue = matcher.group(2);
			boolean recordFound = false;
			for (int i = lastLineIdx + 1; i < fileLines.size(); i++) {
				NexlaMessage nexlaMessage = new NexlaMessage(new LinkedHashMap<>(StreamUtils.jsonUtil().jsonToMap(fileLines.get(i))));
				if (fieldValue.equals(nexlaMessage.getRawMessage().get(fieldName))) {
					this.dataMessageProducer.sendQuarantineMessage(resource, nexlaMessage, new RuntimeException(bqError.getMessage()));
					lastLineIdx = i;
					recordFound = true;
					break;
				}
			}
			if (!recordFound) { // Should not happen
				logger.error("Wrong logic: Failed to find record for big query error message {}", bqError.getMessage());
			}
		}
	}

	private void handleOtherErrors(Resource resource, List<BigQueryError> otherErrors) {
		if (otherErrors == null || otherErrors.isEmpty()) {
			return;
		}
		otherErrors
				.stream()
				.collect(Collectors.groupingBy(BigQueryError::getReason))
				.forEach((reason, errors) -> {
					String errorMessage = String.format("Failed to handle records: %s: %s", reason, errors.stream().findFirst().get().getMessage());
					publishNotification(resource, errorMessage);
					publishMonitoringLog(errorMessage, NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
				});
	}

	private LinkedHashMap<String, Object> getRawMessage(boolean manualCreateTable,
														LinkedHashMap<String, Object> message) {
		if (manualCreateTable) {
			return message;
		}
		return convertMap(message);
	}

	private LinkedHashMap<String, Object> convertMap(LinkedHashMap<String, Object> startMap) {
		if (startMap.isEmpty()) {
			return null;
		}
		LinkedHashMap<String, Object> resultMap = Maps.newLinkedHashMap();
		EntryStream.of(startMap)
				.forKeyValue((k, v) -> resultMap.put(k, processValue(v)));
		return resultMap;
	}

	private List<Object> convertList(List<Object> list) {
		return StreamEx.of(list)
				.map(v -> processValue(v))
				.toList();
	}

	private Object processValue(Object v) {
		if (v instanceof Map) {
			return convertMap((LinkedHashMap<String, Object>) v);
		} else if (v instanceof List) {
			return convertList((List) v);
		} else {
			return v;
		}
	}

	@Override
	public void close(Collection<org.apache.kafka.common.TopicPartition> partitions) throws ConnectException {
		super.close(partitions);

		Optional.ofNullable(this.streaming)
				.flatMap(Function.identity())
				.ifPresent(LoadStrategy::close);
	}

	private void createTable(BigQuerySinkConnectorConfig config, BigQuery bigQuery) {
		logger.debug("Creating table for manual mapping mode");
		publishMonitoringLog("Creating table for manual mapping mode", NexlaMonitoringLogType.EVENT, NexlaMonitoringLogSeverity.INFO);
		config.mappingConfig.ifPresent(mappingConfig -> {
			AutomaticBinding automaticBinding = new AutomaticBindingFactory(adminApiClient).create(config.originals());

			BigQueryDialect dbDialect = new BigQueryDialect();

			Map<String, String> fieldRewrite = new HashMap<>();
			List<Field> fields = mappingConfig
					.getMapping()
					.values()
					.stream()
					.flatMap(EntryStream::of)
					.map(x -> {
						String type;
						if (x.getValue().equals(DEFAULT_MAPPING)) {
							type = automaticBinding.getSqlType(x.getKey(), dbDialect);
						} else {
							type = x.getValue();
						}
						String name = x.getKey().replaceAll(NON_VALID_CHARS, "_");
						if (!name.equals(x.getKey())) {
							fieldRewrite.put(x.getKey(), name);
						}

						Field.Builder fb = Field.newBuilder(name, LegacySQLTypeName.valueOf(type));
						if (config.idFields.contains(name)) {
							fb.setMode(Mode.REQUIRED);
						}
						if (config.arrayFields.contains(name)) {
							fb.setMode(Mode.REPEATED);
						}

						return fb.build();
					})
					.collect(toList());

			if (!fieldRewrite.isEmpty()) {
				fieldMapper = new StaticFieldNameMapper(fieldRewrite);
			}

			if (mappingConfig.getTrackerMode() != Tracker.TrackerMode.NONE) {
				String trackerFieldName = mappingConfig.getTrackerFieldName();
				String trackerType = automaticBinding.getSqlType(trackerFieldName, dbDialect);
				fields.add(Field.of(trackerFieldName, LegacySQLTypeName.valueOf(trackerType)));
			}

			Schema schema = Schema.of(fields);

			StandardTableDefinition.Builder tableDefinitionBldr =
					StandardTableDefinition.newBuilder()
							.setSchema(schema);

			//For setting partitioning column on BQ table
			config.partitioningColumn.ifPresent(pc -> {
				TimePartitioning partitioning =
						TimePartitioning.newBuilder(TimePartitioning.Type.DAY) //Default partitioning is by day
								.setField(pc) //name of column to use for partitioning
								.build();

				tableDefinitionBldr.setTimePartitioning(partitioning);
			});

			//Clustering columns on BQ table
			if (!config.clusteringingColumns.isEmpty()) {
				Clustering clustering =
						Clustering.newBuilder().setFields(config.clusteringingColumns).build();
				tableDefinitionBldr.setClustering(clustering);
			}

			StandardTableDefinition tableDefinition = tableDefinitionBldr.build();

			com.google.cloud.bigquery.TableInfo tableInfo = com.google.cloud.bigquery.TableInfo
					.newBuilder(tableId, tableDefinition)
					.build();

			logger.debug("Mapping mode = MANUAL, creating target table " + config.table);
			bigQuery.create(tableInfo);

			if (config.idFields != null && !config.idFields.isEmpty()) {
				QueryJobConfiguration alterTablePkJob = QueryJobConfiguration.newBuilder(
						String.format("ALTER TABLE %s ADD PRIMARY KEY (%s) NOT ENFORCED",
								String.join(".", tableId.getDataset(), tableId.getTable()),
								String.join(",", config.idFields))).build();

				try {
					bigQuery.query(alterTablePkJob);
				} catch (Throwable exc) {
					// BigQuery pk constraints must be not enforced, however they may be used by the query optimizer
					// So, it's not a fatal error if we fail to set them

					logger.warn("Failed to set primary keys on BigQuery table {} {}, "
									+ "primary key constraints are not enforced, continuing", tableInfo,
							exc.getMessage() != null && !exc.getMessage().isEmpty() ? "due to {}" : "");
				}
			}
		});
	}

	private void deleteTable(BigQuery client, TableId tableId) {
		Table table = client.getTable(tableId);
		if (table != null && table.exists()) {
			boolean deleted = client.delete(table.getTableId());
			logger.debug("Deleted table {}: {}", table.getTableId(), deleted);
		}
	}

	private WriteChannelConfiguration getWriteChannel(BigQuery bigQuery, TableId tableId, String mode, FormatOptions formatOptions) {
		WriteChannelConfiguration.Builder builder = WriteChannelConfiguration
				.newBuilder(tableId)
				.setMaxBadRecords(config.maxBadRecords)
				.setFormatOptions(formatOptions);

		// true for MODE_AUTO as well, becase AutoDetect builds schema based on 500 rows sample,
		// which may or may not include all possible columns for the dateset.
		logger.info("Ignore unknown values allowed: {}", config.ignoreUnknown);
		builder.setIgnoreUnknownValues(config.ignoreUnknown);

		if (config.allowAdditionRelaxation) {
			logger.info("Addition/Relaxation allowed, adding to the schema update options.");
			builder.setSchemaUpdateOptions(Arrays.asList(
					JobInfo.SchemaUpdateOption.ALLOW_FIELD_ADDITION,
					JobInfo.SchemaUpdateOption.ALLOW_FIELD_RELAXATION));
		}

		if (mode.equals(MODE_AUTO)) {
			builder.setSchemaUpdateOptions(Arrays.asList(
					JobInfo.SchemaUpdateOption.ALLOW_FIELD_ADDITION,
					JobInfo.SchemaUpdateOption.ALLOW_FIELD_RELAXATION));
			return builder.setAutodetect(config.autodetect).build();
		}

		Table table = bigQuery.getTable(tableId);
		if (table == null || !table.exists()) {
			logger.warn("Target table {} does not exist. It will be created by BigQuery with schema auto-detection", tableId.getTable());
			return builder.setAutodetect(true).build();
		}

		return builder.setAutodetect(false)
				.setSchema(table.getDefinition().getSchema())
				.build();
	}

	private static final Set<String> SUPPORTED_FORMATS = Set.of(
			"CSV", "AVRO", "PARQUET", "ORC"
	);

	private static FormatOptions toFormatOptions(File file) {
		String format = FilenameUtils.getExtension(file.getName()).toUpperCase();
		if (SUPPORTED_FORMATS.contains(format)) {
			return FormatOptions.of(format);
		}

		return FormatOptions.json();
	}

	private <T> Supplier<T> clientRetriable(Supplier<T> supplier) {
		return () -> {
			try {
				return supplier.get();
			} catch (Exception e) {
				logger.warn("Failed to retrieve client, retry one more time", e);
				return supplier.get();
			}
		};
	}

	@SneakyThrows
	private <T> void withClientRetriable(Supplier<T> supplier, Consumer<T> consumer) {
		T client;
		ScheduledFuture<?> heartbeat = SCHEDULED_POOL.scheduleWithFixedDelay(
			() -> {
        try {
          heartbeatSender.get(new HeartbeatPacerKey(runId, config.sinkId, true));
        } catch (ExecutionException e) {
					logger.warn("Error during heartbeat process.", e);
        }
      }, 0, 1,
			TimeUnit.MINUTES);
		try {
			try {
				client = supplier.get();
			} catch (Exception e) {
				logger.warn("Failed to retrieve client, retry one more time", e);
				client = supplier.get();
			}
			consumer.accept(client);
		} finally {
			heartbeat.cancel(true);
		}
	}

	@SneakyThrows
	private void recreateFileWriter() {
		this.bufferFile = File.createTempFile("big", "query");
		this.writer = new FileWriter(bufferFile);
	}

	private BigQuery getBigQueryClient() {
		return service.getBigQueryService().getService(config.authConfig);
	}

	@SneakyThrows
	private TableResult executeQuery(BigQuery client, String query) {
		logger.info("Execute query: {}", query);
		QueryJobConfiguration config = QueryJobConfiguration.newBuilder(query).build();
		return client.query(config);
	}

	@SneakyThrows
	private BQJobResult executeJob(BigQuery client, String query) {
		logger.info("Execute job: {}", query);
		QueryJobConfiguration config = QueryJobConfiguration.newBuilder(query).build();
		Job job = client.create(JobInfo.of(config));
		try {
			return new BQJobResult(job.waitFor(), null);
		} catch (BigQueryException exception) {
			logger.error("Error while executing job", exception);
			Job updatedJobInformation;
			try {
				updatedJobInformation = client.getJob(job.getJobId());
			} catch (Exception e) {
				logger.error("Cannot get BQ Job", e);
				updatedJobInformation = job;
			}

			return new BQJobResult(updatedJobInformation, exception);
		}

	}

	private List<String> getColumns(BigQuery client, String project, String dataset, String table) {
		final String query = String.format("SELECT column_name FROM `%s.%s.INFORMATION_SCHEMA`.COLUMNS WHERE table_name = '%s'"
				, project, dataset, table);
		final TableResult tableResult = executeQuery(client, query);
		return StreamEx.of(tableResult.iterateAll().iterator())
				.map(f -> f.get(0).getStringValue())
				.toList();
	}

	private String updateStmt(Set<String> keys, List<String> columns) {
		return StreamEx.of(columns)
				.filter(c -> !keys.contains(c))
				.map(c -> String.format("T.`%s` = S.`%s`", c, c))
				.joining(", ");
	}

	private String insertStmt(List<String> columns) {
		final String leftPart = StreamEx.of(columns)
				.map(s -> String.format("`%s`", s))
				.joining(", ", "(", ")");
		final String rightPart = StreamEx.of(columns)
				.map(v -> String.format("S.`%s`", v))
				.joining(", ", "(", ")");
		return String.format("%s VALUES %s", leftPart, rightPart);
	}

	private String keyCondition(Set<String> keys) {
		return StreamEx.of(keys)
				.map(String::trim)
				.map(k -> String.format("T.`%s` = S.`%s`", k, k))
				.joining(" AND ");
	}

	private boolean hasData(File file) {
		try (Stream<String> lines = Files.lines(file.toPath())) {
			return lines.findFirst().isPresent();
		} catch (Exception e) {
			logger.error("Failed to check the actual data in file {}", file, e);
			return file.length() > 0;
		}
	}

	@SneakyThrows
	private long numOfLines(File localTempFile) {
		try (Stream<String> lines = Files.lines(localTempFile.toPath())) {
			return lines.count();
		}
	}

	private interface FieldNameMapper {
		LinkedHashMap<String, Object> map(LinkedHashMap<String, Object> message);

		FieldNameMapper NOOP = m -> m;
	}

	private static class DynamicFieldNameMapper implements FieldNameMapper {
		private final Map<String, String> fieldToName;

		public DynamicFieldNameMapper() {
			this.fieldToName = new HashMap<>();
		}

		private static String normalize(String k) {
			return k.toLowerCase().replaceAll(NON_VALID_CHARS, "_");
		}

		public LinkedHashMap<String, Object> map(LinkedHashMap<String, Object> message) {
			LinkedHashMap<String, Object> result = new LinkedHashMap<>();
			EntryStream
					.of(message)
					.forEach(kv -> {
						String key = fieldToName.computeIfAbsent(kv.getKey(), DynamicFieldNameMapper::normalize);
						result.put(key, kv.getValue());
					});
			return result;
		}
	}

	private static class SchemaAwareFieldNameMapper extends StaticFieldNameMapper {
		public SchemaAwareFieldNameMapper(Schema schema) {
			super(buildMapping(schema));
		}

		private static Map<String, String> buildMapping(Schema schema) {
			return schema.getFields()
					.stream()
					.collect(Collectors.toMap(f -> f.getName().toLowerCase(), Field::getName));
		}
	}

	private static class StaticFieldNameMapper implements FieldNameMapper {
		private final Map<String, String> fieldToName;

		public StaticFieldNameMapper(Map<String, String> fieldToName) {
			this.fieldToName = fieldToName;
		}

		public LinkedHashMap<String, Object> map(LinkedHashMap<String, Object> message) {
			LinkedHashMap<String, Object> result = new LinkedHashMap<>();
			EntryStream
					.of(message)
					.forEach(kv -> {
						String key = fieldToName.getOrDefault(kv.getKey().toLowerCase(), kv.getKey());
						result.put(key, kv.getValue());
					});
			return result;
		}
	}
}
