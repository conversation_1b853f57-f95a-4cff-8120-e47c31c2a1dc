package com.nexla.probe.deltalake;

import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.*;
import com.nexla.common.exception.DeltaLakeException;
import com.nexla.connect.common.spark.SparkSingleContextEnforcer;
import com.nexla.connector.config.deltalake.DeltaLakeAuthConfig;
import com.nexla.connector.config.deltalake.DeltaLakeSinkConnectorConfig;
import com.nexla.connector.config.file.*;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import com.nexla.listing.client.ListingClient;
import io.delta.tables.DeltaTable;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.MapUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.kafka.common.config.AbstractConfig;
import org.apache.spark.sql.DataFrameWriter;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;
import static java.util.Optional.of;

import static com.nexla.common.ConnectionType.*;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix;
import static com.nexla.connect.common.spark.SparkSingleContextEnforcer.extractSparkSessionKeyFromFileConfig;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.empty;

public class DeltaLakeConnectorService extends FileConnectorService<DeltaLakeAuthConfig> {

    public static final String DELTA = "delta";
    public static final String OVERWRITE = "overwrite";

    private final Logger logger = LoggerFactory.getLogger(DeltaLakeConnectorService.class);

    public DeltaLakeConnectorService(AdminApiClient adminApiClient, ListingClient listingClient, String credentialsDecryptKey) {
        super(adminApiClient, listingClient, credentialsDecryptKey);
    }

    @Override
    public AuthResponse authenticate(DeltaLakeAuthConfig config) {
        try {
            final DeltaLakeAware deltaLakeAware = getDeltaLakeHelper(config.credentialsType.get());
            return deltaLakeAware
                    .getFileConnectorService()
                    .authenticate(deltaLakeAware.getDataLakeAuthConfig(config));
        } catch (Exception e) {
            logger.error("Exception while authenticating, credsId={}", config.getCredsId(), e);
            return authError(e);
        }
    }

    @Override
    public boolean checkWriteAccess(AbstractConfig config) {
        FileConnectorAuth connectorConfig = (FileConnectorAuth) config;
        final DeltaLakeAuthConfig authConfig = (DeltaLakeAuthConfig) connectorConfig.getAuthConfig();
        try {
            Map<String, String> sparkSessionConfigs = getSparkSessionConfigs(connectorConfig);
            return SparkSingleContextEnforcer.withSparkSession(
                    extractSparkSessionKeyFromFileConfig(connectorConfig),
                    () -> getSparkSession(authConfig, sparkSessionConfigs),
                    sparkSession -> {
                        Dataset<Long> data = sparkSession.range(0, 3);
                        String path = getDeltaLakeHelper(connectorConfig.getConnectionType())
                                .getDeltaLakePath(connectorConfig, "nexla-lake");
                        data.write().format(DELTA).mode(OVERWRITE).save(path);
                        DeltaTable.forPath(sparkSession, path).delete();
                        return true;
                    });
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public FileDetails writeInternal(FileConnectorAuth connectorConfig, String key, File file) {
        final DeltaLakeAuthConfig authConfig = (DeltaLakeAuthConfig) connectorConfig.getAuthConfig();
        final Map<String, String> sparkSessionConfigs = getSparkSessionConfigs(connectorConfig);
        return SparkSingleContextEnforcer.withSparkSession(
                extractSparkSessionKeyFromFileConfig(connectorConfig),
                () -> getSparkSession(authConfig, sparkSessionConfigs),
                sparkSession -> {
                    final Dataset<Row> dataset = sparkSession.read().json(file.getPath());
                    final DeltaLakeSinkConnectorConfig deltaSinkConnectorConfig = getSinkConfig(connectorConfig);
                    final FileSinkConnectorConfig fileSinkConnectorConfig = (FileSinkConnectorConfig) connectorConfig;
                    final AWSAuthConfig.BucketPrefix bucketPrefix = toBucketPrefix(connectorConfig.getPath(), true);
                    final String targetPath = bucketPrefix.prefix;
                    final String deltaLakePath = getDeltaLakeHelper(authConfig.credentialsType.get())
                            .getDeltaLakePath(fileSinkConnectorConfig, targetPath);

                    try {
                        if (DeltaTableInsertMode.UPSERT == deltaSinkConnectorConfig.deltaTableInsertMode) {
                            final String deltaTableKeys = deltaSinkConnectorConfig.deltaTableKeys;
                            merge(sparkSession, dataset, deltaLakePath, deltaTableKeys.split(","));
                        } else {
                            String overwriteSchema = sparkSessionConfigs.getOrDefault("overwriteSchema", "false");
                            String mergeSchema = sparkSessionConfigs.getOrDefault("mergeSchema", "false");
                            logger.info("M=writeInternal, key={}, mergeSchema={}, overwriteSchema={}", key, mergeSchema, overwriteSchema);

                            DataFrameWriter<Row> frameWriter = dataset.write()
                                    .format(DELTA)
                                    .option("mergeSchema", mergeSchema)
                                    .option("overwriteSchema", overwriteSchema)
                                    .mode(deltaSinkConnectorConfig.deltaTableInsertMode.mode());

                            deltaSinkConnectorConfig.partitionKeys.ifPresent(partitionKeysStr -> {
                                if (!partitionKeysStr.isEmpty()) {
                                    List<String> partitionKeys = Arrays.stream(partitionKeysStr.split(","))
                                            .map(s -> s.replace("\\,", ",").trim())
                                            .collect(Collectors.toList());

                                    frameWriter.partitionBy(partitionKeys.toArray(new String[0]));
                                }
                            });

                            frameWriter.save(deltaLakePath);
                        }
                        return new FileDetails(targetPath, empty(), of(targetPath));
                    } catch (Exception e) {
                        List<String> data = dataset.toJSON().collectAsList();
                        throw new DeltaLakeException(e.getMessage(), e, data);
                    }
                });
    }

    private DeltaLakeSinkConnectorConfig getSinkConfig(FileConnectorAuth config) {
        return new DeltaLakeSinkConnectorConfig(config.getConnectorConfig().originalsStrings());
    }

    @Override
    public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
        FileSourceConnectorConfig connectorConfig = (FileSourceConnectorConfig) config;
        return getFileConnectorService(connectorConfig.getConnectionType())
                .listBuckets(config);
    }

    @Override
    public StreamEx<NexlaFile> listBucketContents(AbstractConfig config) {
        FileSourceConnectorConfig connectorConfig = (FileSourceConnectorConfig) config;
        return getFileConnectorService(connectorConfig.getConnectionType())
                .listBucketContents(config);
    }

    @Override
    public InputStream readInputStreamInternal(FileConnectorAuth connectorAuthConfig, String file) {
        return getFileConnectorService(connectorAuthConfig.getConnectionType())
                .readInputStreamInternal(connectorAuthConfig, file);
    }

    @Override
    public FileDetails writeInternal(FileConnectorAuth connectorAuthConfig, String key, InputStream inputStream) {
        return getFileConnectorService(connectorAuthConfig.getConnectionType())
                .writeInternal(connectorAuthConfig, key, inputStream);
    }

    @Override
    public boolean doesFileExistsInternal(FileConnectorAuth connectorAuthConfig, String key) {
        return getFileConnectorService(connectorAuthConfig.getConnectionType())
                .doesFileExistsInternal(connectorAuthConfig, key);
    }

    @Override
    public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig config) {
        FileSourceConnectorConfig connectorAuthConfig = (FileSourceConnectorConfig) config;
        return getFileConnectorService(connectorAuthConfig.getConnectionType())
                .listTopLevelBuckets(config);
    }

    public SparkSession getSparkSession(DeltaLakeAuthConfig config, Map<String, String> sparkSessionConfigs) {
        final SparkSession.Builder builder = SparkSession.builder()
                .appName(config.sparkAppName)
                .master(config.sparkMaster)
                .config("spark.authenticate", false)
                .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension")
                .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog");

        final Map<String, String> dataLakeConfig = getDeltaLakeHelper(config.credentialsType.get())
                .getDataLakeConfig(config);
        dataLakeConfig
                .forEach(builder::config);

        MapUtils.emptyIfNull(sparkSessionConfigs)
                .keySet()
                .stream()
                .filter(key -> key.contains("spark"))
                .forEach(key -> builder.config(key, sparkSessionConfigs.get(key)));

        UserGroupInformation.setLoginUser(UserGroupInformation.createRemoteUser("appuser"));
        final SparkSession sparkSession = builder.getOrCreate();

        final Configuration hadoopConfiguration = sparkSession.sparkContext().hadoopConfiguration();
        hadoopConfiguration.set("fs.wasbs.impl", org.apache.hadoop.fs.azure.NativeAzureFileSystem.class.getName());
        hadoopConfiguration.set("fs.file.impl", org.apache.hadoop.fs.LocalFileSystem.class.getName());
        hadoopConfiguration.set("fs.delta.impl", org.apache.spark.sql.delta.sources.DeltaDataSource.class.getName());

        return sparkSession;
    }

    @Override
    public FileDetails writeWithSuffixIfExists(FileConnectorAuth config, String key, File file) {
        return writeInternal(config, key, file);
    }

    private FileConnectorService getFileConnectorService(ConnectionType connectionType) {
        return getDeltaLakeHelper(connectionType)
                .getFileConnectorService();
    }

    private void merge(SparkSession sparkSession, Dataset<Row> datasetToMerge, String targetPath, String[] keys) {
        final String[] columns = sparkSession.read().format(DELTA).load(targetPath).columns();
        DeltaTable.forPath(sparkSession, targetPath)
                .as("target")
                .merge(
                        datasetToMerge.as("updates"),
                        keysCondition(keys))
                .whenMatched()
                .updateExpr(updateMap(columns, keys))
                .whenNotMatched()
                .insertExpr(insertMap(columns))
                .execute();
    }

    private String keysCondition(String[] keys) {
        return Arrays.stream(keys)
                .map(String::trim)
                .map(k -> String.format("target.%s = updates.%s", k, k))
                .collect(Collectors.joining(" AND "));

    }

    private Map<String, String> insertMap(String[] columns) {
        Map<String, String> colsMap = new HashMap<>();
        Arrays.stream(columns)
                .forEach(col -> colsMap.put(col, "updates." + col));
        return colsMap;
    }

    private Map<String, String> updateMap(String[] columns, String[] keys) {
        final Set<String> keysSet = Arrays.stream(keys)
                .map(String::trim)
                .collect(Collectors.toSet());
        Map<String, String> nonKeysMap = new HashMap<>();
        Arrays.stream(columns)
                .filter(c -> !keysSet.contains(c))
                .forEach(c -> nonKeysMap.put(c, "updates." + c));
        return  nonKeysMap;
    }

    public DeltaLakeAware getDeltaLakeHelper(CredentialType type) {
        if (Objects.equals(DELTA_LAKE_S3, type)) {
            return new S3DeltaLake();
        } else if (Objects.equals(DELTA_LAKE_AZURE_BLB, type)) {
            return new AzureBlobStoreDeltaLake();
        } else if (Objects.equals(DELTA_LAKE_AZURE_DATA_LAKE, type)) {
            return new AzureDataLakeDeltaLake();
        } else {
            throw new IllegalStateException("Delta lake is not supported.");
        }
    }

    private Map<String, String> getSparkSessionConfigs(FileConnectorAuth fileConnectorConfig) {
        Map<String, String> resultMap = Maps.newHashMap();
        var connectorConfig = fileConnectorConfig.getConnectorConfig();

        if (nonNull(connectorConfig) && connectorConfig instanceof FileSinkConnectorConfig) {
            var sparkSessionConfigs = ((FileSinkConnectorConfig) connectorConfig).sparkSessionConfigs;

            if (isNull(sparkSessionConfigs)) {
                return resultMap;
            }

            for (String config : sparkSessionConfigs.split(",")) {
                String[] split = config.split("=");

                if (split.length == 2) {
                    resultMap.put(split[0], split[1]);
                }
            }
        }

        return resultMap;
    }

}
