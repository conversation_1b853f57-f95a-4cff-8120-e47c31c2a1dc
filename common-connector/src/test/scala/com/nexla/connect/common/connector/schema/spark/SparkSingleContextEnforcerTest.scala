package com.nexla.connect.common.connector.schema.spark

import com.nexla.connect.common.spark.SparkSingleContextEnforcer
import com.nexla.connector.config.file.FileConnectorAuth
import com.nexla.connector.config.iceberg.IcebergSourceConnectorConfig
import com.nexla.connector.config.rest.BaseAuthConfig
import org.apache.spark.SparkContext
import org.apache.spark.sql.SparkSession
import org.mockito.Mockito._
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfterEach, TagAnnotation}

import java.util.Collections

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class SparkSingleContextEnforcerTest extends AnyFlatSpecLike with Matchers with BeforeAndAfterEach {
  private var mockSparkSession: SparkSession = _
  private var mockSparkSessionNew: SparkSession = _
  private var mockSparkContext: SparkContext = _

  override protected def beforeEach(): Unit = {
    mockSparkSession = mock(classOf[SparkSession])
    mockSparkSessionNew = mock(classOf[SparkSession])
    mockSparkContext = mock(classOf[org.apache.spark.SparkContext])
    when(mockSparkSession.sparkContext).thenReturn(mockSparkContext)
    when(mockSparkContext.isStopped).thenReturn(false)
  }

  override protected def afterEach(): Unit = {
    // clean up static state to let tests run independently
    SparkSingleContextEnforcer.synchronized {
      val field = SparkSingleContextEnforcer.getClass.getDeclaredField("currentSession")
      field.setAccessible(true)
      field.set(SparkSingleContextEnforcer, None)
    }
  }

  it should "consecutive calls with the same key reuse active sessions" in {
    var builderCallCount = 0
    val builder = {
      builderCallCount += 1
      mockSparkSession
    }
    SparkSingleContextEnforcer.withSparkSession("key1")(builder) { session =>
      session shouldBe mockSparkSession
    }
    builderCallCount shouldBe 1
    SparkSingleContextEnforcer.withSparkSession("key1")(builder) { session =>
      session shouldBe mockSparkSession
    }
    builderCallCount shouldBe 1
  }

  it should "consecutive calls with a different key build new sessions, stops cached session" in {
    var builderCallCount = 0
    val builder = {
      builderCallCount += 1
      mockSparkSession
    }
    SparkSingleContextEnforcer.withSparkSession("key1")(builder) { session =>
      session shouldBe mockSparkSession
    }
    builderCallCount shouldBe 1
    val builderNew = {
      builderCallCount += 1
      mockSparkSessionNew
    }
    SparkSingleContextEnforcer.withSparkSession("key2")(builderNew) { session =>
      session shouldBe mockSparkSessionNew
    }
    builderCallCount shouldBe 2
    verify(mockSparkSession, times(1)).stop()
  }

  it should "consecutive calls with a same key but stopped context build new sessions" in {
    var builderCallCount = 0
    val builder = {
      builderCallCount += 1
      mockSparkSession
    }
    SparkSingleContextEnforcer.withSparkSession("key1")(builder) { session =>
      session shouldBe mockSparkSession
    }
    builderCallCount shouldBe 1
    val builderNew = {
      builderCallCount += 1
      mockSparkSessionNew
    }
    when(mockSparkContext.isStopped).thenReturn(true)
    SparkSingleContextEnforcer.withSparkSession("key1")(builderNew) { session =>
      session shouldBe mockSparkSessionNew
    }
    builderCallCount shouldBe 2
    verify(mockSparkSession, times(0)).stop()
  }

  class TestBaseAuthConfig(credsIdValue: Integer) extends BaseAuthConfig(Collections.emptyMap[String, Any](), credsIdValue) {
    private val credsId: Integer = credsIdValue
    override def getCredsId: Integer = credsId
  }

  "SparkSessionKey" should "be equal for matching creds ID and warehouse path" in {
    val mockAuth1 = mock(classOf[FileConnectorAuth])
    val mockAuth2 = mock(classOf[FileConnectorAuth])
    val mockBaseAuth1 = new TestBaseAuthConfig(123)
    val mockBaseAuth2 = new TestBaseAuthConfig(123)

    when(mockAuth1.getAuthConfig).thenReturn(mockBaseAuth1)
    when(mockAuth2.getAuthConfig).thenReturn(mockBaseAuth2)
    when(mockAuth1.getPath).thenReturn("same")
    when(mockAuth2.getPath).thenReturn("same")

    val key1 = SparkSingleContextEnforcer.extractSparkSessionKeyFromFileConfig(mockAuth1)
    val key2 = SparkSingleContextEnforcer.extractSparkSessionKeyFromFileConfig(mockAuth2)

    key1 shouldEqual key2
  }

  "SparkSessionKey" should "not be equal when warehouse paths differ" in {
    val mockAuth1 = mock(classOf[FileConnectorAuth])
    val mockAuth2 = mock(classOf[FileConnectorAuth])
    val mockBaseAuth1 = new TestBaseAuthConfig(123)
    val mockBaseAuth2 = new TestBaseAuthConfig(123)

    when(mockAuth1.getAuthConfig).thenReturn(mockBaseAuth1)
    when(mockAuth2.getAuthConfig).thenReturn(mockBaseAuth2)
    when(mockAuth1.getPath).thenReturn("same")
    when(mockAuth2.getPath).thenReturn("other")

    val key1 = SparkSingleContextEnforcer.extractSparkSessionKeyFromFileConfig(mockAuth1)
    val key2 = SparkSingleContextEnforcer.extractSparkSessionKeyFromFileConfig(mockAuth2)

    key1 should not equal key2
  }

  "SparkSessionKey" should "not be equal when cred ids differ" in {
    val mockAuth1 = mock(classOf[FileConnectorAuth])
    val mockAuth2 = mock(classOf[FileConnectorAuth])
    val mockBaseAuth1 = new TestBaseAuthConfig(123)
    val mockBaseAuth2 = new TestBaseAuthConfig(456)

    when(mockAuth1.getAuthConfig).thenReturn(mockBaseAuth1)
    when(mockAuth2.getAuthConfig).thenReturn(mockBaseAuth2)
    when(mockAuth1.getPath).thenReturn("same")
    when(mockAuth2.getPath).thenReturn("same")

    val key1 = SparkSingleContextEnforcer.extractSparkSessionKeyFromFileConfig(mockAuth1)
    val key2 = SparkSingleContextEnforcer.extractSparkSessionKeyFromFileConfig(mockAuth2)

    key1 should not equal key2
  }

  it should "extract Iceberg warehouse paths correctly" in {
    val mockAuth = mock(classOf[FileConnectorAuth])
    val mockBaseAuth = new TestBaseAuthConfig(123)
    val mockIcebergConfig = mock(classOf[IcebergSourceConnectorConfig])

    when(mockAuth.getAuthConfig).thenReturn(mockBaseAuth)
    when(mockAuth.getConnectorConfig).thenReturn(mockIcebergConfig)
    when(mockIcebergConfig.getPath).thenReturn("iceberg-path")

    val key = SparkSingleContextEnforcer.extractSparkSessionKeyFromFileConfig(mockAuth)
    key.asInstanceOf[SparkSingleContextEnforcer.SparkSessionKey].warehouseDir shouldEqual "iceberg-path"
  }
}