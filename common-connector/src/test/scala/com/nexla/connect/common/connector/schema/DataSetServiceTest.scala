package com.nexla.connect.common.connector.schema

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.common.util.concurrent.MoreExecutors
import com.nexla.admin.client.{AdminApiClient, DataSet, NexlaSchema}
import com.nexla.common.NexlaMessage
import com.nexla.common.StreamUtils.lhm
import com.nexla.common.notify.transport.ControlMessageProducer
import com.nexla.connect.common.SchemaDetectionResult
import com.nexla.connect.common.cdc.DebeziumData
import com.nexla.transform.TransformServiceImpl
import com.nexla.transform.schema.FormatDetector
import org.mockito.ArgumentMatchers.{any, anyInt}
import org.mockito.Mockito
import org.mockito.Mockito._
import org.scalatest.{BeforeAndAfterEach, Tag, TagAnnotation}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.util
import java.util.Optional
import java.util.concurrent.{ConcurrentHashMap, Future}
import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class DataSetServiceTest extends AnyFlatSpecLike with Matchers with BeforeAndAfterEach {
  private var mockAdminApi: AdminApiClient = _
  private var testDataSet: DataSet = _
  private val mockMessageProducer = mock(classOf[ControlMessageProducer])
  private val mapper = new ObjectMapper

  override protected def beforeEach(): Unit = {
    mockAdminApi = mock(classOf[AdminApiClient])
    testDataSet = createMockDataSet()
    Mockito.when(mockAdminApi.getDataSet(any())).thenReturn(Optional.of(testDataSet))
    FormatDetector.initDefault()
    DataSetService.setMockExecutorService(MoreExecutors.newDirectExecutorService())
  }

  it should "properly detect schema for CDC sources" in {
    val messages = List(
      new NexlaMessage(lhm(
        "nexla_op", "READ",
        "nexla_op_database", "postgres",
        "nexla_op_table", "NEX_13615_partitioning",
        "nexla_cdc_info", new DebeziumData(
          util.List.of(),
          new util.LinkedHashMap[String, AnyRef](),
          new util.LinkedHashMap[String, AnyRef](),
          new util.LinkedHashMap[String, util.Map[String, String]](),
        )
      )),
    )

    val service = new DataSetService(
      mockAdminApi, mockMessageProducer,
      new TransformServiceImpl,
      new ConcurrentHashMap[Integer, Integer],
      new ConcurrentHashMap[Integer, util.List[NexlaMessage]],
      new util.HashSet[NexlaSchema],
      5
    )

    val schema = service.getSchema(messages.map(_.getRawMessage).asJava)

    schema.getSchemaId shouldBe 570970437
  }

  "addToInMemorySamples" should "add to local samples when less than max" in {
    val nm1: NexlaMessage = new NexlaMessage(lhm("key1", "val1"))
    val nm2: NexlaMessage = new NexlaMessage(lhm("key2", "val2"))
    val nm3: NexlaMessage = new NexlaMessage(lhm("key3", "val3"))
    val nm4: NexlaMessage = new NexlaMessage(lhm("key4", "val4"))
    val nm5: NexlaMessage = new NexlaMessage(lhm("key5", "val5"))

    val samples = new util.ArrayList[NexlaMessage] {{
      add(nm1)
    }}
    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, new TransformServiceImpl, new ConcurrentHashMap[Integer, Integer], new ConcurrentHashMap[Integer, util.List[NexlaMessage]] {
      {
        put(1, samples)
      }
    }, new util.HashSet[NexlaSchema], 5)

    val called = dataSetService.addToInMemorySamples(1, List(nm2, nm3, nm4, nm5).asJava)
    dataSetService.getLocalSamples(1).get() shouldBe List(nm1, nm2, nm3, nm4, nm5).asJava
    called shouldBe true
    mockingDetails(mockAdminApi).getInvocations.size() shouldBe 0
  }

  it should "not add to local samples when greater than max" in {
    val nm1: NexlaMessage = new NexlaMessage(lhm("key1", "val1"))
    val nm2: NexlaMessage = new NexlaMessage(lhm("key2", "val2"))
    val nm3: NexlaMessage = new NexlaMessage(lhm("key3", "val3"))
    val nm4: NexlaMessage = new NexlaMessage(lhm("key4", "val4"))
    val nm5: NexlaMessage = new NexlaMessage(lhm("key5", "val5"))

    val oldSamples = List(nm1).asJava
    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, new TransformServiceImpl, new ConcurrentHashMap[Integer, Integer], new ConcurrentHashMap[Integer, util.List[NexlaMessage]] {
      {
        put(1, oldSamples)
      }
    }, new util.HashSet[NexlaSchema], 1)

    val called = dataSetService.addToInMemorySamples(1, List(nm2, nm3, nm4, nm5).asJava)
    dataSetService.getLocalSamples(1).get() shouldBe oldSamples
    called shouldBe false
    mockingDetails(mockAdminApi).getInvocations.size() shouldBe 0
  }

  "addToRemoteSamples" should "call to adminAPI to add samples" in {
    val nm2: NexlaMessage = new NexlaMessage(lhm("key2", "val2"))

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, new TransformServiceImpl, new ConcurrentHashMap[Integer, Integer], new ConcurrentHashMap[Integer, util.List[NexlaMessage]], new util.HashSet[NexlaSchema], 5)

    val newSamples: util.List[NexlaMessage] = List(nm2).asJava
    val result: Future[_] = dataSetService.addToRemoteSamples(1, newSamples)
    result.get()
    verify(mockAdminApi, times(1)).putDataSetSamples(List(nm2).asJava, 1)
  }

  it should "update schema and sample on superset" in {
    val msg1: util.LinkedHashMap[String, AnyRef] = lhm("key1", "val1")
    val msg2: util.LinkedHashMap[String, AnyRef] = lhm("key1", "val2")
    msg2.put("key2", Integer.valueOf(1))

    val transformService = new TransformServiceImpl
    val firstSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg1).asJava).getOutput, classOf[NexlaSchema])
    val secondSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg1, msg2).asJava).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(firstSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]] {{
      val samples = new util.ArrayList[NexlaMessage]()
      samples.add(new NexlaMessage(msg1))
      put(testDataSet.getId, samples)
    }}, new util.HashSet[NexlaSchema]{{
      add(firstSchema)
    }}, 5)

    dataSetService.setTryCombineSingleSchema();
    val detectedSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2).asJava).getOutput, classOf[NexlaSchema])
    val samples: util.List[NexlaMessage] = util.List.of(new NexlaMessage(msg1), new NexlaMessage(msg2))
    val result = dataSetService.tryUpdateDataSet(List(new NexlaMessage(msg2)).asJava, detectedSchema)
    result.get.updateSamplesCallback.accept(samples)
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getLocalSamples(testDataSet.getId).get() shouldBe samples
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe secondSchema.getSchemaId
    dataSetService.getDatasetId(secondSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, times(1)).updateDataSetSchema(any())
    verify(mockAdminApi, times(1)).putDataSetSamples(any(), anyInt())
  }

  it should "not update schema but add sample on subset" in {
    val msg1: util.LinkedHashMap[String, AnyRef] = lhm("key1", "val2", "key2", Integer.valueOf(1))
    val msg2: util.LinkedHashMap[String, AnyRef] = lhm("key1", "val1")
    val transformService = new TransformServiceImpl
    val firstSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg1).asJava).getOutput, classOf[NexlaSchema])
    val detectedSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2).asJava).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(firstSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]] {{
      val samples = new util.ArrayList[NexlaMessage]()
      samples.add(new NexlaMessage(msg1))
      put(testDataSet.getId, samples)
    }}, new util.HashSet[NexlaSchema]{{
      add(firstSchema)
    }}, 5)

    dataSetService.setTryCombineSingleSchema();
    val samples: util.List[NexlaMessage] = util.List.of(new NexlaMessage(msg1), new NexlaMessage(msg2))
    val result = dataSetService.tryUpdateDataSet(List(new NexlaMessage(msg2)).asJava, detectedSchema)
    result.get.updateSamplesCallback.accept(samples)
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getLocalSamples(testDataSet.getId).get() shouldBe samples
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe firstSchema.getSchemaId
    dataSetService.getDatasetId(firstSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, never()).updateDataSetSchema(any())
    verify(mockAdminApi, times(1)).putDataSetSamples(any(), anyInt())
  }

  it should "not update schema and not add sample on equal when have max samples" in {
    val msg1: util.LinkedHashMap[String, AnyRef] = lhm("key1", "val1")
    val msg2: util.LinkedHashMap[String, AnyRef] = lhm("key1", "val2")
    val transformService = new TransformServiceImpl
    val firstSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg1).asJava).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(firstSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]] {{
      val samples = new util.ArrayList[NexlaMessage]()
      samples.add(new NexlaMessage(msg1))
      put(testDataSet.getId, samples)
    }}, new util.HashSet[NexlaSchema]{{
      add(firstSchema)
    }}, 1)

    val detectedSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2).asJava).getOutput, classOf[NexlaSchema])
    dataSetService.tryUpdateDataSet(List(new NexlaMessage(msg2)).asJava, detectedSchema)
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getLocalSamples(testDataSet.getId).get() shouldBe List(new NexlaMessage(msg1)).asJava
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe firstSchema.getSchemaId
    dataSetService.getDatasetId(firstSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, never()).updateDataSetSchema(any())
    verify(mockAdminApi, never()).putDataSetSamples(any(), anyInt())
  }

  it should "not update schema but add sample on equal when do not have max samples" in {
    val msg1: util.LinkedHashMap[String, AnyRef] = lhm("key1", "val1")
    val msg2: util.LinkedHashMap[String, AnyRef] = lhm("key1", "val2")
    val transformService = new TransformServiceImpl
    val firstSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg1).asJava).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(firstSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]] {{
      val samples = new util.ArrayList[NexlaMessage]()
      samples.add(new NexlaMessage(msg1))
      put(testDataSet.getId, samples)
    }}, new util.HashSet[NexlaSchema]{{
      add(firstSchema)
    }}, 5)

    dataSetService.setTryCombineSingleSchema();
    val detectedSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2).asJava).getOutput, classOf[NexlaSchema])
    val result: Optional[SchemaDetectionResult] = dataSetService.tryUpdateDataSet(List(new NexlaMessage(msg2)).asJava, detectedSchema)
    val samples: util.List[NexlaMessage] = util.List.of(new NexlaMessage(msg1), new NexlaMessage(msg2))
    result.get().updateSamplesCallback.accept(samples)
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getLocalSamples(testDataSet.getId).get() shouldBe samples
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe firstSchema.getSchemaId
    dataSetService.getDatasetId(firstSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, never()).updateDataSetSchema(any())
    verify(mockAdminApi, times(1)).putDataSetSamples(any(), anyInt())
  }

  "tryUpdateDataSet in single schema mode" should "update schema or samples on disjoint" in {
    val msg1 = new NexlaMessage(lhm("key1", "val1"))
    val msg2 = new NexlaMessage(lhm("key2", "val2"))
    val transformService = new TransformServiceImpl
    val firstSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg1.getRawMessage).asJava).getOutput, classOf[NexlaSchema])
    val detectedSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2.getRawMessage).asJava).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(firstSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]] {{
      val samples = new util.ArrayList[NexlaMessage]()
      samples.add(msg1)
      put(testDataSet.getId, samples)
    }}, new util.HashSet[NexlaSchema]{{
      add(firstSchema)
    }}, 5)

    assert(dataSetService.singleSchemaMode().equals(false))

    val result: Optional[SchemaDetectionResult] = dataSetService.tryUpdateDataSet(List(msg2).asJava, detectedSchema)
    result shouldBe Optional.empty()
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getLocalSamples(testDataSet.getId).get() shouldBe List(msg1).asJava
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe firstSchema.getSchemaId
    dataSetService.getDatasetId(firstSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, never()).updateDataSetSchema(any())
    verify(mockAdminApi, never()).putDataSetSamples(any(), anyInt())
  }

  "tryUpdateDataSet in multiple schema mode" should "not update schema or samples on disjoint" in {
    val msg1 = new NexlaMessage(lhm("key1", "val1"))
    val msg2 = new NexlaMessage(lhm("key2", "val2"))
    val transformService = new TransformServiceImpl
    val firstSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg1.getRawMessage).asJava).getOutput, classOf[NexlaSchema])
    val detectedSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2.getRawMessage).asJava).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(firstSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]] {{
      val samples = new util.ArrayList[NexlaMessage]()
      samples.add(msg1)
      put(testDataSet.getId, samples)
    }}, new util.HashSet[NexlaSchema]{{
      add(firstSchema)
    }}, 5)

    assert(dataSetService.singleSchemaMode().equals(false))

    val result: Optional[SchemaDetectionResult] = dataSetService.tryUpdateDataSet(List(msg2).asJava, detectedSchema)
    result shouldBe Optional.empty()
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getLocalSamples(testDataSet.getId).get() shouldBe List(msg1).asJava
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe firstSchema.getSchemaId
    dataSetService.getDatasetId(firstSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, never()).updateDataSetSchema(any())
    verify(mockAdminApi, never()).putDataSetSamples(any(), anyInt())
  }

  it should "update schema and sample on superset" in {
    val msg1 = new NexlaMessage(lhm("key1", "val1"))
    val msg2 = new NexlaMessage(lhm("key1", "val2", "key2", Integer.valueOf(1)))
    val transformService = new TransformServiceImpl
    val firstSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg1.getRawMessage).asJava).getOutput, classOf[NexlaSchema])
    val detectedSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2.getRawMessage).asJava).getOutput, classOf[NexlaSchema])
    val secondSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(util.List.of(msg1.getRawMessage, msg2.getRawMessage)).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(firstSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]] {{
      val samples = new util.ArrayList[NexlaMessage]()
      samples.add(msg1)
      put(testDataSet.getId, samples)
    }}, new util.HashSet[NexlaSchema]{{
      add(firstSchema)
    }}, 5)

    val result: Optional[SchemaDetectionResult] = dataSetService.tryUpdateDataSet(List(msg2).asJava, detectedSchema)
    val samples: util.List[NexlaMessage] = util.List.of(msg1, msg2)
    result.get().updateSamplesCallback.accept(samples)
    result.get().dataSetId shouldBe testDataSet.getId
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getLocalSamples(testDataSet.getId).get() shouldBe samples
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe secondSchema.getSchemaId
    dataSetService.getDatasetId(secondSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, times(1)).updateDataSetSchema(any())
    verify(mockAdminApi, times(1)).putDataSetSamples(any(), anyInt())
  }

  it should "not update schema but add sample on subset if not max samples" in {
    val msg1 = new NexlaMessage(lhm("key1", "val2", "key2", Integer.valueOf(1)))
    val msg2 = new NexlaMessage(lhm("key1", "val1"))
    val transformService = new TransformServiceImpl
    val firstSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg1.getRawMessage).asJava).getOutput, classOf[NexlaSchema])
    val detectedSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2.getRawMessage).asJava).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(firstSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]] {{
      val samples = new util.ArrayList[NexlaMessage]()
      samples.add(msg1)
      put(testDataSet.getId, samples)
    }}, new util.HashSet[NexlaSchema]{{
      add(firstSchema)
    }}, 5)

    val samples: util.List[NexlaMessage] = util.List.of(msg1, msg2)
    val result: Optional[SchemaDetectionResult] = dataSetService.tryUpdateDataSet(List(msg2).asJava, detectedSchema)
    result.get().updateSamplesCallback.accept(samples)
    result.get().dataSetId shouldBe testDataSet.getId
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getLocalSamples(testDataSet.getId).get() shouldBe samples
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe firstSchema.getSchemaId
    dataSetService.getDatasetId(firstSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, never()).updateDataSetSchema(any())
    verify(mockAdminApi, times(1)).putDataSetSamples(any(), anyInt())
  }

  it should "not update schema and not add sample on subset if max samples" in {
    val msg1 = new NexlaMessage(lhm("key1", "val2", "key2", Integer.valueOf(1)))
    val msg2 = new NexlaMessage(lhm("key1", "val1"))
    val transformService = new TransformServiceImpl
    val firstSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg1.getRawMessage).asJava).getOutput, classOf[NexlaSchema])
    val detectedSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2.getRawMessage).asJava).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(firstSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]] {{
      val samples = new util.ArrayList[NexlaMessage]()
      samples.add(msg1)
      put(testDataSet.getId, samples)
    }}, new util.HashSet[NexlaSchema]{{
      add(firstSchema)
    }}, 1)

    val result: Optional[SchemaDetectionResult] = dataSetService.tryUpdateDataSet(List(msg2).asJava, detectedSchema)
    result.get().dataSetId shouldBe testDataSet.getId
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getLocalSamples(testDataSet.getId).get() shouldBe List(msg1).asJava
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe firstSchema.getSchemaId
    dataSetService.getDatasetId(firstSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, never()).updateDataSetSchema(any())
    verify(mockAdminApi, never()).putDataSetSamples(any(), anyInt())
  }

  it should "not update schema and not add sample on equal" in {
    val msg1 = new NexlaMessage(lhm("key1", "val1"))
    val msg2 = new NexlaMessage(lhm("key1", "val2"))
    val transformService = new TransformServiceImpl
    val firstSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg1.getRawMessage).asJava).getOutput, classOf[NexlaSchema])
    val detectedSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2.getRawMessage).asJava).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(firstSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]] {{
      val samples = new util.ArrayList[NexlaMessage]()
      samples.add(msg1)
      put(testDataSet.getId, samples)
    }}, new util.HashSet[NexlaSchema]{{
      add(firstSchema)
    }}, 5)

    val result: Optional[SchemaDetectionResult] = dataSetService.tryUpdateDataSet(List(msg2).asJava, detectedSchema)
    result shouldBe Optional.empty()
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getLocalSamples(testDataSet.getId).get() shouldBe List(msg1).asJava
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe firstSchema.getSchemaId
    dataSetService.getDatasetId(firstSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, never()).updateDataSetSchema(any())
    verify(mockAdminApi, never()).putDataSetSamples(any(), anyInt())
  }

  it should "update schema on csv parsing integer coersion" in {
    val msg1 = new NexlaMessage(lhm("key1", "1"))
    val msg2 = new NexlaMessage(lhm("key1", Integer.valueOf(1)))
    val transformService = new TransformServiceImpl
    val oldSchema: NexlaSchema = mapper.convertValue( transformService.accumulateSchema(List(msg1.getRawMessage).asJava).getOutput, classOf[NexlaSchema])
    val newSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2.getRawMessage).asJava).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(oldSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]](), new util.HashSet[NexlaSchema]{{
      add(oldSchema)
    }}, 5)

    val result: Optional[SchemaDetectionResult] = dataSetService.tryUpdateDataSet(List(msg2).asJava, newSchema)
    result.get().dataSetId shouldBe testDataSet.getId
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe newSchema.getSchemaId
    dataSetService.getDatasetId(newSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, times(1)).updateDataSetSchema(any())
  }

  it should "update schema on csv parsing number coersion" in {
    val msg1 = new NexlaMessage(lhm("key1", "2.123"))
    val msg2 = new NexlaMessage(lhm("key1", Double.box(2.123)))
    val transformService = new TransformServiceImpl
    val oldSchema: NexlaSchema = mapper.convertValue( transformService.accumulateSchema(List(msg1.getRawMessage).asJava).getOutput, classOf[NexlaSchema])
    val newSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2.getRawMessage).asJava).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(oldSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]](), new util.HashSet[NexlaSchema]{{
      add(oldSchema)
    }}, 5)

    val result: Optional[SchemaDetectionResult] = dataSetService.tryUpdateDataSet(List(msg2).asJava, newSchema)
    result.get().dataSetId shouldBe testDataSet.getId
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe newSchema.getSchemaId
    dataSetService.getDatasetId(newSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, times(1)).updateDataSetSchema(any())
  }

  it should "update schema on csv parsing boolean coersion" in {
    val msg1 = new NexlaMessage(lhm("key1", "true"))
    val msg2 = new NexlaMessage(lhm("key1", Boolean.box(true)))
    val transformService = new TransformServiceImpl
    val oldSchema: NexlaSchema = mapper.convertValue( transformService.accumulateSchema(List(msg1.getRawMessage).asJava).getOutput, classOf[NexlaSchema])
    val newSchema: NexlaSchema = mapper.convertValue(transformService.accumulateSchema(List(msg2.getRawMessage).asJava).getOutput, classOf[NexlaSchema])

    val dataSetService = new DataSetService(mockAdminApi, mockMessageProducer, transformService, new ConcurrentHashMap[Integer, Integer]{{
      put(oldSchema.getSchemaId, testDataSet.getId)
    }}, new ConcurrentHashMap[Integer, util.List[NexlaMessage]](), new util.HashSet[NexlaSchema]{{
      add(oldSchema)
    }}, 5)

    val result: Optional[SchemaDetectionResult] = dataSetService.tryUpdateDataSet(List(msg2).asJava, newSchema)
    result.get().dataSetId shouldBe testDataSet.getId
    dataSetService.getNumSchemas shouldBe 1
    dataSetService.getSchemaId(testDataSet.getId).get() shouldBe newSchema.getSchemaId
    dataSetService.getDatasetId(newSchema.getSchemaId).get() shouldBe testDataSet.getId
    verify(mockAdminApi, times(1)).updateDataSetSchema(any())
  }

  private def createMockDataSet() = {
    val dataSet = new DataSet
    dataSet.setId(2)
    dataSet
  }
}