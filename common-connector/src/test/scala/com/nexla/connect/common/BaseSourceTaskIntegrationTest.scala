package com.nexla.connect.common

import com.dimafeng.testcontainers.KafkaContainer
import com.dimafeng.testcontainers.scalatest.TestContainerForAll
import com.nexla.admin.client.{AdminApiClient, DataSource, Org}
import com.nexla.common.metrics.NexlaRawMetric
import com.nexla.common.notify.NexlaNotificationEvent
import com.nexla.common.{ConnectionType, NexlaConstants, ResourceType}
import com.nexla.connect.common.BaseKafkaTest.{BOOTSTRAP_SERVERS, BOOTSTRAP_SECURITY_PROTOCOL, init}
import com.nexla.connector.ConnectorService
import com.nexla.connector.config.BaseConnectorConfig
import com.nexla.connector.config.file.FileSourceConnectorConfig
import org.apache.kafka.common.config.ConfigDef
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{mock, when}
import org.scalatest.BeforeAndAfterAll
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.util
import java.util.{Collections, Optional}
import scala.collection.JavaConverters._

@com.nexla.test.ScalaIntegrationTests
class BaseSourceTaskIntegrationTest extends BaseKafkaTest
  with AnyFlatSpecLike with Matchers
  with BeforeAndAfterAll with TestContainerForAll {

  override val containerDef: KafkaContainer.Def = KafkaContainer.Def(
    dockerImageName = "confluentinc/cp-kafka:7.2.11"
  )

  override def beforeAll(): Unit = {
    withContainers {
      kafka =>
        init(kafka.container)
    }
  }

  val testTask = new BaseSourceTask[FileSourceConnectorConfig] {
    override protected def parseConfig(props: util.Map[String, String]): FileSourceConnectorConfig =
      new FileSourceConnectorConfig(props)

    /**
     * Collects records from source
     *
     * @return null in order to continue silently
     */
    override protected def collectRecords(): CollectRecordsResult = new CollectRecordsResult(Collections.emptyList())

    override def doStart(props: util.Map[String, String]): Unit = {
      throw new NumberFormatException("I am a message 2")
    }

    override def configDef(): ConfigDef = {
      FileSourceConnectorConfig.configDef()
    }
  }


  "exception propagated to the top of connector task" should "not generate metric" in {
    val sourceId = 1
    val mockAdminApiClient = mock(classOf[AdminApiClient])
    //nex-5239
    withConsumer((metricsTopic, metricsConsumer) => {
      when(mockAdminApiClient.getDataSource(any())).thenThrow(new IllegalArgumentException("I am a message 1"))

      val props = Map(
        NexlaConstants.SOURCE_ID -> "1",
        NexlaConstants.CREDS_ENC -> "",
        NexlaConstants.CREDS_ENC_IV -> "",
        NexlaConstants.CREDENTIALS_DECRYPT_KEY -> "",
        NexlaConstants.PATH -> "/",
        NexlaConstants.SOURCE_TYPE -> ConnectionType.S3.name(),
        NexlaConstants.MONITOR_POLL_MS -> "1",
        NexlaConstants.LISTING_ENABLED -> "false",
        NexlaRawMetric.RUN_ID -> "12345",
        ConnectorService.UNIT_TEST -> "true",
        NexlaConstants.CONTROL_KAFKA_BOOTSTRAP_SERVERS -> BOOTSTRAP_SERVERS,
        NexlaConstants.DATA_KAFKA_BOOTSTRAP_SERVERS -> BOOTSTRAP_SERVERS,
        NexlaConstants.CONTROL_KAFKA_SECURITY_PROTOCOL -> BOOTSTRAP_SECURITY_PROTOCOL,
        NexlaConstants.DATA_KAFKA_SECURITY_PROTOCOL -> BOOTSTRAP_SECURITY_PROTOCOL,
        NexlaConstants.SOURCE_ID -> String.valueOf(sourceId))

      assertThrows[IllegalArgumentException] {
        testTask.start(
          props.asJava,
          "",
          Optional.of(mockAdminApiClient)
        )
      }
      val metrics = readMetrics(metricsConsumer)
      metrics.size() shouldEqual 0
      if (testTask != null && testTask.getControlMessageProducer != null) {
        testTask.controlMessageProducer.close()
      }
    })
  }

  it should "generate notification" in {
    val sourceId = 2

    val mockAdminApiClient = mock(classOf[AdminApiClient])
    withConsumer((notifyTopic, notifyConsumer) => {
      val dataSource = new DataSource()
      dataSource.setId(sourceId)
      dataSource.setSourceConfig(Map.empty[String, Object].asJava)
      dataSource.setConnectionType(ConnectionType.S3)
      dataSource.setOrg(new Org(1, null, null, null, Optional.empty(), false, null, null, null))
      when(mockAdminApiClient.getDataSource(any())).thenReturn(Optional.ofNullable(dataSource))

      val props = Map(
        NexlaConstants.SOURCE_ID -> "1",
        NexlaConstants.CREDS_ENC -> "",
        NexlaConstants.CREDS_ENC_IV -> "",
        NexlaConstants.CREDENTIALS_DECRYPT_KEY -> "",
        NexlaConstants.PATH -> "/",
        NexlaConstants.SOURCE_TYPE -> ConnectionType.S3.name(),
        NexlaConstants.MONITOR_POLL_MS -> "1",
        NexlaConstants.LISTING_ENABLED -> "false",
        NexlaRawMetric.RUN_ID -> "12345",
        ConnectorService.UNIT_TEST -> "true",
        NexlaConstants.CONTROL_KAFKA_BOOTSTRAP_SERVERS -> BOOTSTRAP_SERVERS,
        NexlaConstants.DATA_KAFKA_BOOTSTRAP_SERVERS -> BOOTSTRAP_SERVERS,
        NexlaConstants.CONTROL_KAFKA_SECURITY_PROTOCOL -> BOOTSTRAP_SECURITY_PROTOCOL,
        NexlaConstants.DATA_KAFKA_SECURITY_PROTOCOL -> BOOTSTRAP_SECURITY_PROTOCOL,
        BaseConnectorConfig.NOTIFY_TOPIC -> notifyTopic,
        NexlaConstants.SOURCE_ID -> String.valueOf(sourceId))

      assertThrows[NumberFormatException] {
        testTask.start(props.asJava,
          "",
          Optional.of(mockAdminApiClient)
        )
      }
      val notifications = readNotifications(notifyConsumer, sourceId)
      notifications.size() shouldEqual 1
      val notification: NexlaNotificationEvent = notifications.get(0)
      notification.getContext.getErrorMessage shouldEqual "I am a message 2"
      notification.getResourceType shouldEqual ResourceType.SOURCE
      testTask.controlMessageProducer.close()
    }, "notify")
  }

}