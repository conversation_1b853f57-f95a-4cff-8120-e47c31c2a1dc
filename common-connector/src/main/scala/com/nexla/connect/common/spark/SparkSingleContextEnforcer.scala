package com.nexla.connect.common.spark

import com.nexla.connector.config.file.FileConnectorAuth
import com.nexla.connector.config.iceberg.{IcebergSinkConnectorConfig, IcebergSourceConnectorConfig}
import org.apache.spark.sql.SparkSession
import com.nexla.sc.util.StrictNexlaLogging
import com.nexla.telemetry.TelemetryContext
import org.apache.kafka.common.config.AbstractConfig

import java.util.concurrent.{Callable, Executors}

object SparkSingleContextEnforcer extends StrictNexlaLogging {

  // It should be a single-threaded pool to guarantee sequential access to the SparkContext
  private val EXECUTOR = Executors.newSingleThreadExecutor(r => {
    val t = new Thread(r, "spark-session-manager")
    t.setDaemon(true)
    t
  })

  private var currentSession: Option[ReusableSparkSession] = None

  private val telemetry = TelemetryContext.get()
  private val SESSION_REUSE_COUNTER = "spark_session_reuses_total"
  private val SESSION_CREATION_COUNTER = "spark_session_creations_total"
  private val SESSION_CREATION_TIME = "spark_session_creation_time"

  def withSparkSession[T](key: AnyRef)(builder: => SparkSession)(callback: SparkSession => T): T = {
    EXECUTOR.submit(new Callable[T] {
      override def call(): T = {
        val session = getOrCreateSession(key, builder)
        try {
          callback(session)
        } catch {
          case e: Exception =>
            logger.error(s"Error executing SparkSession function: ${e.getMessage}", e)
            throw e
        }
      }
    }).get()
  }

  private def getOrCreateSession(key: AnyRef, builder: => SparkSession): SparkSession = {
    currentSession match {
      case Some(reusableSession) =>
        reusableSession.tryReusingElseClose(key) match {
          case Some(session) =>
            logger.info(s"Reusing existing SparkSession for key: $key")
            telemetry.recordCounter(SESSION_REUSE_COUNTER)
            session
          case None =>
            logger.info(s"Creating new SparkSession for key: $key")
            createNewSession(key, builder)
        }
      case None =>
        logger.info(s"Creating first SparkSession for key: $key")
        createNewSession(key, builder)
    }
  }

  private def createNewSession(key: AnyRef, builder: => SparkSession): SparkSession = {
    telemetry.recordCounter(SESSION_CREATION_COUNTER)
    val startTime = System.currentTimeMillis()
    val newSession = builder
    currentSession = Some(ReusableSparkSession(newSession, key))
    telemetry.recordGauge(SESSION_CREATION_TIME, (System.currentTimeMillis() - startTime) / 1000)
    newSession
  }

  def extractSparkSessionKey(config: AbstractConfig): AnyRef = {
    config match {
      case fileConfig: FileConnectorAuth =>
        extractSparkSessionKeyFromFileConfig(fileConfig)
      case _ =>
        logger.info(s"Cannot extract SparkSessionKey for non-FileConnectorAuth config: ${config.getClass().getSimpleName}, using config as key")
        config
    }
  }

  def extractSparkSessionKeyFromFileConfig(fileConfig: FileConnectorAuth): AnyRef = {
    val authConfig = fileConfig.getAuthConfig
    SparkSessionKey(
      authConfig.getCredsId,
      extractWarehouseDirConfig(fileConfig)
    )
  }

  def extractWarehouseDirConfig(config: FileConnectorAuth): String = {
    config.getConnectorConfig match {
      case sinkConfig: IcebergSinkConnectorConfig => sinkConfig.getPath
      case sourceConfig: IcebergSourceConnectorConfig => sourceConfig.getPath
      case _ => config.getPath
    }
  }

  case class SparkSessionKey(
    credsId: Integer,
    warehouseDir: String
  )

}
