package com.nexla.sinkagent

import com.bazaarvoice.jolt.JsonUtils
import com.github.rholder.retry.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WaitStrategies}
import com.nexla.admin.client.AdminApiClient
import com.nexla.admin.client.config.EnrichedConfig.EnrichSinkParams
import com.nexla.admin.client.config.SinkConfigUtils
import com.nexla.common.NexlaConstants.{CONFIG_PARAMS_64, CONNECTOR_CLASS, DATASET_ID, SINK_AGENT}
import com.nexla.common.NexlaNamingUtils.nameDataSetTopic
import com.nexla.control.message.ConnectorConfig
import com.nexla.kafka.control.listener.ControlConsumerConstants.TOPICS
import com.nexla.kafka.service.TopicMetaService
import com.nexla.sc.client.job_scheduler.PipelineTaskMeta
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import org.apache.commons.codec.binary.Base64
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpMethod.{DELETE, GET, POST}
import org.springframework.http.{HttpEntity, HttpHeaders, HttpStatus, MediaType}
import org.springframework.web.client.{HttpClientErrorException, RestTemplate}

import java.util
import java.util.Collections.singletonList
import java.util.concurrent.TimeUnit
import scala.collection.JavaConverters._
import scala.util.Try

class SinkConnectorClient(enrichSinkParams: EnrichSinkParams,
                          restTemplate: RestTemplate)
  extends StrictNexlaLogging
    with WithLogging {

  val CONNECTORS_URL = "http://localhost:8083/connectors"
  val headers = {
    val h = new HttpHeaders()
    h.setContentType(MediaType.APPLICATION_JSON)
    h.setAccept(singletonList(MediaType.APPLICATION_JSON))
    h
  }

  def isAssignedConnector(sinkId: Int): Boolean = {
    getConnectors().contains(sinkConnectorName(sinkId))
  }

  def getConnectors() = {
    val responseType = new ParameterizedTypeReference[util.List[String]]() {}
    val request = new HttpEntity("", headers)
    restTemplate.exchange(CONNECTORS_URL, GET, request, responseType)
      .getBody
  }

  def restartSink(index: String, sinkId: Int) = try {
    logger.info(s"[$index] Restarting sink $sinkId")
    restTemplate.exchange(s"$CONNECTORS_URL/${sinkConnectorName(sinkId)}/restart?includeTasks=true",
      POST,
      new HttpEntity("", headers),
      classOf[util.Map[_, _]])
  } catch {
    case e: Exception =>
      logger.error("", e)
  }

  def stopSink(sinkId: Int) = {
    val retryer = RetryerBuilder.newBuilder[Unit]
      .retryIfException
      .withWaitStrategy(WaitStrategies.randomWait(5, TimeUnit.SECONDS))
      .withStopStrategy(attempt => {
        var statusCode: Option[HttpStatus] = None
        if (attempt.hasException) {
          statusCode = attempt.getExceptionCause match {
            case e: HttpClientErrorException => Some(e.getStatusCode)
            case _ => None
          }
        }
        attempt.getAttemptNumber >= 12 || statusCode.contains(HttpStatus.NOT_FOUND)
      })
      .build

    retryer.call(() => doStopSink(sinkId))
  }

  def doStopSink(sinkId: Int): Unit = {
    logger.info(s"Stopping sink $sinkId")
    restTemplate.exchange(s"$CONNECTORS_URL/${sinkConnectorName(sinkId)}",
      DELETE,
      new HttpEntity("", headers),
      classOf[String])
  }

  def startSink(topicMetaService: TopicMetaService,
                index: String,
                adminClient: AdminApiClient,
                sinkId: Int,
                meta: Option[PipelineTaskMeta]) = {
    try {
      logger.info(s"[$index] Starting sink $sinkId with $meta")
      adminClient
        .getDataSink(sinkId)
        .ifPresent { dataSink =>
          val sinkConfig = SinkConfigUtils.enrichSinkWithCtrl(dataSink, enrichSinkParams)
          val connectorConfig = new ConnectorConfig(sinkConnectorName(sinkId), sinkConfig)

          sinkConfig.put(CONNECTOR_CLASS, meta.flatMap(_.connectorClass).get)
          val dataSetId = if (dataSink.getDataSetId == null) "-1" else dataSink.getDataSetId.toString
          val dataSetTopic = nameDataSetTopic(Integer.valueOf(dataSetId))
          if (topicMetaService.isExistingTopic(dataSetTopic)) {
            sinkConfig.put(TOPICS, dataSetTopic)
            sinkConfig.put(DATASET_ID, dataSetId)

            val allParams64 = Base64.encodeBase64String(JsonUtils.toJsonString(sinkConfig).getBytes())
            sinkConfig.put(CONFIG_PARAMS_64, allParams64)

            restTemplate.postForObject(CONNECTORS_URL, new HttpEntity[ConnectorConfig](connectorConfig, headers), classOf[util.Map[_, _]])
          } else {
            logger.warn(s"[sink-${sinkId}] Did not start: topic $dataSetTopic does not exist")
          }
        }
    } catch {
      case e: Exception =>
        logger.error("", e)
    }

  }

  def getCurrentSinks(): Try[Set[Int]] =
    Try {
      val responseType = new ParameterizedTypeReference[String]() {}
      val request = new HttpEntity("", headers)
      val body = restTemplate.exchange(CONNECTORS_URL, GET, request, responseType)
        .getBody
      JsonUtils.jsonToList(body)
        .asScala
        .filterNot(_.equals(SINK_AGENT))
        .map(x => x.toString.split("-")(1).toInt)
        .toSet
    }

  private def sinkConnectorName(sinkId: Int) = s"sink-$sinkId"

}