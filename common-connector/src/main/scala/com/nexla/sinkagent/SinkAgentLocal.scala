package com.nexla.sinkagent

import com.nexla.admin.client.AdminApiClientBuilder
import com.nexla.admin.client.config.EnrichedConfig.EnrichSinkParams
import com.nexla.admin.client.config.SinkConfigUtils
import com.nexla.client.{JavaJobSchedulerClient, KafkaClusterPropertiesDefault}
import com.nexla.common.notify.transport.ControlMessageProducer
import com.nexla.common.{AppType, NexlaSslContext, RestTemplateBuilder}
import com.nexla.connect.common.offsets.OffsetsTracker
import com.nexla.connector.config.SinkConnectorConfig
import com.nexla.kafka.KafkaMessageTransport
import com.nexla.listing.client.ListingCoordinationClient
import com.nexla.sc.client.job_scheduler.JavaNodeTaskManagerClient
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.sc.util.{AppUtils, Async, StrictNexlaLogging}
import org.apache.commons.lang3.StringUtils
import org.apache.kafka.clients.CommonClientConfigs
import org.apache.kafka.clients.consumer.{ConsumerConfig, KafkaConsumer}

import java.net.InetAddress
import java.util.Collections.singletonList
import java.util.{Properties, UUID}
import scala.concurrent.ExecutionContext

class SinkAgentLocal(props: SinkConnectorConfig)
  extends AppUtils
    with StrictNexlaLogging {

  implicit val executionContext: ExecutionContext = Async.ioExecutorContext

  implicit val appSslContext: NexlaSslContext = props.nexlaSslConfig

  val restTemplate = new RestTemplateBuilder().withSSL(appSslContext).build()

  val nodeId = System.getenv("NODE_ID")

  if (!props.unitTest && nodeId == null) {
    throw new Error("NodeId is mandatory for sinks")
  }

  val nodeIdUniqueSuffix = nodeId + UUID.randomUUID().toString

  implicit val adminApiClient = new AdminApiClientBuilder()
    .setAppName(s"${AppType.SINK_AGENT.appName}-$nodeIdUniqueSuffix")
    .setDataPlaneUid(props.dataplaneUid)
    .setEnrichmentUrl(props.enrichmentUrl)
    .setNoCache(true)
    .create(props.apiCredentialsServer, props.apiAccessKey, restTemplate)

  val coordinationClient = new ListingCoordinationClient(props.coordinationServer, props.nexlaUsername, props.nexlaPassword, restTemplate)

  val nexlaCredsConf = NexlaCredsConf(props.nexlaUsername, props.nexlaPassword)
  val nodeTaskManagerClient = new JavaNodeTaskManagerClient(restTemplate, props.ctrlNodeTaskManagerUrl, nexlaCredsConf)
  val staticKafkaProperties: KafkaClusterPropertiesDefault = null // passing null because connector does not use methods that request Kafka properties from Control
  val javaJobSchedulerClient = new JavaJobSchedulerClient(props.ctrlNodeTaskManagerUrl, nexlaCredsConf.username, nexlaCredsConf.password, staticKafkaProperties, restTemplate);
  val ctrlClient = new JavaJobSchedulerClient(props.ctrlHttpUrl.orElse(null), nexlaCredsConf.username, nexlaCredsConf.password, staticKafkaProperties, restTemplate)

  val podName = Option(System.getenv("POD_NAME"))
    .orElse(Option(System.getenv("HOSTNAME")))
    .orElse(Option(System.getenv("SERVICE_NAME")))

  val podIp = Option(System.getenv("POD_IP"))
    .getOrElse(InetAddress.getLocalHost.getHostAddress)

  val serviceName = Option(System.getenv("MARATHON_APP_ID"))
    .map(x => StringUtils.removeStart(x, "/"))
    .orElse {
      podName.map(_.split("-").dropRight(2).mkString("-"))
    }

  var sinkParams = new EnrichSinkParams(
    props.credentialsSource,
    props.vaultHost,
    props.vaultToken,
    props.secretManagerRegion,
    props.secretManagerAccessKey,
    props.secretManagerSecretKey,
    props.secretNames
  )

  val sinkClient = new SinkConnectorClient(sinkParams, restTemplate)

  val offsetsCoordinationClient = new OffsetsTracker(coordinationClient)

  val sinkOffsetsByTopic = new SinkOffsetsByTopic(adminApiClient, offsetsCoordinationClient)

  def createConsumer(topic: String, bootstrapServers: String): KafkaConsumer[String, String] = {
    val podNameChecked = podName
      .filter(_.contains("sink")) // Extra check to ensure that application has properly specified pod name
      .map(name => s"--pod-$name--")
      .orElse({
        logger.warn("Pod name is not imported into current deployment as environment variable or does not match nodeID. Using UUID")
        Some(s"--uuid-${UUID.randomUUID().toString}--")
      })
      .get
    val groupId = nodeId + "-" + podNameChecked

    val consumerProps = new Properties
    consumerProps.put(CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers)
    consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, groupId)
    consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest")
    consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer")
    consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer")
    val consumer = new KafkaConsumer[String, String](consumerProps)
    consumer.subscribe(singletonList(topic))
    consumer
  }

}

object SinkAgentLocal {

  var instance: SinkAgentLocal = _

  def isPresent = instance != null

  def apply(props: SinkConnectorConfig): SinkAgentLocal = synchronized {
    if (instance == null) {
      instance = new SinkAgentLocal(props)
    }
    instance
  }

}
