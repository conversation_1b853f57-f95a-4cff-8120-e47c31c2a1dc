package com.nexla.sinkagent

import com.nexla.connector.config.{PipelineTaskType, SinkInstanceConfig}
import com.nexla.control.coordination.NodeTaskManagerAssignment
import com.nexla.kafka.ntm.ConnectorAssignmentConsumer
import com.nexla.kafka.service.TopicMetaService
import com.nexla.sc.config.NexlaCredsConf
import com.nexla.sc.util.{AppUtils, StrictNexlaLogging}
import org.apache.commons.lang3.StringUtils

import java.net.InetAddress
import java.util.concurrent.{Executors, ScheduledFuture, TimeUnit}
import java.util.function.Consumer
import scala.compat.java8.OptionConverters._

class SinkAgentTaskReceiver(appProps: SinkInstanceConfig)
  extends AppUtils
    with StrictNexlaLogging {

  val sinkAgentLocal = SinkAgentLocal(appProps)

  import sinkAgentLocal.executionContext

  val taskType = PipelineTaskType.fromString(appProps.taskType)
  val nexlaCredsConf = NexlaCredsConf(appProps.nexlaUsername, appProps.nexlaPassword)

  val nodeTags = appProps.nodeTags.asScala
    .map(_.split(",").toSeq)
    .getOrElse(Seq())

  val podName = Option(System.getenv("POD_NAME"))
    .orElse(Option(System.getenv("HOSTNAME")))
    .orElse(Option(System.getenv("SERVICE_NAME")))

  val podIp = Option(System.getenv("POD_IP"))
    .getOrElse(InetAddress.getLocalHost.getHostAddress)

  val serviceName = Option(System.getenv("MARATHON_APP_ID"))
    .map(x => StringUtils.removeStart(x, "/"))
    .orElse {
      podName.map(_.split("-").dropRight(2).mkString("-"))
    }

  val topicMetaService = new TopicMetaService(appProps.controlKafkaContext)

  val taskReceiver = new TaskReceiver(sinkAgentLocal.nodeId, podIp, podName, appProps.dedicatedNode, taskType,
    sinkAgentLocal.sinkClient, sinkAgentLocal.nodeTaskManagerClient, nodeTags, serviceName,
    sinkAgentLocal.adminApiClient, topicMetaService, appProps)

  val SCHEDULED_EXECUTOR_SERVICE = Executors.newScheduledThreadPool(0)

  var future: ScheduledFuture[_] = null
  var taskAssignmentConsumer: ConnectorAssignmentConsumer = null

  def start() = {
    logger.info(s"Starting SinkAgent with TaskReceiver(nodeId = ${sinkAgentLocal.nodeId}, podIp = $podIp, podName = $podName, " +
      s"dedicatedNode = ${appProps.dedicatedNode}, taskType = $taskType, nodeTags = $nodeTags, serviceName = $serviceName)")

    taskAssignmentConsumer = startTaskAssignmentConsumer()
    taskAssignmentConsumer.start()

    future = SCHEDULED_EXECUTOR_SERVICE.scheduleAtFixedRate(() => taskReceiver.receiveAndProcessTasks(),
      0, taskReceiver.intervalDurationMs, TimeUnit.MILLISECONDS)
  }

  def stop() = {
    logger.info(s"Stopping SinkAgent")
    taskAssignmentConsumer.stop()
    logger.info(s"Stoped SinkAgent")
    future.cancel(true)
  }

  def startTaskAssignmentConsumer(): ConnectorAssignmentConsumer = {
    val connectorAssignmentConsumer = new Consumer[NodeTaskManagerAssignment] {
      override def accept(t: NodeTaskManagerAssignment): Unit = {
        t match {
          case e: NodeTaskManagerAssignment => if (e.getNodeId == sinkAgentLocal.nodeId) {
            taskReceiver.receiveAndProcessTasks()
          }
          case _ =>
        }
      }
    }
    new ConnectorAssignmentConsumer(appProps.controlKafkaContext, appProps.controlKafkaContext.bootstrapServer, sinkAgentLocal.nodeId, connectorAssignmentConsumer)
  }

}
