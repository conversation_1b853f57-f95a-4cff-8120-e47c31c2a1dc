package com.nexla.connect.common;

import com.nexla.common.NotificationEventType;
import com.nexla.common.ResourceType;
import com.nexla.common.exception.NexlaError;
import com.nexla.common.exception.NexlaErrorMessage;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.common.notify.NexlaNotificationEvent;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.connector.config.FlowType;
import com.nexla.sc.metric.BaseSinkMetric;
import com.nexla.sc.metric.MetadataBuilder;

import java.util.Optional;

import static com.nexla.common.NexlaConstants.ERROR_MESSAGE;
import static com.nexla.common.NexlaConstants.EXCEPTION_TRACE;
import static com.nexla.common.NexlaConstants.RESPONSE_BODY;
import static com.nexla.common.NotificationEventType.ERROR;
import static com.nexla.common.NotificationUtils.createNexlaNotificationEvent;
import static com.nexla.common.NotificationUtils.notifyErrorMessage;


public class NexlaConnectorUtils {

	public static void sendNexlaNotificationEvent(
		ControlMessageProducer controlMessageProducer,
		NotificationEventType eventType,
		Long runId,
		ResourceType resourceType,
		Integer resourceId,
		String name,
		Long records,
		String errorMessage
	) {
		NexlaNotificationEvent notificationEvent = createNexlaNotificationEvent(eventType, runId, resourceType, resourceId, name, records, errorMessage);
		controlMessageProducer.publishNotification(notificationEvent);
	}

	public static void publishException(
		ControlMessageProducer controlMessageProducer,
		Long runId,
		ResourceType resourceType,
		int resourceId,
		long numRecords,
		String resourceVal,
		NexlaError error
	) {
		sendNexlaNotificationEvent(
			controlMessageProducer, ERROR, runId, resourceType, resourceId, resourceVal, numRecords, notifyErrorMessage(error));
	}

	public static void publishSinkMetrics(ControlMessageProducer controlMessageProducer, BaseSinkMetric sinkMetric) {
		NexlaRawMetric metric = NexlaRawMetric.create(sinkMetric.resourceType(), sinkMetric.resourceId(),
			sinkMetric.recordCount(), sinkMetric.fileSize(),
			sinkMetric.errorCount(), sinkMetric.timestamp(), Optional.ofNullable(sinkMetric.runId()),
			Optional.empty(), Optional.empty(), Optional.ofNullable(sinkMetric.datasetId()),
			Optional.empty(), Optional.empty(), Optional.ofNullable(sinkMetric.metadata().build()),
			FlowType.STREAMING, Optional.empty(), Optional.empty(), Optional.empty(),
			sinkMetric.orgId(), sinkMetric.ownerId()
		);

		controlMessageProducer.publishMetrics(metric);
	}

	public static void publishMetrics(
		ControlMessageProducer controlMessageProducer,
		ResourceType resourceType,
		Integer resourceId,
		String fileName,
		long recordCount,
		long fileSizeBytes,
		long errorCount,
		long timestamp,
		Optional<Long> runId,
		Optional<Boolean> eof,
		Optional<String> hash,
		Optional<String> displayPath,
		Optional<Integer> datasetId,
		Optional<Long> lastModified,
		Optional<Long> messageNumber,
		Optional<NexlaErrorMessage> nexlaError,
		Optional<String> publishKey,
		FlowType flowType,
		Integer orgId,
		Integer ownerId
	) {
		MetadataBuilder metadataBuilder = new MetadataBuilder().withName(fileName);
		displayPath.ifPresent(metadataBuilder::withDisplayPath);

		NexlaRawMetric metric = NexlaRawMetric.create(resourceType, resourceId, recordCount, fileSizeBytes,
			errorCount, timestamp, runId, eof, hash, datasetId, lastModified, messageNumber, Optional.of(metadataBuilder.build()), flowType,
				Optional.empty(), Optional.empty(), Optional.empty(), orgId, ownerId);
		nexlaError.ifPresent(error -> {
			metric.getFields().put(EXCEPTION_TRACE, error.getExceptionTrace());
			metric.getFields().put(ERROR_MESSAGE, error.getMessage());
			metric.getFields().put(RESPONSE_BODY, error.getResponseBody());
		});

		controlMessageProducer.publishMetrics(metric);
	}
}
