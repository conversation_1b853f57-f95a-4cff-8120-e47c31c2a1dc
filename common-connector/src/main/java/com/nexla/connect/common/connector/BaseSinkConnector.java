package com.nexla.connect.common.connector;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.config.EnrichedConfig;
import com.nexla.admin.client.config.SinkConfigUtils;
import com.nexla.client.SinkUtils;
import com.nexla.common.RestTemplateBuilder;
import com.nexla.common.VersionComparator;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.notify.transport.NexlaMessageProducer;
import com.nexla.connect.common.connector.lifecycle.SinkShutdownLifecycleManagement$;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetry;
import com.nexla.connector.config.ConnectorAdminConfig;
import com.nexla.connector.config.NexlaConfigKey;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.TelemetryConfig;
import com.nexla.connector.config.vault.NexlaAppConfigProperties;
import com.nexla.sinkagent.SinkAgentLocal;
import com.nexla.telemetry.TelemetryContext$;
import one.util.streamex.EntryStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.connect.sink.SinkConnector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.nexla.admin.config.ConfigUtils.enrichWithCredentialsStore;
import static com.nexla.admin.config.ConfigUtils.enrichWithDataCredentials;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.connector.config.SinkInstanceConfig.DEDICATED_NODE;
import static java.util.Collections.nCopies;
import static java.util.stream.Collectors.joining;

public abstract class BaseSinkConnector extends SinkConnector {
	protected static final String DEDICATED_TASK_ID = "DEDICATED_TASK_ID";

	protected Logger logger;
	protected Integer sinkId;
	protected NexlaAppConfigProperties nexlaAppConfigProperties;
	private static final ScheduledExecutorService SCHEDULED_EXECUTOR_SERVICE = Executors.newScheduledThreadPool(0);
	protected Map<String, String> configProperties;
	protected int maxTasksOnStart = Integer.MAX_VALUE;

	@Override
	public List<Map<String, String>> taskConfigs(int maxTasks) {
		return nCopies(Integer.min(maxTasks, maxTasksOnStart), configProperties);
	}

	@Override
	public String version() {
		return CONNECTOR_VERSION;
	}

	public Map<String, String> customConnectorProperties(int sinkId) {
		return Collections.emptyMap();
	}

	protected abstract String telemetryAppName();

	@Override
	public void start(Map<String, String> props) {
		String dedicatedTaskId = StringUtils.trimToNull(System.getenv(DEDICATED_TASK_ID));
		if (dedicatedTaskId != null && !dedicatedTaskId.equalsIgnoreCase(props.get(SINK_ID))) {
			throw new IllegalStateException(String.format(
					"Prevent sink %s from starting on dedicated node of sink %s", props.get(SINK_ID), dedicatedTaskId));
		}

		String configParams64 = props.get(CONFIG_PARAMS_64);
		if (configParams64 != null) {
			String rawPropertiesJson = new String(org.apache.commons.codec.binary.Base64.decodeBase64(configParams64));
			props.putAll((Map) JsonUtils.jsonToMap(rawPropertiesJson));
		}

		this.nexlaAppConfigProperties = new NexlaAppConfigProperties(props);
		this.configProperties = enrichWithCredentialsStore(props, config());

		boolean isDedicatedNode = Optional.ofNullable(this.configProperties.get(DEDICATED_NODE)).map(Boolean::parseBoolean)
			.orElse(false);
		TelemetryConfig telemetryConfig = new TelemetryConfig(configProperties);
		ConnectorTelemetry.init(nexlaAppConfigProperties.getEnvironment().getName(), telemetryAppName(), telemetryConfig,
			isDedicatedNode);

		ConnectorAdminConfig adminConfig = new ConnectorAdminConfig(configProperties);

		RestTemplate restTemplate = new RestTemplateBuilder().withSSL(adminConfig.nexlaSslConfig).build();

		AdminApiClient adminApiClient = new AdminApiClientBuilder()
				.setAppName("sink-" + props.get(SINK_ID))
				.setDataPlaneUid(adminConfig.dataplaneUid)
				.setEnrichmentUrl(adminConfig.enrichmentUrl)
				.setTelemetry(Optional.of(TelemetryContext$.MODULE$.get()))
				.setNoCache(true)
				.create(adminConfig.apiCredentialsServer, adminConfig.apiAccessKey, restTemplate);

		enrichWithDataCredentials(adminApiClient, configProperties);

		Integer sinkId = Integer.parseInt(props.get(SINK_ID));
		var dataSink = adminApiClient.getDataSink(sinkId).get();
		var isStateful = SinkUtils.isStateful(dataSink);
		configProperties.putAll(customConnectorProperties(sinkId));
		configProperties.putAll(enrichWithDataSinkParams(adminApiClient, dataSink));

		SinkConnectorConfig sinkConnectorConfig = new SinkConnectorConfig(configProperties);
		SinkAgentLocal sinkAgentLocal = SinkAgentLocal.apply(sinkConnectorConfig);

		this.sinkId = sinkConnectorConfig.getInt(SINK_ID);
		this.logger = new NexlaLogger(LoggerFactory.getLogger(this.getClass()), new NexlaLogKey(SINK, this.sinkId, Optional.empty()));

		boolean nodeIdCheck = VersionComparator.isVersionGE(sinkAgentLocal.javaJobSchedulerClient().getVersion(), "2.12.0")
			&& !sinkConnectorConfig.skipCtrlNodeCheck;

		if (nodeIdCheck) {
			boolean unknownNodeId = sinkAgentLocal.nodeTaskManagerClient().nodeTasksExists(sinkAgentLocal.nodeId()).isEmpty();
			if (unknownNodeId) {
				logger.info("NodeId {} is not registered or Ctrl is down, pausing Sink {} and triggering restart after delay",
					sinkAgentLocal.nodeId(), this.sinkId);
				this.maxTasksOnStart = 0;
				SCHEDULED_EXECUTOR_SERVICE.schedule(() -> sinkAgentLocal.sinkClient().restartSink("0", this.sinkId), 1, TimeUnit.MINUTES);
			}
		}

		if (isStateful)
			SinkShutdownLifecycleManagement$.MODULE$.registerNode(sinkAgentLocal.nodeId());

		String propertiesLog = EntryStream.of(config().configKeys())
			.filterKeys(props::containsKey)
			.mapKeyValue((name, nexlaKeyObj) -> {
				NexlaConfigKey key = (NexlaConfigKey) nexlaKeyObj;
				if (key.maskValue) {
					return name + " = XXXXXXXX";
				} else {
					return name + " = " + props.get(name);
				}
			})
			.collect(joining("\n"));

		logger.info("Starting connector");
		if (sinkConnectorConfig.logVerbose) {
			logger.info("Connector properties:\n" + propertiesLog + "\n\n");
		}

	}

	private Map<String, String> enrichWithDataSinkParams(AdminApiClient adminApiClient, DataSink dataSink) {
		// no need to fill these params at this point, as this method will be called later by BaseSinkTask, we just need sink config
		var enrichSinkParams = new EnrichedConfig.EnrichSinkParams(null,
				Optional.empty(),
				Optional.empty(),
				Optional.empty(),
				Optional.empty(),
				Optional.empty(),
				Optional.empty());
		return SinkConfigUtils.enrichSinkWithCtrl(dataSink, enrichSinkParams);
	}

	@Override
	public void stop() {
	}
}
