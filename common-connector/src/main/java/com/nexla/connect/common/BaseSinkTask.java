package com.nexla.connect.common;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.Org;
import com.nexla.admin.client.config.EnrichedConfig;
import com.nexla.client.JavaJobSchedulerClient;
import com.nexla.client.SinkUtils;
import com.nexla.common.*;
import com.nexla.common.datetime.NexlaBackoff;
import com.nexla.common.exception.*;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.MetricWithErrors;
import com.nexla.common.notify.monitoring.*;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.common.notify.transport.DataMessageProducer;
import com.nexla.common.notify.transport.NexlaMessageTransport;
import com.nexla.common.tracker.SinkItem;
import com.nexla.connect.common.connector.SinkAgentConnector;
import com.nexla.connect.common.connector.lifecycle.SinkLifecycleState;
import com.nexla.connect.common.connector.lifecycle.SinkShutdownLifecycleManagement$;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connect.common.flush.DistributedFlushControl;
import com.nexla.connect.common.offsets.OffsetsTracker;
import com.nexla.connect.common.postponedFlush.KafkaProgressTracker;
import com.nexla.connect.common.report.PipelineStatusReport;
import com.nexla.connect.common.report.PipelineStatusReporter;
import com.nexla.connector.ExtractedMessage;
import com.nexla.connector.MessageMapper;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.BaseConnectorConfig;
import com.nexla.connector.config.ConnectorAdminConfig;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.ssh.tunnel.SshTunnel;
import com.nexla.connector.config.ssh.tunnel.SshTunnelSupport;
import com.nexla.connector.config.vault.CredentialsStore;
import com.nexla.control.coordination.FlushedCoordination;
import com.nexla.control.coordination.HeartbeatConnectorCoordination;
import com.nexla.control.coordination.HeartbeatConnectorStateCoordination;
import com.nexla.control.coordination.HeartbeatType;
import com.nexla.control.message.ControlEventType;
import com.nexla.control.message.SinkControlMessage;
import com.nexla.kafka.KafkaMessageTransport;
import com.nexla.kafka.service.KafkaLagCalculator;
import com.nexla.kafka.service.KafkaLagCalculatorPooled;
import com.nexla.kafka.service.TopicMetaService;
import com.nexla.listing.client.ListingClient;
import com.nexla.listing.client.ListingCoordinationClient;
import com.nexla.sc.metric.BaseSinkMetric;
import com.nexla.sc.metric.MetadataBuilder;
import com.nexla.sinkagent.SinkAgentLocal;
import com.nexla.sinkagent.SinkConnectorClient;
import com.nexla.telemetry.TelemetryContext$;
import lombok.Getter;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.errors.RetriableException;
import org.apache.kafka.connect.runtime.GetTaskProperties;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTask;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.jetbrains.annotations.NotNull;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.nexla.admin.client.config.SinkConfigUtils.enrichSinkWithCtrl;
import static com.nexla.admin.config.ConfigUtils.enrichWithCredentialsStore;
import static com.nexla.admin.config.ConfigUtils.enrichWithDataCredentials;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.NexlaNamingUtils.sinkConnectorServiceName;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.StreamUtils.jsonUtil;
import static com.nexla.common.TraceMessage.NX_RUN_TRACE;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.exception.NexlaError.getErrorDetails;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.BaseConnectorConfig.FAST_MODE;
import static com.nexla.connector.config.BaseConnectorConfig.VAULT_HOST;
import static com.nexla.connector.config.BaseConnectorConfig.VAULT_TOKEN;
import static com.nexla.connector.config.SinkInstanceConfig.DEDICATED_NODE;
import static com.nexla.connector.config.vault.VaultUtils.createNexlaCredentialsStore;
import static java.lang.Boolean.TRUE;
import static java.util.Optional.*;
import static java.util.UUID.randomUUID;
import static java.util.concurrent.TimeUnit.MINUTES;
import static java.util.concurrent.TimeUnit.SECONDS;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static com.nexla.connect.common.DetailedFlowInsightsSender.RequestResponseDetailedMessages;

public abstract class BaseSinkTask<C extends SinkConnectorConfig> extends SinkTask
		implements SshTunnelSupport {
	public static final String CRITICAL_FAILURE = "CRITICAL_FAILURE";
	public static final String MESSAGE = "MESSAGE";
	public static final Path FLUSH_FLAGS_DIR = Paths.get("/tmp", "flushflags");
	private static final Path FLUSH_FLAGS_DECOMMISSION_FILE = FLUSH_FLAGS_DIR.resolve("decomission");
	protected static final ScheduledExecutorService SCHEDULED_POOL =
			Executors.newScheduledThreadPool(1, new ThreadFactoryBuilder().setNameFormat("sink-scheduled-pool-%d").setDaemon(true).build());
	private static final PipelineStatusReporter PIPELINE_STATUS_REPORTER = new PipelineStatusReporter(SCHEDULED_POOL);

	private static final AtomicBoolean nodeShutdownInProgress = new AtomicBoolean(false);

	static {
		Runtime.getRuntime().addShutdownHook(new Thread(() -> nodeShutdownInProgress.set(true)));
	}

	public static final String ADMIN_API_PREFIX = "sink";

	protected final AtomicLong byteCounter = new AtomicLong();

	protected NexlaLogger logger = new NexlaLogger(LoggerFactory.getLogger(this.getClass()));
	protected Long runId;
	protected ControlMessageProducer controlMessageProducer;
	protected DataMessageProducer dataMessageProducer;
	protected TopicMetaService topicMetaService;

	@VisibleForTesting
	public C config;

	protected MessageMapper messageMapper;

	private NexlaBackoff backoff;

	private long totalRecords;

	protected AdminApiClient adminApiClient;

	protected RestTemplate restTemplate;
	protected CredentialsStore nexlaCredentialsStore;

	protected Optional<JavaJobSchedulerClient> ctrlClient;
	protected LoadingCache<Resource, Boolean> quarantineTopicExistsCache;

	protected final LoadingCache<HeartbeatPacerKey, Object> heartbeatSender = CacheBuilder
			.newBuilder()
			.expireAfterWrite(1, MINUTES)
			.build(new CacheLoader<>() {

				@Override
				@SneakyThrows
				public Object load(HeartbeatPacerKey key) {
					ctrlClient
							.filter(x -> !config.fastMode && !config.unitTest)
							.ifPresent(jpc -> {
								logger.info("Heartbeating run_id = {} trace = {}", key.getRunId(), key.isTrace());
								HeartbeatType heartbeatType = key.isTrace() ? HeartbeatType.TRACE : HeartbeatType.DATA;
								HeartbeatConnectorCoordination heartbeat = new HeartbeatConnectorCoordination(
										UUID.randomUUID().toString(), SINK, config.sinkId, key.getRunId(), heartbeatType, System.currentTimeMillis());

								controlMessageProducer.sendHeartbeat(heartbeat);
								heartbeatPipelineTask(!key.isTrace());
							});
					return new Object();
				}
			});
	protected Optional<SinkAgentLocal> sinkAgentLocal;
	protected DataSink dataSink;

	@Getter
	private KafkaLagCalculator kafkaLagCalculator;

	protected Optional<PipelineStatusReport> pipelineStatusReport;
	protected Optional<Integer> taskId = empty();
	protected PostponedFlush postponedFlush;
	private EnrichedConfig.EnrichSinkParams enrichSinkParams;
	protected boolean isDedicatedNode;
	@Getter
	private long lastRecordsPutTs = System.currentTimeMillis();

	private final AtomicInteger successDetailedFlowInsightsSentCount = new AtomicInteger(0);
	private final AtomicInteger errorDetailedFlowInsightsSentCount = new AtomicInteger(0);
	private final AtomicLong lastSuccessDetailedFlowInsightsResetTime = new AtomicLong(0);
	private final AtomicLong lastErrorDetailedFlowInsightsResetTime = new AtomicLong(0);
	public static final long DETAILED_FLOW_INSIGHTS_WINDOW_MS = 60000L;


	@Override
	public String version() {
		return CONNECTOR_VERSION;
	}

	public abstract void doStart();

	protected abstract ConfigDef configDef();

	// to be used from fast-connector
	public void setRunId(long runId) {
		this.runId = runId;
	}

	public void setLogger(NexlaLogger logger) {
		this.logger = logger;
	}

	protected abstract void doPut(StreamEx<NexlaMessageContext> messages, int streamSize);

	protected Optional<EncryptionUtils> encryptionUtils;

	private Optional<SshTunnel> tunnel = empty();

	protected ListingClient listingClient;
	protected ListingCoordinationClient coordinationClient;

	@VisibleForTesting
	public Optional<OffsetsTracker> offsetsSender = empty();

	// Flag to indicate if sink is stateful (if sink buffers data in intermediate storage on not-final flush operations)
	protected boolean isStateful;

	protected Optional<ConnectorTelemetryReporter> sinkTelemetryReporter = Optional.empty();

	@Override
	public void start(Map<String, String> rawProperties) {
		start(rawProperties, ADMIN_API_PREFIX);
	}

	@Override
	public void initialize(SinkTaskContext context) {
		super.initialize(context);
		try {
			this.taskId = Optional.ofNullable(new GetTaskProperties().getTaskId(context));
		} catch (Exception e) {
			LoggerFactory.getLogger(this.getClass()).error("", e);
		}
	}

	public void start(Map<String, String> taskProps, String adminApiPrefix) {
		logger.info("BaseSinkTask::start");
		int sinkId = Integer.parseInt(taskProps.get(SINK_ID));
		Optional.ofNullable(SinkAgentConnector.SINK_AGENT_LINK.get())
				.ifPresent(x -> x.taskReceiver().notifyTaskStarted(sinkId));

		ConnectorAdminConfig adminConfig = new ConnectorAdminConfig(taskProps);

		this.controlMessageProducer = createControlMessageProducer(adminConfig.controlKafkaConfig);
		this.dataMessageProducer = createDataMessageProducer(adminConfig.dataKafkaConfig);

		this.topicMetaService = new TopicMetaService(adminConfig.dataKafkaConfig);
		this.quarantineTopicExistsCache = CacheBuilder.newBuilder()
				.build(CacheLoader.from(topicMetaService::createDefaultQuarantineTopicIfNotExist));

		this.restTemplate = new RestTemplateBuilder().withSSL(adminConfig.nexlaSslConfig).build();

		this.adminApiClient = new AdminApiClientBuilder()
				.setAppName(adminApiPrefix + "-" + sinkId)
				.setDataPlaneUid(adminConfig.dataplaneUid)
				.setEnrichmentUrl(adminConfig.enrichmentUrl)
				.setNoCache(true)
				.setTelemetry(Optional.of(TelemetryContext$.MODULE$.get()))
				.create(adminConfig.apiCredentialsServer, adminConfig.apiAccessKey, restTemplate);

		this.dataSink = adminApiClient.getDataSink(sinkId).get();

		EnrichedConfig.EnrichSinkParams enrichParams = enrichSinkParams(taskProps);
		Map<String, String> props = enrichSinkWithCtrl(this.dataSink, enrichParams);

		enrichWithDataCredentials(adminApiClient, props);

		if (configDef() == null) {
			LoggerFactory.getLogger(this.getClass()).warn("Config definition is null, props won't be enriched using credential store.");
		} else {
			enrichWithCredentialsStore(props, configDef());
		}

		this.nexlaCredentialsStore = createNexlaCredentialsStore(props);

		if ("true".equalsIgnoreCase(taskProps.get(FAST_MODE))) {
			props.put(FAST_MODE, "true");
		}
		if ("true".equalsIgnoreCase(taskProps.get(UNIT_TEST))) {
			props.putAll(taskProps);
		}

		this.isDedicatedNode = Optional.ofNullable(taskProps.get(DEDICATED_NODE)).map(Boolean::parseBoolean).orElse(false);

		this.config = parseConfig(props);
		this.sinkAgentLocal = config.fastMode || config.unitTest ? Optional.empty() : Optional.of(SinkAgentLocal.apply(config));

		this.listingClient = createListingClient();
		this.coordinationClient = new ListingCoordinationClient(config.coordinationServer, config.nexlaUsername, config.nexlaPassword, restTemplate);

		this.offsetsSender = sinkAgentLocal.map(SinkAgentLocal::offsetsCoordinationClient);

		this.restTemplate = new RestTemplateBuilder().withSSL(config.nexlaSslConfig).build();

		this.encryptionUtils = of(config.trackerEncryptionEnabled)
				.filter(TRUE::equals)
				.map(i -> new EncryptionUtils(config.trackerEncryptionKey));

		this.messageMapper = createMessageMapper();
		this.logger = new NexlaLogger(LoggerFactory.getLogger(this.getClass()), new NexlaLogKey(SINK, config.sinkId, taskId));

		this.tunnel = createTunnel(logger);
		this.backoff = new NexlaBackoff(logger, MINUTES.toMillis(15));
		this.ctrlClient = createCtrlClient();

		this.isStateful = SinkUtils.isStateful(dataSink);
		this.kafkaLagCalculator = new KafkaLagCalculatorPooled(config.dataKafkaContext);
		this.pipelineStatusReport = !config.unitTest && isStateful ? Optional.of(PIPELINE_STATUS_REPORTER.register(config.sinkId)) : Optional.empty();
		reportLifecycleState(SinkLifecycleState.STARTED);
		this.postponedFlush = postponedFlush();
		heartbeatConnectorState();
		try {
			publishMonitoringLog("Task initialization is done", NexlaMonitoringLogType.EVENT, NexlaMonitoringLogSeverity.INFO);
			doStart();
			heartbeatPipelineTask(false);
			publishMonitoringLog("Start processing data", NexlaMonitoringLogType.EVENT, NexlaMonitoringLogSeverity.INFO);
		} catch (Throwable e) {
			logger.error("Error to start the sink connector with ID={}", config.sinkId, e);
			ConnectException exception = new ConnectException("Error to start the Sink connector with ID=" + config.sinkId, e);
			NexlaConnectorUtils.publishException(controlMessageProducer, runId, SINK, config.sinkId, 0L, e.getMessage(), getErrorDetails(e, empty()));
			publishMonitoringLog("Error to start sink connector. Error: " + e.getMessage(), NexlaMonitoringLogType.EVENT, NexlaMonitoringLogSeverity.ERROR);
			reportTelemetryStartError();
			throw exception;
		}
		logger.info("BaseSinkTask::start finished");
	}

	@NotNull
	protected DataMessageProducer createDataMessageProducer(NexlaKafkaConfig dataKafkaConfig) {
		return new DataMessageProducer(new KafkaMessageTransport(dataKafkaConfig));
	}

	@NotNull
	protected ControlMessageProducer createControlMessageProducer(NexlaKafkaConfig controlKafkaConfig) {
		return new ControlMessageProducer(new KafkaMessageTransport(controlKafkaConfig));
	}

	protected PostponedFlush postponedFlush() {
		KafkaProgressTracker kafkaProgressTracker = new KafkaProgressTracker(getKafkaLagCalculator(), adminApiClient, ctrlClient, logger);
		return new PostponedFlush(this, kafkaProgressTracker, logger) {
			@Override
			protected boolean dataMatureMs(long delayMs) {
				return false;
			}

			@Override
			protected boolean noDataMatureMs(long delayMs) {
				return !hasAccumulatedRecords() && System.currentTimeMillis() - lastRecordsPutTs > delayMs;
			}

			@Override
			protected boolean hasAccumulatedRecords() {
				return false;
			}
		};
	}

	private static EnrichedConfig.EnrichSinkParams enrichSinkParams(Map<String, String> taskProps) {
		String credentialsSource = taskProps.get(CREDENTIALS_SOURCE);

		Optional<String> vaultHost = Optional.ofNullable(taskProps.get(VAULT_HOST));
		Optional<String> vaultToken = Optional.ofNullable(taskProps.get(VAULT_TOKEN));

		Optional<String> secretManagerRegion = Optional.ofNullable(taskProps.get(AWS_SECRET_MANAGER_REGION));
		Optional<String> secretManagerAccessKey = Optional.ofNullable(taskProps.get(AWS_SECRET_MANAGER_ACCESS_KEY));
		Optional<String> secretManagerSecretKey = Optional.ofNullable(taskProps.get(AWS_SECRET_MANAGER_SECRET_KEY));
		Optional<String> secretNames = Optional.ofNullable(taskProps.get(SECRET_NAMES));

		return new EnrichedConfig.EnrichSinkParams(
				credentialsSource, vaultHost, vaultToken, secretManagerRegion,
				secretManagerAccessKey, secretManagerSecretKey, secretNames);
	}

	protected Optional<JavaJobSchedulerClient> createCtrlClient() {
		return config
				.ctrlHttpUrl
				.flatMap(ignored -> sinkAgentLocal.map(SinkAgentLocal::ctrlClient));
	}

	@Override
	public void open(Collection<TopicPartition> partitions) {
		logger.info("Open: {}", partitions);
		try {
			doRecoverAssignment(partitions);
		} catch (Exception e) {
			reportTelemetryFromError(e);
			throw e;
		}
	}

	protected ListingClient createListingClient() {
		return new ListingClient(config.listingAppServer, config.nexlaUsername, config.nexlaPassword, restTemplate);
	}

	public CredentialsStore credentialsStore() {
		return this.nexlaCredentialsStore;
	}

	protected void doRecoverAssignment(Collection<TopicPartition> tps) throws ConnectException {

		if (tps.isEmpty()) {
			return;
		}

		Optional<Map<com.nexla.common.sink.TopicPartition, Long>> sinkOffsets = offsetsSender.flatMap(x -> x.getSinkOffsets(config.sinkId));

		tps.forEach(tp -> {
			// See if this is a new assignment
			com.nexla.common.sink.TopicPartition partition = new com.nexla.common.sink.TopicPartition(tp.topic(), tp.partition());

			context.pause(tp);
			// Recover last committed offset from storage
			long nextOffset = sinkOffsets.map(offsets -> offsets.get(partition)).orElse(0L);

			logger.info("Recovering partition {} from offset {}", tp, nextOffset);

			context.offset(tp, nextOffset);
			context.resume(tp);
		});
	}

	protected MessageMapper createMessageMapper() {
		return new MessageMapper(config.mappingConfig, config, false, encryptionUtils);
	}

	@SneakyThrows
	@Override
	public final void put(Collection<SinkRecord> records) {
		if (isEmpty(records)) {
			return;
		}

		List<NexlaMessageContext> messages = StreamEx
				.of(records)
				.flatMap(record -> {
					Pair<TraceMessage, NexlaMessage> parsedMessage = parseMessage(record);
					boolean isTrace = Objects.nonNull(parsedMessage.getLeft());
					com.nexla.common.sink.TopicPartition topicPartition = new com.nexla.common.sink.TopicPartition(record.topic(), record.kafkaPartition());
					offsetsSender.ifPresent(offsetsSender -> offsetsSender.trackMessageOffset(config.sinkId, topicPartition, record.kafkaOffset(), isTrace));
					if (isTrace) {
						TraceMessage traceMessage = jsonUtil().stringToType(record.value().toString(), TraceMessage.class);
						if (!Optional.ofNullable(traceMessage.getLastBatch()).orElse(false)) {
							updateRunId(traceMessage.getRunId(), true);
						}
						return StreamEx.empty();
					} else {
						NexlaMessage originalMessage = parsedMessage.getRight();
						ExtractedMessage extracted = messageMapper.extractMessage(originalMessage);
						return StreamEx.of(
								new NexlaMessageContext(extracted.getOriginal(), extracted.getMapped(), topicPartition, record.kafkaOffset())
						);
					}
				}).toList();

		if (!messages.isEmpty()) {
			// double check that we have valid records to deliver, batch may have only contained trace messages
			doPut(messages.size(), StreamEx.of(messages));
		}
		this.lastRecordsPutTs = System.currentTimeMillis();
	}

	private Pair<TraceMessage, NexlaMessage> parseMessage(SinkRecord record) {
		String stringRecord = record.value().toString();

		NexlaMessage originalMessage = jsonUtil().stringToType(stringRecord, NexlaMessage.class);

		if (Objects.isNull(originalMessage.getRawMessage()) && stringRecord.contains(NX_RUN_TRACE)) {
			TraceMessage traceMessage = jsonUtil().stringToType(record.value().toString(), TraceMessage.class);
			return Pair.of(traceMessage, null);
		}
		return Pair.of(null, originalMessage);
	}

	public final void putNexlaMessages(List<NexlaMessage> records, com.nexla.common.sink.TopicPartition tp) {
		if (isEmpty(records)) {
			return;
		}

		StreamEx<NexlaMessageContext> nexlaMessageContexts = StreamEx
				.of(records)
				.map(originalMessage -> {
					ExtractedMessage extracted = messageMapper.extractMessage(originalMessage);
					return new NexlaMessageContext(extracted.getOriginal(), extracted.getMapped(), tp,
							extracted.getMapped().getNexlaMetaData().getSourceOffset());
				});

		doPut(records.size(), nexlaMessageContexts);
	}

	@SneakyThrows
	private void doPut(int size, StreamEx<NexlaMessageContext> nexlaMessageContexts) {
		StreamEx<NexlaMessageContext> messages = nexlaMessageContexts
				.map(this::updateMessageRunId).map(this::updateMessageTracker);

		if (!config.fastMode) {
			long sleepTimeFromNow = backoff.computeSleepTimeFromNow();
			if (sleepTimeFromNow > 0) {
				Thread.sleep(sleepTimeFromNow);
			}
		}

		try {
			StopWatch stopWatch = new StopWatch();
			stopWatch.start();

			// reset bytes counter to 0 for each iteration
			byteCounter.set(0);

			AtomicLong minIngestTime = new AtomicLong(Long.MAX_VALUE);
			StreamEx<NexlaMessageContext> messages2 = messages.peek(m -> {
				NexlaMessage mapped = m.getMapped();
				if (mapped != null) {
					NexlaMetaData md = mapped.getNexlaMetaData();
					if (md != null) {
						Long ingestTimeFromMsg = md.getIngestTime();
						if (ingestTimeFromMsg != null) {
							minIngestTime.set(Math.min(ingestTimeFromMsg, minIngestTime.get()));
						} else {
							logger.warn("Ingest time from metadata of a mapped msg was null, leaving default ingestion time");
						}
					} else {
						logger.warn("Metadata of mapped msg was null, leaving default ingestion time");
					}
				} else {
					logger.warn("Mapped msg was null, leaving default ingestion time");
				}
			});

			doPut(messages2, size);

			backoff.decreaseDelay();
			logTaskBackoffTimeout();
			backoff.markCurrentTime();

			logger.info("RECORDS={} PUT TIME={} RUN_ID={} MIN INGESTION TIME={}", size, stopWatch.toString(), runId, minIngestTime);
			if (size > 0) {
				totalRecords += size;
			}
		} catch (RetriableException e) {
			logger.error("Connection failed for ID={}, exception: {}", config.sinkId, e);
			NexlaConnectorUtils.publishException(controlMessageProducer, runId, SINK, config.sinkId, 0L, e.getMessage(), getErrorDetails(e, empty()));
			publishMonitoringLog("Connection failed. Error: " + e.getMessage(), NexlaMonitoringLogType.EVENT, NexlaMonitoringLogSeverity.ERROR);
			backoff.increaseDelay();
			logTaskBackoffTimeout();
			backoff.markCurrentTime();
			reportTelemetryRetriableError();
			throw e;
		} catch (Throwable e) {
			logger.error("Error to put records in the Sink connector with ID={}, exception: {}", config.sinkId, e);
			ConnectException exception = new ConnectException("Error to put records in the Sink connector with ID=" + config.sinkId, e);
			NexlaConnectorUtils.publishException(controlMessageProducer, runId, SINK, config.sinkId, 0L, e.getMessage(), getErrorDetails(e, empty()));
			publishMonitoringLog("Error to put records in the sink connector. Error: " + e.getMessage(), NexlaMonitoringLogType.EVENT, NexlaMonitoringLogSeverity.ERROR);
			reportTelemetryWriteError();
			throw exception;
		}
	}

	private NexlaMessageContext updateMessageTracker(NexlaMessageContext nexlaMessageContext) {
		if (nexlaMessageContext.original.getNexlaMetaData() == null
			|| nexlaMessageContext.original.getNexlaMetaData().getTrackerId() == null) {
			return nexlaMessageContext;
		}
		NexlaMetaData nexlaMetaData = nexlaMessageContext.original.getNexlaMetaData();
		nexlaMetaData.getTrackerId().setSink(new SinkItem(config.sinkId, config.version,
			nexlaMetaData.getSourceOffset()));
		return nexlaMessageContext;
	}

	private NexlaMessageContext updateMessageRunId(NexlaMessageContext x) {
		if (x.mapped != null && x.mapped.getNexlaMetaData() != null) {
			updateRunId(x.mapped.getNexlaMetaData().getRunId(), false);
		}
		return x;
	}

	private void updateRunId(Long newRunId, boolean trace) {
		heartbeat(newRunId, trace);
		Long oldRunId = this.runId;
		if (!trace && newRunId != null && (oldRunId == null || newRunId > oldRunId)) {
			this.runId = newRunId;
			onRunIdChanged(oldRunId, newRunId);
		}
	}

	@SneakyThrows
	protected void heartbeat(Long runId, Boolean trace) {
		if (runId != null) {
			heartbeatSender.get(new HeartbeatPacerKey(runId, config.sinkId, trace));
		}
	}

	protected void onRunIdChanged(Long prevRunId, Long currRunId) {

	}

	private void logTaskBackoffTimeout() {
		long backoffIntervalMs = backoff.getCurrDelayMs();

		if (backoffIntervalMs > 0) {
			logger.info("Set backoff={} ms, next time to call put()={}",
					backoffIntervalMs, nowUTC().plusMillis((int) backoffIntervalMs));
		}
	}

	private static boolean isDecommissioning() {
		return Files.exists(FLUSH_FLAGS_DECOMMISSION_FILE);
	}

	public ReadyToFlush readyToFlush() {
		return postponedFlush.readyToFlush();
	}

	@Override
	public void flush(Map<TopicPartition, OffsetAndMetadata> currentOffsets) {
		refreshPipelineStatus();

		if (isStateful && SinkShutdownLifecycleManagement$.MODULE$.isShutdownRequested()) {
			logger.info("Prevent sink flush: Shutdown requested by signal. Node is/will be marked for decommission");
			return;
		}

		if (isStateful && isDecommissioning()) {
			logger.info("Prevent sink flush: node is marked for decommission ({})", FLUSH_FLAGS_DECOMMISSION_FILE);
			return;
		}

		try {
			reportLifecycleState(SinkLifecycleState.FLUSHING);
			ReadyToFlush readyToFlush = postponedFlush.readyToFlush();
			logger.info("BaseSinkTask flush readiness check returned: {}", readyToFlush);
			if (!readyToFlush.pipelineCompleted) {
				heartbeatConnectorState();
			}

			boolean flushed;
			try (DistributedFlushControl distributedFlush = new DistributedFlushControl(coordinationClient, config, SCHEDULED_POOL);
				 ScheduledRunIdHeartbeat runIdHeartbeat = new ScheduledRunIdHeartbeat()) {
				flushed = doFlush(readyToFlush);
				distributedFlush.sync();
			}
			offsetsSender.ifPresent(offsetsSender -> offsetsSender.flushTraceOffsets(config.sinkId, toNexlaTopicPartitions(currentOffsets.keySet())));
			if (flushed) {
				logger.info("flushed, calling onPipelineFlushed");
				onPipelineFlushed();
				stopSinkConnectorIfDedicated();
			}
		} catch (Throwable e) {
			logger.error("Error to flush all records in the Sink connector with ID={}, exception: {}", config.sinkId, e);
			if (e instanceof NexlaException && ((NexlaException) e).getData(CRITICAL_FAILURE).map(x -> (Boolean) x).orElse(false)) {
				issuePauseEvent(true, true, e.getMessage());
			}
			ConnectException exception = new ConnectException("Error to flush all records in the Sink connector with ID=" + config.sinkId, e);
			NexlaConnectorUtils.publishException(controlMessageProducer, runId, SINK, config.sinkId, 0L, e.getMessage(), getErrorDetails(e, empty()));
			publishMonitoringLog("Error flushing records. Error: " + e.getMessage(), NexlaMonitoringLogType.EVENT, NexlaMonitoringLogSeverity.ERROR);
			reportTelemetryFromError(e);
			throw exception;
		} finally {
			reportLifecycleState(SinkLifecycleState.FLUSH_COMPLETED);
			pipelineStatusReport.ifPresent(PipelineStatusReport::reportPipelineFlushStopped);
		}
	}

	protected void onPipelineFlushed() {
		getAndResetFlushedRunIds().ifPresent(this::publishFlushedCoordinationEvent);
		refreshPipelineStatus();
	}

	/**
	 * Should return true for stateful sinks, which process data in batched manner after all batched records are delivered to the destination
	 */
	public boolean doFlush(ReadyToFlush readyToFlush) {
		return false;
	}

	/**
	 * Should return Set of active (not flushed) run IDs
	 * For stateless sinks - returns the value of BaseSinkTask::runId field - runID of the last processed message
	 * For stateful sinks - method should return all runIDs from accumulated data (override the method)
	 */
	protected Set<Long> getActiveRunIds() {
		return runId == null ? Collections.emptySet() : Set.of(runId);
	}

	/**
	 * Should return set of fully flushed runIDs for stateful sinks in case if
	 * messages for that run ID were batched during sink execution, and then were flushed all at once.
	 * (For example, non-empty set should be returned by file sink, bigQuery sink in Upsert mode, etc.)
	 * Stateless sinks should return an empty optional
	 */
	protected Optional<Set<Long>> getAndResetFlushedRunIds(){
		return empty();
	}

	protected void refreshPipelineStatus() {
		try {
			// Disabled Kafka lag calculation because of pressure on Kafka. Be careful when enabling it
			if (Files.exists(BaseSinkTask.FLUSH_FLAGS_DIR.resolve("refresh_kafka_lag_status"))) {
				pipelineStatusReport.ifPresent(status -> status.kafkaLag.set(EntryStream
						.of(kafkaLagCalculator.getPipelineLag(config.sinkId, adminApiClient.getDataSetsBySink(config.sinkId)))
						.values().mapToLong(x -> x).sum()));
			}
		} catch (Exception e) {
			logger.error("Failed to refresh pipeline status", e);
		}
	}

	private void publishFlushedCoordinationEvent(Set<Long> heartbeatRunIds) {
		FlushedCoordination flush = new FlushedCoordination(UUID.randomUUID().toString(), SINK, config.sinkId, heartbeatRunIds, System.currentTimeMillis());
		controlMessageProducer.sendCoordinationEvent(flush);
	}

	@Override
	public void onPartitionsRevoked(Collection<TopicPartition> partitions) {
		offsetsSender.ifPresent(OffsetsCoordinationClient::flush);
	}

	// this will execute only on node which runs connector
	// so we are heartbeating only from 1 node, which is fine
	private void heartbeatPipelineTask(boolean data) {
		Optional.ofNullable(SinkAgentConnector.SINK_AGENT_LINK.get())
				.ifPresent(x -> x.taskReceiver().notifyPipeline(config.sinkId, data));
	}

	protected abstract C parseConfig(Map<String, String> props);

	public BaseConnectorConfig getConfig() {
		return config;
	}

	public Optional<PipelineStatusReport> getPipelineStatusReport() {
		return this.pipelineStatusReport;
	}

	@Override
	public void stop() {
		logger.info("BaseSinkTask::stop");
		offsetsSender.ifPresent(OffsetsCoordinationClient::flush);
		publishMonitoringLog("Stopping task...", NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.INFO);
		if (totalRecords > 0) {
			publishMonitoringLog("Total of " + totalRecords + " records processed", NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.INFO);
		}
		tunnel.ifPresent(SshTunnel::close);
		reportLifecycleState(SinkLifecycleState.STOPPED);
		PIPELINE_STATUS_REPORTER.deregister(config.sinkId);
		Optional.ofNullable(SinkAgentConnector.SINK_AGENT_LINK.get())
				.ifPresent(x -> x.taskReceiver().notifyTaskStopped(config.sinkId));
		issuePauseEventOnStop();
		controlMessageProducer.close();
		logger.info("BaseSinkTask::stop finished");
	}

	protected void stopSinkConnectorIfDedicated() {
		try {
			if (nodeShutdownInProgress.get()) {
				logger.info("Skip stopSinkConnectorIfDedicated: node is shutting down");
				return;
			}

			if (!config.fastMode && isDedicatedNode && config.tasksMax == 1) {
				final SinkConnectorClient sinkConnectorClient = new SinkConnectorClient(enrichSinkParams, restTemplate);
				// Stop sink without retries, we don't want to block sink task thread
				sinkConnectorClient.doStopSink(dataSink.getId());
			}
		} catch (Exception e) {
			logger.error("Failed to stopSinkConnectorIfDedicated", e);
		}
	}

	private void issuePauseEventOnStop() {
		try {
			if (nodeShutdownInProgress.get()) {
				logger.info("Skip issuePauseEventOnStop: node is shutting down");
				return;
			}
			issuePauseEvent(true, false, "issuePauseEventOnStop");
		} catch (Exception e) {
			logger.error("Failed to issuePauseEventOnStop", e);
		}
	}

	private boolean isConnectorStopped() {
		final SinkConnectorClient sinkConnectorClient = new SinkConnectorClient(enrichSinkParams, restTemplate);
		return !sinkConnectorClient.isAssignedConnector(dataSink.getId());
	}

	private void issuePauseEvent(boolean hardStop, boolean criticalFailure, String ctxMessage) {
		if (config.fastMode || !isDedicatedNode || config.tasksMax != 1) {
			logger.info("Skip issuing Pause event: fastMode={}, isDedicatedNode={}, tasksMax={}", config.fastMode, isDedicatedNode, config.tasksMax);
			return;
		}
		if (!criticalFailure && hardStop && !isConnectorStopped()) {
			logger.info("Skip issuing hardStop Pause event: connector is not stopped");
			return;
		}
		final String origin = sinkConnectorServiceName(dataSink.getId(), dataSink.getConnectionType());
		final Map<String, String> messageContext = new HashMap<>();
		messageContext.put(HARD_STOP, String.valueOf(hardStop));
		messageContext.put(CRITICAL_FAILURE, String.valueOf(criticalFailure));
		messageContext.put(MESSAGE, ctxMessage);

		// Do not attach resourceJson to Control message, because adminApi client operates with expanded resource versions. Expanded dataSource version may not fit into Kafka message
		// ctrl-listeners will delete dedicated sink deployment after receiving the HARD_STOP event
		SinkControlMessage message = new SinkControlMessage(
				randomUUID(), dataSink.getId(), ControlEventType.PAUSE, dataSink.getConnectionType(),
				origin, messageContext, Optional.empty(), Optional.empty());
		controlMessageProducer.sendControlMessage(message);
		controlMessageProducer.flush();
	}

	public ControlMessageProducer getControlMessageProducer() {
		return controlMessageProducer;
	}

	protected void sendQuarantineMessage(String name, List<NexlaQuarantineMessage> quarantineMessages) {
		if (quarantineMessages.isEmpty()) {
			return;
		}

		Resource sink = Resource.sink(config.sinkId);
		Optional<NexlaErrorMessage> nexlaErrorMessage = ofNullable(quarantineMessages.get(0).getError());

		if (quarantineTopicExistsCache.getUnchecked(sink)) {
			quarantineMessages.forEach(qMessage ->
					dataMessageProducer.publishQuarantineMessage(sink, qMessage));
		}

		NexlaConnectorUtils.sendNexlaNotificationEvent(
			controlMessageProducer, NotificationEventType.ERROR, runId, SINK,
				config.sinkId, name, 0L, nexlaErrorMessage.get().getMessage());

		publishMonitoringLog(nexlaErrorMessage.get().getMessage(), NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
		reportTelemetryWriteError();
	}

	protected void sendMetric(String name, Optional<String> displayPath, long numRecords, long numBytes, long numErrors) {
		sendMetricsByRunId(runId, name, displayPath, numRecords, numBytes, numErrors);
	}

	protected void sendMetricsByRunId(Long runId,String name, Optional<String> displayPath, long numRecords, long numBytes, long numErrors) {
		if (numRecords > 0 || numBytes > 0 || numErrors > 0) {

			if (runId == null) {
				// If we don't see this log message, we can remove this conditional.
				// If we see this log message, we should find and fix the bug
				logger.error("RunId should never be null for sink task. Not sending metric for " +
						"name={}, displaypath={}, numRecords={}, numBytes={}, numErrors={}", name, displayPath, numRecords, numBytes, numErrors);
				return;
			}

			// When NexlaMessageContext::runId is not set, we use BaseSinkTask::runId
			if (runId == 0L) {
				logger.info("RunId placeholder 0L was used, substituting with actual runId={}", this.runId);
				runId = this.runId;
			}

			// When NexlaMessageContext::runId is not set, we use BaseSinkTask::runId
			if (runId == 0L) {
				runId = this.runId;
			}

			String resultingMetricName;
			if (config.externallySetMetricName != null) {
				// adaptive flows case - just use it instead
				resultingMetricName = config.externallySetMetricName;
			} else {
				// usual scenario
				resultingMetricName = name;
			}

			logger.info("metric name in BaseSinkTask: {}", resultingMetricName);
			MetadataBuilder builder = new MetadataBuilder().withName(resultingMetricName);

			displayPath.ifPresent(builder::withDisplayPath);
			BaseSinkMetric baseSinkMetric = new BaseSinkMetric(config.sinkId, SINK, runId, config.datasetId, numRecords, numBytes, numErrors, builder,
					dataSink.getOrgId(), dataSink.getOwnerId());
			NexlaConnectorUtils.publishSinkMetrics(controlMessageProducer, baseSinkMetric);

			publishMonitoringLogMetrics(numRecords, numBytes, numErrors);
		}
	}

	protected void publishExceptionMessage(Throwable exp, long errorCount, String name) {
		NexlaError errorDetails = getErrorDetails(exp, of(errorCount));
		NexlaConnectorUtils.publishException(controlMessageProducer, runId, SINK, config.sinkId, 0L, name, errorDetails);
	}

	protected void publishMonitoringLog(
			String log,
			NexlaMonitoringLogType type,
			NexlaMonitoringLogSeverity severity) {
		NexlaMonitoringLogEvent monitoringLogEvent = NexlaMonitoringLogEvent.of(
				Optional.ofNullable(dataSink.getOrg())
						.map(Org::getId)
						.orElse(0),
				this.runId,
				config.sinkId,
				SINK,
				log,
				type,
				severity,
				System.currentTimeMillis()
		);
		controlMessageProducer.publishMonitoringLog(monitoringLogEvent);
	}

	protected void publishMonitoringLog(
			String log,
			NexlaMonitoringLogType type,
			NexlaMonitoringLogSeverity severity,
			Long eventTime) {
		NexlaMonitoringLogEvent monitoringLogEvent = NexlaMonitoringLogEvent.of(
				Optional.ofNullable(dataSink.getOrg())
						.map(Org::getId)
						.orElse(0),
				this.runId,
				config.sinkId,
				SINK,
				log,
				type,
				severity,
				eventTime
		);
		controlMessageProducer.publishMonitoringLog(monitoringLogEvent);
	}

	protected void publishMonitoringLogMetrics(long records, long size, long errors) {
		publishMonitoringLogMetrics(new MetricWithErrors(records, size, errors));
	}

	protected void publishMonitoringLogMetrics(MetricWithErrors metricWithErrors) {
		NexlaMonitoringLogMetricsProducer.publishMonitoringLogMetrics(
			controlMessageProducer,
				Optional.ofNullable(dataSink.getOrg())
						.map(Org::getId)
						.orElse(0),
				this.runId,
				config.sinkId,
				SINK,
				metricWithErrors
		);
	}

	private static Set<com.nexla.common.sink.TopicPartition> toNexlaTopicPartitions(Set<TopicPartition> currentOffsets) {
		return currentOffsets.stream()
				.map(topicPartition -> new com.nexla.common.sink.TopicPartition(topicPartition.topic(), topicPartition.partition()))
				.collect(Collectors.toSet());
	}

	/**
	 * Schedules task to heartbeat active runIDs
	 */
	class ScheduledRunIdHeartbeat implements AutoCloseable{
		private final Future<?> runIdHeartbeat;

		public ScheduledRunIdHeartbeat() {
			Set<Long> activeRunIds = getActiveRunIds();

			// For STATEFUL sinks - heartbeat all active runIDs from accumulated data at every flush operation
			// After stateful sink is flushed, we do call onPipelineFlushed method which resets active runIDs.
			// So, after the stateful sink final flush, there will be no runID value to heartbeat.

			// For STATELESS sinks - active runID is the runID of the last processed message
			// Heartbeat should not be sent immediately in this case because it may lead to sending heartbeat for not-active runID.
			// We don't want to heartbeat runID at every flush operation if there is no new data coming.

			if (isStateful) {
				activeRunIds.forEach(runId -> heartbeat(runId, false));
			}
			this.runIdHeartbeat = activeRunIds.isEmpty()
					? CompletableFuture.completedFuture(null)
					: SCHEDULED_POOL.scheduleWithFixedDelay(
					() -> activeRunIds.forEach(runId -> heartbeat(runId, false)),
					60,
					10,                                // Keep delay lower that heartbeatSender cache expiration time
					SECONDS);
		}

		@Override
		public void close() throws Exception {
			runIdHeartbeat.cancel(true);
		}
	}

	protected void reportTelemetryStartError() {
		this.sinkTelemetryReporter.ifPresent(x -> x.reportStartError());
	}

	protected void reportTelemetryWriteError() {
		this.sinkTelemetryReporter.ifPresent(x -> x.reportWriteError());
	}

	protected void reportTelemetryRetriableError() {
		this.sinkTelemetryReporter.ifPresent(x -> x.reportRetriableError());
	}

	protected void reportTelemetryFromError(Throwable error) {
		if (isCausedByRetriableError(error))
			reportTelemetryRetriableError();
		else
			reportTelemetryWriteError();
	}

	protected boolean isSinkSpecificTelemetryRetriableError(Throwable e) {
		// For child sinks to customize that is RetriableError
		return false;
	}

	private boolean isCommonTelemetryRetriableError(Throwable e) {
		return (e instanceof ProbeRetriableException || e instanceof IOException);
	}

	private boolean isCausedByRetriableError(Throwable e) {
		if (isCommonTelemetryRetriableError(e) || isSinkSpecificTelemetryRetriableError(e)) {
			return true;
		} else {
			if (e.getCause() != null) {
				return isCausedByRetriableError(e.getCause());
			}
		}
		return false;
	}

	protected void reportLifecycleState(SinkLifecycleState lifecycleState) {
		if (!config.unitTest && isStateful) {
			SinkShutdownLifecycleManagement$.MODULE$.reportLifecycleState(this.config.sinkId, lifecycleState);
		}
	}

	public interface ExceptionHandler {
		void handleException(Exception exception, long exceptionCount, String table);
		void handleMonitoring(Exception exception);
	}

	protected ExceptionHandler getExceptionHandler() {
		return new ExceptionHandler() {
			@Override
			public void handleException(Exception exp, long errorCount, String name) {
				publishExceptionMessage(exp, errorCount, name);
			}

			@Override
			public void handleMonitoring(Exception exception) {
				publishMonitoringLog(
						"Exception while writing data. Reason: " + exception.getMessage(),
						NexlaMonitoringLogType.LOG,
						NexlaMonitoringLogSeverity.ERROR);
				reportTelemetryWriteError();
			}
		};
	}

	/**
	 * Send extra heartbeats for connector_state table ONLY, without runId value
	 * Used when the pipeline is still not finished, but sink is not receiving any records
	 */
	private void heartbeatConnectorState() {
		if (!config.fastMode && !config.unitTest) {
			controlMessageProducer.sendHeartbeat(new HeartbeatConnectorStateCoordination(
					UUID.randomUUID().toString(), SINK, config.sinkId, System.currentTimeMillis()));
		}
	}

	private Consumer<RequestResponseDetailedMessages> createRateLimitedFlowInsightsSender(boolean logVerbose, int rateLimitPerMinute,
																								  AtomicLong lastDetailedFlowInsightsResetTime, AtomicInteger detailedFlowInsightsSentCount) {
		return (detailedMessages) -> {
			long now = System.currentTimeMillis();
			long lastResetTime = lastDetailedFlowInsightsResetTime.get();
			if (now - lastResetTime >= DETAILED_FLOW_INSIGHTS_WINDOW_MS) {
				if (lastDetailedFlowInsightsResetTime.compareAndSet(lastResetTime, now)) {
					detailedFlowInsightsSentCount.set(0);
				}
			}
			long lastCount = detailedFlowInsightsSentCount.get();
			if (lastCount < rateLimitPerMinute) {
				detailedFlowInsightsSentCount.incrementAndGet();
				publishMonitoringLog(detailedMessages.getRequest(), NexlaMonitoringLogType.LOG, detailedMessages.getRequestSeverity(), detailedMessages.getRequestTime());
				publishMonitoringLog(detailedMessages.getResponse(), NexlaMonitoringLogType.LOG, detailedMessages.getResponseSeverity(), detailedMessages.getResponseTime());
			}
		};
	}

	protected Consumer<RequestResponseDetailedMessages> getSuccessFlowInsightsSender() {
		return createRateLimitedFlowInsightsSender(
				config.logVerbose,
				config.detailedFlowInsightsSuccessesPerMinute,
				lastSuccessDetailedFlowInsightsResetTime,
				successDetailedFlowInsightsSentCount
		);
	}

	protected Consumer<RequestResponseDetailedMessages> getErrorFlowInsightsSender() {
		return createRateLimitedFlowInsightsSender(
				config.logVerbose,
				config.detailedFlowInsightsErrorsPerMinute,
				lastErrorDetailedFlowInsightsResetTime,
				errorDetailedFlowInsightsSentCount
		);
	}
}
