package com.nexla.connect.common.offsets;

import com.google.common.annotations.VisibleForTesting;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connect.common.OffsetsCoordinationClient;
import com.nexla.kafka.KafkaMessageTransport;
import com.nexla.listing.client.ListingCoordinationClient;
import one.util.streamex.EntryStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * The purpose of logic in this class is to track Kafka offsets for trace messages, since they are not a part of common pipeline.
 * How it works:
 * 1. When SinkTask consumes records from Kafka (PUT phase), it provides offsets to this class via trackMessageOffset method
 * OffsetsTracker stores the biggest offsets into two separate collections - one for trace messages, and one for common messages
 * <p>
 * 2. When SinkTask is ready to commit processed offsets (during PUT or FLUSH phase),
 * they are pre-processed by OffsetsTracker and complemented with trace message offsets (see inherited updateSinkOffsets method)
 * <p>
 * 3. After offsets are committed, cleanupStaleOffsets method is used to delete committed offsets from internal collections
 * <p>
 * 4. When SinkTask has finished FLUSH operation (and possibly committed all offsets), OffsetTracker performs extra check for trace messages
 * in order to handle case when only trace messages were consumed from Kafka
 * <p>
 * Note. Values from internal collections should always be retrieved/removed using sinkID and topicPartition.
 * It is required in order to prevent concurrency conflicts between different sink tasks (possibly with the same sink id)
 */
public class OffsetsTracker extends OffsetsCoordinationClient {
	private static final Logger logger = LoggerFactory.getLogger(OffsetsTracker.class);

	// Collections keep the highest offsets for trace and not-trace (tracked) messages (Map<sinkId, Map<topicPartition, offset>>)
	private final Map<Integer, Map<TopicPartition, Long>> traceOffsets = new ConcurrentHashMap<>();
	private final Map<Integer, Map<TopicPartition, Long>> trackedOffsets = new ConcurrentHashMap<>();

	public OffsetsTracker(ListingCoordinationClient coordinationClient) {
		super(coordinationClient);
	}

	/**
	 * Method stores last seen offsets into internal collections
	 */
	public void trackMessageOffset(int sinkId, TopicPartition topicPartition, long offset, boolean isTrace) {
		Map<TopicPartition, Long> offsetsMap = isTrace ? getSinkTraceOffsets(sinkId) : getSinkTrackedOffsets(sinkId);
		offsetsMap.merge(topicPartition, offset, Math::max);
	}

	/**
	 * Methods receives tracked offsets to be committed, complements them with trace offsets
	 * and passes updated values to the parent class
	 */
	@Override
	public void updateSinkOffsets(int sinkId, Map<TopicPartition, Long> newOffsets) {
		Map<TopicPartition, Long> offsetsToCommit = mergeTraceOffsets(sinkId, newOffsets);
		super.updateSinkOffsets(sinkId, offsetsToCommit);
		cleanupStaleOffsets(sinkId, offsetsToCommit);
	}

	/**
	 * Check and commit trace offsets if possible
	 * Trace offsets can be committed in case there were no tracked offsets before them (eq there were no data messages)
	 */
	public void flushTraceOffsets(int sinkId, Set<TopicPartition> topicPartitions) {
		Map<TopicPartition, Long> commitableTraceOffsets = getCommitableTraceOffsets(sinkId, topicPartitions);
		if (!commitableTraceOffsets.isEmpty()) {
			logger.info("Commit trace messages ONLY offsets for sink: {} ({})", sinkId, commitableTraceOffsets);
			super.updateSinkOffsets(sinkId, commitableTraceOffsets);
			cleanupStaleOffsets(sinkId, commitableTraceOffsets);
		}
	}

	@VisibleForTesting
	Map<TopicPartition, Long> getSinkTraceOffsets(final int sinkId) {
		return this.traceOffsets.computeIfAbsent(sinkId, ConcurrentHashMap::new);
	}

	@VisibleForTesting
	Map<TopicPartition, Long> getSinkTrackedOffsets(final int sinkId) {
		return this.trackedOffsets.computeIfAbsent(sinkId, ConcurrentHashMap::new);
	}

	/**
	 * Method complements the original offsets with trace offsets. The idea is:
	 * When we commit some offset, we check if it is tracked. If yes, it means that the offset corresponds
	 * to the last processed message of that partition, so we can commit the higher seen offset for that partition.
	 * Note. Values from internal collections should always be retrieved/removed using sinkID and topicPartition.
	 */
	private Map<TopicPartition, Long> mergeTraceOffsets(int sinkId, Map<TopicPartition, Long> originalOffsets) {
		Map<TopicPartition, Long> sinkTrackedOffsets = getSinkTrackedOffsets(sinkId);
		Map<TopicPartition, Long> sinkTraceOffsets = getSinkTraceOffsets(sinkId);
		if (sinkTraceOffsets.isEmpty()) {
			return originalOffsets;
		}
		return EntryStream
				.of(originalOffsets)
				.mapToValue((topicPartition, originalOffset) -> {
					Long trackedOffset = sinkTrackedOffsets.get(topicPartition);
					Long traceOffset = sinkTraceOffsets.get(topicPartition);
					return traceOffset != null && trackedOffset != null && trackedOffset.equals(originalOffset)
							? Math.max(originalOffset, traceOffset)
							: originalOffset;
				})
				.toMap();
	}

	/**
	 * Method removes committed offsets from internal collections.
	 * Note. Values from internal collections should always be retrieved/removed using sinkID and topicPartition.
	 */
	private void cleanupStaleOffsets(int sinkId, Map<TopicPartition, Long> committedOffsets) {
		committedOffsets.forEach((topicPartition, committedOffset) -> {
			getSinkTrackedOffsets(sinkId).computeIfPresent(
					topicPartition,
					(ignored, trackedOffset) -> trackedOffset <= committedOffset ? null : trackedOffset);
			getSinkTraceOffsets(sinkId).computeIfPresent(
					topicPartition,
					(ignored, traceOffset) -> traceOffset <= committedOffset ? null : traceOffset);
		});
	}

	/**
	 * Method returns trace messages offsets for cases when only trace messages were consumed by BaseSinkTask
	 * Note. Values from internal collections should always be retrieved/removed using sinkID and topicPartition.
	 */
	private Map<TopicPartition, Long> getCommitableTraceOffsets(int sinkId, Set<TopicPartition> topicPartitions) {
		// Apply filtering because sink task should work only with its own partitions when it calls this method
		Map<TopicPartition, Long> sinkTraceOffsets = EntryStream.of(getSinkTraceOffsets(sinkId))
																.filterKeys(topicPartitions::contains)
																.toMap();
		Map<TopicPartition, Long> sinkTrackedOffsets = EntryStream.of(getSinkTrackedOffsets(sinkId))
																  .filterKeys(topicPartitions::contains)
																  .toMap();
		boolean traceOffsetsOnlyPresent = sinkTrackedOffsets.isEmpty() && !sinkTraceOffsets.isEmpty();
		return traceOffsetsOnlyPresent ? new HashMap<>(sinkTraceOffsets) : Collections.emptyMap();
	}
}