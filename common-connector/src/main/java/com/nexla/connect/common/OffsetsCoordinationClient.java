package com.nexla.connect.common;

import com.nexla.common.sink.TopicPartition;
import com.nexla.control.coordination.SinkOffset;
import com.nexla.listing.client.ListingCoordinationClient;
import one.util.streamex.EntryStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public class OffsetsCoordinationClient implements AutoCloseable {
	private static final Logger logger = LoggerFactory.getLogger(OffsetsCoordinationClient.class);

	private final ListingCoordinationClient coordinationClient;

	public OffsetsCoordinationClient(ListingCoordinationClient coordinationClient) {
		this.coordinationClient = coordinationClient;
	}

	/**
	 * Submit offsets to offsets sender.
	 * Submitted offsets should be considered as committed for BaseSinkTask
	 */
	public void updateSinkOffsets(int sinkId, Map<TopicPartition, Long> newOffsets) {
		if (newOffsets.isEmpty()) {
			return;
		}
		List<SinkOffset> sinkOffsets = toSinkOffsets(sinkId, newOffsets);
		coordinationClient.updateSinkOffsets(sinkId, sinkOffsets);
		// TODO FIXME Fix offsets commit logic in 2.13: Use the new approach
//		kafkaMessageTransport.publish(
//				TOPIC_COORDINATION,
//				JsonUtils.toJsonString(new CommitSinkOffsetCoordination(UUID.randomUUID().toString(), sinkOffsets, System.currentTimeMillis())),
//				String.valueOf(sinkId));
	}

	public void flush() {
	}

	@Override
	public void close() {
		flush();
	}

	public Optional<Map<TopicPartition, Long>> getSinkOffsets(int sinkId) {
		// Kafka coordination topic is used to make sure that the requested offsets are the latest
		// Coordination listener should read RequestSinkOffsetCoordination event before returning the latest Kafka offset
		// TODO FIXME Fix offsets commit logic in 2.13. Use the new approach
//		long ts = System.currentTimeMillis();
//		publishCoordinationEvent(sinkId, ts);
//		Uninterruptibles.sleepUninterruptibly(15, TimeUnit.SECONDS);		// Give few seconds for coordination app to handle coordination message
//		Optional<Map<TopicPartition, Long>> sinkOffsets = coordinationClient.getSinkOffsets(sinkId, ts, () -> publishCoordinationEvent(sinkId, ts));
		Optional<Map<TopicPartition, Long>> sinkOffsets = coordinationClient.getSinkOffsets(sinkId);
		logger.debug("Retrieved sink #{} offsets: {}", sinkId, sinkOffsets);
		return sinkOffsets;
	}

	private static List<SinkOffset> toSinkOffsets(int sinkId, Map<TopicPartition, Long> offsets) {
		return EntryStream
				.of(offsets)
				.mapKeyValue((tp, offset) -> new SinkOffset(sinkId, tp.topic, tp.partition + "", offset))
				.toList();
	}
}