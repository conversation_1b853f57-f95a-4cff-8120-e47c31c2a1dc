package com.nexla.connect.common;

import com.nexla.common.exception.NexlaErrorNotificationEvent;
import com.nexla.common.exception.NexlaQuarantineMessage;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.common.notify.NexlaNotificationEvent;
import com.nexla.common.notify.error.NexlaInternalNotificationEvent;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent;
import com.nexla.common.notify.transport.NexlaMessageTransport;
import static com.nexla.connect.common.connector.schema.DataSetService.*;

public class SchemaDetectionNexlaTransport implements NexlaMessageTransport {

    private NexlaMessageTransport inner;

    public SchemaDetectionNexlaTransport(NexlaMessageTransport inner) {
        this.inner = inner;
    }

    @Override
    public void publishMetrics(NexlaRawMetric nexlaRawMetric) {
    }

    @Override
    public void publishMetrics(NexlaRawMetric nexlaRawMetric, String s) {
        
    }

    @Override
    public void publishNotification(NexlaNotificationEvent notificationEvent) {
        if (notificationEvent.getEventSource().equals(SCHEMA_DETECTOR)) {
            inner.publishNotification(notificationEvent);
        }
    }

    @Override
    public void publishQuarantineMessage(NexlaQuarantineMessage quarantineMessage, String topicName) {
    }

    @Override
    public void publishTransformIOMessage(String quarantineWriteTopic, NexlaQuarantineMessage quarantineMessage) {
    }

    @Override
    public void publishErrorMessage(String topic, NexlaErrorNotificationEvent notificationEvent) {
    }

    @Override
    public void flush() {
        inner.flush();
    }

    @Override
    public void publish(String topic, String json, String key) {
    }

    @Override
    public void publish(String topic, int partition, String key, String value) {

    }

    @Override
    public void publishInternalNotification(NexlaInternalNotificationEvent notificationEvent) {
    }

    @Override
    public void publishMonitoringLog(NexlaMonitoringLogEvent monitoringLogEvent) {
        inner.publishMonitoringLog(monitoringLogEvent);
    }
}
