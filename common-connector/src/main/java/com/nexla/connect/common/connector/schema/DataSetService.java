package com.nexla.connect.common.connector.schema;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSet;
import com.nexla.admin.client.DataSource;
import com.nexla.admin.client.DataSourceRunId;
import com.nexla.admin.client.FindOrCreateDataSetResult;
import com.nexla.admin.client.NexlaSchema;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NotificationEventType;
import com.nexla.common.SchemaUtils;
import com.nexla.common.exception.ProbeRetriableException;
import com.nexla.common.notify.NexlaNotificationEvent;
import com.nexla.common.notify.context.NexlaNotificationEventContext;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.connect.common.SchemaDetectionResult;
import com.nexla.transform.JsonCompare;
import com.nexla.transform.JsonCompareResult;
import com.nexla.transform.TransformService;
import com.nexla.transform.TransformerResult;
import java.util.stream.Collectors;
import com.nexla.transform.schema.SchemaMerger;
import one.util.streamex.StreamEx;
import org.javatuples.Pair;
import org.joda.time.DateTime;
import org.jooq.tools.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Consumer;

import static com.nexla.common.NexlaConstants.LEFT;
import static com.nexla.common.NexlaConstants.RIGHT;
import static com.nexla.common.NexlaNamingUtils.nameDataSetTopic;
import static com.nexla.common.NotificationEventType.CREATE;
import static com.nexla.common.NotificationEventType.UPDATE;
import static com.nexla.common.ResourceType.DATASET;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.StreamUtils.reverseMap;
import static com.nexla.transform.JsonCompareResult.DISJOINT;
import static com.nexla.transform.JsonCompareResult.RELATED;
import static com.nexla.transform.JsonCompareResult.SUBSET;
import static com.nexla.transform.JsonCompareResult.SUPERSET;
import static java.lang.Math.min;
import static java.lang.String.format;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;

public class DataSetService {

	private static final Logger logger = LoggerFactory.getLogger(DataSetService.class);

	private static ExecutorService EXECUTOR_SERVICE = Executors.newWorkStealingPool(8);

	public static final String SCHEMA_DETECTOR = "schema-detector";

	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

	private final ConcurrentHashMap<Integer, Integer> schemaToDataSet;
	private final ConcurrentHashMap<Integer, Integer> datasetToSchema;
	private final ConcurrentHashMap<NexlaSchema, NexlaSchema> schemas;
	private final ConcurrentHashMap<Integer, List<NexlaMessage>> dataSetSamples;

	private final AdminApiClient adminApiClient;
	private final ControlMessageProducer controlMessageProducer;
	private final TransformService transformService;
	private final JsonCompare jsonCompare = new JsonCompare();
	private final int maxSamples;

	private volatile boolean tryCombineSingleSchema = false;

	public DataSetService(
		AdminApiClient adminApiClient,
		ControlMessageProducer messageProducer,
		TransformService transformService,
		ConcurrentHashMap<Integer, Integer> schemaToDataSet,
		ConcurrentHashMap<Integer, List<NexlaMessage>> dataSetSamples,
		Set<NexlaSchema> schemas,
		int maxSamples)
	{
		this.adminApiClient = adminApiClient;
		this.controlMessageProducer = messageProducer;
		this.transformService = transformService;
		this.schemaToDataSet = schemaToDataSet;
		this.dataSetSamples = dataSetSamples;
		this.schemas = new ConcurrentHashMap<>(schemas.stream().collect(toMap(identity(), identity())));
		this.datasetToSchema = new ConcurrentHashMap<>(reverseMap(schemaToDataSet));
		this.maxSamples = maxSamples;
	}

	@VisibleForTesting
	protected static void setMockExecutorService(ExecutorService testService) {
		EXECUTOR_SERVICE = testService;
	}

	void setTryCombineSingleSchema() {
		this.tryCombineSingleSchema = true;
	}

	public boolean singleSchemaMode() {
		return tryCombineSingleSchema && schemaToDataSet.size() > 0;
	}

	public Optional<SchemaDetectionResult> tryUpdateDataSet(List<NexlaMessage> messages, NexlaSchema detectedSchema) {
		Map<String, Object> detectedSchemaMap = getSchemaAsMap(detectedSchema);
		return schemas.keySet()
			.stream()
			.map(schema -> Pair.with(schema, jsonCompare.compareJsonSchema(detectedSchemaMap, getSchemaAsMap(schema))))
				// first search for superset or subset relationships with existing schemas
			.filter(result -> result.getValue1() == SUPERSET || result.getValue1() == SUBSET)
			.findFirst()
			.map(res -> {
				NexlaSchema oldSchema = res.getValue0();
				return processSchemas(messages, detectedSchema, oldSchema,
					schemaToDataSet.get(oldSchema.getSchemaId()), res.getValue1());
			})
			.or(() ->
				// no superset or subset relationships, try to combine single schema
				combineDataset(messages, detectedSchema)
			);
	}

	private Optional<SchemaDetectionResult> combineDataset(List<NexlaMessage> messages,
			NexlaSchema resolvedSchema) {

		// precondition: should only attempt to blindly combine schemas when in single schema mode
		if (!singleSchemaMode()) {
			return Optional.empty();
		}

		NexlaSchema oldSchema = schemas.keySet().iterator().next();
		int datasetId = dataSetSamples.keySet().iterator().next();

		Map<String, Object> oldSchemaMap = getSchemaAsMap(oldSchema);
		Map<String, Object> newSchemaMap = getSchemaAsMap(resolvedSchema);
		JsonCompareResult compare = jsonCompare.compareJsonSchema(newSchemaMap, oldSchemaMap);

		return Optional.of(processSchemas(messages, resolvedSchema, oldSchema, datasetId, compare));
	}

	private static Map<String, Object> getSchemaAsMap(NexlaSchema schema) {
		return OBJECT_MAPPER.convertValue(schema, Map.class);
	}

	private SchemaDetectionResult processSchemas(List<NexlaMessage> messages,
												 NexlaSchema newSchema, NexlaSchema oldSchema,
												 Integer datasetId, JsonCompareResult result) {
		boolean addedToInMemory = addToInMemorySamples(datasetId, messages);
		if (result == SUPERSET || result == RELATED || result == DISJOINT) {
			DataSet dataSet = adminApiClient.getDataSet(datasetId).get();
			updateSchema(dataSet, newSchema, oldSchema);

			logger.info("[{}] DataSet with previous schemaId={} updated to schemaId={} with {} samples added",
					dataSet.getId(),
					oldSchema.getSchemaId(),
					newSchema.getSchemaId(),
					messages.size());
			NexlaNotificationEvent event = createNotification(datasetId, UPDATE);
			NexlaNotificationEventContext context = createNotificationContext(newSchema, oldSchema);
			event.setContext(context);
			controlMessageProducer.publishNotification(event);
		}

		Consumer<List<NexlaMessage>> listConsumer = c -> {
			if (result == SUPERSET || result == RELATED || result == DISJOINT || addedToInMemory) {
				addToRemoteSamples(datasetId, c);
			}
		};
		return new SchemaDetectionResult(datasetId, nameDataSetTopic(datasetId), listConsumer);
	}

	private void updateSchema(DataSet dataSet, NexlaSchema schema, NexlaSchema oldSchema) {
		dataSet.setSourceSchema(schema);
		if (isEmpty(dataSet.getParentDatasets())) {
			dataSet.setOutputSchema(schema);
		}
		doUpdateDataSetSchema(dataSet, schema, oldSchema);
	}

	private void doUpdateDataSetSchema(
			DataSet dataSet,
			NexlaSchema schema,
			NexlaSchema oldSchema
	) {
		adminApiClient.updateDataSetSchema(dataSet);
		schemaToDataSet.remove(oldSchema.getSchemaId());
		schemaToDataSet.put(schema.getSchemaId(), dataSet.getId());
		datasetToSchema.put(dataSet.getId(), schema.getSchemaId());
		schemas.remove(oldSchema);
		schemas.put(schema, schema);
	}

	public SchemaDetectionResult createDataset(
			List<NexlaMessage> messages,
			Integer sourceId,
			NexlaSchema sourceSchema,
			String name
	) {
		DataSet dataset = new DataSet();
		dataset.setDataSourceId(of(sourceId));
		dataset.setSourceSchema(sourceSchema);

		DataSource dataSource = adminApiClient.getDataSource(sourceId).get();
		long runId = Optional.ofNullable(dataSource.getRunIds())
				.orElse(List.of())
				.stream()
				.mapToLong(DataSourceRunId::getId)
				.max()
				.orElse(0L);

		String dataSetCount = Integer.toString(dataSource.getDatasets().size() + 1);
		String dataSourceName = dataSource.getName();

		if (!StringUtils.isBlank(name)) {
			logger.info("Custom dataset name is used while creating dataset - " + name + " ; Source ID - " + sourceId);	
		}
		dataset.setName(StringUtils.isBlank(name)? format("%s - %s", dataSetCount, dataSourceName) : name);
		dataset.setDescription(format("DataSet #%s detected from %s", dataSetCount, dataSourceName));

		final FindOrCreateDataSetResult dataSetResult;
		final int datasetId;
		List<NexlaMessage> mes = Lists.newArrayList(messages.subList(0, min(messages.size(), maxSamples)));
		try {
			dataSetResult = adminApiClient.findOrCreateDataSet(dataset);
		} catch (Exception e) {
			logger.error("Dataset creation failed for sourceId={}", sourceId, e);
			NexlaMonitoringLogEvent monitoringLogEvent = NexlaMonitoringLogEvent.of(
					getOrgId(dataSource),
					runId,
					sourceId,
					SOURCE,
					"Failed to detect/create Nexset for source. Reason: " + e.getMessage(),
					NexlaMonitoringLogType.EVENT,
					NexlaMonitoringLogSeverity.ERROR,
					System.currentTimeMillis());
			controlMessageProducer.publishMonitoringLog(monitoringLogEvent);
			throw new ProbeRetriableException("Dataset creation failed for sourceId=" + sourceId, e);
		}

		if (dataSetResult.result == FindOrCreateDataSetResult.Result.DATASET_LIMIT_REACHED) {
			throw new DataSetLimitReached();
		}

		datasetId = dataSetResult.dataSetId;
		schemas.put(sourceSchema, sourceSchema);
		dataSetSamples.put(datasetId, mes);
		schemaToDataSet.put(sourceSchema.getSchemaId(), datasetId);
		datasetToSchema.put(datasetId, sourceSchema.getSchemaId());

		logger.info("Found or created dataset with id={}, schemaId={}", datasetId, sourceSchema.getSchemaId());
		NexlaMonitoringLogEvent monitoringLogEvent = NexlaMonitoringLogEvent.of(
				getOrgId(dataSource),
				runId,
				datasetId,
				DATASET,
				String.format("Nexset %d found or created successfully", datasetId),
				NexlaMonitoringLogType.EVENT,
				NexlaMonitoringLogSeverity.INFO,
				System.currentTimeMillis());

		controlMessageProducer.publishMonitoringLog(monitoringLogEvent);

		dataset.setId(dataSetResult.dataSetId);

		if (dataSetResult.result == FindOrCreateDataSetResult.Result.CREATED) {
			NexlaNotificationEvent event = createNotification(dataSetResult.dataSetId, CREATE, runId);
			controlMessageProducer.publishNotification(event);
		}

		Consumer<List<NexlaMessage>> listConsumer = x -> {
			dataSetSamples.put(datasetId, x);
			addToRemoteSamples(datasetId, x);
		};
		return new SchemaDetectionResult(dataSetResult.dataSetId, nameDataSetTopic(dataSetResult.dataSetId), listConsumer);
	}

	private Integer getOrgId(DataSource dataSource) {
		if(dataSource == null) {
			return 0;
		} else if(dataSource.getOrg() == null) {
			return 0;
		} else if(dataSource.getOrg().getId() == null) {
			return 0;
		}
		return dataSource.getOrg().getId();
	}

	private NexlaNotificationEvent createNotification(Integer datasetId, NotificationEventType notificationEventType, Long runId) {
		NexlaNotificationEvent notificationEvent = createNotification(datasetId, notificationEventType);
		notificationEvent.getContext().setRunId(runId);
		return notificationEvent;
	}

	private NexlaNotificationEvent createNotification(Integer datasetId, NotificationEventType notificationEventType) {
		NexlaNotificationEvent notificationEvent = new NexlaNotificationEvent();
		notificationEvent.setEventSource(SCHEMA_DETECTOR);
		notificationEvent.setEventTimeMillis(new DateTime().getMillis());
		notificationEvent.setResourceId(datasetId);
		notificationEvent.setResourceType(DATASET);
		notificationEvent.setEventType(notificationEventType);
		notificationEvent.setContext(new NexlaNotificationEventContext());
		return notificationEvent;
	}

	@VisibleForTesting
	NexlaNotificationEventContext createNotificationContext(NexlaSchema newSchema, NexlaSchema oldSchema) {

		Map<String, Object> oldSchemaProperties = (Map<String, Object>) ofNullable(oldSchema.getProperties())
			.orElseGet(() -> Maps.newHashMap());
		Map<String, Object> newSchemaProperties = (Map<String, Object>) ofNullable(newSchema.getProperties())
			.orElseGet(() -> Maps.newHashMap());

		NexlaNotificationEventContext context = new NexlaNotificationEventContext();
		MapDifference mapDifference = Maps.difference(oldSchemaProperties, newSchemaProperties);

		Map<String, Object> diffAttributes = Maps.newHashMap(mapDifference.entriesDiffering());
		deepConvertToModifiable(diffAttributes);
		convertIntoSerializableMap(diffAttributes);

		Map<String, Object> newAttributes = Maps.newHashMap(mapDifference.entriesOnlyOnRight());
		deepConvertToModifiable(newAttributes);
		convertIntoSerializableMap(newAttributes);

		context.setEntriesDiffering(diffAttributes);
		context.setEntriesOnlyOnRight(newAttributes);
		return context;
	}

	private void deepConvertToModifiable(Map<String, Object> map) {
		map = Maps.newHashMap(map);
		for (Map.Entry<String, Object> entry : map.entrySet()) {
			Object value = entry.getValue();
			if (value instanceof Map) {
				Map<String, Object> valueMap = (Map<String, Object>) value;
				entry.setValue(Maps.newHashMap(valueMap));
			}
		}
	}

	private void convertIntoSerializableMap(Map<String, Object> map) {
		for (Map.Entry<String, Object> entry : map.entrySet()) {
			Object value = entry.getValue();
			if (value instanceof MapDifference.ValueDifference) {
				MapDifference.ValueDifference valueMap = (MapDifference.ValueDifference) value;
				Map<String, Object> tempMap = Maps.newHashMap();
				tempMap.put(LEFT, valueMap.leftValue());
				tempMap.put(RIGHT, valueMap.rightValue());
				entry.setValue(tempMap);
			} else if (value instanceof Map) {
				convertIntoSerializableMap((Map<String, Object>) value);
			}
		}
	}

	public boolean addToInMemorySamples(int datasetId, List<NexlaMessage> messages) {
		List<NexlaMessage> dataSamples = getLocalSamples(datasetId).orElse(new ArrayList<>());
		int numDataSamples = dataSamples.size();

		if (numDataSamples >= maxSamples) {
			return false;
		} else {

			int maxSamplesToAdd = maxSamples - numDataSamples;

			List<NexlaMessage> samples = messages.size() < maxSamplesToAdd ?
				messages :
				messages.subList(0, maxSamplesToAdd);

			dataSamples.addAll(samples);
			dataSetSamples.put(datasetId, dataSamples);
			logger.info("Added {} local samples for dataSetId={}, total samples={}", samples.size(), datasetId, dataSamples.size());

			return true;
		}
	}

	public Future<?> addToRemoteSamples(int datasetId, List<NexlaMessage> samples) {
		Future<?> callToAdminApi = EXECUTOR_SERVICE.submit(() -> {

			List<NexlaMessage> samplesToAdd = samples.size() < maxSamples ?
				samples :
				samples.subList(0, maxSamples);
			adminApiClient.putDataSetSamples(samplesToAdd, datasetId);
			logger.info("Added {} remote samples for dataSetId={}", samplesToAdd.size(), datasetId);
		});
		return callToAdminApi;
	}

	public <T extends Map<String, Object>> NexlaSchema getSchema(List<T> rawMessages) {
		return getSchema(rawMessages, null);
	}

	public <T extends Map<String, Object>> NexlaSchema getSchema(List<T> rawMessages, NexlaSchema schema) {
		TransformerResult result;
		if (schema == null || schema.getProperties() == null) {
			result = transformService.accumulateSchema(rawMessages);
		} else {
			result = transformService.accumulateSchema(rawMessages, getSchemaAsMap(schema));
		}
		return OBJECT_MAPPER.convertValue(result.getOutput(), NexlaSchema.class);
	}

	public Optional<List<NexlaMessage>> getLocalSamples(int dataSetId) {
		return Optional.ofNullable(dataSetSamples.get(dataSetId));
	}

	public int getNumSchemas() {
		return schemaToDataSet.size();
	}

	public Optional<NexlaSchema> getFirstSchema() {
		return schemas.keySet().stream().findFirst();
	}

	public Optional<Integer> getDatasetId(int schemaId) {
		return Optional.ofNullable(schemaToDataSet.get(schemaId));
	}

	public Optional<Integer> getSchemaId(int datasetId) {
		return Optional.ofNullable(datasetToSchema.get(datasetId));
	}

	public Optional<NexlaSchema> getSchemaForDataset(int datasetId) {
		return getSchemaId(datasetId)
				.flatMap(schemaId ->
						schemas.keySet()
								.stream()
								.filter(nexlaSchema -> Objects.equals(nexlaSchema.getSchemaId(), schemaId))
								.findFirst()
				);
	}

	public NexlaSchema mergeSchemas(NexlaSchema schema1, NexlaSchema schema2) {
		SchemaMerger merger = new SchemaMerger();
		Map<String, Object> mergedSchema = SchemaUtils.addSchemaMetadata(
				merger.merge(DataSetService.getSchemaAsMap(schema1), DataSetService.getSchemaAsMap(schema2))
		);
		return OBJECT_MAPPER.convertValue(mergedSchema, NexlaSchema.class);
	}

	public JsonCompareResult compareSchemas(NexlaSchema newSchema, NexlaSchema oldSchema) {
		Map<String, Object> newSchemaMap = getSchemaAsMap(newSchema);
		Map<String, Object> oldSchemaMap = getSchemaAsMap(oldSchema);
		return jsonCompare.compareJsonSchema(newSchemaMap, oldSchemaMap);
	}

	public static class DataSetLimitReached extends RuntimeException {
		public DataSetLimitReached() {
			super("Dataset limit reached, cannot create a new dataset");
		}
	}
}