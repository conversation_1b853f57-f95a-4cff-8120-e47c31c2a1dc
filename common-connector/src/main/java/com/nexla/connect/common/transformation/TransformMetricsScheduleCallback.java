package com.nexla.connect.common.transformation;

import com.nexla.admin.client.DataSet;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.metrics.MetricWithErrors;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.connect.common.NexlaConnectorUtils;
import com.nexla.connector.config.FlowType;
import lombok.AllArgsConstructor;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ResourceType.DATASET;
import static java.util.Optional.empty;

@AllArgsConstructor
public class TransformMetricsScheduleCallback {

	private final String fileName;
	private final DataSet dataSet;
	private final ControlMessageProducer controlMessageProducer;
	private final FlowType flowType;

	public void onSchedule(Map<Long, MetricWithErrors> metricWithErrors) {
		Integer dataSetId = dataSet.getId();
		metricWithErrors.forEach((runId, metricWithError) -> {
			NexlaConnectorUtils.publishMetrics(controlMessageProducer, DATASET, dataSetId, fileName,
				metricWithError.getRecords(), metricWithError.getSize(), metricWithError.getErrors(),
				DateTimeUtils.nowUTC().getMillis(), Optional.of(runId), empty(), empty(),
				empty(), Optional.of(dataSetId),
				empty(), empty(), empty(), empty(), flowType,
					dataSet.getOrgId(), dataSet.getOwnerId());
		});
	}

	public Map<Long, MetricWithErrors> onAggregate(Map<Long, MetricWithErrors> newMetric, Map<Long, MetricWithErrors> metric) {
		Map.Entry<Long, MetricWithErrors> entry = newMetric.entrySet().iterator().next();
		metric.merge(entry.getKey(), entry.getValue(), TransformMetricsScheduleCallback::addMetrics);
		return metric;
	}

	private static MetricWithErrors addMetrics(MetricWithErrors firstMetric, MetricWithErrors secondMetric) {
		firstMetric.setRecords(firstMetric.getRecords() + secondMetric.getRecords());
		firstMetric.setSize(firstMetric.getSize() + secondMetric.getSize());
		firstMetric.setErrors(firstMetric.getErrors() + secondMetric.getErrors());
		return firstMetric;
	}
}