package com.nexla.cloud.job.connector.cloud.aws

import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.emr.EmrClient
import software.amazon.awssdk.services.emr.model.{AddJobFlowStepsRequest, Application, AutoTerminationPolicy, ClusterState, Configuration, DescribeClusterRequest, DescribeClusterResponse, DescribeStepRequest, DescribeStepResponse, HadoopJarStepConfig, JobFlowInstancesConfig, ListStepsRequest, RunJobFlowRequest, StepConfig, StepState, TerminateJobFlowsRequest}
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.{HeadObjectRequest, NoSuchKeyException, PutObjectRequest, S3Object}
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient
import software.amazon.awssdk.services.secretsmanager.model._
import com.github.rholder.retry._
import com.google.common.hash.Hashing
import com.nexla.cloud.job.connector.{MetricsHelper, Utils}
import com.nexla.cloud.job.connector.cloud.aws.AWSTools._
import com.nexla.cloud.job.connector.cloud._
import com.nexla.connector.config.rest.BaseAuthConfig
import com.typesafe.scalalogging.LazyLogging

import java.io.File
import java.net.URI
import java.util
import java.util.concurrent.TimeUnit
import scala.collection.JavaConverters._
import scala.concurrent.ExecutionContext
import scala.util.Try

class AWSCloudProvider(metricsHelper: MetricsHelper, logTag: String, cfg: BaseAuthConfig, srcCfg: Map[String, AnyRef],
                       cloudCfg: Map[String, AnyRef],
                       emrClientArg: Option[EmrClient] = None,
                       secretsMgr: Option[SecretsManagerClient] = None,
                       s3ClientArg: Option[S3Client] = None,
                       sleepTimeMilli: Int,
                       maxApiRetries: Int,
                       adminApiKey: String) extends CloudProvider(cfg) with LazyLogging {
  override val cloudName: String = "AWS"
  private val EMR_VERSION = "emr-6.7.0"
  private val NEXLA_API_KEY_SECRET = "nexla.admin.api.key"
  implicit val ec: ExecutionContext = ExecutionContext.global
  private val awsCfg: AwsCloudSpecificCfg = AwsCloudSpecificCfg(cloudCfg)

  private def getAwsCreds(useDataCredentials: Boolean): AwsCredentialsProvider = {
    if (useDataCredentials) {
      buildFromNexlaCreds(cfg.asAWS())
    } else {
      roleCredentialsProvider(awsCfg.roleWithEMRAccess, awsCfg.region, "Nexla-Spark-Pipeline-Runner-" + Thread.currentThread().getId)
    }
  }

  private lazy val emrClient: EmrClient = {
    if (emrClientArg.isDefined) {
      emrClientArg.get
    } else {
      getEMRClient(getAwsCreds(false), Region.of(awsCfg.region))
    }
  }

  private lazy val destinationS3Client: S3Client = {
    if (s3ClientArg.isDefined) {
      s3ClientArg.get
    } else {
      // todo: currently we support only the same region, as for EMR
      getS3Client(getAwsCreds(false), Region.of(awsCfg.region))
    }
  }

  private val emrStatusRetryer: Retryer[DescribeClusterResponse] = RetryerBuilder
    .newBuilder[DescribeClusterResponse]()
    .withStopStrategy(new StopStrategy {
      override def shouldStop(failedAttempt: Attempt[_]): Boolean = {
        if (failedAttempt.getAttemptNumber >= maxApiRetries) {
          true
        } else {
          val castValue = failedAttempt.asInstanceOf[Attempt[DescribeClusterResponse]]
          val terminalClusterStates = util.EnumSet.of(ClusterState.TERMINATED, ClusterState.TERMINATING, ClusterState.TERMINATED_WITH_ERRORS)
          val currentClusterState = castValue.get().cluster().status().state()

          castValue.hasResult && terminalClusterStates.contains(currentClusterState)
        }
      }
    })
    .retryIfResult(arpt => {
      val expectedStates = util.EnumSet.of(ClusterState.RUNNING, ClusterState.WAITING)
      val currentState = arpt.cluster().status().state()
      !expectedStates.contains(currentState)
    })
    .withWaitStrategy(WaitStrategies.fixedWait(sleepTimeMilli, TimeUnit.MILLISECONDS))
    .withRetryListener(new RetryListener {
      override def onRetry[V](attempt: Attempt[V]): Unit = {
        if (attempt.hasException) {
          logger.debug(s"got error on attempt $attempt, cause ${attempt.getExceptionCause}")
        } else {
          val clusterState = attempt.get().asInstanceOf[DescribeClusterResponse].cluster().status()
          logger.debug(s"retrying because cluster state/status $clusterState is not RUNNING yet")
        }
      }
    })
    .build()

  private val jobStatusRetryer: Retryer[DescribeStepResponse] = RetryerBuilder.newBuilder[DescribeStepResponse]()
    .withStopStrategy(new StopStrategy {
      override def shouldStop(failedAttempt: Attempt[_]): Boolean = {
        if (failedAttempt.getAttemptNumber >= maxApiRetries) {
          true
        } else {
          val castValue = failedAttempt.asInstanceOf[Attempt[DescribeStepResponse]]
          val terminalStepStates = util.EnumSet.of(
            StepState.CANCELLED, StepState.COMPLETED,
            StepState.FAILED, StepState.INTERRUPTED
          )
          val currentClusterState = castValue.get().step().status().state()

          castValue.hasResult && terminalStepStates.contains(currentClusterState)
        }
      }
    })
    .retryIfResult(arpt => {
      val inFlightStates = util.EnumSet.of(StepState.PENDING, StepState.RUNNING, StepState.CANCEL_PENDING)
      val currentState = arpt.step().status().state()
      inFlightStates.contains(currentState)
    })
    .withWaitStrategy(WaitStrategies.fixedWait(sleepTimeMilli, TimeUnit.MILLISECONDS))
    .withRetryListener(new RetryListener {
      override def onRetry[V](attempt: Attempt[V]): Unit = {
        if (attempt.hasException) {
          logger.error(s"got error on attempt $attempt, cause ${attempt.getExceptionCause}")
        } else {
          val stepState = attempt.get().asInstanceOf[DescribeStepResponse].step().status()
          logger.debug(s"retrying because step $stepState has not reached terminal status yet")
        }
      }
    })
    .build()

  override def createCluster(): ClusterResponse = {
    val clusterConfig = Utils.getClusterConfig(logTag, awsCfg, srcCfg)
    val hive = Application.builder().name("Hive").build()
    val spark = Application.builder().name("Spark").build()
    val ganglia = Application.builder().name("Ganglia").build()
    val clusterName = if (clusterConfig.name.isDefined) {
      clusterConfig.name.get
    } else {
      s"Nexla-Spark-Pipeline-${System.currentTimeMillis()}"
    }

    val hdfsProps: Map[String, String] = Map(
      "dfs.client.use.datanode.hostname" -> "true", "dfs.datanode.use.datanode.hostname" -> "true")
    val javaHome11: Map[String, String] = Map(
      "JAVA_HOME" -> "/usr/lib/jvm/java-11-amazon-corretto.x86_64",
      "HADOOP_OPTS" -> "-XX:+IgnoreUnrecognizedVMOptions"
    )
    val sparkDefaultEnvs: Map[String, String] = Map(
      "spark.executorEnv.JAVA_HOME" -> "/usr/lib/jvm/java-11-amazon-corretto.x86_64",
      "spark.driver.defaultJavaOptions" -> "-XX:+IgnoreUnrecognizedVMOptions",
      "spark.executor.defaultJavaOptions" -> "-XX:+IgnoreUnrecognizedVMOptions"
    )

    val sparkResourceAllocation: Map[String, String] = Map("maximizeResourceAllocation" -> "true")

    val hdfsConf: Configuration = Configuration.builder().classification("hdfs-site")
      .properties(hdfsProps.asJava).build()
    val hadoopJdk11CfgsInternal = Configuration.builder()
      .classification("export")
      .configurations(new util.ArrayList[Configuration]())
      .properties(javaHome11.asJava)
      .build()
    val hadoopJdk11CfgExternal = Configuration.builder().classification("hadoop-env")
      .configurations(hadoopJdk11CfgsInternal).build()

    val sparkJdk11External = Configuration.builder().classification("spark-env")
      .configurations(hadoopJdk11CfgsInternal).build()

    val sparkDefaultsJdk11External = Configuration.builder().classification("spark-defaults")
      .properties(sparkDefaultEnvs.asJava).build()

    val sparkMaximizeResourceAllocation = Configuration.builder().classification("spark")
      .properties(sparkResourceAllocation.asJava).build()

    val jobFlowInstancesConfig = JobFlowInstancesConfig.builder().ec2SubnetId(awsCfg.clusterVmSubnet)

    // set the ec2 key name if it's defined
    awsCfg.sshKeyName.foreach(sshKeyName => jobFlowInstancesConfig.ec2KeyName(sshKeyName))
    val autoTerminationPolicy = AutoTerminationPolicy.builder()
    // max idle period: 30 minutes in seconds
    autoTerminationPolicy.idleTimeout(60 * 30)

    val request: RunJobFlowRequest = RunJobFlowRequest.builder()
      .name(clusterName)
      .releaseLabel(EMR_VERSION)
      .applications(hive, spark, ganglia)
      .logUri(s"s3://nexla-emr-test-bucket/logs/$clusterName") // a URI in S3 for log files is required when debugging is enabled
      .serviceRole(awsCfg.clusterServiceRole)
      .autoTerminationPolicy(autoTerminationPolicy.build())
      .configurations(hdfsConf, hadoopJdk11CfgExternal, sparkJdk11External, sparkDefaultsJdk11External, sparkMaximizeResourceAllocation)
      .jobFlowRole(awsCfg.clusterEntityRole)
      .instances(
        jobFlowInstancesConfig
          .instanceCount(clusterConfig.mainNodes + clusterConfig.workerNodes)
          // this needs to be true because we may reuse the cluster between runs
          .keepJobFlowAliveWhenNoSteps(true)
          .masterInstanceType(toAmazonVmType(clusterConfig.mainNodeVCPU))
          .slaveInstanceType(toAmazonVmType(clusterConfig.workerNodeVCPU)).build()
      ).build()

    // secrets are also created at this stage
    val awsSecretMgr = secretsMgr.getOrElse(SecretsManagerClient.builder()
      .region(Region.of(awsCfg.region))
      .credentialsProvider(getAwsCreds(true))
      .build())
    logger.info("Verifying necessary secrets")
    val maybeExistingApiKey: Option[String] = Option(awsSecretMgr
      .getSecretValue(GetSecretValueRequest.builder().secretId(NEXLA_API_KEY_SECRET).build()).secretString())
    if (maybeExistingApiKey.isDefined) {
      logger.info("Existing Nexla Admin API Key found in AWS Secrets Manager, updating it to be sure it's actual")
      val updateKeyRequest = UpdateSecretRequest.builder()
        .secretId(NEXLA_API_KEY_SECRET)
        .secretString(adminApiKey)
        .description("Nexla Admin API Key (necessary for the Spark pipeline")
      awsSecretMgr.updateSecret(updateKeyRequest.build())
    } else {
      logger.info("No Nexla Admin API Keys found in AWS Secrets Manager, creating new entry")
      val createSecret = CreateSecretRequest.builder()
        .name(NEXLA_API_KEY_SECRET)
        .secretString(adminApiKey)
      awsSecretMgr.createSecret(createSecret.build())
    }
    logger.info("AWS Secrets Manager entry for Nexla Admin API Key is now created")

    logger.info(s"Cluster creation request prepared: [$request]")
    logger.info("Sending to AWS")
    val start = System.currentTimeMillis()
    try {
      val response = emrClient.runJobFlow(request)
      metricsHelper.incrementClusterSpawns()
      logger.info("CLUSTER CREATION REQUEST SENT: The cluster ID is " + response.jobFlowId())
      val describeReq: DescribeClusterRequest = DescribeClusterRequest.builder()
        .clusterId(response.jobFlowId()).build()

      // that may take up to 10 minutes worst case
      val result = emrStatusRetryer.call(() => emrClient.describeCluster(describeReq))
      val end = System.currentTimeMillis()
      metricsHelper.gaugeClusterCreation(cloudName, end - start)

      ClusterResponse(result.cluster().id(),
        List(ClusterNode("N/A", result.cluster().masterPublicDnsName(), result.cluster().masterPublicDnsName())),
        // it's safe to put empty lists here, as we don't have any jobs yet anyway
        List(), List(), List()
      )
    } catch {
      case e: Exception =>
        val end = System.currentTimeMillis()
        metricsHelper.gaugeClusterCreation(cloudName, end - start)
        throw new IllegalArgumentException("cannot create AWS EMR cluster", e)
      case t: Throwable =>
        val end = System.currentTimeMillis()
        metricsHelper.gaugeClusterCreation(cloudName, end - start)
        throw new IllegalArgumentException("unknown issue during cluster creation", t)
    }
  }

  override def stopCluster(clusterId: String): Unit = {
    val terminateRequest = TerminateJobFlowsRequest.builder().jobFlowIds(clusterId)
    val start = System.currentTimeMillis()
    val response = emrClient.terminateJobFlows(terminateRequest.build())
    logger.info(s"Cluster terminate request issued for cluster ID [$clusterId]")
    metricsHelper.incrementClusterTerminates()
    val end = System.currentTimeMillis()
    metricsHelper.gaugeClusterTermination(cloudName, end - start)
    logger.info(s"Response received: [$response]")
  }

  override def checkExistingCluster(clusterId: String): ClusterResponse = {
    val describeReq: DescribeClusterRequest = DescribeClusterRequest.builder()
      .clusterId(clusterId).build()
    val serviceResponse = emrClient.describeCluster(describeReq)

    val currentClusterState = ClusterState.fromValue(serviceResponse.cluster().status().stateAsString())
    val deadStates = util.EnumSet.of(ClusterState.TERMINATED, ClusterState.TERMINATING, ClusterState.TERMINATED_WITH_ERRORS)

    if (deadStates.contains(currentClusterState)) {
      throw new IllegalArgumentException("This EMR cluster is unusable, either terminating or terminated already")
    } else {
      val req = ListStepsRequest.builder()
        .clusterId(clusterId).build()
      val runningJobs = emrClient.listSteps(req).steps().asScala.map(step => toJobDef(step)).toList
      ClusterResponse(serviceResponse.cluster().id(),
        List(ClusterNode("N/A", serviceResponse.cluster().masterPublicDnsName(), serviceResponse.cluster().masterPublicDnsName())),
        List(),
        List(),
        runningJobs
      )
    }
  }

  override def trackUntilTerminalState(jobId: String, clusterId: String): JobDefinition = {
    val req = DescribeStepRequest.builder()
      .clusterId(clusterId)
      .stepId(jobId).build()
    logger.debug("Starting to track until terminal state: [job-id: {} cluster-id: {}]", jobId, clusterId)
    val start = System.currentTimeMillis()
    val resp = try {
      jobStatusRetryer.call( () => emrClient.describeStep(req) )
    } catch {
      case e: Exception =>
        logger.error(s"error during tracking jobId [$jobId] until terminal state", e)
        val end = System.currentTimeMillis()
        metricsHelper.gaugeTaskRunTime(end - start)
        throw e
    }
    val end = System.currentTimeMillis()
    metricsHelper.gaugeTaskRunTime(end - start)

    // can take a while
    logger.debug("Finished tracking until terminal state: [job-id: {} cluster-id: {}]", jobId, clusterId)
    AWSTools.toJobDef(resp.step())
  }

  private def doesObjectExist(s3Client: S3Client, bucket: String, key: String): Boolean = {
    val headObjectRequest: HeadObjectRequest = HeadObjectRequest.builder()
      .bucket(bucket)
      .key(key)
      .build()
    try {
      s3Client.headObject(headObjectRequest)
      true
    } catch {
      case _: NoSuchKeyException =>
        false
      case e: Exception =>
        logger.error(s"error during checking if object exists in S3 bucket [$bucket] with key [$key]", e)
        false
    }
  }

  private def copyFileIfNotExists(dstCloudJarLocation: String): Unit = {
    val uri: URI = new URI(dstCloudJarLocation)
    val bucketName = uri.getHost
    val key = uri.getPath.substring(1)

    val jarExists = doesObjectExist(destinationS3Client, bucketName, key)
    if (jarExists) {
      logger.warn(s"Target jar at specified path [$dstCloudJarLocation] already exists, checking its md5")
      val objMetadata = destinationS3Client.headObject(HeadObjectRequest.builder().bucket(bucketName).key(key).build())
      val currentFileMd5 = getMd5(new File(LOCAL_JAR_FILE_PATH))
      if (objMetadata.eTag().toLowerCase.equals(currentFileMd5)) {
        logger.info("md5 digest matches, using the existing file")
      } else {
        logger.warn("replacing it with a new file")
        val agentJarPath = LOCAL_JAR_FILE_PATH
        val putObjectRequest = PutObjectRequest.builder.bucket(bucketName).key(key).build()
        destinationS3Client.putObject(putObjectRequest, new File(agentJarPath).toPath)
        logger.info(s"File successfully uploaded to specified folder [$dstCloudJarLocation]")
      }
    } else {
      logger.info(s"Target jar does not exist at specified path [$dstCloudJarLocation], uploading it")
      // our docker image has this file built-in
      val agentJarPath = LOCAL_JAR_FILE_PATH
      val putObjectRequest = PutObjectRequest.builder.bucket(bucketName).key(key).build()
      destinationS3Client.putObject(putObjectRequest, new File(agentJarPath).toPath)
      logger.info(s"File successfully uploaded to specified folder [$dstCloudJarLocation]")
    }
  }

  private def getMd5(file: File): String = {
    val byteSource = com.google.common.io.Files.asByteSource(file)
    val hc = byteSource.hash(Hashing.md5)
    hc.toString.toLowerCase
  }

  override def runSparkSubmitStep(clusterId: String, sinkId: Int, partitionToHandle: String, nexlaEnv: String, runId: Long): List[JobDefinition] = {
    // check if our jar is already in target place
    // if not, upload it. remember, that it has to be built in advance, with built-in shaded admin API jar file.
    // after making sure file's there, send this request
    copyFileIfNotExists(awsCfg.cloudJarLocation)

    val stepConfigs: util.ArrayList[StepConfig] = new java.util.ArrayList[StepConfig]()
    val mainArgs = Seq(
      "spark-submit",
      "--class", "com.nexla.spark_agent.SparkAgentApp",
      "--jars", awsCfg.cloudJarLocation,
      "--conf", "spark.hadoop.fs.s3a.fast.upload.buffer=bytebuffer",
      "--conf", s"spark.jars.packages=$scalaLoggingJar,$guavaJar,$awsHadoopJar,$awsSdkBundleJar"
    )
    val args: java.util.ArrayList[String] = new util.ArrayList[String]()
    args.addAll(mainArgs.toList.asJava)

    if (Utils.isDebugMode(srcCfg)) {
      args.addAll(Seq(
        "--conf", "spark.executor.extraJavaOptions=-DdebugEnabled",
        "--conf", "spark.driver.extraJavaOptions=-DdebugEnabled"
      ).toList.asJava)
    }

    args.addAll(Seq(
      awsCfg.cloudJarLocation, String.valueOf(sinkId), partitionToHandle, nexlaEnv, String.valueOf(runId)
    ).toList.asJava)

    val sparkStepConf: HadoopJarStepConfig = HadoopJarStepConfig.builder()
      .jar("command-runner.jar")
      .args(args.asScala: _*).build()

    val partitionIsInt = Try(partitionToHandle.toInt).isSuccess

    val sparkStep: StepConfig = StepConfig.builder()
      .name(s"Nexla sink [$sinkId], source ${if (partitionIsInt) "max src files per run" else "partition" } [$partitionToHandle]")
      .actionOnFailure("CONTINUE")
      .hadoopJarStep(sparkStepConf).build()
    stepConfigs.add(sparkStep)

    val req: AddJobFlowStepsRequest.Builder = AddJobFlowStepsRequest.builder()
      .jobFlowId(clusterId)
      .steps(stepConfigs)
    emrClient.addJobFlowSteps(req.build())
    // and now list what's running there
    val listJobsRunning = ListStepsRequest.builder()
      .clusterId(clusterId)
    emrClient.listSteps(listJobsRunning.build()).steps().asScala.map(step => toJobDef(step)).toList
  }

  private val scalaLoggingJar: String = "com.typesafe.scala-logging:scala-logging_2.12:3.9.3"
  private val guavaJar: String = "com.google.guava:guava:31.1-jre"
  private val awsHadoopJar: String = "org.apache.hadoop:hadoop-aws:3.2.2"
  private val awsSdkBundleJar: String = "com.amazonaws:aws-java-sdk-bundle:1.12.170"
  private val LOCAL_JAR_FILE_PATH: String = if (Utils.devMode()) {
    System.getProperty("user.dir") + "/cloud-job-connector/target/nexla-spark-agent.jar"
  } else {
    "/nexla-spark-agent.jar"
  }
}

object AWSCloudProvider {
  private val defaultSleep: Int = 30 * 1000
  private val defaultMaxApiRetries: Int = 256

  def apply(metricsHelper: MetricsHelper, logTag: String, cfg: BaseAuthConfig,
            srcCfg: Map[String, AnyRef], extraCfg: Map[String, AnyRef], apiKey: String): AWSCloudProvider = {
    this (metricsHelper, logTag, cfg, srcCfg, extraCfg, apiKey,
      None, None, None // empty options for the possible mocks
    )
  }

  def apply(metricsHelper: MetricsHelper,
            logTag: String,
            cfg: BaseAuthConfig,
            srcCfg: Map[String, AnyRef],
            extraCfg: Map[String, AnyRef],
            apiKey: String,
            emrClientArg: Option[EmrClient] = None,
            secretsMgrArg: Option[SecretsManagerClient] = None,
            s3ClientArg: Option[S3Client] = None,
            sleepTimeMilli: Int = defaultSleep,
            maxApiRetries: Int = defaultMaxApiRetries): AWSCloudProvider = {
    new AWSCloudProvider(metricsHelper, logTag, cfg, srcCfg, extraCfg, emrClientArg, secretsMgrArg, s3ClientArg, sleepTimeMilli, maxApiRetries, apiKey)
  }
}
