package com.nexla.cloud.job.connector.pipeline

import com.databricks.sdk.WorkspaceClient
import com.databricks.sdk.core.{ConfigLoader, DatabricksConfig}
import com.databricks.sdk.service.workspace.{GetSecretRequest, PutSecret, SecretScope}
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import com.nexla.admin.client.{DataCredentials, DataSink, DataSource}
import com.nexla.cloud.job.connector.cloud.{DatabricksCloudSpecificCfg, DatabricksOidcAuth, DatabricksTokenAuth}
import com.nexla.common.{ConnectionType, NexlaDataCredentials}
import com.nexla.connector.config.file.{AWSAuthConfig, NexlaAWSCredentialsProvider}
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.sc.util.StrictNexlaLogging
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient
import software.amazon.awssdk.services.secretsmanager.model.{CreateSecretRequest, GetSecretValueRequest, UpdateSecretRequest}

import scala.jdk.CollectionConverters.mapAsScalaMapConverter
import scala.collection.JavaConverters._
import scala.util.{Failure, Success, Try}

trait ClusterSecretClient {
  def getSecret(key: String, additionalScope: String = ""): String
  def updateSecret(key: String, value: String, additionalScope: String = "")
  def createSecret(key: String, value: String, additionalScope: String = "")
}

class ClusterSecretManager(decryptKey: String, clusterProvider: String, storageProvider: ConnectionType,
                           clusterId: String, authConfig: BaseAuthConfig) extends StrictNexlaLogging {
  val secretManager: ClusterSecretClient = clusterProvider.toLowerCase match {
    case "aws" => new ClusterSecretClient {
      val awsCredentials: AwsCredentialsProvider =
        NexlaAWSCredentialsProvider.getCredentialsProvider(authConfig.asAWS())
      val awsSecretMgr: SecretsManagerClient = SecretsManagerClient.builder()
        .credentialsProvider(awsCredentials)
        .build()

      override def getSecret(key: String, additionalScope: String): String = {
        val valueRequest = GetSecretValueRequest.builder().secretId(key)
        val valueResponse = awsSecretMgr.getSecretValue(valueRequest.build())
        val secret = valueResponse.secretString()
        secret
      }

      override def createSecret(key: String, value: String, additionalScope: String = ""): Unit = {
        awsSecretMgr.createSecret(CreateSecretRequest.builder().name(key).secretString(value).build())
      }

      override def updateSecret(id: String, value: String, additionalScope: String = ""): Unit = {
        awsSecretMgr.updateSecret(UpdateSecretRequest.builder().secretId(id).secretString(value).build())
      }
    }
    case "databricks" => new ClusterSecretClient {
      val cloudCfg: DatabricksCloudSpecificCfg = DatabricksCloudSpecificCfg.apply(authConfig.originals().asScala.toMap)
      val bricksCfg: DatabricksConfig = ConfigLoader.getDefault.setHost(cloudCfg.host)
      cloudCfg.auth match {
        case ta: DatabricksTokenAuth => bricksCfg.setToken(ta.token)
        case oidc: DatabricksOidcAuth =>
        bricksCfg.setClientId(oidc.clientId).setClientSecret(oidc.clientSecret)
      }
      val workspaceClient: WorkspaceClient = new WorkspaceClient(bricksCfg)

      override def getSecret(key: String, additionalScope: String = ""): String = {
        val req: GetSecretRequest = new GetSecretRequest().setKey(key).setScope(additionalScope)
        Try(workspaceClient.secrets().getSecret(req)) match {
          case Failure(exception) => throw exception
          case Success(value) => value.getValue
        }
      }

      override def createSecret(key: String, value: String, additionalScope: String): Unit = {
        if (additionalScope.isEmpty) {
          throw new IllegalArgumentException("Scope is required for databricks secrets, typically the cluster ID")
        }
        createScope(additionalScope)
        val req: PutSecret = new PutSecret
        req.setKey(key).setScope(additionalScope).setStringValue(value)
        workspaceClient.secrets().putSecret(req)
      }

      override def updateSecret(key: String, value: String, additionalScope: String): Unit = {
        createSecret(key, value, additionalScope)
      }

      private def createScope(scope: String): Unit = {
        val scopes = Option(workspaceClient.secrets().listScopes())
        val existingScope = scopes.map(scopes => scopes.asScala.toList).flatMap(scopeList => scopeList.find(secret => clusterId.equals(secret.getName)))
        if (existingScope.isEmpty) {
          logger.info(s"secrets scope for the cluster with ID $clusterId not found, creating it")
          workspaceClient.secrets().createScope(clusterId)
        }
      }
    }
  }

  def initializeClusterSecrets(src: DataSource, sink: DataSink): Unit = {
    val srcCreds: java.util.Map[String, String] = NexlaDataCredentials.getCreds(decryptKey, src.getDataCredentials.getCredentialsEnc, src.getDataCredentials.getCredentialsEncIv)

    storageProvider match {
      case e if e == ConnectionType.S3 =>
        val awsCfg = new AWSAuthConfig(srcCreds, -1)
        setOrUpdateSecret("aws_access_key_id", awsCfg.accessKeyId, clusterId)
        setOrUpdateSecret("aws_secret_access_key", awsCfg.secretKey, clusterId)
      case _ =>
        // generic case we can just set the entire creds map
        val objMapper: ObjectMapper = new ObjectMapper()
        objMapper.registerModule(DefaultScalaModule)
        val ndc: Map[String, String] = unpackCredentials(src.getDataCredentials)
        val dstNdc: Map[String, String] = unpackCredentials(sink.getDataCredentials)
        setOrUpdateSecret("nexla.credentials.src." + src.getId, objMapper.writeValueAsString(ndc), clusterId)
        setOrUpdateSecret("nexla.credentials.sink." + sink.getId, objMapper.writeValueAsString(dstNdc), clusterId)
    }
  }

  private def setOrUpdateSecret(key: String, value: String, additionalScope: String = ""): Unit = {
    val existingSecret: Option[String] = Try(secretManager.getSecret(key)) match {
      case Failure(_) => Option.empty
      case Success(value) => Option(value)
    }

    existingSecret match {
      case Some(existing) => {
        if (!existing.equals(value)) {
          secretManager.updateSecret(key, value, additionalScope)
        }
      }
      case None => {
        secretManager.createSecret(key, value, additionalScope)
      }
    }
  }

  private def unpackCredentials(creds: DataCredentials): Map[String, String] = {
    NexlaDataCredentials.getCreds(decryptKey, creds.getCredentialsEnc, creds.getCredentialsEncIv).asScala.toMap
  }
}
