package com.nexla.parser.pdf.strategy.textract.table;

import software.amazon.awssdk.services.textract.model.Block;
import com.nexla.parser.pdf.strategy.textract.Blocks;
import com.nexla.parser.pdf.strategy.textract.data.TextractNexlaCell;
import com.nexla.parser.pdf.strategy.textract.data.TextractNexlaTableBlock;
import com.nexla.parser.pdf.strategy.textract.data.TextractNexlaTuple;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.nexla.parser.pdf.strategy.textract.Blocks.BlockType.MERGED_CELL;

public class AggressiveTableProcessor extends SimpleTableProcessor {
    private final Blocks blocks;

    public AggressiveTableProcessor(Blocks blocks) {
        this.blocks = blocks;
    }

    @Override
    public List<TextractNexlaTableBlock> process(List<TextractNexlaTableBlock> tables) {
        tables = super.process(tables);

        tables.sort(Comparator.comparingInt(x -> x.getHeader().getPageNumber()));

        // first try to fill continuation of merged cells on next row
        fillContinuesCellValuesAcrossPages(tables);
        // merge tables with the same header
        mergeTables(tables);

        return tables;
    }

    private void mergeTables(List<TextractNexlaTableBlock> tables) {
        // merge tables with the same header
        for (int i = 1; i < tables.size(); ) {
            TextractNexlaTableBlock previous = tables.get(i - 1);
            TextractNexlaTableBlock current = tables.get(i);

            if (current.getHeader().getCells().size() != previous.getHeader().getCells().size()) {
                i++;
                continue;
            }

            // all header columns are the same
            boolean allMatch = sameCellValues(current, previous);
            if (!allMatch) {
                i++;
                continue;
            }

            previous.getTuples().addAll(current.getTuples());
            tables.remove(i);
        }
    }

    private static boolean sameCellValues(TextractNexlaTableBlock lhs, TextractNexlaTableBlock rhs) {
        boolean allMatch = true;
        for (int j = 0; j < lhs.getHeader().getCells().size(); j++) {
            TextractNexlaCell pCell = rhs.getHeader().getCells().get(j);
            TextractNexlaCell cCell = lhs.getHeader().getCells().get(j);

            if (!Objects.equals(pCell.getValue(), cCell.getValue())) {
                allMatch = false;
                break;
            }
        }
        return allMatch;
    }

    private void fillContinuesCellValuesAcrossPages(List<TextractNexlaTableBlock> tables) {
        for (int i = 1; i < tables.size(); i++) {
            TextractNexlaTableBlock previous = tables.get(i - 1);
            TextractNexlaTableBlock current = tables.get(i);

            if (current.getHeader().getCells().size() != previous.getHeader().getCells().size()) {
                continue;
            }

            // all header columns are the same
            boolean allMatch = sameCellValues(current, previous);

            if (!allMatch) {
                continue;
            }

            TextractNexlaTuple lastRow = previous.getTuples().get(previous.getTuples().size() - 1);
            TextractNexlaTuple firstRow = current.getTuples().get(0);

            for (int j = 0; j < lastRow.getCells().size(); j++) {
                TextractNexlaCell pCell = lastRow.getCells().get(j);
                TextractNexlaCell cCell = firstRow.getCells().get(j);

                if (pCell == null || cCell == null) {
                    continue;
                }

                if (pCell.getValue() == null || pCell.getValue().isBlank()) {
                    continue;
                }

                if (cCell.getValue() != null && !cCell.getValue().isBlank()) {
                    continue;
                }

                Block pBlock = blocks.get(pCell.getId());
                if (blocks.parentsOf(pBlock, MERGED_CELL::isTypeOf).isEmpty()) {
                    continue;
                }

                Block cBlock = blocks.get(cCell.getId());
                List<Block> cParents = blocks.parentsOf(cBlock, MERGED_CELL::isTypeOf);
                if (cParents.isEmpty()) {
                    continue;
                }

                Set<String> mergedCellChildrenOfCurrentTable = cParents
                        .stream()
                        .flatMap(x -> blocks.relationshipsOf(x).stream())
                        .map(Block::id)
                        .collect(Collectors.toSet());


                cCell.setValue(pCell.getValue());
                // propagate the value to the rest of the cells in the rows below
                for (int k = 1; k < current.getTuples().size(); k++) {
                    TextractNexlaTuple row = current.getTuples().get(k);
                    TextractNexlaCell cell = row.getCells().get(j);

                    if (!mergedCellChildrenOfCurrentTable.contains(cell.getId())) {
                        continue;
                    }

                    if (cell.getValue() != null && !cell.getValue().isBlank()) {
                        continue;
                    }

                    cell.setValue(pCell.getValue());
                }
            }
        }
    }
}
