package com.nexla.parser.pdf.strategy.textract;

import com.google.common.collect.Iterables;
import com.nexla.common.ResourceType;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.parse.BatchParser;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.file.NexlaAWSCredentialsProvider;
import com.nexla.parser.pdf.PdfParser;
import com.nexla.parser.pdf.strategy.BatchingStrategy;
import com.nexla.parser.pdf.strategy.parse.PDText;
import com.nexla.parser.pdf.strategy.parse.util.Lists;
import com.nexla.parser.pdf.strategy.textract.data.TextractNexlaCell;
import com.nexla.parser.pdf.strategy.textract.data.TextractNexlaTableBlock;
import com.nexla.parser.pdf.strategy.textract.data.TextractNexlaTuple;
import com.nexla.parser.pdf.strategy.textract.table.AggressiveTableProcessor;
import com.nexla.parser.pdf.strategy.textract.table.SimpleTableProcessor;
import com.nexla.parser.pdf.strategy.textract.table.TableProcessor;
import com.nexla.parser.pdf.strategy.textract.table.VeryAggressiveTableProcessor;
import lombok.Data;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.io.IOUtils;
import org.apache.pdfbox.io.RandomAccessBufferedFileInputStream;
import org.apache.pdfbox.pdfparser.PDFParser;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.core.async.AsyncRequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.textract.TextractAsyncClient;
import software.amazon.awssdk.services.textract.model.*;

import java.awt.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.time.Duration;
import java.util.List;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.nexla.admin.config.ConfigUtils.enrichWithCredentialsStore;
import static com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix;
import static com.nexla.parser.pdf.strategy.textract.Blocks.BlockType.*;

public class TextractStrategy implements BatchingStrategy {
    private static final Logger LOGGER = LoggerFactory.getLogger(TextractStrategy.class);

    private static final int MIN_JOB_WAIT_TIME = 1000 * 60 * 5;
    private static final int MAX_JOB_WAIT_TIME = 1000 * 60 * 20;

    private static final String TEMP_LOCAL_FILE_SUFFIX = ".pdf";
    private static final int TEXTRACT_PAGE_LIMIT = 3000;

    private final Logger logger;

    private final AWSAuthConfig textractEnabledAwsAuthConfig;
    private final TextractAsyncClient textractAsyncClient;
    private final boolean text;
    private final boolean tables;
    private final String _prefix;
    private final String textractS3Destination;
    private final String tableProcessorOption;
    private final Scheduler customScheduler;
    private final int parallelismLevel;

    public TextractStrategy(Map<String, String> config, boolean text, boolean tables) {
        this.text = text;
        this.tables = tables;

        this.tableProcessorOption = config.getOrDefault(PdfParser.Options.STRATEGY_AUTO_1_TABLE_PROCESSOR, null);

        FileSourceConnectorConfig fileSourceConnectorConfig = createInternalConfig(
                enrichWithCredentialsStore(config, FileSourceConnectorConfig.configDef())
        );
        textractEnabledAwsAuthConfig = fileSourceConnectorConfig.textractAwsAuthConfig;
        textractS3Destination = fileSourceConnectorConfig.nexlaTextractS3Destination;
        textractAsyncClient = getTextractClient(textractEnabledAwsAuthConfig);

        this._prefix = fileSourceConnectorConfig.sourceId + "/" + UUID.randomUUID();
        this.parallelismLevel = Integer.parseInt(config.getOrDefault(PdfParser.Options.TEXTRACT_PARALLELISM, "8"));
        this.customScheduler = Schedulers.newParallel("textract-parallel-scheduler", parallelismLevel);
        this.logger = new NexlaLogger(LOGGER, new NexlaLogKey(ResourceType.SOURCE, fileSourceConnectorConfig.sourceId, Optional.empty()));
    }

    private static FileSourceConnectorConfig createInternalConfig(Map<String, String> config) {
        return new FileSourceConnectorConfig(new HashMap<>(config));
    }

    @SneakyThrows
    public Stream<com.nexla.parser.pdf.strategy.model.Block> executeBatch(List<BatchParser.NexlaStreamDescriptor> iss) {
        return Flux.fromStream(iss.stream())
                .parallel(parallelismLevel)
                .runOn(customScheduler)
                .flatMap(is -> {
                    return Flux.fromStream(execute(is.getIs()))
                            .<com.nexla.parser.pdf.strategy.model.Block>map(block -> new BatchedBlock(block, is));
                })
                .sequential()
                .toStream();
    }

    @SneakyThrows
    @Override
    public Stream<com.nexla.parser.pdf.strategy.model.Block> execute(InputStream inputStream) {
        File localFileCopy = Files.createTempFile("temp", ".pdf").toFile();
        try (FileOutputStream out = new FileOutputStream(localFileCopy)) {
            IOUtils.copy(inputStream, out);
        }

        PDFParser parser = new PDFParser(new RandomAccessBufferedFileInputStream(localFileCopy));
        parser.parse();
        PDDocument document = parser.getPDDocument();

        List<Context> contexts = new ArrayList<>();
        if (document.getNumberOfPages() > TEXTRACT_PAGE_LIMIT) {
            logger.warn("Document has more than {} pages, going to split it", TEXTRACT_PAGE_LIMIT);
            for (List<PDPage> pageList : Iterables.partition(document.getPages(), TEXTRACT_PAGE_LIMIT)) {
                try {
                    Context context = new Context();
                    context.file = File.createTempFile("temp-part", ".pdf");
                    context.pageMapping = new HashMap<>();

                    PDDocument newDocument = new PDDocument();
                    for (PDPage page : pageList) {
                        newDocument.importPage(page);

                        int pageNumber = Lists.indexOf(document.getPages(), page::equals) + 1;
                        context.pageMapping.put(pageNumber, newDocument.getNumberOfPages());
                    }

                    context.pages = newDocument.getNumberOfPages();

                    newDocument.save(context.file);
                    newDocument.close();

                    contexts.add(context);
                } catch (Throwable e) {
                    logger.error("Error splitting PDF", e);
                }
            }
        } else {
            Context context = new Context();
            context.file = localFileCopy;
            context.pageMapping = new HashMap<>();
            context.pages = document.getNumberOfPages();

            for (int i = 0; i < document.getNumberOfPages(); i++) {
                context.pageMapping.put(i + 1, i + 1);
            }

            document.close();

            contexts.add(context);
        }


        return Flux.fromIterable(contexts)
                .parallel(parallelismLevel)
                .runOn(customScheduler)
                .flatMap(context -> {
                    context.bucketPrefix = toBucketPrefix(textractS3Destination + "/" + _prefix, true);

                    return upload(context.file, textractEnabledAwsAuthConfig, UUID.randomUUID() + TEMP_LOCAL_FILE_SUFFIX, context.bucketPrefix)
                            .map(x -> {
                                context.s3FilePath = x;
                                return context;
                            });

                })
                .flatMap(context -> {
                    return initiate(textractAsyncClient, context.s3FilePath, context.bucketPrefix.bucket)
                            .map(jobId -> {
                                context.jobId = jobId;
                                return context;
                            });
                })
                .flatMap(this::fetchJobResult)
                .sequential()
                .flatMap(x -> Flux.fromStream(processAnalysisResult(x)))
                .toStream()
                .onClose(() -> {
                    contexts.forEach(c -> {
                        if (c.file != null) {
                            try {
                                c.file.delete();
                            } catch (Throwable e) {
                                logger.error("Error deleting temp file", e);
                            }
                        }
                    });
                });
    }

    @Data
    private static final class Context {
        private File file;
        private int pages;
        private int firstPageNr;
        private Map<Integer, Integer> pageMapping;

        private AWSAuthConfig.BucketPrefix bucketPrefix;
        private String s3FilePath;

        private String jobId;

        public List<GetDocumentAnalysisResponse> analysisResults;
    }

    private StreamEx<com.nexla.parser.pdf.strategy.model.Block> processAnalysisResult(Context context) {
        List<GetDocumentAnalysisResponse> analysisResults = context.analysisResults;

        DocumentMetadata meta = analysisResults.get(0).documentMetadata();
        logger.info("Processing Textract analysis result: {}", meta);

        List<Block> rawBlocks = analysisResults
                .stream()
                .filter(x -> x.blocks() != null)
                .flatMap(x -> x.blocks().stream())
                .collect(Collectors.toList());

        if (rawBlocks.isEmpty()) {
            logger.info("No blocks found in Textract analysis result");
            return StreamEx.empty();
        }

        Blocks blocks = new Blocks(rawBlocks);

        Function<Integer, Integer> pageRemap = pageNr -> context.pageMapping.getOrDefault(pageNr, pageNr);

        StreamEx<com.nexla.parser.pdf.strategy.model.Block> textBlocks = extractText(blocks, pageRemap);
        StreamEx<com.nexla.parser.pdf.strategy.model.Block> tables = extractTablesAndForms(blocks, pageRemap);

        if (this.tables && this.text) {
            return textBlocks.append(tables);
        }

        if (this.text) {
            return textBlocks;
        }

        if (this.tables) {
            return tables;
        }

        return textBlocks;
    }

    private StreamEx<com.nexla.parser.pdf.strategy.model.Block> extractText(Blocks blocks, Function<Integer, Integer> pageMapping) {
        return EntryStream.of(
                        StreamEx.of(blocks.getBlocks())
                                .filter(block -> block.blockType().equals("LINE"))
                                .groupingBy(Block::page)
                )
                .map(block -> {
                    String text1 = block.getValue()
                            .stream()
                            .map(Block::text)
                            .collect(Collectors.joining("\n"));
                    return new PDText(text1, new Rectangle(0, 0, 0, 0), pageMapping.apply(block.getKey()));
                });
    }

    private static Rectangle toRect(Block block) {
        BoundingBox bb = block.geometry().boundingBox();

        return new Rectangle(
                bb.left().intValue(), bb.top().intValue(),
                bb.width().intValue(), bb.height().intValue()
        );
    }

    private StreamEx<com.nexla.parser.pdf.strategy.model.Block> extractTablesAndForms(Blocks blocks, Function<Integer, Integer> pageMapping) {
        List<Block> tableBlocks = new ArrayList<>();
        for (Block block : blocks.getBlocks()) {
            String blockType = block.blockTypeAsString();
            if (blockType.equals("TABLE")) {
                tableBlocks.add(block);
            }
        }
        logger.info("table blocks size: {}", tableBlocks.size());

        tableBlocks.sort(Comparator.comparingInt(Block::page));

        List<TextractNexlaTableBlock> tables = new ArrayList<>();
        for (int index = 0; index < tableBlocks.size(); index++) {
            Block tableBlock = tableBlocks.get(index);
            if (tableBlock.confidence() < 95) {
                continue;
            }

            TextractNexlaTableBlock data = parseTableKVData(blocks, tableBlock, pageMapping);
            if (data == null) {
                continue;
            }

            tables.add(data);
        }

        TableProcessor tableProcessor = Optional.ofNullable(tableProcessorOption)
                .<TableProcessor>map(x -> {
                    switch (x) {
                        case "aggressive":
                            return new AggressiveTableProcessor(blocks);
                        case "very-aggressive":
                            return new VeryAggressiveTableProcessor(blocks);
                        default:
                            return new SimpleTableProcessor();
                    }
                })
                .orElse(new SimpleTableProcessor());

        return StreamEx.of(
                tableProcessor.process(tables)
        );
    }

    @SneakyThrows
    private Mono<String> upload(File inputStream, AWSAuthConfig nexlaDefaultAwsAuthConfig, String fileName, AWSAuthConfig.BucketPrefix bucketPrefix) {
        try {
            S3AsyncClient s3Client = createS3ClientFromCreds(nexlaDefaultAwsAuthConfig, nexlaDefaultAwsAuthConfig.region);

            String relativePath = bucketPrefix.prefix + fileName;
            logger.info("Uploading file: {}", relativePath);
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketPrefix.bucket)
                    .key(relativePath)
                    .build();
            Mono<PutObjectResponse> putObjectResponse = Mono.fromFuture(s3Client.putObject(putObjectRequest, AsyncRequestBody.fromFile(inputStream.toPath())));
            logger.info("Uploaded file: {}", putObjectResponse);

            return Mono.just(relativePath);
        } catch (Throwable e) {
            logger.error("Error uploading file to S3", e);
            return Mono.error(e);
        }
    }

    public S3AsyncClient createS3ClientFromCreds(AWSAuthConfig authConfig, String region) {
        AwsCredentialsProvider credentialsProvider = NexlaAWSCredentialsProvider.getCredentialsProvider(authConfig);

        if ((region == null) && authConfig.region != null) {
            region = authConfig.region;
        }
        if (region == null) {
            region = "us-east-1";
        }

        String finalRegion = region;
        S3AsyncClient client = S3AsyncClient.builder()
                .credentialsProvider(credentialsProvider)
                .region(Region.of(finalRegion))
                .crossRegionAccessEnabled(true)
                .build();

        logger.info("Created S3 client: {}", client);
        return client;
    }

    private TextractAsyncClient getTextractClient(AWSAuthConfig awsAuthConfig) {
        AwsCredentialsProvider awsCredentialsProvider = NexlaAWSCredentialsProvider.getCredentialsProvider(awsAuthConfig);

        //Set AWS Credentials to use Textract
        return TextractAsyncClient.builder()
                .region(Region.of(awsAuthConfig.region))
                .credentialsProvider(awsCredentialsProvider)
                .build();
    }

    /**
     * Start Textract job of PDF processing
     *
     * @return - Textract jobId
     */
    private Mono<String> initiate(TextractAsyncClient client, String filePath, String bucket) {
        // Start document text detection
        StartDocumentAnalysisRequest request = StartDocumentAnalysisRequest.builder()
                .documentLocation(
                        DocumentLocation.builder()
                                .s3Object(
                                        S3Object.builder()
                                                .bucket(bucket)
                                                .name(filePath)
                                                .build()
                                )
                                .build()
                )
                .featureTypesWithStrings("TABLES", "FORMS")
                .build();

        return Mono.create(sink -> {
            logger.info("Starting Textract job for file: {}", filePath);
            client.startDocumentAnalysis(request).whenComplete((startDocumentAnalysisResponse, exception) -> {
                if (exception != null) {
                    logger.error("Error starting Textract job", exception);
                    sink.error(exception);
                } else {
                    logger.info("Started Textract job with jobId: {}", startDocumentAnalysisResponse.jobId());
                    sink.success(startDocumentAnalysisResponse.jobId());
                }
            });
        });
    }

    /**
     * Retrieve Textract Job results
     *
     * @param context - context with jobId
     * @return - document analysis result
     */
    private Mono<Context> fetchJobResult(Context context) {
        int maxWaitMillis = Math.min(
                Math.max(context.pages * 1000, MIN_JOB_WAIT_TIME), // approx 1 seconds per page, but not less than 5 min)
                MAX_JOB_WAIT_TIME // max wait time 20 minutes
        );

        int queryInterval = 5_000;
        int tries = (int) Math.ceil(maxWaitMillis * 1d / queryInterval);

        logger.info("Waiting for Textract job results for jobId: {} (max {} ms, {} tries with {}ms query interval, pages: {})", context.jobId, maxWaitMillis, tries, queryInterval, context.pages);

        return Flux.interval(Duration.ofMillis(queryInterval))
                .onBackpressureDrop()
                .concatMap(count -> pollForResult(context.jobId, Optional.empty()))
                .take(tries, true)
                .map(r -> {
                    logger.info("Job {} status: {}", context.jobId, r.jobStatus());
                    return r;
                })
                .takeUntil(r -> r.jobStatusAsString().equals("SUCCEEDED") || r.jobStatusAsString().equals("FAILED"))
                .filter(r -> r.jobStatusAsString().equals("SUCCEEDED"))
                .take(1)
                .map(x -> fetchAllIfNeeded(context.jobId, x))
                .next()
                .map(rs -> {
                    logger.info("Fetched all Textract job results for jobId: {}", context.jobId);

                    context.analysisResults = rs;

                    return context;
                });
    }

    private List<GetDocumentAnalysisResponse> fetchAllIfNeeded(String jobId, GetDocumentAnalysisResponse x) {
        List<GetDocumentAnalysisResponse> results = new ArrayList<>();
        results.add(x);
        if (x.nextToken() == null) {
            return results;
        }

        GetDocumentAnalysisResponse local = x;
        do {
            local = pollForResult(jobId, Optional.of(local.nextToken())).block();
            results.add(local);
        } while (local.nextToken() != null);

        return results;
    }

    private Mono<GetDocumentAnalysisResponse> pollForResult(String jobId, Optional<String> nextToken) {
        return Mono.create(sink -> {
            logger.info("Fetching Textract job result for jobId: {}", jobId);
            GetDocumentAnalysisRequest getAnalysisRequest = GetDocumentAnalysisRequest.builder()
                    .jobId(jobId)
                    .nextToken(nextToken.orElse(null))
                    .build();

            textractAsyncClient.getDocumentAnalysis(getAnalysisRequest).whenComplete((result, exception) -> {
                if (exception != null) {
                    logger.error("Error fetching Textract job result", exception);
                    sink.error(exception);
                } else {
                    sink.success(result);
                }
            });
        });
    }

    private TextractNexlaTuple mkHeader(Blocks blocks, Block rawTableBlock, Function<Integer, Integer> pageMapping) {
        TextractNexlaTuple header = new TextractNexlaTuple();
        header.setPageNumber(pageMapping.apply(rawTableBlock.page()));

        List<Block> rawHeaderBlocks = blocks.relationshipsOf(rawTableBlock, x -> !MERGED_CELL.isTypeOf(x) && Blocks.EntityType.COLUMN_HEADER.isTypeOf(x));
        if (!rawHeaderBlocks.isEmpty()) {
            TextractNexlaCell[] headerCells = new TextractNexlaCell[rawHeaderBlocks.stream().mapToInt(Block::columnIndex).max().orElse(0)];
            for (Block headerCell : rawHeaderBlocks) {
                TextractNexlaCell cell = headerCells[headerCell.columnIndex() - 1];
                if (cell == null) {
                    cell = headerCells[headerCell.columnIndex() - 1] = new TextractNexlaCell(headerCell.id(), "");
                }

                String cellText = cellText(headerCell, blocks);
                // this is algorithmically ugly, but the point is that we can have merged cells in multirow header,
                // and we want to concatenate all values from all rows for the particular column.
                // The problem is that a cell may be a part of vertical merged cell, we need to get the text only once.
                // (cellText method will get the text from the merged cell if it is a part of it)
                if (!cell.getValue().trim().equals(cellText.trim())) {
                    String str = cell.getValue().isEmpty()
                            ? cellText
                            : " " + cellText;

                    cell.setValue(cell.getValue() + str);
                }
            }

            for (int i = 0; i < headerCells.length; i++) {
                if (headerCells[i] == null) {
                    // this is for case when table has empty column name.
                    // textract just doesn't mark such cell as COLUMN_HEADER even if other cells are COLUMN_HEADERs and next cell is just column index + 1
                    // so, header cells are (by column index): [1, 2, 3, 5]
                    // we need to fill the gap with empty cells
                    headerCells[i] = new TextractNexlaCell(null, "(empty column " + (i + 1) + ")");
                }
            }

            header.getCells().addAll(Arrays.asList(headerCells));

        }

        return header;
    }

    private TextractNexlaTableBlock parseTableKVData(Blocks blocks, Block rawTableBlock, Function<Integer, Integer> pageMapping) {
        TextractNexlaTuple header = mkHeader(blocks, rawTableBlock, pageMapping);

        Map<Integer, List<Block>> rowBlocks = blocks.relationshipsOf(rawTableBlock, x -> CELL.isTypeOf(x) && !Blocks.EntityType.COLUMN_HEADER.isTypeOf(x))
                .stream()
                .sorted(Comparator.comparingInt(Block::rowIndex).thenComparing(Block::columnIndex))
                .collect(Collectors.groupingBy(Block::rowIndex));

        List<TextractNexlaTuple> rows = new ArrayList<>();
        for (Map.Entry<Integer, List<Block>> rowEntry : rowBlocks.entrySet()) {
            TextractNexlaTuple tuple = new TextractNexlaTuple();
            tuple.setPageNumber(pageMapping.apply(rawTableBlock.page()));

            int cellNum = rowEntry.getValue().stream().mapToInt(Block::columnIndex).max().orElse(0);
            cellNum = Math.max(cellNum, header.getCells().size());

            TextractNexlaCell[] cells = new TextractNexlaCell[cellNum];
            for (Block colEntry : rowEntry.getValue()) {
                cells[colEntry.columnIndex() - 1] = new TextractNexlaCell(colEntry.id(), cellText(colEntry, blocks));
            }

            for (int i = 0; i < cells.length; i++) {
                if (cells[i] == null) {
                    cells[i] = new TextractNexlaCell(null, "");
                }
            }

            tuple.getCells().addAll(Arrays.asList(cells));

            rows.add(tuple);
        }

        if (rows.isEmpty()) {
            return null;
        }
        if (header.getCells().isEmpty()) {
            header = null;
        }

        TextractNexlaTableBlock newTableObj = new TextractNexlaTableBlock();
        newTableObj.setId(rawTableBlock.id());
        newTableObj.setHeader(header);
        newTableObj.setTuples(rows);

        return newTableObj;
    }

    private String cellText(Block cell, Blocks blocks) {
        // this cell may be a part of a merged cell
        // if so, we need to get the text from the merged cell
        List<Block> mergedCells = blocks.parentsOf(cell, Blocks.BlockType.MERGED_CELL::isTypeOf);
        if (mergedCells.isEmpty()) {
            return getTextFromTextractBlock(blocks, cell);
        }

        if (mergedCells.size() > 1) {
            logger.warn("Found that cell belongs to several merged cells, this is NOT OK. Using the first one to get job done.");
        }

        Block mergedCell = mergedCells.stream().max(Comparator.comparingDouble(Block::confidence)).orElseThrow();

        String text = blocks.traverse(mergedCell, Blocks.BlockType.WORD::isTypeOf)
                .stream().map(Block::text)
                .collect(Collectors.joining(" "));

        return text.replaceAll("\\s+", " ").trim();
    }

    static String getTextFromTextractBlock(Blocks blocks, Block root) {
        String text = blocks.traverse(root, x -> WORD.isTypeOf(x) || SELECTION_ELEMENT.isTypeOf(x))
                .stream()
                .map(x -> {
                    if (WORD.isTypeOf(x)) {
                        return x.text();
                    }

                    if (SELECTION_ELEMENT.isTypeOf(x)) {
                        SelectionStatus selectionStatus = x.selectionStatus();
                        if (selectionStatus == SelectionStatus.SELECTED) {
                            return "X";
                        } else if (selectionStatus == SelectionStatus.NOT_SELECTED) {
                            return " ";
                        }
                    }

                    return "";
                })
                .collect(Collectors.joining(" "));

        return text.replaceAll("\\s+", " ").trim();
    }

}
