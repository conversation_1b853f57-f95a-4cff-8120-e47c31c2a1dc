package com.nexla.parser.pdf;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.nexla.common.NexlaMessage;
import com.nexla.common.parse.BatchParser;
import com.nexla.common.parse.NexlaParser;
import com.nexla.parser.pdf.strategy.*;
import com.nexla.parser.pdf.strategy.BatchingStrategy.BatchedBlock;
import com.nexla.parser.pdf.strategy.config.TableConfig;
import com.nexla.parser.pdf.strategy.model.Block;
import com.nexla.parser.pdf.strategy.render.*;
import com.nexla.parser.pdf.strategy.TesseractStrategy;
import com.nexla.parser.pdf.strategy.textract.TextractStrategy;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.nexla.parser.TransformingParserWrapper.Options.OPENAI_API_KEY;
import static com.nexla.parser.TransformingParserWrapper.Options.OPENAI_API_KEY_ALTERNATIVE;

public class PdfParser extends NexlaParser implements BatchParser {
    private static final org.slf4j.Logger LOGGER = org.slf4j.LoggerFactory.getLogger(PdfParser.class);

    private String strategy;

    private static final String STRATEGY_TEXT = "text";
    private static final String STRATEGY_SEMI_AUTO = "semi-auto";
    private static final String STRATEGY_TEXTRACT = "auto-1";
    private static final String STRATEGY_TESSERACT = "auto-2";
    private static final String STRATEGY_IMAGE = "image";
    private static final String STRATEGY_TEXT_PLUS_TEXTRACT = "hybrid-1";
    private static final String STRATEGY_TEXT_PLUS_TESSERACT = "hybrid-2";

    private static final String DEFAULT_EMPTY_VALUE = "(blank)";
    private double ratio;


    @SneakyThrows
    @Override
    public StreamEx<Optional<NexlaMessageWithContext>> parseMessages(List<NexlaStreamDescriptor> iss) {
        Renderer renderer = createRenderer();

        Strategy parsingStrategy = findStrategy();
        if (!(parsingStrategy instanceof BatchingStrategy)) {
            throw new IllegalArgumentException("Strategy must implement BatchingStrategy");
        }

        Stream<Block> blockStream = ((BatchingStrategy) parsingStrategy)
                .executeBatch(iss);

        return StreamEx.of(blockStream)
                .flatMap(b -> {
                    if (b == null) {
                        return Stream.empty();
                    }

                    return renderer.render(Stream.of(b))
                            .map(x -> {
                                if (b instanceof BatchedBlock) {
                                    BatchedBlock bb = (BatchedBlock) b;
                                    return new NexlaMessageWithContext(x, bb.getDescriptor());
                                } else {
                                    return new NexlaMessageWithContext(x, null);
                                }
                            });
                })
                .map(Optional::of);
    }

    public interface Options {
        String PASSWORD = "pdf.document.password";
        /**
         * The page numbers to extract from the document. 1-based. Comma separated.
         */
        String PAGES = "pdf.document.pages";
        /**
         * PDF parsing strategy:
         * "text" -- symbols sorted by position.
         * "semi-auto" -- table parser
         * "auto-1" -- send documents to textract
         * "auto-2" -- does OCR locally using tesseract.
         * "hybrid-1" -- first try to parse using "text" strategy, and send not parsed pages to textract;
         * "hybrid-2" -- try to parse using "text" strategy, and then tesseract
         */
        String STRATEGY = "pdf.parsing.strategy";
        String TABLE_CONFIG = "pdf.parsing.tables";
        String EMIT_TEXT_BLOCKS = "pdf.parsing.emitTextBlocks";
        String INCLUDE_ENTIRE_FILE_CONTENT = "pdf.parsing.includeEntireFileContent";
        String EMPTY_VALUE_PLACEHOLDER = "pdf.parsing.emptyValuePlaceholder";
        String STRATEGY_AUTO_1_TEXT = "pdf.parsing.auto-1.outputText";
        String STRATEGY_AUTO_1_TABLES = "pdf.parsing.auto-1.outputTables";
        String STRATEGY_AUTO_3_ASSISTANT_PROMPT = "pdf.parsing.auto-3.assistant-prompt";
        String STRATEGY_IMAGE_SCALE = "pdf.parsing.image.scale";

        String STRATEGY_AUTO_1_TABLE_PROCESSOR = "pdf.parsing.auto-1.table-processor";
        String STRATEGY_AUTO_1_TABLE_PROCESSOR_AGGRESSIVE = "aggressive";
        /**
         * The configuration key for the rendering strategy used for PDF rendering.
         * The default value is "default".
         * Possible values are "default", "extended", and "discarded".
         *
         * extended adds "pageNumber" to rawMessage.
         * discarded -- discards all the data, outputs empty message. Used for debugging.
         */
        String RENDERING_STRATEGY = "pdf.rendering.strategy";
        String RENDERING_DEFAULT = "default";
        String RENDERING_TABLE_AS_MESSAGE = "table.as.message";
        String RENDERING_DOCUMENT_AS_MESSAGE = "document.as.message";
        String RENDERING_EXTENDED = "extended";
        String RENDERING_DISCARDED = "discarded";

        /**
         *
         * If area of parsed text bounding box to page area ratio is less than this value the parser will attempt to parse the page with one of OCR services.
         *
         * Used with hybrid strategies.
         * Default is 0.15.
         */
        String PARSED_TO_NOT_PARSED_RATIO = "pdf.parsing.coverage.ratio";
        String TEXTRACT_PARALLELISM = "pdf.parsing.textract.parallelism";
    }

    private static final String DEFAULT_STRATEGY = STRATEGY_TEXT;
    private Map<String, String> options;

    private final Map<String, String> originals;

    public PdfParser(Map<String, String> config) {
        this.originals = config;
    }

    @Override
    public NexlaParser config(Map<String, String> options) {
        this.options = options;

        strategy = options.getOrDefault(Options.STRATEGY, DEFAULT_STRATEGY);
        ratio = Double.parseDouble(options.getOrDefault(Options.PARSED_TO_NOT_PARSED_RATIO, "0.15"));

        return super.config(options);
    }

    @SneakyThrows
    @Override
    public StreamEx<Optional<NexlaMessage>> parseMessages(InputStream inputStream) {
        Renderer renderer = createRenderer();

        // copying the input stream to a temp file to avoid multiple reads
        File localFile = null;
        try {
            localFile = File.createTempFile("nexla-pdf-parser", ".pdf");

            try (FileOutputStream fos = new FileOutputStream(localFile)) {
                IOUtils.copy(inputStream, fos);
            }
        } catch (IOException e) {
            LOGGER.error("Failed to create temp file to allow fall back", e);
            return StreamEx.of(Optional.empty());
        }

        List<Block> blocks;
        try (FileInputStream is = new FileInputStream(localFile)) {
            Stream<Block> blockStream = findStrategy()
                    .execute(is);

            blocks = blockStream == null
                    ? List.of()
                    : blockStream.collect(Collectors.toList());
        }

        // here we're trying to fall back to the default strategy if the requested strategy fails
        if (blocks.isEmpty()) {
            LOGGER.warn("Block stream is null, strategy {} failed, falling back to the default strategy", strategy);

            try (FileInputStream is2 = new FileInputStream(localFile)) {
                blocks = defaultStrategy()
                        .execute(is2)
                        .collect(Collectors.toList());
            }
        }

        return StreamEx.of(renderer.render(blocks.stream()))
                .map(Optional::ofNullable);
    }

    private Renderer createRenderer() {
        String emptyPlaceholder = Optional.ofNullable(this.options.get(Options.EMPTY_VALUE_PLACEHOLDER))
                .orElse(DEFAULT_EMPTY_VALUE);
        String normalizedPlaceholder = emptyPlaceholder.equalsIgnoreCase("null") ? null : emptyPlaceholder;

        switch (options.getOrDefault(Options.RENDERING_STRATEGY, Options.RENDERING_DEFAULT)) {
            case Options.RENDERING_DISCARDED:
                return new DiscardOutputRenderer();
            case Options.RENDERING_EXTENDED:
                return new ExtendedRenderer(normalizedPlaceholder);
            case Options.RENDERING_DOCUMENT_AS_MESSAGE:
                boolean includeEntireFileContent = Optional.ofNullable(this.options.get(Options.INCLUDE_ENTIRE_FILE_CONTENT))
                        .map(Boolean::parseBoolean)
                        .orElse(false);

                return new DocumentAsMessageRenderer(normalizedPlaceholder, includeEntireFileContent);
            case Options.RENDERING_TABLE_AS_MESSAGE:
                return new TableAsMessageRenderer(normalizedPlaceholder);
            case Options.RENDERING_DEFAULT:
            default:
                return new DefaultRenderer(normalizedPlaceholder);
        }
    }

    private Strategy findStrategy() {
        List<Integer> pages = parsePagesProp();

        List<TableConfig> tableConfigs = parseTableConfigs(this.options.get(Options.TABLE_CONFIG));
        switch (strategy) {
            case STRATEGY_SEMI_AUTO:
                boolean emitTextBlocks = Optional.ofNullable(this.options.get(Options.EMIT_TEXT_BLOCKS))
                        .map(Boolean::parseBoolean)
                        .orElse(true);

                return new SemiAutoStrategy(options.get(Options.PASSWORD), pages, emitTextBlocks, tableConfigs);
            case STRATEGY_TEXTRACT:
                HashMap<String, String> v = new HashMap<>(originals);
                v.putAll(options);
                
                boolean parseText = Optional.ofNullable(options.get(Options.STRATEGY_AUTO_1_TEXT))
                        .map(Boolean::parseBoolean)
                        .orElse(true);

                boolean parseTables = Optional.ofNullable(options.get(Options.STRATEGY_AUTO_1_TABLES))
                        .map(Boolean::parseBoolean)
                        .orElse(false);

                return new TextractStrategy(v, parseText, parseTables);
            case STRATEGY_TESSERACT:
                return new TesseractStrategy(options.get(Options.PASSWORD), pages);
            case STRATEGY_IMAGE:
                float scale = Optional.ofNullable(options.get(Options.STRATEGY_IMAGE_SCALE))
                    .map(Float::parseFloat)
                    .orElse(1.25f);

                return new ImageStrategy(options.get(Options.PASSWORD), pages, scale);
            case STRATEGY_TEXT_PLUS_TEXTRACT:
                HashMap<String, String> hybridConfig = new HashMap<>(originals);
                hybridConfig.putAll(options);
                return new Hybrid1Strategy(ratio, hybridConfig);
            case STRATEGY_TEXT_PLUS_TESSERACT:
                HashMap<String, String> hybrid2Config = new HashMap<>(originals);
                hybrid2Config.putAll(options);
                return new Hybrid2Strategy(ratio, hybrid2Config);
            case "coverage-1":
                return new TextCoverageStrategy();
            case STRATEGY_TEXT:
            default:
                return defaultStrategy();
        }
    }

    private List<Integer> parsePagesProp() {
        return Stream.ofNullable(StringUtils.trimToNull(options.get(Options.PAGES)))
                .flatMap(x -> Arrays.stream(x.split(",")))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    private DefaultStrategy defaultStrategy() {
        return new DefaultStrategy(options.get(Options.PASSWORD), parsePagesProp());
    }

    private List<TableConfig> parseTableConfigs(String json) {
        return Optional.ofNullable(json)
                .map(c -> JsonUtils.stringToType(c, new TypeReference<List<TableConfig>>() {}))
                .orElse(List.of());

    }
}
