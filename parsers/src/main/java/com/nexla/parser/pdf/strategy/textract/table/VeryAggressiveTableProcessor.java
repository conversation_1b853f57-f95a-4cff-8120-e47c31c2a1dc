package com.nexla.parser.pdf.strategy.textract.table;

import software.amazon.awssdk.services.textract.model.Block;
import com.nexla.parser.pdf.strategy.textract.Blocks;
import com.nexla.parser.pdf.strategy.textract.data.TextractNexlaCell;
import com.nexla.parser.pdf.strategy.textract.data.TextractNexlaTableBlock;
import com.nexla.parser.pdf.strategy.textract.data.TextractNexlaTuple;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.nexla.parser.pdf.strategy.textract.Blocks.BlockType.MERGED_CELL;

public class VeryAggressiveTableProcessor extends AggressiveTableProcessor {

    public VeryAggressiveTableProcessor(Blocks blocks) {
        super(blocks);
    }

    @Override
    public List<TextractNexlaTableBlock> process(List<TextractNexlaTableBlock> tables) {
        tables = super.process(tables);

        tables.sort(Comparator.comparingInt(x -> x.getHeader().getPageNumber()));

        // first try to fill continuation of merged cells on next row
        fillEmptyCells(tables);

        return tables;
    }

    private void fillEmptyCells(List<TextractNexlaTableBlock> tables) {
        // so if we are here, it means it is ok to fill the empty cells with the value of the cell above it.
        // since we extend aggressive table processor, we are sure that continues tables are already merged,
        // so we can just check each individual table.
        for (TextractNexlaTableBlock table : tables) {
            List<TextractNexlaTuple> tuples = table.getTuples();
            // 1 - since we can do nothing for the first row,
            // this may be indication that we can merge this table with the table above it.
            // or not.
            for (int i = 1; i < tuples.size(); i++) {
                TextractNexlaTuple tuple = tuples.get(i);

                List<TextractNexlaCell> cells = tuple.getCells();
                for (int j = 0; j < cells.size(); j++) {
                    TextractNexlaCell cell = cells.get(j);
                    if (cell == null || !StringUtils.isBlank(cell.getValue())) {
                        continue;
                    }

                    TextractNexlaTuple previousRow = tuples.get(i - 1);
                    TextractNexlaCell previousCell = previousRow.getCells().get(j);

                    if (previousCell == null || StringUtils.isBlank(previousCell.getValue())) {
                        continue;
                    }

                    cell.setValue(previousCell.getValue());
                }
            }
        }
    }
}
