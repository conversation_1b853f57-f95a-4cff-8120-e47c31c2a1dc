package com.nexla.parser.pdf.strategy.textract;

import software.amazon.awssdk.services.textract.model.Block;
import software.amazon.awssdk.services.textract.model.Relationship;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class Blocks {
    private final List<Block> blocks;
    private final Map<String, Block> blockIndex;
    private final Map<String, Set<String>> childToParentsIndex;

    Blocks(List<Block> blocks) {
        this.blocks = blocks;

        this.blockIndex = blocks.stream()
                .collect(Collectors.toMap(Block::id, x -> x));

        this.childToParentsIndex = buildChildParentIndex(blocks);
    }

    private Map<String, Set<String>> buildChildParentIndex(List<Block> blocks) {
        final Map<String, Set<String>> childToParent = new HashMap<>();

        for (Block block : blocks) {
            if (block.relationships() == null || block.relationships().isEmpty()) {
                continue;
            }

            String parentId = block.id();
            List<Relationship> relationships = block.relationships();

            for (Relationship relationship : relationships) {
                if (!"CHILD".equals(relationship.type())) {
                    continue;
                }

                Collection<String> childIds = relationship.ids();
                for (String childId : childIds) {
                    childToParent.computeIfAbsent(childId, k -> new HashSet<>())
                            .add(parentId);

                }
            }
        }

        return childToParent;
    }

    public List<Block> relationshipsOf(Block block) {
        if (block.relationships() == null) return List.of();

        return block.relationships().stream()
                .flatMap(x -> x.ids().stream())
                .map(blockIndex::get)
                .collect(Collectors.toList());
    }

    public List<Block> traverse(Block block, Predicate<Block> predicate) {
        List<Block> result = new ArrayList<>();
        relationshipsOf(block)
                .forEach(x -> result.addAll(traverse(x, predicate)));

        if (predicate.test(block)) {
            result.add(block);
        }

        return result;
    }

    public List<Block> relationshipsOf(Block block, Predicate<Block> predicate) {
        if (block.relationships() == null) return List.of();

        return relationshipsOf(block)
                .stream()
                .filter(predicate)
                .collect(Collectors.toList());
    }

    public List<Block> parentsOf(Block block) {
        return parentsOf0(block)
                .collect(Collectors.toList());
    }

    private Stream<Block> parentsOf0(Block block) {
        return childToParentsIndex.getOrDefault(block.id(), Collections.emptySet())
                .stream()
                .map(blockIndex::get);
    }

    public List<Block> parentsOf(Block block, Predicate<Block> predicate) {
        return parentsOf0(block)
                .filter(predicate)
                .collect(Collectors.toList());
    }

    public List<Block> getBlocks() {
        return blocks;
    }

    public Block get(String id) {
        return blockIndex.get(id);
    }

    public enum EntityType {
        COLUMN_HEADER;

        boolean isTypeOf(Block b) {
            return b.entityTypes() != null && b.entityTypes().contains(this.name());
        }
    }

    public enum BlockType {
        MERGED_CELL,
        CELL,
        WORD,
        SELECTION_ELEMENT;

        public boolean isTypeOf(Block b) {
            return this.name().equals(b.blockType());
        }
    }
}
