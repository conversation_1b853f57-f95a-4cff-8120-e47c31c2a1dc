package com.nexla.inmemory_connector_common.utils.jwrap;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSet;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.DataSource;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.inmemory_connector_common.utils.ConfigurationOverrider;
import com.nexla.listing.client.AdaptiveFlowTask;

import java.util.ArrayList;

import scala.collection.JavaConverters;

public class ConfigurationOverriderWrapper {
    public static void overrideConfigsDebug(AdminApiClient client, ArrayList<DataSource> sources, ArrayList<DataSet> dataSets, ArrayList<DataSink> sinks, AdaptiveFlowTask task, ControlMessageProducer controlMessageProducer, long runId) {
        ConfigurationOverrider overrider = new ConfigurationOverrider(client, true, controlMessageProducer, runId);
        overrider.overrideConfigs(
                JavaConverters.collectionAsScalaIterable(sources).toList(),
                JavaConverters.collectionAsScalaIterable(dataSets).toList(),
                JavaConverters.collectionAsScalaIterable(sinks).toList(),
                task);
    }

    public static void overrideConfigs(AdminApiClient client, ArrayList<DataSource> sources, ArrayList<DataSet> dataSets, ArrayList<DataSink> sinks, AdaptiveFlowTask task, ControlMessageProducer controlMessageProducer, long runId) {
        ConfigurationOverrider overrider = new ConfigurationOverrider(client, false, controlMessageProducer, runId);
        overrider.overrideConfigs(
                JavaConverters.collectionAsScalaIterable(sources).toList(),
                JavaConverters.collectionAsScalaIterable(dataSets).toList(),
                JavaConverters.collectionAsScalaIterable(sinks).toList(),
                task);
    }
}
