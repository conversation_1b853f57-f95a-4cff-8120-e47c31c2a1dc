package com.nexla.inmemory_connector_common.utils

import com.bazaarvoice.jolt.JsonUtils
import com.nexla.admin.client.{AdminApiClient, DataCredentials, DataSink, DataSource}
import com.nexla.common.ConnectionType
import com.nexla.common.notify.transport.ControlMessageProducer
import com.nexla.listing.client.AdaptiveFlowTask
import org.mockito.Mockito.{mock, when}
import org.scalatest.TagAnnotation
import org.scalatest.funsuite.AnyFunSuite

import java.util
import java.util.Optional

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class ConfigurationOverriderTest extends AnyFunSuite {

  def prepareCreds(id: Int, adminApiClientMock: AdminApiClient): Unit = {
    val creds = new DataCredentials
    creds.setId(id)
    creds.setCredentialsType(ConnectionType.S3_ICEBERG)
    creds.setCredentialsEnc("enc")
    creds.setCredentialsEncIv("encIv")
    when(adminApiClientMock.getDataCredentials(id)).thenReturn(Optional.of(creds))
    return
  }

  test("replace the original config values") {
    val taskDef: AdaptiveFlowTask = JsonUtils.streamToType(getClass.getClassLoader.getResourceAsStream("./adaptiveTaskDef.json"), classOf[AdaptiveFlowTask])
    val src1 = new DataSource()
    src1.setId(123)
    src1.setConnectionType(ConnectionType.S3)
    src1.setSourceConfig(new util.HashMap[String, AnyRef]())
    val s3CredsDefault = new DataCredentials
    s3CredsDefault.setId(7557)
    src1.setDataCredentials(s3CredsDefault)
    //
    val src2 = new DataSource()
    src2.setId(124)
    src2.setConnectionType(ConnectionType.S3)
    src2.setSourceConfig(new util.HashMap[String, AnyRef]())
    src2.setDataCredentials(s3CredsDefault)

    val sink1 = new DataSink()
    sink1.setId(3001)
    sink1.setConnectionType(ConnectionType.REST)
    sink1.setSinkConfig(new util.HashMap[String, AnyRef]())
    sink1.setDataCredentials(s3CredsDefault)

    val sink2 = new DataSink()
    sink2.setId(3002)
    sink2.setConnectionType(ConnectionType.REST)
    sink2.setSinkConfig(new util.HashMap[String, AnyRef]())
    sink2.setDataCredentials(s3CredsDefault)

    val dataSources = List(src1, src2)
    val dataSinks = List(sink1, sink2)

    val adminApiMock: AdminApiClient = mock(classOf[AdminApiClient])
    prepareCreds(100, adminApiMock)
    prepareCreds(200, adminApiMock)
    prepareCreds(300, adminApiMock)
    prepareCreds(301, adminApiMock)

    val mockMsgProducer = mock(classOf[ControlMessageProducer])
    val replacer = new ConfigurationOverrider(adminApiMock, true, mockMsgProducer, 123L)
    replacer.overrideConfigs(dataSources, List(), dataSinks, taskDef)
    // expectations:

    // 1: all sources and sinks must have "c.type": "custom"
    // "c.property": "myProperty" is default. it's going to be overridden by the precise entries later.
    dataSources.foreach(src => {
      assert(src.getSourceConfig.get("c.type") == "custom")
    })
    dataSinks.foreach(sink => {
      assert(sink.getSinkConfig.get("c.type") == "custom")
    })

    // 3: only source with id "123" must have ("c.property") == "mySourceProperty"
    assert(src1.getSourceConfig.get("c.property") == "mySourceProperty")

    // 4: only source with id "123" must have data credentials 200. 124 should have original data cred id.
    assert(src1.getDataCredentials.getId == 200)
    assert(src2.getDataCredentials.getId == 7557)

    // 5: all data sinks must have "c.property"
    dataSinks.foreach(sink => {
      assert(sink.getSinkConfig.get("c.property") != null)
    })

    // 6: only sink with id 3001 will have "c.property": "mySinkProperty-1". other must have it too, but not equal to "mySinkProperty-1"
    assert(sink1.getSinkConfig.get("c.property") == "mySinkProperty-1")
    assert(sink2.getSinkConfig.get("c.property") != "mySinkProperty-1")

    // 7: only sink with id 3001 will have "data_credentials_id": 301. sink 3002 must have the creds 301
    assert(sink1.getDataCredentials.getId == 300)
    assert(sink2.getDataCredentials.getId == 301)
  }

  test("omit replacing if the task is empty") {
    val taskDef: AdaptiveFlowTask = JsonUtils.streamToType(getClass.getClassLoader.getResourceAsStream("./emptyAdaptiveTaskDef.json"), classOf[AdaptiveFlowTask])
    val src1 = new DataSource()
    src1.setId(123)
    src1.setConnectionType(ConnectionType.S3)
    src1.setSourceConfig(new util.HashMap[String, AnyRef]())
    val s3CredsDefault = new DataCredentials
    s3CredsDefault.setId(7557)
    src1.setDataCredentials(s3CredsDefault)
    //
    val src2 = new DataSource()
    src2.setId(124)
    src2.setConnectionType(ConnectionType.S3)
    src2.setSourceConfig(new util.HashMap[String, AnyRef]())
    src2.setDataCredentials(s3CredsDefault)

    val sink1 = new DataSink()
    sink1.setId(3001)
    sink1.setConnectionType(ConnectionType.REST)
    sink1.setSinkConfig(new util.HashMap[String, AnyRef]())
    sink1.setDataCredentials(s3CredsDefault)

    val sink2 = new DataSink()
    sink2.setId(3002)
    sink2.setConnectionType(ConnectionType.REST)
    sink2.setSinkConfig(new util.HashMap[String, AnyRef]())
    sink2.setDataCredentials(s3CredsDefault)

    val dataSources = List(src1, src2)
    val dataSinks = List(sink1, sink2)

    val adminApiMock: AdminApiClient = mock(classOf[AdminApiClient])
    prepareCreds(100, adminApiMock)
    prepareCreds(200, adminApiMock)
    prepareCreds(300, adminApiMock)
    prepareCreds(301, adminApiMock)

    val mockMsgProducer = mock(classOf[ControlMessageProducer])
    val replacer = new ConfigurationOverrider(adminApiMock, true, mockMsgProducer, 124L)
    replacer.overrideConfigs(dataSources, List(), dataSinks, taskDef)
    // expectations:

    // 1: all sources must have empty configs
    dataSources.foreach(src => {
      assert(src.getSourceConfig.isEmpty)
    })
    // 2: creds must remain as original
    assert(sink1.getDataCredentials.getId == 7557)
    assert(sink2.getDataCredentials.getId == 7557)
    // end goal: this is just one more activation which is performed for this particular source
  }

  test("only credentials should be replaced if there is no config") {
    val taskDef: AdaptiveFlowTask = JsonUtils.streamToType(getClass.getClassLoader.getResourceAsStream("./adaptiveTaskDefWithoutCfg.json"), classOf[AdaptiveFlowTask])
    val src1 = new DataSource()
    src1.setId(123)
    src1.setConnectionType(ConnectionType.S3)
    src1.setSourceConfig(new util.HashMap[String, AnyRef]())
    val s3CredsDefault = new DataCredentials
    s3CredsDefault.setId(7557)
    src1.setDataCredentials(s3CredsDefault)
    //
    val src2 = new DataSource()
    src2.setId(124)
    src2.setConnectionType(ConnectionType.S3)
    src2.setSourceConfig(new util.HashMap[String, AnyRef]())
    src2.setDataCredentials(s3CredsDefault)

    val sink1 = new DataSink()
    sink1.setId(3001)
    sink1.setConnectionType(ConnectionType.REST)
    sink1.setSinkConfig(new util.HashMap[String, AnyRef]())
    sink1.setDataCredentials(s3CredsDefault)

    val sink2 = new DataSink()
    sink2.setId(3002)
    sink2.setConnectionType(ConnectionType.REST)
    sink2.setSinkConfig(new util.HashMap[String, AnyRef]())
    sink2.setDataCredentials(s3CredsDefault)

    val dataSources = List(src1, src2)
    val dataSinks = List(sink1, sink2)

    val adminApiMock: AdminApiClient = mock(classOf[AdminApiClient])
    prepareCreds(100, adminApiMock)
    prepareCreds(200, adminApiMock)
    prepareCreds(300, adminApiMock)
    prepareCreds(301, adminApiMock)

    val mockMsgProducer = mock(classOf[ControlMessageProducer])
    val replacer = new ConfigurationOverrider(adminApiMock, true, mockMsgProducer, 125L)
    replacer.overrideConfigs(dataSources, List(), dataSinks, taskDef)
    // expectations:

    // 1: source must have creds 200
    assert(src1.getDataCredentials.getId == 200)

    // 2: sinks must remain as original
    assert(sink1.getDataCredentials.getId == 7557)
    assert(sink2.getDataCredentials.getId == 7557)
  }

  test("error should be emitted if we try to use deleted creds") {
    val taskDef: AdaptiveFlowTask = JsonUtils.streamToType(getClass.getClassLoader.getResourceAsStream("./adaptiveTaskDef.json"), classOf[AdaptiveFlowTask])
    val src1 = new DataSource()
    src1.setId(123)
    src1.setConnectionType(ConnectionType.S3)
    src1.setSourceConfig(new util.HashMap[String, AnyRef]())
    val s3CredsDefault = new DataCredentials
    s3CredsDefault.setId(7557)
    src1.setDataCredentials(s3CredsDefault)

    val dataSources = List(src1)
    val dataSinks = List()

    val adminApiMock: AdminApiClient = mock(classOf[AdminApiClient])
    val mockMsgProducer = mock(classOf[ControlMessageProducer])
    val replacer = new ConfigurationOverrider(adminApiMock, true, mockMsgProducer, 126L)


    assertThrows[RuntimeException](() -> replacer.overrideConfigs(dataSources, List(), dataSinks, taskDef))
  }

}
