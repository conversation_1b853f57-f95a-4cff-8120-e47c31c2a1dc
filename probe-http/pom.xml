<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.nexla</groupId>
		<artifactId>backend-connectors</artifactId>
		<version>3.3.0-SNAPSHOT</version>
	</parent>

	<artifactId>probe-http</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>file-service-utils</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.github.mwiede</groupId>
			<artifactId>jsch</artifactId>
			<version>${jsch.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>connect-runtime</artifactId>
			<version>${kafka.version}</version>
			<scope>runtime</scope>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>commons-httpclient</groupId>
			<artifactId>commons-httpclient</artifactId>
			<version>${commons-httpclient.version}</version>
		</dependency>

		<dependency>
			<groupId>javax.jms</groupId>
			<artifactId>javax.jms-api</artifactId>
			<version>2.0.1</version>
		</dependency>

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>

		<dependency>
			<groupId>com.google.cloud</groupId>
			<artifactId>google-cloud-pubsub</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.google.code.gson</groupId>
					<artifactId>gson</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.google.protobuf</groupId>
					<artifactId>protobuf-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla.tibcojms</groupId>
			<artifactId>tibcojms</artifactId>
			<version>8.6.0</version>
		</dependency>

		<dependency>
			<groupId>com.nexla.tibcojms</groupId>
			<artifactId>tibcojmsadmin</artifactId>
			<version>8.6.0</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common</artifactId>
			<version>${nexla-backend-common.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common-sc</artifactId>
			<version>${nexla-backend-common.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>documentdb-service</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>vectordb-service</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-http-testkit_${scala.short.version}</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-stream-testkit_${scala.short.version}</artifactId>
			<version>${akka.version}</version>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-to-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-http_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-http-spray-json_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>io.spray</groupId>
			<artifactId>spray-json_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-actor_${scala.short.version}</artifactId>
		</dependency>


		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>parsers</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>servlet-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jersey</groupId>
					<artifactId>jersey-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jersey</groupId>
					<artifactId>jersey-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.jcraft</groupId>
					<artifactId>jsch</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- storage services -->
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>kafka-probe-service</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>s3-probe</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>azure-blob-probe</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>delta-lake-service</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>gcs-probe</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>ftp-probe</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>http-probe</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>dropbox-probe</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>box-service</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>documentdb-service</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>redis-probe</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>bigquery-probe</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>sharepoint-probe</artifactId>
			<version>${project.version}</version>
		</dependency>
		<!-- storage services -->

		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>bom</artifactId>
			<version>${aws.sdk.version}</version>
			<type>pom</type>
			<scope>import</scope>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-cbor</artifactId>
			<version>${jackson-dataformat-cbor.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>ec2</artifactId>
			<version>${aws.sdk.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.dataformat</groupId>
					<artifactId>jackson-dataformat-cbor</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>s3</artifactId>
			<version>${aws.sdk.version}</version>
		</dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>${springfox.version}</version>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-library</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.module</groupId>
			<artifactId>jackson-module-scala_${scala.short.version}</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>sql-probe</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.google.cloud</groupId>
					<artifactId>google-cloud-spanner-jdbc</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.mock-server</groupId>
			<artifactId>mockserver-netty-no-dependencies</artifactId>
			<version>${mock-server.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>connector-test</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>

        <dependency>
            <groupId>com.nexla.probe</groupId>
            <artifactId>gdrive-probe</artifactId>
			<version>${project.version}</version>
        </dependency>

		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>one-drive-probe</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla.probe</groupId>
			<artifactId>webdav-probe</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sap.cloud.db.jdbc</groupId>
			<artifactId>ngdbc</artifactId>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-rest-sink</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-connect-soap-sink</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.dimafeng</groupId>
			<artifactId>testcontainers-scala-scalatest_${scala.short.version}</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>transform-service</artifactId>
			<version>${nexla-backend-transform.version}</version>
			<scope>test</scope>
		</dependency>
    <dependency>
      <groupId>com.nexla.probe</groupId>
      <artifactId>iceberg-probe</artifactId>
      <version>${project.version}</version>
    </dependency>

  </dependencies>

	<build>

		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>

				<configuration>
					<requiresUnpack>
						<dependency>
							<groupId>org.python</groupId>
							<artifactId>jython-standalone</artifactId>
						</dependency>
						<dependency>
							<groupId>org.openjdk.nashorn</groupId>
							<artifactId>nashorn-core</artifactId>
						</dependency>
					</requiresUnpack>
				</configuration>
			</plugin>

		</plugins>

	</build>

</project>
