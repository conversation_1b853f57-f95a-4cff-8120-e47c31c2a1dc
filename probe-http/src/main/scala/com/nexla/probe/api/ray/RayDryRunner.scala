package com.nexla.probe.api.ray

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import cats.implicits.toTraverseOps
import com.bazaarvoice.jolt.JsonUtils.toJsonString
import com.google.common.collect.Maps
import com.nexla.admin.client.AdminApiClient
import com.nexla.common._
import com.nexla.common.logging.NexlaLogger
import com.nexla.common.metrics.Metric
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils
import com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix
import com.nexla.connector.config.file.{AWSAuthConfig, CustomRtMode, FileSourceConnectorConfig}
import com.nexla.connector.config.rest.RestAuthConfig
import com.nexla.connector.file.source._
import com.nexla.listing.client.ListingClient
import com.nexla.probe.api.{DryRunReadSampleContext, ProbeInput}
import com.nexla.probe.api.ray.ExtraDataBuilder.ExtraData
import com.nexla.probe.custom.ray.{RayApiClient, RayApiClientImpl}
import com.nexla.probe.custom.ray.RayApiClient.SubmitDirectoryJobRequest
import com.nexla.probe.{AppProps, DryRunStartRequest, MezzanineAuthConfig}
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import org.apache.commons.lang3.StringUtils
import org.apache.kafka.connect.data.Schema
import org.apache.kafka.connect.source.SourceRecord
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.s3.{S3AsyncClient, S3Client}
import spray.json.{DefaultJsonProtocol, JsonParser}

import java.io.File
import java.util
import java.util.Optional.empty
import scala.collection.JavaConverters._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class RayDryRunner(adminApiClient: AdminApiClient,
                   listingClient: ListingClient,
                   rayApi: RayApiClientImpl,
                   appProps: AppProps)
                  (implicit ec: ExecutionContext)
  extends StrictNexlaLogging with WithLogging
    with SprayJsonSupport
    with DefaultJsonProtocol {

  var testS3Instance: Option[S3AsyncClient] = None

  private def extractExtraData(extraData: String): Option[ExtraData] = {
    val res = for {
      maybeRawJson <- Try(Option(extraData).map(JsonParser(_)))
      extraData <- maybeRawJson.traverse(new ExtraDataBuilder().parse)
    } yield {
      extraData
    }
    res.toOption.flatten
  }

  def scheduleDryRunStart(ctx: DryRunReadSampleContext,
                          connectionType: ConnectionType,
                          fileConfig: FileSourceConnectorConfig,
                          pageSize: Option[Int]): Unit = {
    val kafkaMessageSender = new NoopFileSourceNotificationSender()
    val probeService = Utils.getProbeService(connectionType, adminApiClient, listingClient, appProps.decryptKey)

    val schemaDetection: SchemaDetectionUtils = null
    val nexlaLogger = new NexlaLogger(LoggerFactory.getLogger(this.getClass))

    val req = ctx.dryRunRequest

    val fileSourceContext = new FileSourceContext(fileConfig, empty(), -1, -1, -1)
    val tfr = new CustomRtOnFlexTransportFileReader(
      kafkaMessageSender,
      probeService,
      schemaDetection,
      new NoopOffsetWriter(),
      new NoopMessageGrouper(),
      nexlaLogger,
      empty(),
      empty(),
      empty(),
      fileSourceContext,
      empty(),
      false,
      ctx.mode
    ) {
      override protected def createUploadBase(fileSourceContext: FileSourceContext, fscc: FileSourceConnectorConfig): AWSAuthConfig.BucketPrefix = {
        if (req.runRayProcessingJob) {
          toBucketPrefix(req.dryRunInputPath, true)
        } else {
          toBucketPrefix(req.dryRunOutputPath, true)
        }

      }

      override protected def toUploadPath(fullPath: String): String = {
        super.toUploadPath(new File(fullPath).getName)
      }

      override protected def createS3Client(): S3AsyncClient = {
        testS3Instance.getOrElse(super.createS3Client());
      }
    }

    val simpleFileReadResult = new SimpleFileReadResult
    val nexlaFile = new NexlaFile(null, null, NexlaFile.FileSourceType.LISTING, req.inputFilePathNoBucket, 0, "", "", null, null, ListingResourceType.FILE)
    tfr.addFiles(List(new TransportFile(nexlaFile, 0, false)).asJava)
    tfr.readNextBatch(simpleFileReadResult, adminApiClient)

    if (req.runRayProcessingJob) {
      val extraData = req.extraData.flatMap(extractExtraData)
      val privatePackages = extraData.flatMap(_.privatePackage).map(handlePrivatePackages)

      val packages = Option(req.packages).map(_.split(",").filterNot(_.isEmpty).toList).getOrElse(List()) ++ List("boto3")
      val submitDirectoryJobRequest = SubmitDirectoryJobRequest(
        dryRunId = req.dryRunId,
        customCode = req.codeBase64.get,
        driverFunction = req.driverFunction,
        packages = packages,
        privatePackages = privatePackages,
        sinkType = appProps.imcDryRunMezzanineConnectionType.name.toLowerCase,
        sourceType = appProps.imcDryRunMezzanineConnectionType.name.toLowerCase,
        fullSourceDir = req.dryRunInputPath,
        fullDestinationDir = req.dryRunOutputPath,
        sourceS3Creds = req.mezzanineS3Creds,
        destS3Creds = req.mezzanineS3Creds,
        extraData = extraData.map(_.extraData),
        limit = pageSize,
        pushStatus = true
      )

      rayApi
        .submitJobV2(submitDirectoryJobRequest)
        .foreach(jobId => logger.info(s"Job was submitted (jobId: ${jobId.id}, srcDir: ${submitDirectoryJobRequest.fullSourceDir}, dstDir: ${req.dryRunOutputPath})"))
    }
  }

  def probeInputDryStartRequest(probeInput: ProbeInput): DryRunStartRequest = {
    val params = probeInput.params.getOrElse(throw new Exception("Probe input params are missing"))
    val userId = params.get("userId").map(_.toInt).getOrElse(throw new Exception("Probe input param `userId` is missing"))
    val inputFilePath = params.getOrElse("path", throw new Exception("Probe input param `path` is missing"))
    val codeContainerConfig = params.get("code_container_config")
      .map(StringUtils.trimToNull)
      .filter(_ != null)
      .map(x => StreamUtils.jsonUtil().jsonToMap(x))
    val packages = codeContainerConfig
      .map(x => x.get("packages"))
      .filter(_ != null)
      .map(_.toString)
      .getOrElse("")
    val extraData = codeContainerConfig
      .map(x => x.get("extra_data"))
      .filter(_ != null)
      .map(_.toString)
      .getOrElse("")
    val dstFileExtension = params.getOrElse("dstFileExtension", "csv")
    val codeBase64 = params.get("codeBase64")
    val sourceConfig = params.get("source_config").map(StringUtils.trimToNull).filter(_ != null).getOrElse("")

    // Include all unique parameters that determines IMC dry run
    val uuidHash = uuidHashCode(
      codeBase64.map(_ => "r").getOrElse("p"),
      List(userId.toString, probeInput.credsId.toString, inputFilePath, appProps.imcDryRunDriverFunction,
        packages, dstFileExtension, codeBase64.getOrElse(""), extraData, sourceConfig))

    val mezzanineBucketPrefix = AWSAuthConfig.toBucketPrefix(appProps.imcDryRunMezzanineSamplePath, true)
    val bucketPrefix: String = Some(mezzanineBucketPrefix.prefix).filterNot(_ == "/").getOrElse("")

    val mezzanineCredentials = adminApiClient.getDataCredentials(appProps.imcMezzanineCredsId.get).get()
    val mezzanineAuthConfig = new AWSAuthConfig(NexlaDataCredentials.getCreds(appProps.decryptKey, mezzanineCredentials.getCredentialsEnc, mezzanineCredentials.getCredentialsEncIv), mezzanineCredentials.getId)
    val mezzanineS3Creds = MezzanineAuthConfig(mezzanineAuthConfig, mezzanineCredentials)

    DryRunStartRequest(
      uuidHash = uuidHash,
      dryRunId = generateDryRunId,
      userId = userId,
      mezzFullPath = appProps.imcDryRunMezzanineSamplePath,
      mezzBucket = mezzanineBucketPrefix.bucket,
      mezzBucketPrefix = bucketPrefix,
      sourceCredsId = probeInput.credsId,
      inputFilePath = inputFilePath,
      inputFilePathNoBucket = AWSAuthConfig.toBucketPrefix(inputFilePath, false).prefix,
      driverFunction = appProps.imcDryRunDriverFunction,
      packages = packages,
      dstFileExtension = dstFileExtension,
      codeBase64 = codeBase64,
      limited = Some(true),
      extraData = Some(extraData),
      sourceConfig = Some(sourceConfig),
      mezzanineS3Creds = mezzanineS3Creds,
      runRayProcessingJob = codeBase64.isDefined,
      autoStatusFile = appProps.ffMockStatusFile || codeBase64.isEmpty)
  }

  def generateDryRunId: Long = {
    System.currentTimeMillis()
  }

  private def uuidHashCode(prefix: String, strings: List[String]): String = {
    // Generate hash code from concatenated String to have it consistent across different pods
    val signedIntHash = strings.sorted.mkString(",").hashCode()
    prefix + Integer.toString(Math.abs(signedIntHash), Character.MAX_RADIX)
  }

  class SimpleFileReadResult extends FileReadResult[SourceRecord] {
    private var messages: util.List[NexlaMessage] = new util.LinkedList[NexlaMessage]

    override def removeResult: util.List[SourceRecord] = {
      val result: util.List[SourceRecord] = messages.asScala.map(m => new SourceRecord(Maps.newHashMap(), Maps.newHashMap(), "dummy", null, null, null, Schema.STRING_SCHEMA, toJsonString(m))).toList.asJava
      messages = new util.LinkedList[NexlaMessage]
      result
    }

    override def acceptsMoreData: Boolean = true

    override def onEof(ctx: ReadingContext, skip: Boolean, error: Boolean): Unit = {
    }

    override def onException(e: Exception, ctx: ReadingContext): Unit = {
    }

    override def onSuccess(ctx: ReadingContext, fileEofReached: Boolean, datasetMetrics: util.Map[Integer, Metric]): Unit = {
    }

    override def acceptMessage(schema: SchemaContext, ctx: ReadingContext, dataItem: NexlaMessage, eofReached: Boolean, messageNumber: Long, datasetMetrics: util.Map[Integer, Metric]): Unit = {
      messages.add(dataItem)
    }


  }

  def handlePrivatePackages(p: ExtraDataBuilder.PrivatePackages): RayApiClient.PrivatePackages = {
    val dataCreds = adminApiClient.getDataCredentials(p.credentialId).get()
    val creds = NexlaDataCredentials.getCreds(appProps.decryptKey, dataCreds.getCredentialsEnc, dataCreds.getCredentialsEncIv)
    val restAuth = new RestAuthConfig(creds, p.credentialId)
    RayApiClient.PrivatePackages(restAuth.basicUsername, restAuth.basicPassword, p.urls)
  }


}