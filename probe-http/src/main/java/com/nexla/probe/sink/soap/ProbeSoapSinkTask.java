package com.nexla.probe.sink.soap;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.Org;
import com.nexla.common.NexlaKafkaConfig;
import com.nexla.common.NexlaSslContext;
import com.nexla.common.metrics.MetricWithErrors;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogMetricsProducer;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.common.notify.transport.DataMessageProducer;
import com.nexla.common.notify.transport.NexlaMessageTransport;
import com.nexla.common.notify.transport.NoopNexlaMessageTransport;
import com.nexla.connector.soap.sink.SoapSinkTask;
import org.jetbrains.annotations.NotNull;

import java.util.Optional;

import static com.nexla.common.ResourceType.SINK;

public class ProbeSoapSinkTask extends SoapSinkTask {
    public ProbeSoapSinkTask(AdminApiClient adminApiClient) {
        this.adminApiClient = adminApiClient;
    }

    @NotNull
    @Override
    protected ControlMessageProducer createControlMessageProducer(NexlaKafkaConfig controlKafkaConfig) {
        NexlaMessageTransport nexlaMessageTransport = new NoopNexlaMessageTransport();
        return new ControlMessageProducer(nexlaMessageTransport);
    }

    @NotNull
    @Override
    protected DataMessageProducer createDataMessageProducer(NexlaKafkaConfig dataKafkaConfig) {
        NexlaMessageTransport nexlaMessageTransport = new NoopNexlaMessageTransport();
        return new DataMessageProducer(nexlaMessageTransport);
    }


    @Override
    protected void publishMonitoringLog(
            String log,
            NexlaMonitoringLogType type,
            NexlaMonitoringLogSeverity severity) {}

    @Override
    protected void publishMonitoringLogMetrics(MetricWithErrors metricWithErrors) {}
    
}
