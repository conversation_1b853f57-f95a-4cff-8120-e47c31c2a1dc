package com.nexla.probe.api

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import akka.http.scaladsl.model.ContentTypes.`application/json`
import akka.http.scaladsl.model.{HttpEntity, StatusCodes}
import akka.http.scaladsl.testkit.{RouteTestTimeout, ScalatestRouteTest}
import com.dimafeng.testcontainers.{Container, ForAllTestContainer, GenericContainer, MultipleContainers}
import com.google.common.collect.{Lists, Sets}
import com.nexla.admin.client.DataMap
import com.nexla.common.NexlaConstants.{MAP_ID, REDIS_CLUSTER_ENABLED, REDIS_HOSTS, REDIS_TLS_ENABLED}
import com.bazaarvoice.jolt.JsonUtils
import com.google.common.collect.Sets
import com.nexla.admin.client.{AdminApiClient, DataMap}
import com.nexla.common.NexlaConstants.{MAP_ID, REDIS_CLUSTER_ENABLED, REDIS_HOSTS, REDIS_TLS_ENABLED}
import com.nexla.common.StreamUtils._
import com.nexla.common.io.RedisConnect
import com.nexla.common._
import com.nexla.common.ui.PaginatedResult
import com.nexla.connect.common.DbTestUtils._
import com.nexla.connector.ConnectorService
import com.nexla.connector.ConnectorService.{AuthResponse, UNIT_TEST}
import com.nexla.connector.config.MappingConfig
import com.nexla.connector.config.jdbc.JdbcAuthConfig.{PASSWORD, URL, USERNAME}
import com.nexla.connector.config.jdbc.{JdbcAuthConfig, JdbcSinkConnectorConfig}
import com.nexla.connector.config.redis.RedisAuthConfig.ID_FIELD_NAME
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.connector.config.vault.{LocalCredentialsStore, NexlaAppConfig}
import com.nexla.connector.properties.SqlConfigAccessor
import com.nexla.connector.properties.SqlConfigAccessor._
import com.nexla.probe.api.ray.RayDryRunner
import com.nexla.probe.sql.SqlConnectorService
import com.nexla.probe.sql.dto.ListDataTypesResult
import com.nexla.probe.{AppProps, ProbeController, ProbeReadOutput}
import com.nexla.redis.LookupUtils.withLookup
import com.nexla.redis.{Lookup, RedisCreds}
import com.nexla.sc.api.common.ApiResponse
import com.nexla.sc.config.NexlaCredsEncodedConf
import connect.jdbc.sink.dialect._
import one.util.streamex.StreamEx
import org.apache.kafka.common.config.AbstractConfig
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito
import org.mockito.Mockito._
import org.scalatest.{BeforeAndAfterAll, OneInstancePerTest, TagAnnotation}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import software.amazon.awssdk.services.s3.S3Client
import spray.json._

import java.time.format.DateTimeFormatter
import java.time.{LocalDateTime, ZoneOffset}
import java.util
import java.util.Collections
import java.util.function.{BiConsumer, Consumer}
import scala.collection.JavaConverters._
import scala.concurrent.duration._

@com.nexla.test.ScalaIntegrationTests
class ProbeApiHandlerTest
  extends AnyFlatSpecLike
    with OneInstancePerTest
    with ScalatestRouteTest
    with Matchers
    with SprayJsonSupport
    with DefaultJsonProtocol
    with BeforeAndAfterAll
    with ForAllTestContainer {

  private val defaultTestTableName = "default_test_table"

  implicit val ec = scala.concurrent.ExecutionContext.global

  implicit val timeout = RouteTestTimeout(1000000.seconds)

  private val redis = GenericContainer(
    dockerImage = "redis:7.0.12",
    exposedPorts = Seq(6379)
  )

  override val container: Container = MultipleContainers(
    redis
  )

  def toHostPort(redis: GenericContainer): RedisConnect = {
    new RedisConnect(redis.host, redis.mappedPort(6379), 0);
  }

  val DATA_MAP_ID = 1234

  val probeServiceMock = mock(classOf[SqlConnectorService])

  val probeController = oldController(probeServiceMock)

  val probeHelper = new ProbeService(NexlaCredsEncodedConf("", ""), "")

  val props = appProps()

  val adminApi: AdminApiClient = mock(classOf[AdminApiClient])
  val imcDryRunHelper: RayDryRunner = mock(classOf[RayDryRunner])
  val mezzanineS3Client = mock(classOf[S3Client])
  val api = new ProbeApiHandler(probeController, probeHelper, Some(true), imcDryRunHelper, adminApi, Option(mezzanineS3Client), props)
  val sinkConfig: JdbcSinkConnectorConfig = mock(classOf[JdbcSinkConnectorConfig])

  implicit val ProbeInputFormat = com.nexla.probe.api.ProbeInputFormat.Format

  //FIXME Deactivating failed tests in release/v3.2.0
  ignore should "process authentication with forbidden status" in {
    when(probeServiceMock.authenticate(any())).thenReturn(AuthResponse.authError(new RuntimeException))

    val input = ProbeInput(-1, "", "", Some(Map[String, String]()))
    Post("/authenticate/s3", HttpEntity(`application/json`, input.toJson.toString())) ~> api.route ~> check {
      status shouldBe StatusCodes.Forbidden
      responseAs[ApiResponse] shouldBe ApiResponse("Unable to authenticate")
    }
  }

  //FIXME Deactivating failed tests in release/v3.2.0
  ignore should "process authentication with ok status" in {

    when(probeServiceMock.authenticate(any())).thenReturn(AuthResponse.SUCCESS)

    val input = ProbeInput(-1, "", "", Some(Map[String, String]()))
    Post("/authenticate/s3", HttpEntity(`application/json`, input.toJson.toString())) ~> api.route ~> check {
      status shouldBe StatusCodes.OK
      responseAs[ApiResponse] shouldBe ApiResponse("Success")
    }
  }

  //FIXME Deactivating failed tests in release/v3.2.0
  ignore should "read postgres" in {
    val readMessage = new NexlaMessage(lhm("key", "value"))
    val serviceMock = new SqlConnectorService(null, null) {
      override def readStream(c: AbstractConfig): StreamEx[NexlaMessage] = StreamEx.of[NexlaMessage](readMessage)
    }
    val probeController = oldController(serviceMock)

    val probeApi = new ProbeApiHandler(probeController, probeHelper, Some(true), imcDryRunHelper, adminApi, Option(mezzanineS3Client), props)
    val input = ProbeInput(-1, "", "", Some(baseConfig))

    Post("/read/postgres", HttpEntity(`application/json`, input.toJson.toString())) ~> probeApi.route ~> check {
      status shouldBe StatusCodes.OK
      val response = StreamUtils.jsonUtil().stringToType(responseAs[String], classOf[ProbeReadOutput])
      response should matchPattern {
        case a: ProbeReadOutput if a.messages == List(readMessage).asJava =>
      }
    }
  }

  Seq("postgres", "mysql", "oracle", "sqlserver").foreach { implicit database =>
    def parsedResponse = {
      val response: util.Map[String, AnyRef] = JsonUtils.jsonToMap(responseAs[String]).getOrDefault("response", Collections.emptyMap()).asInstanceOf[util.Map[String, AnyRef]]
      val data = response.getOrDefault("data", Collections.emptyList()).asInstanceOf[util.List[Map[String, AnyRef]]]
      data
    }

    val setup = () => {
      val (db, insert) = using(database, defaultTestTableName)

      insert(1, "2017-01-01 05:41:01", "test1")
      insert(2, "2017-01-01 05:42:01", "test2")
      insert(3, "2017-01-01 05:43:01", "test3")
      insert(4, "2017-01-01 05:44:01", "test4")
      insert(5, "2017-01-01 05:45:01", "test5")
      insert(6, "2017-01-01 05:26:01", "test6")
      insert(7, "2017-01-01 05:47:01", "test7")

      val probeApi = new ProbeApiHandler(oldController(new SqlConnectorService(new LocalCredentialsStore(), adminApi)), probeHelper, Some(false), imcDryRunHelper, adminApi, Option(mezzanineS3Client), props)

      val configuration = baseConfig ++ Map(
        ID_FIELD_NAME -> "*",
        URL -> db.url,
        TABLE -> (if (database == "oracle") defaultTestTableName.toUpperCase else defaultTestTableName), // oracle doesn't recognize lowercased table name here due to escapeStart and escapeEnd // todo fix
        JdbcAuthConfig.DATABASE_NAME -> db.databaseName,
        USERNAME -> db.username,
        PASSWORD -> db.password,
        NexlaConstants.CREDENTIALS_TYPE -> database,
        POLL_MS -> "1",
        MONITOR_POLL_MS -> "10000",
      )

      (configuration, database, probeApi)
    }

    //FIXME Deactivating failed tests in release/v3.2.0
    ignore should s"""read $database samples "incrementing" mode""" in { //TODO Fix and remove ignore
      val (configuration, database, probeApi) = setup()

      val input = ProbeInput(-1, "", "", Some(configuration ++ Map(
        MODE -> MODE_VALUE_INCREMENTING,
        INCREMENTING_COLUMN_NAME -> "id",
        INCREMENTING_LOAD_FROM -> "5",
      )))

      Post(s"/read/sample/$database", HttpEntity(`application/json`, input.toJson.toString())) ~> probeApi.route ~> check {
        status shouldBe StatusCodes.OK

        parsedResponse shouldBe List(
          Map("id" -> 5, "ts" -> toTs("2017-01-01 05:45:01"), "description" -> "test5").asJava,
          Map("id" -> 6, "ts" -> toTs("2017-01-01 05:26:01"), "description" -> "test6").asJava,
          Map("id" -> 7, "ts" -> toTs("2017-01-01 05:47:01"), "description" -> "test7").asJava,
        ).asJava
      }
    }

    //FIXME Deactivating failed tests in release/v3.2.0
    ignore should s"""read $database samples "timestamp" mode""" in {//TODO Fix and remove ignore
      val (configuration, database, probeApi) = setup()

      val pollFromStr = toTs("2017-01-01 05:42:31").toString
      val input = ProbeInput(-1, "", "", Some(configuration ++ Map(
        MODE -> SqlConfigAccessor.MODE_VALUE_TIMESTAMP,
        SqlConfigAccessor.TIMESTAMP_LOAD_FROM -> pollFromStr,
        SqlConfigAccessor.TIMESTAMP_COLUMN_NAME -> "ts",
      )))

      Post(s"/read/sample/$database", HttpEntity(`application/json`, input.toJson.toString())) ~> probeApi.route ~> check {
        status shouldBe StatusCodes.OK

        parsedResponse shouldBe List(
          Map("id" -> 3, "ts" -> toTs("2017-01-01 05:43:01"), "description" -> "test3").asJava,
          Map("id" -> 4, "ts" -> toTs("2017-01-01 05:44:01"), "description" -> "test4").asJava,
          Map("id" -> 5, "ts" -> toTs("2017-01-01 05:45:01"), "description" -> "test5").asJava,
          Map("id" -> 7, "ts" -> toTs("2017-01-01 05:47:01"), "description" -> "test7").asJava,
        ).asJava
      }
    }

    //FIXME Deactivating failed tests in release/v3.2.0
    ignore should s"""read $database samples "incrementing,timestamp" mode""" in {//TODO Fix and remove ignore
      val (configuration, database, probeApi) = setup()

      val pollFromStr = toTs("2017-01-01 05:42:02").toString;
      val input = ProbeInput(-1, "", "", Some(configuration ++ Map(
        MODE -> SqlConfigAccessor.MODE_VALUE_TIMESTAMP_INCREMENTING,
        INCREMENTING_COLUMN_NAME -> "id",
        INCREMENTING_LOAD_FROM -> "5",
        SqlConfigAccessor.TIMESTAMP_LOAD_FROM -> pollFromStr,
        SqlConfigAccessor.TIMESTAMP_COLUMN_NAME -> "ts",
      )))

      Post(s"/read/sample/$database", HttpEntity(`application/json`, input.toJson.toString())) ~> probeApi.route ~> check {
        status shouldBe StatusCodes.OK

        parsedResponse shouldBe List(
          Map("id" -> 5, "ts" -> toTs("2017-01-01 05:45:01"), "description" -> "test5").asJava,
          Map("id" -> 7, "ts" -> toTs("2017-01-01 05:47:01"), "description" -> "test7").asJava,
        ).asJava
      }
    }
  }

  //FIXME Deactivating failed tests in release/v3.2.0
  ignore should "list data types" in {//TODO Fix it and remove ignore

    val probeServiceMock = spy(new SqlConnectorService(null, null))
    val probeController = oldController(probeServiceMock)
    val api = new ProbeApiHandler(probeController, probeHelper, Some(true), imcDryRunHelper, adminApi, Option(mezzanineS3Client), props)

    Get("/listDataTypes/postgres") ~> api.route ~> check {
      status shouldBe StatusCodes.OK
      val response = StreamUtils.jsonUtil().stringToType(responseAs[String], classOf[ListDataTypesResult])
      response should matchPattern {
        case a: ListDataTypesResult if !a.getDbTypes.isEmpty =>
      }
    }
  }

  //FIXME Deactivating failed tests in release/v3.2.0
  ignore should "read quarantine sample messages with success" in {
    val nexlaMetaData = map("sourceType", "s3", "topic", "dataset-19736-source-9747")
    val error = map("message", "Too many entries", "lineNumber", "8")
    val rawMessage = map("message", "test")
    val messages: util.List[Object] = util.Arrays.asList(map("nexlaMetaData", nexlaMetaData, "error", error, "rawMessage", rawMessage))

    val result = new PaginatedResult().setData(messages).setMeta(1, 1, 1).asInstanceOf[PaginatedResult[util.List[Object]]]

    val kafkaController = new ProbeController(null, null, props, appConfig, null, null, null) {
      override def readQuarantine(input: util.Map[String, String]) = {
        result
      }
    }

    val quarantineApi = new ProbeApiHandler(kafkaController, probeHelper, Some(true), imcDryRunHelper, adminApi, Option(mezzanineS3Client), props)

    val expectedString: String =
      "{\"data\":[{\"nexlaMetaData\":{\"sourceType\":\"s3\",\"topic\":\"dataset-19736-source-9747\"},\"error\":{\"message\":\"Too many entries\",\"lineNumber\":\"8\"},\"rawMessage\":{\"message\":\"test\"}}],\"meta\":{\"currentPage\":1,\"totalCount\":1,\"pageCount\":1}}"

    Post("/read/quarantine", HttpEntity(`application/json`, Map[String, String]().toJson.toString())) ~> quarantineApi.route ~> check {
      status shouldBe StatusCodes.OK
      responseAs[String] shouldBe expectedString
    }
  }

  //FIXME Deactivating failed tests in release/v3.2.0
  ignore should "read data_map sample with success" in {
    val entries = util.Arrays.asList(map("ID", "abc", "key", "value1"), map("ID", "defc", "key", "value2"))
    val creds = new RedisCreds(Sets.newHashSet(toHostPort(redis)), false, java.util.Optional.empty(), java.util.Optional.empty())
    fillDataMap(creds, DATA_MAP_ID, entries.asInstanceOf[util.List[util.Map[String, String]]])

    val input = ProbeInput(-1, "", "", Some(Map(ID_FIELD_NAME -> "*", MAP_ID -> DATA_MAP_ID.toString, UNIT_TEST -> "true",
      REDIS_HOSTS -> toHostPort(redis).toUrl,
      REDIS_CLUSTER_ENABLED -> "false",
      REDIS_TLS_ENABLED -> "false")))

    Post("/read/sample/data_map", HttpEntity(`application/json`, input.toJson.toString())) ~> api.route ~> check {
      status shouldBe StatusCodes.OK
      val response = StreamUtils.jsonUtil().stringToType(responseAs[String], classOf[util.List[util.Map[String, String]]])
      response.size() shouldBe 2
    }
  }

  private def fillDataMap(actualRedisCreds: RedisCreds, mapId: Int, entries: util.List[util.Map[String, String]]): Unit = {
    val dm = new DataMap
    dm.setEmitDataDefault(true)
    dm.setUseVersioning(true)
    dm.setMapPrimaryKey("ID")
    dm.setDataSinkId(5001)
    dm.setId(mapId)
    dm.setDataMap(entries)

    val consumer = new Consumer[Lookup] {
      override def accept(lookup: Lookup): Unit = {
        lookup.save(dm)
      }
    }
    withLookup(actualRedisCreds, dm.getId, consumer)
  }

  private def appProps() = {
    val props = mock(classOf[AppProps])
    Mockito.when(props.nexlaCredsEncoded).thenReturn(NexlaCredsEncodedConf("", ""))
    Mockito.when(props.consumerMaxPartitionFetchBytes).thenReturn(None)
    Mockito.when(props.consumerFetchMaxBytes).thenReturn(None)
    props
  }

  private def toTs(tsAsStr: String): Long = {
    LocalDateTime.parse(tsAsStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
      .atZone(ZoneOffset.UTC).toEpochSecond * 1000
  }

  val appConfig = mock(classOf[NexlaAppConfig])

  private def oldController(c: ConnectorService[_]) = {
    new ProbeController(null, null, appProps(), appConfig, null, null, null) {
      override def getProbeService(connectionType: ConnectionType): ConnectorService[_] = c

      override protected def parseAuthConfig(decryptKey: String,
                                             probeInput: probe.ProbeInput,
                                             connectionType: ConnectionType): BaseAuthConfig = null
    }
  }

  private def baseConfig = Map(UNIT_TEST -> "true")

  private def recreateTable(db: DbData, tableName: String, dialect: DbDialect): Unit = {

    dropIfExists(db, tableName)

    val config = new MappingConfig()
    config.setMapping(lhm("_",
      lhm(
        "id", "INT",
        "ts", if (db.image.startsWith("mysql:") || db.image.startsWith("mssql")) "DATETIME" else "TIMESTAMP",
        "description", "VARCHAR(100)"
      )
    ))

    val createSql = dialect
      .getCreateSql(
        tableName,
        Collections.singletonList("id"),
        util.List.of("ts", "description"),
        () => null,
        config,
        sinkConfig
      )

    try {
      val connection = createConnection(db)
      try {
        connection.createStatement.execute(createSql)
        connection.commit()
      } finally if (connection != null) connection.close()
    }
  }


  private def dropIfExists(db: DbData, tableName: String): Unit = {
    try {
      val connection = createConnection(db)
      try {
        connection.createStatement.execute(
          if (db.url.contains("oracle"))
            s"""BEGIN EXECUTE IMMEDIATE 'DROP TABLE "$tableName"'; EXCEPTION WHEN OTHERS THEN NULL; END;"""
          else
            s"""DROP TABLE IF EXISTS $tableName"""
        )
        connection.commit()
      } finally if (connection != null) connection.close()
    }
  }

  private def using(dbType: String, tableName: String) = {
    def insertRow(db: DbData)(id: Int, ts: String, description: String): Unit = {
      try {
        val connection = createConnection(db)
        try {
          val time = if (dbType == "oracle") s"TO_DATE('$ts', 'YYYY-MM-DD HH24:MI:SS')" else s"'$ts'"
          val sql = if (dbType == "sqlserver")
            s"INSERT INTO $tableName(id, ts, description) VALUES ($id, $time, '${description}')"
          else
            s"INSERT INTO $tableName VALUES ($id, $time, '${description}')"

          connection.createStatement.execute(sql)
          connection.commit()
        } finally if (connection != null) connection.close()
      }
    }


    def prepare(dbData: DbData, dialect: DbDialect) = {
      val db = newDb(dbData)
      recreateTable(db, tableName, dialect)

      (db, insertRow(db) _)
    }

    dbType match {
      case "postgres" => prepare(POSTGRES.get, new PostgreSqlDialect())
      case "mysql" => prepare(MYSQL_5_7.get, new MySqlDialect())
      case "oracle" => prepare(ORACLE.get, new OracleDialect())
      case "sqlserver" => prepare(SQLSERVER.get, new SqlServerDialect())
      case _ => throw new RuntimeException(s"Add db setup method for $dbType...")
    }
  }
}
