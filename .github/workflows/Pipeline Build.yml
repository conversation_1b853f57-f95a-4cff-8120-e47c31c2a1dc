name: Pipeline Build

on:
  workflow_dispatch:
    inputs:
      branch:
        type: string
        description: The branch, tag or SHA to checkout
        required: true
      projects:
        type: string
        description: |
          Comma separated list of projects to build. Possible values:

          ALL,
          kafka-connect-bigquery-sink,
          kafka-connect-bigquery-source,
          kafka-connect-file-sink,
          kafka-connect-file-source,
          kafka-connect-jdbc-sink,
          kafka-connect-jdbc-source,
          kafka-connect-documentdb-sink,
          kafka-connect-documentdb-source,
          kafka-connect-redis-sink,
          kafka-connect-rest-sink,
          kafka-connect-rest-source,
          kafka-connect-soap-sink,
          kafka-connect-soap-source,
          kafka-connect-spreadsheets-sink,
          kafka-connect-iceberg-source,
          kafka-connect-iceberg-sink,
          kafka-connect-api-streams-source,
          kafka-connect-vectordb-source,
          kafka-connect-vectordb-sink,
          probe-http,
          inmemory-connector,
          replication-connector,
          cloud-job-connector
        required: true
        default: "ALL"
      vulnerability:
        description: "Generate vulnerabilities report"
        type: boolean
        required: false
        default: false
      ecr:
        description: "ECR"
        type: boolean
        required: false
        default: true
      dockerhub:
        description: "DOCKERHUB"
        type: boolean
        required: false
        default: false

jobs:
  build:
    runs-on: nexla-dind-runners-large
    timeout-minutes: 240
    steps:
      - name: Validate Projects
        shell: bash
        run: |
          projects="${{ github.event.inputs.projects }}"
  
          if [ "$projects" = "ALL" ]; then
            echo "Building all projects"
            exit 0
          fi
          
          kafkaConnectBigquerySink=false
          kafkaConnectBigquerySource=false
          kafkaConnectFileSink=false
          kafkaConnectFileSource=false
          kafkaConnectJdbcSink=false
          kafkaConnectJdbcSource=false
          kafkaConnectDocumentdbSink=false
          kafkaConnectDocumentdbSource=false
          kafkaConnectRedisSink=false
          kafkaConnectRestSink=false
          kafkaConnectRestSource=false
          kafkaConnectSoapSink=false
          kafkaConnectSoapSource=false
          kafkaConnectSpreadsheetsSink=false
          kafkaConnectIcebergSource=false
          kafkaConnectIcebergSink=false
          kafkaConnectApiStreamsSource=false
          kafkaConnectVectordbSource=false
          kafkaConnectVectordbSink=false
          kafkaConnectVectordbSource=false
          kafkaConnectVectordbSink=false
          probeHttp=false
          inmemoryConnector=false
          replicationConnector=false
          cloudJobConnector=false
          
          if echo "$projects" | grep -q "kafka-connect-bigquery-sink"; then
            kafkaConnectBigquerySink=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-bigquery-source"; then
            kafkaConnectBigquerySource=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-file-sink"; then
            kafkaConnectFileSink=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-file-source"; then
            kafkaConnectFileSource=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-jdbc-sink"; then
            kafkaConnectJdbcSink=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-jdbc-source"; then
            kafkaConnectJdbcSource=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-documentdb-sink"; then
            kafkaConnectDocumentdbSink=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-documentdb-source"; then
            kafkaConnectDocumentdbSource=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-redis-sink"; then
            kafkaConnectRedisSink=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-rest-sink"; then
            kafkaConnectRestSink=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-rest-source"; then
            kafkaConnectRestSource=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-soap-sink"; then
            kafkaConnectSoapSink=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-soap-source"; then
            kafkaConnectSoapSource=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-spreadsheets-sink"; then
            kafkaConnectSpreadsheetsSink=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-iceberg-source"; then
            kafkaConnectIcebergSource=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-iceberg-sink"; then
            kafkaConnectIcebergSink=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-api-streams-source"; then
            kafkaConnectApiStreamsSource=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-vectordb-source"; then
            kafkaConnectVectordbSource=true
          fi
          
          if echo "$projects" | grep -q "kafka-connect-vectordb-sink"; then
            kafkaConnectVectordbSink=true
          fi
          
          if echo "$projects" | grep -q "probe-http"; then
            probeHttp=true
          fi
          
          if echo "$projects" | grep -q "inmemory-connector"; then
            inmemoryConnector=true
          fi
          
          if echo "$projects" | grep -q "replication-connector"; then
            replicationConnector=true
          fi
          
          if echo "$projects" | grep -q "cloud-job-connector"; then
            cloudJobConnector=true
          fi

          echo "httpSink: $httpSink"
          echo "kafkaConnectBigquerySink: $kafkaConnectBigquerySink"
          echo "kafkaConnectBigquerySource: $kafkaConnectBigquerySource"
          echo "kafkaConnectFileSink: $kafkaConnectFileSink"
          echo "kafkaConnectFileSource: $kafkaConnectFileSource"
          echo "kafkaConnectJdbcSink: $kafkaConnectJdbcSink"
          echo "kafkaConnectJdbcSource: $kafkaConnectJdbcSource"
          echo "kafkaConnectDocumentdbSink: $kafkaConnectDocumentdbSink"
          echo "kafkaConnectDocumentdbSource: $kafkaConnectDocumentdbSource"
          echo "kafkaConnectRedisSink: $kafkaConnectRedisSink"
          echo "kafkaConnectRestSink: $kafkaConnectRestSink"
          echo "kafkaConnectRestSource: $kafkaConnectRestSource"
          echo "kafkaConnectSoapSink: $kafkaConnectSoapSink"
          echo "kafkaConnectSoapSource: $kafkaConnectSoapSource"
          echo "kafkaConnectSpreadsheetsSink: $kafkaConnectSpreadsheetsSink"
          echo "kafkaConnectIcebergSource: $kafkaConnectIcebergSource"
          echo "kafkaConnectIcebergSink: $kafkaConnectIcebergSink"
          echo "kafkaConnectApiStreamsSource: $kafkaConnectApiStreamsSource"
          echo "kafkaConnectVectordbSource: $kafkaConnectVectordbSource"
          echo "kafkaConnectVectordbSink: $kafkaConnectVectordbSink"
          echo "probeHttp: $probeHttp"
          echo "inmemoryConnector: $inmemoryConnector"
          echo "replicationConnector: $replicationConnector"
          echo "cloudJobConnector: $cloudJobConnector"

          if [ "$httpSink" = false ] && \
            [ "$kafkaConnectBigquerySink" = false ] && \
            [ "$kafkaConnectBigquerySource" = false ] && \
            [ "$kafkaConnectFileSink" = false ] && \
            [ "$kafkaConnectFileSource" = false ] && \
            [ "$kafkaConnectJdbcSink" = false ] && \
            [ "$kafkaConnectJdbcSource" = false ] && \
            [ "$kafkaConnectDocumentdbSink" = false ] && \
            [ "$kafkaConnectDocumentdbSource" = false ] && \
            [ "$kafkaConnectRedisSink" = false ] && \
            [ "$kafkaConnectRestSink" = false ] && \
            [ "$kafkaConnectRestSource" = false ] && \
            [ "$kafkaConnectSoapSink" = false ] && \
            [ "$kafkaConnectSoapSource" = false ] && \
            [ "$kafkaConnectSpreadsheetsSink" = false ] && \
            [ "$kafkaConnectIcebergSource" = false ] && \
            [ "$kafkaConnectIcebergSink" = false ] && \
            [ "$kafkaConnectApiStreamsSource" = false ] && \
            [ "$kafkaConnectVectordbSource" = false ] && \
            [ "$kafkaConnectVectordbSink" = false ] && \
            [ "$probeHttp" = false ] && \
            [ "$inmemoryConnector" = false ] && \
            [ "$replicationConnector" = false ] && \
            [ "$cloudJobConnector" = false ]; then
          echo "No valid projects found"
            exit 1
          fi
          echo "Found valid projects, proceeding with build."

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Code Artifact Login
        uses: nexla/cloud-actions/actions/code-artifact-login@v1

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: 11
          distribution: corretto

      - uses: sdkman/sdkman-action@master
        id: sdkman
        with:
          candidate: scala
          version: 2.12.14

      - name: Set up Maven
        uses: stCarolas/setup-maven@v5
        with:
          maven-version: 3.9.9

      - name: Restore Maven cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.m2/repository
          key: maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            maven-

      - name: Build with Maven
        env:
          MAVEN_OPTS: -Xmx8g -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/root/.m2/heapdump
        shell: bash
        run: |
          projects="${{ github.event.inputs.projects }}"
          if [ "$projects" == "ALL" ]; then
            my_array=(
              "kafka-connect-bigquery-sink"
              "kafka-connect-bigquery-source"
              "kafka-connect-file-sink"
              "kafka-connect-file-source"
              "kafka-connect-jdbc-sink"
              "kafka-connect-jdbc-source"
              "kafka-connect-documentdb-sink"
              "kafka-connect-documentdb-source"
              "kafka-connect-redis-sink"
              "kafka-connect-rest-sink"
              "kafka-connect-rest-source"
              "kafka-connect-soap-sink"
              "kafka-connect-soap-source"
              "kafka-connect-spreadsheets-sink"
              "kafka-connect-iceberg-source"
              "kafka-connect-iceberg-sink"
              "kafka-connect-api-streams-source"
              "kafka-connect-vectordb-source"
              "kafka-connect-vectordb-sink"
              "probe-http"
              "inmemory-connector"
              "replication-connector"
              "cloud-job-connector"
            )
          else
            IFS=',' read -r -a my_array <<< "$projects"
          fi
          
          # build connector apps
          maven_projects=""
          for project in "${my_array[@]}"; do
            trimmed_project=$(echo "$project" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
            maven_projects="$maven_projects -pl $trimmed_project"
          done
          
          if [ -z "$maven_projects" ]; then
            echo "No projects to build"
          else
            echo "Building projects: $maven_projects"             
            mvn --settings=.mvn/local-settings.xml --no-transfer-progress -U -T 10 clean install --also-make -Dmaven.test.skip -DskipTests -e $maven_projects
          fi

      - name: Generate Image Tag
        shell: bash
        run: |
          calculatedSha=$(git rev-parse --short ${{ github.sha }})
          echo "short hash: $calculatedSha"
          tag="${{ github.event.inputs.branch }}-${{ github.run_number }}-$calculatedSha"
          tag="${tag//\//-}"
          echo "tag: $tag"
          echo "IMAGE_TAG=$tag" >> $GITHUB_ENV

      - name: Login to ECR
        if: ${{ inputs.ecr || inputs.dockerhub }}
        uses: nexla/cloud-actions/actions/ecr-login@v1

      - name: Login into docker sessions
        if: ${{ inputs.ecr || inputs.dockerhub }}
        env:
          AWS_ECR_ACCOUNT_URL: ************.dkr.ecr.us-east-1.amazonaws.com
          DOCKERHUB_ACCOUNT_URL: registry-1.docker.io
        shell: bash
        run: |
          # We have to login to ecr even for dockerhub because the references in the docker file refer to ecr
          echo ${{ env.ECR_PASS_INTERACTIVE }} | docker login --username AWS --password-stdin ${{ env.AWS_ECR_ACCOUNT_URL }}
          
          # We have to login to dockerhub because older branches have dockerhub references
          echo ${{ secrets.DOCKERHUB_PASSWORD }} | docker login --username ${{ secrets.DOCKERHUB_USERNAME }} --password-stdin

      - name: Push to ECR
        if: ${{ inputs.ecr }}
        env:
          DOCKER_URL: ************.dkr.ecr.us-east-1.amazonaws.com
        shell: bash
        run: |
          # The login to ecr occurred in the previous step
          projects="${{ github.event.inputs.projects }}"
          if [ "$projects" == "ALL" ]; then
           my_array=(
              "kafka-connect-bigquery-sink"
              "kafka-connect-bigquery-source"
              "kafka-connect-file-sink"
              "kafka-connect-file-source"
              "kafka-connect-jdbc-sink"
              "kafka-connect-jdbc-source"
              "kafka-connect-documentdb-sink"
              "kafka-connect-documentdb-source"
              "kafka-connect-redis-sink"
              "kafka-connect-rest-sink"
              "kafka-connect-rest-source"
              "kafka-connect-soap-sink"
              "kafka-connect-soap-source"
              "kafka-connect-spreadsheets-sink"
              "kafka-connect-iceberg-source"
              "kafka-connect-iceberg-sink"
              "kafka-connect-api-streams-source"
              "kafka-connect-vectordb-source"
              "kafka-connect-vectordb-sink"
              "probe-http"
              "inmemory-connector"
              "replication-connector"
              "cloud-job-connector"
            )
          else
            IFS=',' read -r -a my_array <<< "$projects"
          fi
          
          for project in "${my_array[@]}"; do
            trimmed_project=$(echo "$project" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
          
            pushd $trimmed_project
          
            if [ ! -f "docker/Dockerfile" ]; then
              echo "Dockerfile not found for $trimmed_project"
              exit 1
            fi
            
            echo "building docker file for: $trimmed_project"
            docker build -t ${{ env.DOCKER_URL }}/${trimmed_project}:${{ env.IMAGE_TAG }} -f docker/Dockerfile . 
            
            echo "pushing docker file for: $trimmed_project"
            docker push ${{ env.DOCKER_URL }}/${trimmed_project}:${{ env.IMAGE_TAG }}
            
            popd
          done

      - name: Push to Dockerhub
        if: ${{ inputs.dockerhub }}
        env:
          DOCKER_URL: nexla
        shell: bash
        run: |
          # The login to dockerhub occurred in the previous step

          projects="${{ github.event.inputs.projects }}"
          if [ "$projects" == "ALL" ]; then
           my_array=(
              "kafka-connect-bigquery-sink"
              "kafka-connect-bigquery-source"
              "kafka-connect-file-sink"
              "kafka-connect-file-source"
              "kafka-connect-jdbc-sink"
              "kafka-connect-jdbc-source"
              "kafka-connect-documentdb-sink"
              "kafka-connect-documentdb-source"
              "kafka-connect-redis-sink"
              "kafka-connect-rest-sink"
              "kafka-connect-rest-source"
              "kafka-connect-soap-sink"
              "kafka-connect-soap-source"
              "kafka-connect-spreadsheets-sink"
              "kafka-connect-iceberg-source"
              "kafka-connect-iceberg-sink"
              "kafka-connect-api-streams-source"
              "kafka-connect-vectordb-source"
              "kafka-connect-vectordb-sink"
              "probe-http"
              "inmemory-connector"
              "replication-connector"
              "cloud-job-connector"
            )
          else
            IFS=',' read -r -a my_array <<< "$projects"
          fi
          
          for project in "${my_array[@]}"; do
            trimmed_project=$(echo "$project" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
          
            pushd $trimmed_project
          
            if [ ! -f "docker/Dockerfile" ]; then
              echo "Dockerfile not found for $trimmed_project"
              exit 1
            fi
          
            echo "building docker file for: $trimmed_project"
            docker build -t ${{ env.DOCKER_URL }}/${trimmed_project}:${{ env.IMAGE_TAG }} -f docker/Dockerfile . 
          
            echo "pushing docker file for: $trimmed_project"
            docker push ${{ env.DOCKER_URL }}/${trimmed_project}:${{ env.IMAGE_TAG }}

            popd
          done

      - name: Setup Trivy
        if: ${{ inputs.vulnerability }}
        uses: aquasecurity/setup-trivy@v0.2.0

      - name: Run Trivy
        if: ${{ inputs.vulnerability }}
        shell: bash
        run: |
          if [ "${{ github.event.inputs.ecr }}" != "true" ] && [ "${{ github.event.inputs.dockerhub }}" != "true" ]; then
            echo "Skipping Trivy run because neither ECR nor Dockerhub is enabled"
            exit 0
          fi
          
          if [ "${{ github.event.inputs.ecr }}" == "true" ]; then
            docker_url="************.dkr.ecr.us-east-1.amazonaws.com"
          else
            docker_url="nexla"
          fi          
          
          echo "Generating Trivy reports"

          projects="${{ github.event.inputs.projects }}"
          if [ "$projects" == "ALL" ]; then
            my_array=(
              "kafka-connect-bigquery-sink"
              "kafka-connect-bigquery-source"
              "kafka-connect-file-sink"
              "kafka-connect-file-source"
              "kafka-connect-jdbc-sink"
              "kafka-connect-jdbc-source"
              "kafka-connect-documentdb-sink"
              "kafka-connect-documentdb-source"
              "kafka-connect-redis-sink"
              "kafka-connect-rest-sink"
              "kafka-connect-rest-source"
              "kafka-connect-soap-sink"
              "kafka-connect-soap-source"
              "kafka-connect-spreadsheets-sink"
              "kafka-connect-iceberg-source"
              "kafka-connect-iceberg-sink"
              "kafka-connect-api-streams-source"
              "kafka-connect-vectordb-source"
              "kafka-connect-vectordb-sink"
              "probe-http"
              "inmemory-connector"
              "replication-connector"
              "cloud-job-connector"
            )
          else
            IFS=',' read -r -a my_array <<< "$projects"
          fi
          
          echo "Downloading html template"
          curl -LJO https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/html.tpl
          
          echo "Creating trivy-reports directory"
          mkdir trivy-reports
          
          for project in "${my_array[@]}"; do
            trimmed_project=$(echo "$project" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
  
            echo "building trivy report file for: $trimmed_project"
            trivy image --format template --template "@html.tpl" -o trivy-reports/${trimmed_project}.html ${docker_url}/${trimmed_project}:${{ env.IMAGE_TAG }}
            
          done

      - name: Upload Trivy Artifacts
        if: ${{ inputs.vulnerability }}
        uses: actions/upload-artifact@v4
        with:
          name: Trivy
          path: |
            trivy-reports/*
          retention-days: 10

      - name: Generate Summary
        shell: bash
        run: |
          branch="${{ github.event.inputs.branch }}"
          
          summary="# Build Success  🚀\n"
          summary="${summary}#### Branch: \`${branch}\`, SHA: \`${{ github.sha }}\`\n"
          summary="${summary}--------------------\n"
          summary="${summary}#### Tag: \`${{ env.IMAGE_TAG }}\`\n"
          summary="${summary}#### Project Build Status\n"
          summary="${summary}| Name                   | Build         | ECR        | Dockerhub    |\n"          
          summary="${summary}| ---------------------- |---------------|------------|--------------|\n"
          
          # List of projects
          projects_list=(
            "kafka-connect-bigquery-sink"
            "kafka-connect-bigquery-source"
            "kafka-connect-file-sink"
            "kafka-connect-file-source"
            "kafka-connect-jdbc-sink"
            "kafka-connect-jdbc-source"
            "kafka-connect-documentdb-sink"
            "kafka-connect-documentdb-source"
            "kafka-connect-redis-sink"
            "kafka-connect-rest-sink"
            "kafka-connect-rest-source"
            "kafka-connect-soap-sink"
            "kafka-connect-soap-source"
            "kafka-connect-spreadsheets-sink"
            "kafka-connect-iceberg-source"
            "kafka-connect-iceberg-sink"
            "kafka-connect-api-streams-source"
            "kafka-connect-vectordb-source"
            "kafka-connect-vectordb-sink"
            "probe-http"
            "inmemory-connector"
            "replication-connector"
            "cloud-job-connector"
          )
          projects="${{ github.event.inputs.projects }}"
          if [ "$projects" == "ALL" ]; then
            projects=(
              "kafka-connect-bigquery-sink"
              "kafka-connect-bigquery-source"
              "kafka-connect-file-sink"
              "kafka-connect-file-source"
              "kafka-connect-jdbc-sink"
              "kafka-connect-jdbc-source"
              "kafka-connect-documentdb-sink"
              "kafka-connect-documentdb-source"
              "kafka-connect-redis-sink"
              "kafka-connect-rest-sink"
              "kafka-connect-rest-source"
              "kafka-connect-soap-sink"
              "kafka-connect-soap-source"
              "kafka-connect-spreadsheets-sink"
              "kafka-connect-iceberg-source"
              "kafka-connect-iceberg-sink"
              "kafka-connect-api-streams-source"
              "kafka-connect-vectordb-source"
              "kafka-connect-vectordb-sink"
              "probe-http"
              "inmemory-connector"
              "replication-connector"
              "cloud-job-connector"
            )
          fi

          # Loop through the list and add each project to the table
          for project in "${projects_list[@]}"; do        
            built=false
            status=""
            ecr_status=""
            dockerhub_status=""
          
            if echo "$projects" | grep -q "$project"; then
              built=true
              status=":white_check_mark:"
            fi

            if [ "$built" = true ] && [ "${{ github.event.inputs.ecr }}" == "true" ]; then
              ecr_status=":white_check_mark:"
            fi

            if [ "$built" = true ] && [ "${{ github.event.inputs.dockerhub }}" == "true" ]; then
              dockerhub_status=":white_check_mark:"
            fi
  
            summary="${summary}| $project | $status | $ecr_status | $dockerhub_status |\n"            
          done
          
          echo -e "$summary" >> "$GITHUB_STEP_SUMMARY"
