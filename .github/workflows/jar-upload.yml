name: Jar Upload

on:
  workflow_dispatch:
    inputs:
      branch:
        type: string
        description: The branch, tag or SHA to checkout
        required: true
      version:
        type: string
        description: Base version to use in artifact version string (e.g., 1.0.0-SNAPSHOT)
        required: true
      ignored_modules:
        type: string
        description: Comma-separated list of modules to ignore during build
        required: false
        default: ''
      java_version:
        type: string
        description: Java version to use (e.g., 11, 17, 21)
        required: false
        default: '11'

jobs:
  build:
    runs-on: nexla-dind-runners-large
    timeout-minutes: 320
    env:
      MAVEN_SETTINGS: .mvn/local-settings.xml
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-2

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: ${{ github.event.inputs.java_version }}
          distribution: corretto

      - name: Set up Maven
        uses: stCarolas/setup-maven@v5
        with:
          maven-version: 3.9.9

      - name: Restore Maven cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.m2/repository
          key: maven-${{ hashFiles('**/pom.xml') }}-java${{ github.event.inputs.java_version }}
          restore-keys: |
            maven-${{ hashFiles('**/pom.xml') }}-
            maven-

      - name: Install AWS CLI
        run: |
          sudo apt-get update
          sudo apt-get install -y awscli

      - name: Pre-Build (Parallel)
        run: |
          # ECR & CodeArtifact Login
          aws ecr get-login-password --region us-east-1
          export CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain nexla --domain-owner 433433586750 --query authorizationToken --output text)
          echo "CODEARTIFACT_AUTH_TOKEN=$CODEARTIFACT_AUTH_TOKEN" >> $GITHUB_ENV
          # Install Trivy
          sudo apt-get update && sudo apt-get install -y curl
          curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sudo sh -s -- -b /usr/local/bin v0.40.0
          curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/html.tpl > html.tpl
          mkdir -p reports
          # Prepare file for tags
          echo "" > image_versions.txt

      - name: Process Ignored Modules
        id: ignored_modules
        run: |
          if [ -n "${{ github.event.inputs.ignored_modules }}" ]; then
            IGNORED_ARGS=$(echo "${{ github.event.inputs.ignored_modules }}" | tr ',' '\n' | while read module; do
              echo -n "-pl '!$module' "
            done)
            echo "ignored_args=$IGNORED_ARGS" >> $GITHUB_OUTPUT
          else
            echo "ignored_args=" >> $GITHUB_OUTPUT
          fi

      - name: Export CodeArtifact Token for Maven
        run: |
          echo "CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain nexla --domain-owner 433433586750 --query authorizationToken --output text)" >> $GITHUB_ENV

      - name: Build & Deploy Jar
        env:
          _JAVA_OPTIONS: -Xms8192M -Xmx16384M -XX:+CMSClassUnloadingEnabled -Daether.enhancedLocalRepository.trackingFilename=some_dummy_file_name2
          CODEARTIFACT_AUTH_TOKEN: ${{ env.CODEARTIFACT_AUTH_TOKEN }}
        run: |
          mvn versions:set -DnewVersion="${{ github.event.inputs.version }}" -DgenerateBackupPoms=false -DprocessAllModules --settings $MAVEN_SETTINGS --no-transfer-progress -X
          mvn -e clean source:jar deploy --settings $MAVEN_SETTINGS -DskipTests ${{ steps.ignored_modules.outputs.ignored_args }} --no-transfer-progress

      - name: Generate Summary
        run: |
          branch="${{ github.event.inputs.branch }}"
          java_version="${{ github.event.inputs.java_version }}"
          version="${{ github.event.inputs.version }}"
          ignored_modules="${{ github.event.inputs.ignored_modules }}"
          summary="# Jar Upload Success 📦\n"
          summary="${summary}#### Branch: \`${branch}\`, SHA: \`${{ github.sha }}\`\n"
          summary="${summary}--------------------\n"
          summary="${summary}#### Build Details\n"
          summary="${summary}| Parameter | Value |\n"
          summary="${summary}|-----------|-------|\n"
          summary="${summary}| Java Version | ${java_version} |\n"
          summary="${summary}| Maven Version | ${version} |\n"
          if [ -n "$ignored_modules" ]; then
            summary="${summary}| Ignored Modules | \`${ignored_modules}\` |\n"
          fi
          echo -e "$summary" >> "$GITHUB_STEP_SUMMARY"
