name: Validate PR Title Format

# Trigger on PR events: opened, edited, or synchronized (e.g., new commits)
on:
  pull_request:
    types: [opened, edited, synchronize]

jobs:
  validate-title:
    runs-on: nexla-runners

    steps:
      - name: Validate PR title format
        uses: actions/github-script@v7  # Run inline JavaScript using GitHub context
        with:
          script: |
            // Get the PR title from the event context
            const title = context.payload.pull_request.title || "";

            // Define the pattern: "NEX-12345: Description" or "DEVOPS-123: Description"
            const pattern = /^(NEX|DEVOPS)-\d+: (.+)$/;

            // Try to match the title with the expected pattern
            const match = title.trim().match(pattern);

            // If no match, fail the workflow with instructions
            if (!match) {
              core.setFailed(
                `❌ Invalid PR title format.\nTitle must match:\nNEX-12345: Description\nor\nDEVOPS-410: Description`
              );
              return;
            }

            // Extract the description part (after the colon and space)
            const descPart = match[2].trim();

            // Count the number of words
            const wordCount = descPart.split(/\s+/).filter(Boolean).length;

            // Count non-space characters
            const charCount = descPart.replace(/\s+/g, '').length;

            // Fail if fewer than 2 words or fewer than 10 non-space characters
            if (wordCount < 2 || charCount < 10) {
              core.setFailed(
                `❌ PR title description must contain at least 2 words and 10 non-space characters.\nYou provided: "${descPart}"`
              );
            }
