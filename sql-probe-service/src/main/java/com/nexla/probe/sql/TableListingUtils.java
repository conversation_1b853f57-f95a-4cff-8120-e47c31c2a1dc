package com.nexla.probe.sql;

import com.google.common.collect.Sets;
import com.nexla.common.pool.SimplePool;
import com.nexla.common.probe.ColumnInfo;
import com.nexla.connector.config.jdbc.DbPage;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.NexlaDbInfo;
import connect.jdbc.sink.dialect.PostgreSqlDialect;
import connect.jdbc.sink.dialect.RedshiftSqlDialect;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.IntStreamEx;
import one.util.streamex.StreamEx;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.jdbc.PgConnection;

import java.sql.*;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.probe.sql.dto.InternalDto.Column;
import static com.nexla.probe.sql.dto.InternalDto.Pk;
import static com.nexla.probe.sql.dto.InternalDto.Table;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toSet;

public class TableListingUtils {

	public static final String TABLE = "TABLE";
	public static final String VIEW = "VIEW";
	public static final String EXTERNAL_TABLE = "EXTERNAL_TABLE";

	public static final String TABLE_TYPE = "TABLE_TYPE";
	public static final String KEY_SEQ = "KEY_SEQ";
	public static final String TYPE_NAME = "TYPE_NAME";
	public static final String COLUMN_DEF = "COLUMN_DEF";
	public static final String COLUMN_NAME = "COLUMN_NAME";
	public static final String TABLE_NAME = "TABLE_NAME";
	public static final String TABLE_CAT = "TABLE_CAT";
	public static final String TABLE_SCHEM = "TABLE_SCHEM";

	private static final String AWS_ATHENA_DB_TYPE = "AWS.ATHENA";

	@AllArgsConstructor
	static class Entry {
		Connection conn;
		DatabaseMetaData metaData;
	}

	public static SimplePool<Entry> createPool(Supplier<Connection> getConnectionFn) {

		SimplePool<Entry> connPool = new SimplePool<>(2, e -> closeSilently(e.conn));

		IntStreamEx
			.rangeClosed(1, 2).boxed()
			.parallel()
			.map(i -> {
				try {
					Connection connection = getConnectionFn.get();
					DatabaseMetaData metaData = connection.getMetaData();
					return new Entry(connection, metaData);
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
			})
			.forEach(connPool::add);

		return connPool;
	}

	@SneakyThrows
	public static List<Column> selectTableColumns(
		DbDialect dbDialect, String dbName, String schema, String tableName, DatabaseMetaData metaData, Set<String> primaryKeys
	) {
		List<Column> result = Lists.newArrayList();
		try (ResultSet rs = metaData.getColumns(dbName, schema, tableName, null)) {
			while (rs.next()) {
				Table table = table(rs, dbName);
				result.add(columnInfo(rs, table, dbDialect, primaryKeys));
			}
		}
		return result;
	}

	public static void setPrimaryKeyFlagsForColumns(List<String> primaryKeys, List<Column> tableColumns) {
		tableColumns.forEach(column -> {
			column.primaryKey = primaryKeys.contains(column.name);
		});
	}

	public static ColumnInfo columnInfo(Column c) {
		return new ColumnInfo(c.name, c.primaryKey, c.type, c.defaultValue);
	}

	public static Table table(ResultSet tt, String dbName) throws SQLException {
		String rsDbName = StringUtils.trimToNull(tt.getString(TABLE_CAT));
		String dbNameNullable = rsDbName == null ? dbName : rsDbName;
		String dbNameOrEmpty = ofNullable(dbNameNullable).orElse("");
		return new Table(dbNameOrEmpty, tt.getString(TABLE_SCHEM), tt.getString(TABLE_NAME));
	}

	@SneakyThrows
	public static Set<String> selectPrimaryKeyCols(String dbName, String schema, String table, DatabaseMetaData metaData) {
		List<Pk> primaryKeys = new ArrayList<>();
		ResultSet pkRs = metaData.getPrimaryKeys(dbName, schema, table);
		while (pkRs.next()) {
			primaryKeys.add(new Pk(pkRs.getString(COLUMN_NAME), pkRs.getShort(KEY_SEQ)));
		}
		closeSilently(pkRs);
		return primaryKeys
			.stream()
			.map(p -> p.column)
			.collect(toSet());
	}

	@SneakyThrows
	public static NexlaDbInfo extractDbInfo(JdbcAuthConfig authConfig, DbDialect dbDialect, Connection connection) {
		Set<String> availableSchemas = getAvailableSchemas(dbDialect, connection);
		return authConfig.url != null
			? dbDialect.extractDbInfo(connection, authConfig.url, availableSchemas, authConfig.dbType.isSupportingSchema())
			: new NexlaDbInfo(authConfig.databaseName, authConfig.schemaName == null ? availableSchemas : Sets.newHashSet(authConfig.schemaName));
	}

	@SneakyThrows
	private static Set<String> getAvailableSchemas(DbDialect dbDialect, Connection connection) {
		Set<String> result = Sets.newHashSet();
		if (!dbDialect.systemSchemas().isEmpty()) {
			ResultSet schemasResSet = connection.getMetaData().getSchemas();
			while (schemasResSet.next()) {
				String schemaName = schemasResSet.getString(TABLE_SCHEM);
				if (!dbDialect.systemSchemas().contains(schemaName)) {
					result.add(schemaName);
				}
			}
		} else {
			result.add(null); // null stands for all schemas
		}
		return result;
	}

	@SneakyThrows
	public static Column columnInfo(ResultSet rs, Table table, DbDialect dbDialect, Set<String> pkSet) {
		String columnName = rs.getString(COLUMN_NAME);
		String typeName = rs.getString(TYPE_NAME);
		String defValue = parseDefaultValue(dbDialect, rs.getString(COLUMN_DEF));
		return new Column(table, columnName, typeName, pkSet.contains(columnName), defValue);
	}

	@SneakyThrows
	public static Set<Table> listTables(Optional<String> database, Optional<String> schema, DatabaseMetaData metaData) {
		Set<Table> tablesResult = Sets.newHashSet();
		String catalog = database.orElse(null);
		ResultSet tablesRs = metaData.getTables(catalog, schema.orElse(null), null, new String[]{"TABLE", "VIEW"});
		while (tablesRs.next()) {
			String tableType = tablesRs.getString(TABLE_TYPE);
			if (TABLE.equalsIgnoreCase(tableType) || VIEW.equalsIgnoreCase(tableType)) {
				tablesResult.add(table(tablesRs, database.orElse(null)));
			}
		}
		return tablesResult;
	}

	@SneakyThrows
	public static DbPage<Iterable<String>> listTables(Connection connection,
										String database,
										int pageSize,
										int offset) {

		if(connection instanceof PgConnection) {
			ResultSet totalRs = connection.createStatement().executeQuery(getCountTablesQuery());
			int total = 0;
			while (totalRs.next()) {
				total = totalRs.getInt("total");
			}

			Set<Table> tablesResult = Sets.newHashSet();
			if (total > 0) {
				ResultSet pagesRs = connection.createStatement().executeQuery(getTablesQuery(pageSize, offset));
				while (pagesRs.next()) {
					tablesResult.add(table(pagesRs, database));
				}
			}

			return new DbPage<>(StreamEx.of(tablesResult).map(x -> x.name),
					pageSize,
					String.valueOf(offset),
					offset + pageSize < total);
		} else {
			return DbPage.of(listTables(
						Optional.of(database),
				Optional.empty(),
						connection.getMetaData())
					.stream()
					.map(x->x.name)
					.collect(Collectors.toList()));
		}
	}

	private static String getTablesQuery(int pageSize, int offset) {
		String select = "SELECT NULL AS TABLE_CAT, " +
				"n.nspname AS TABLE_SCHEM, " +
				"c.relname AS TABLE_NAME,  " +
				"CASE n.nspname ~ '^pg_' OR n.nspname = 'information_schema'  " +
				"WHEN true THEN CASE  " +
				"WHEN n.nspname = 'pg_catalog' OR n.nspname = 'information_schema' THEN CASE c.relkind   " +
				"WHEN 'r' THEN 'SYSTEM TABLE'   " +
				"WHEN 'v' THEN 'SYSTEM VIEW'   " +
				"WHEN 'i' THEN 'SYSTEM INDEX'   ELSE NULL   END  " +
				"WHEN n.nspname = 'pg_toast' THEN CASE c.relkind   " +
				"WHEN 'r' THEN 'SYSTEM TOAST TABLE'   " +
				"WHEN 'i' THEN 'SYSTEM TOAST INDEX'   ELSE NULL   END  ELSE CASE c.relkind   " +
				"WHEN 'r' THEN 'TEMPORARY TABLE'   " +
				"WHEN 'p' THEN 'TEMPORARY TABLE'   " +
				"WHEN 'i' THEN 'TEMPORARY INDEX'   " +
				"WHEN 'S' THEN 'TEMPORARY SEQUENCE'   " +
				"WHEN 'v' THEN 'TEMPORARY VIEW'   ELSE NULL   END  END  " +
				"WHEN false THEN CASE c.relkind  " +
				"WHEN 'r' THEN 'TABLE'  " +
				"WHEN 'p' THEN 'PARTITIONED TABLE'  " +
				"WHEN 'i' THEN 'INDEX'  " +
				"WHEN 'P' then 'PARTITIONED INDEX'  " +
				"WHEN 'S' THEN 'SEQUENCE'  " +
				"WHEN 'v' THEN 'VIEW'  " +
				"WHEN 'c' THEN 'TYPE'  " +
				"WHEN 'f' THEN 'FOREIGN TABLE'  " +
				"WHEN 'm' THEN 'MATERIALIZED VIEW'  ELSE NULL  END  ELSE NULL  END  AS TABLE_TYPE, d.description AS REMARKS,  '' as TYPE_CAT, '' as TYPE_SCHEM, '' as TYPE_NAME, '' AS SELF_REFERENCING_COL_NAME, '' AS REF_GENERATION  " +
				"FROM pg_catalog.pg_namespace n, pg_catalog.pg_class c  " +
				"LEFT JOIN pg_catalog.pg_description d ON (c.oid = d.objoid AND d.objsubid = 0  and d.classoid = 'pg_class'::regclass)  " +
				"WHERE c.relnamespace = n.oid AND (false  OR ( c.relkind = 'r' AND n.nspname !~ '^pg_' AND n.nspname <> 'information_schema' )  OR ( c.relkind = 'v' AND n.nspname <> 'pg_catalog' AND n.nspname <> 'information_schema' ) ) ";

		String orderBy = " ORDER BY TABLE_TYPE,TABLE_SCHEM,TABLE_NAME ";
		String pagination = " LIMIT " + pageSize;
		pagination += " OFFSET " + offset;

		return select + orderBy + pagination;
	}

	private static String getCountTablesQuery() {
		return "SELECT COUNT(*) as total " +
				"FROM pg_catalog.pg_namespace n, pg_catalog.pg_class c " +
				"LEFT JOIN pg_catalog.pg_description d ON (c.oid = d.objoid AND d.objsubid = 0  and d.classoid = 'pg_class'::regclass)  " +
				"WHERE c.relnamespace = n.oid " +
				"AND (false  OR ( c.relkind = 'r' AND n.nspname !~ '^pg_' AND n.nspname <> 'information_schema' )  " +
				"OR ( c.relkind = 'v' AND n.nspname <> 'pg_catalog' AND n.nspname <> 'information_schema' ) ) ";
	}

	public static String parseDefaultValue(DbDialect dbDialect, String defValue) {
		if (defValue == null) {
			return defValue;
		}
		if (dbDialect instanceof PostgreSqlDialect ||
			dbDialect instanceof RedshiftSqlDialect) {
			// '12'::character varying => '12'
			int index = defValue.indexOf("::");
			if (index != -1) {
				return defValue.substring(0, index);
			}
		}
		return defValue;
	}

}
