package com.nexla.probe.sql.connection;

import com.nexla.common.ConnectionType;
import com.nexla.common.ConnectionType;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.DialectRegistry;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class ConnectionHelper {

	private static final Logger logger = LoggerFactory.getLogger(ConnectionHelper.class);

	public static final String ORACLE_ADW_HIGH_SERVICE = "_high";
	public static final String ORACLE_ADW_MEDIUM_SERVICE = "_medium";
	public static final String ORACLE_ADW_LOW_SERVICE = "_low";

	public static Optional<String> getNextAllowedOracleService(String usedOracleService) {
		switch (usedOracleService) {
			case ORACLE_ADW_HIGH_SERVICE:
				return Optional.of(ORACLE_ADW_MEDIUM_SERVICE);
			case ORACLE_ADW_MEDIUM_SERVICE:
				return Optional.of(ORACLE_ADW_LOW_SERVICE);
			case ORACLE_ADW_LOW_SERVICE:
				return Optional.empty();
			default:
				// still preserving the default behavior
				logger.warn("Unknown used Oracle ADW service: {}, returning {} by default.", usedOracleService, ORACLE_ADW_HIGH_SERVICE);
				return Optional.of(ORACLE_ADW_HIGH_SERVICE);
		}
	}

	@SneakyThrows
	static void addSchemaToConnection(JdbcAuthConfig config, Connection connection) {
		DbDialect dbDialect = DialectRegistry.getInstance().fromConnectionString(config);
		Optional<String> defaultSchemaName = dbDialect.getDefaultSchemaName();

		// Special handling for PostgreSQL to properly set search_path
		if (ConnectionType.POSTGRES.equals(config.dbType)) {
			// Debug logging to diagnose the issue
			logger.debug("PostgreSQL connection: schemaName={}, defaultSchemaName={}", config.schemaName, defaultSchemaName.orElse(null));

			// Check if no schema is specified
			if (config.schemaName == null) {
				logger.info("No schema specified, querying user's search_path");
				String searchPath = getUserSearchPath(connection);
				if (searchPath != null) {
					logger.debug("Setting search_path to: {}", searchPath);
					setSearchPath(connection, searchPath);
					return;
				}
			}

			// Check if schema is the default "public"
			if (defaultSchemaName.isPresent() && "public".equals(config.schemaName)) {
				logger.info("Schema is default 'public', querying user's search_path");
				String searchPath = getUserSearchPath(connection);
				if (searchPath != null) {
					logger.debug("Setting search_path to: {}", searchPath);
					setSearchPath(connection, searchPath);
					return;
				}
			}
			// First, get the current search_path from the database
			String currentSearchPath = getUserSearchPath(connection);

			// If we couldn't get the search_path or a non-default schema was specified, use the normal method
			setPostgresSearchPath(connection, config.schemaName, defaultSchemaName, currentSearchPath);
		} else {
			// For other database types, use the standard setSchema method
			if (config.schemaName != null) {
				connection.setSchema(dbDialect.getQualifiedSchemaName(config.schemaName, config.databaseName));
			} else if (defaultSchemaName.isPresent()) {
				connection.setSchema(defaultSchemaName.get());
			}
		}
	}

	/**
	 * Sets the PostgreSQL search_path to include all specified schemas.
	 * This allows functions from multiple schemas to be found without schema qualification.
	 *
	 * @param connection PostgreSQL connection
	 * @param schemaName Schema name(s) from configuration, can be comma-separated
	 * @param defaultSchemaName Default schema name if none is specified
	 * @param currentSearchPath The current search_path from the database, if available
	 */
	private static void setPostgresSearchPath(Connection connection, String schemaName, Optional<String> defaultSchemaName, String currentSearchPath) {
		try {
			String searchPath;
			boolean hasPublicSchema;

			logger.info("setPostgresSearchPath called with schemaName={}, defaultSchemaName={}, currentSearchPath={}",
				schemaName, defaultSchemaName.orElse(null), currentSearchPath);

			// If we have a current search_path from the database and no schema specified, use it
			if (currentSearchPath != null && schemaName == null) {
				searchPath = currentSearchPath;
				hasPublicSchema = searchPath.toLowerCase().contains("\"public\"");
				logger.debug("Using current search_path from database: {}", searchPath);
			} else if (schemaName != null) {
				// Handle comma-separated schema names
				if (schemaName.contains(",")) {
					// Process schema list and check if public is already included
					List<String> schemas = Arrays.stream(schemaName.split(","))
						.map(String::trim)
						.filter(StringUtils::isNotBlank)
						.collect(Collectors.toList());

					logger.debug("Parsed schemas: {}", schemas);

					// Check if public schema is already in the list
					hasPublicSchema = schemas.stream()
						.anyMatch(schema -> "public".equalsIgnoreCase(schema));

					// Convert to quoted, comma-separated list
					searchPath = schemas.stream()
						.map(schema -> "\"" + schema + "\"")
						.collect(Collectors.joining(", "));
				} else {
					// If we have a current search_path and a single schema, try to preserve the structure
					if (currentSearchPath != null && !"public".equalsIgnoreCase(schemaName)) {
						// Add the requested schema to the beginning of the search_path
						searchPath = "\"" + schemaName + "\", " + currentSearchPath;
						hasPublicSchema = currentSearchPath.toLowerCase().contains("\"public\"");
						logger.debug("Adding requested schema to current search_path: {}", searchPath);
					} else {
						// Single schema with no current search_path
						searchPath = "\"" + schemaName + "\"";
						hasPublicSchema = "public".equalsIgnoreCase(schemaName);
					}
				}
			} else if (defaultSchemaName.isPresent()) {
				// Use default schema
				searchPath = "\"" + defaultSchemaName.get() + "\"";
				hasPublicSchema = "public".equalsIgnoreCase(defaultSchemaName.get());
			} else {
				// No schema specified and no current search_path, use public as a fallback
				logger.info("No schema specified and no current search_path, using 'public' as fallback");
				searchPath = "\"public\"";
				hasPublicSchema = true;
			}

			// Add public schema to search_path to find common functions if not already included
			if (!hasPublicSchema) {
				searchPath = searchPath + ", \"public\"";
			}

			// Execute SET search_path statement
			try (Statement stmt = connection.createStatement()) {
				String sql = "SET search_path TO " + searchPath;
				stmt.execute(sql);
				logger.info("Successfully executed SET search_path statement");
			}
		} catch (Exception e) {
			// Log the error but don't throw it to avoid breaking the connection process
			logger.error("Error setting PostgreSQL search_path: {}", e.getMessage(), e);
		}
	}

	/**
	 * Queries the database to get the user's current search_path.
	 *
	 * @param connection PostgreSQL connection
	 * @return The current search_path as a string of quoted schema names, or null if it couldn't be retrieved
	 */
	private static String getUserSearchPath(Connection connection) {
		try {
			Statement stmt = connection.createStatement();
			ResultSet rs = stmt.executeQuery("SHOW search_path");

			if (rs.next()) {
				String currentSearchPath = rs.getString(1);
				logger.info("Current PostgreSQL search_path: {}", currentSearchPath);
				return currentSearchPath;
			} else {
				logger.info("No results from SHOW search_path query");
			}

			rs.close();
			stmt.close();
		} catch (Exception e) {
			logger.error("Error getting PostgreSQL search_path: {}", e.getMessage(), e);
		}
		logger.info("Returning null from getUserSearchPath");
		return null;
	}

	/**
	 * Sets the search_path for a PostgreSQL connection.
	 *
	 * @param connection PostgreSQL connection
	 * @param searchPath The search_path to set
	 */
	private static void setSearchPath(Connection connection, String searchPath) {
		try {
			Statement stmt = connection.createStatement();
			String sql = "SET search_path TO " + searchPath;
			stmt.execute(sql);
			logger.info("Successfully set search_path to: {}", searchPath);
			stmt.close();
		} catch (Exception e) {
			logger.error("Error setting PostgreSQL search_path: {}", e.getMessage(), e);
		}
	}
}
