package connect.jdbc.sink.warehouse;

import com.nexla.connector.NexlaMessageContext;
import connect.jdbc.sink.dialect.copy.CopyOperationLocalBuffer;
import connect.jdbc.sink.dialect.copy.FlushResult;

import java.io.File;
import java.sql.Connection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;

public interface DataWarehouseSink {

	void writeData(List<NexlaMessageContext> records, Map<Long, Integer> streamSizeByRunId);

	void onConnectorStop(Supplier<Connection> connProvider);

	void onConnectorStart(Supplier<Connection> connProvider);

	CopyOperationLocalBuffer getBuffer();

	int getBufferSize();

	Map<Long, Integer> getStreamSizeByRunId();

	int getCurrentBatchSize();

	Optional<FlushResult> flushBatch(Connection connection);

	Optional<FlushResult> flushPipeline(Connection connection);

	Optional<FlushResult> directFlush(Connection connection, List<File> files, List<String> columnsPassed);

	void truncateTable(Connection connection, int streamSize);
}
