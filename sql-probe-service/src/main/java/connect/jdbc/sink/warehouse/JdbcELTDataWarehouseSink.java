package connect.jdbc.sink.warehouse;

import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import com.nexla.probe.sql.SchemaUtils;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.copy.CopyOperationLocalBuffer;
import connect.jdbc.sink.dialect.copy.FlushResult;
import connect.jdbc.util.ELTUtils;

import java.io.File;
import java.sql.Connection;
import java.util.*;
import java.util.function.Supplier;

import static java.util.Objects.isNull;

public class JdbcELTDataWarehouseSink implements DataWarehouseSink {

    private final Map<String, DataWarehouseSink> warehouseSinkStore = Maps.newHashMap();

    private final Supplier<Connection> connProvider;
    private final DbDialect dbDialect;
    private final NexlaLogger logger;

    public JdbcELTDataWarehouseSink(Supplier<Connection> connProvider,
                                    DbDialect dbDialect,
                                    NexlaLogger logger) {
        this.connProvider = connProvider;
        this.dbDialect = dbDialect;
        this.logger = logger;
    }

    public void invalidate() {
        warehouseSinkStore.clear();
    }

    public void writeData(JdbcSinkConnectorConfig jdbcConfig,
                          WarehouseCopyFileFormat fileFormat,
                          List<NexlaMessageContext> records,
                          Map<Long, Integer> streamSizeByRunId) {
        DataWarehouseSink dataWarehouseSink = getDataWarehouseSink(jdbcConfig, fileFormat);
        dataWarehouseSink.writeData(records, streamSizeByRunId);
    }

    private DataWarehouseSink getDataWarehouseSink(JdbcSinkConnectorConfig enriched,
                                                   WarehouseCopyFileFormat fileFormat) {
        String key = getKey(enriched);
        DataWarehouseSink dataWarehouseSink = warehouseSinkStore.get(key);
        if (Objects.isNull(dataWarehouseSink)) {
            logger.debug("ELT: DataWarehouseSink is empty, creating new one for the key - {}", key);
            Schema schema = SchemaUtils.selectSchema(this.connProvider, this.dbDialect, enriched);
            dataWarehouseSink = this.dbDialect.newDataWarehouseSink(enriched, fileFormat, schema, this.dbDialect, logger);
            dataWarehouseSink.onConnectorStart(this.connProvider);
            warehouseSinkStore.put(key, dataWarehouseSink);
        }

        return dataWarehouseSink;
    }

    @Override
    public void writeData(List<NexlaMessageContext> records, Map<Long, Integer> streamSizeByRunId) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void onConnectorStop(Supplier<Connection> connProvider) {
        warehouseSinkStore.values().forEach(it -> it.onConnectorStop(connProvider));
    }

    @Override
    public void onConnectorStart(Supplier<Connection> connProvider) {
        throw new UnsupportedOperationException();
    }

    @Override
    public int getBufferSize() {
        return warehouseSinkStore.values()
                .stream()
                .mapToInt(DataWarehouseSink::getBufferSize)
                .sum();
    }

    @Override
    public Map<Long, Integer> getStreamSizeByRunId() {
        throw new UnsupportedOperationException();
    }

    @Override
    public CopyOperationLocalBuffer getBuffer() {
        throw new UnsupportedOperationException();
    }

    @Override
    public void truncateTable(Connection connection, int streamSize) {
        throw new UnsupportedOperationException();
    }

    @Override
    public int getCurrentBatchSize() {
        return warehouseSinkStore.values()
                .stream()
                .mapToInt(DataWarehouseSink::getCurrentBatchSize)
                .sum();
    }


    @Override
    public Optional<FlushResult> flushBatch(Connection connection) {
        FlushResult flushResult = null;
        for (DataWarehouseSink sink : warehouseSinkStore.values()) {
            try {
                Optional<FlushResult> resultOptional = sink.flushBatch(connection);
                if (resultOptional.isPresent()) {
                    flushResult = ELTUtils.mergeFlushResult(resultOptional.get(), flushResult);
                }
            } catch (Exception e) {
                // if an exception is caught for a particular table, we should continue processing the other tables and
                // quarantine messages
                logger.warn("Error caught during elt flush batch, exception message is " + e.getMessage(), e);
                flushResult = mergeFlushResult(flushResult, sink, e, sink.getBufferSize());
            }
        }
        return Optional.ofNullable(flushResult);
    }

    @Override
    public Optional<FlushResult> flushPipeline(Connection connection) {
        FlushResult flushResult = null;
        for (DataWarehouseSink sink : warehouseSinkStore.values()) {
            try {
                Optional<FlushResult> resultOptional = sink.flushPipeline(connection);
                if (resultOptional.isPresent()) {
                    flushResult = ELTUtils.mergeFlushResult(resultOptional.get(), flushResult);
                }
            } catch (Exception e) {
                // if an exception is caught for a particular table, we should continue processing the other tables and
                // quarantine messages
                logger.warn("Error caught during elt flush pipeline, exception message is " + e.getMessage(), e);
                flushResult = mergeFlushResult(flushResult, sink, e, sink.getCurrentBatchSize());
            }
        }

        return Optional.ofNullable(flushResult);
    }

    private FlushResult mergeFlushResult(FlushResult flushResult, DataWarehouseSink sink, Exception e, int errorRecords) {
        Map<Long, RecordMetric> metricsByRunId = Optional.ofNullable(flushResult)
                .map(FlushResult::getMetricsByRunId)
                .orElse(new HashMap<>());

        var offsets = Optional.ofNullable(flushResult)
                .map(FlushResult::getBufferOffsets)
                .orElse(Maps.newLinkedHashMap());

        if (sink.getBufferSize() > 0) {
            sink.getBuffer()
                    .getRecords()
                    .forEach(message -> {
                        Long runId = 0L;
                        if (message != null && message.getNexlaMetaData() != null && message.getNexlaMetaData().getTags() != null && message.getNexlaMetaData().getTags().get("originalMessageRunId") != null) {
                            runId = (long) message.getNexlaMetaData().getTags().get("originalMessageRunId");
                        }
                        RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
                        recordMetric.quarantineMessages.add(RecordMetric.quarantineMessage(message, e.getMessage()));
                        recordMetric.errorRecords.addAndGet(errorRecords);
                        
                    });
        } else {
            // If no messages in buffer, create a default record metric for error tracking
            RecordMetric defaultMetric = new RecordMetric();
            defaultMetric.quarantineMessages.add(RecordMetric.quarantineMessage(new NexlaMessage(), e.getMessage()));
            defaultMetric.errorRecords.addAndGet(errorRecords);
            // Use 0L as default runId when no messages, and use the global runId in JdbcBaseSink to send metrics
            metricsByRunId.put(0L, defaultMetric); 
        }

        return FlushResult.builder()
                .metricsByRunId(metricsByRunId)
                .bufferOffsets(offsets)
                .build();
    }

    @Override
    public Optional<FlushResult> directFlush(Connection connection, List<File> files, List<String> columnsPassed) {
        return Optional.empty();
    }

    private String getKey(JdbcSinkConnectorConfig enriched) {
        return enriched.sinkId + "_" + enriched.table;
    }
}
