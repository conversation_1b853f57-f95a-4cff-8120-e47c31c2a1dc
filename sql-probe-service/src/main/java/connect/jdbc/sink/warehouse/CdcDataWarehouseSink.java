package connect.jdbc.sink.warehouse;

import com.google.common.collect.Maps;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import com.nexla.probe.sql.SchemaUtils;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.copy.CopyOperationLocalBuffer;
import connect.jdbc.sink.dialect.copy.FlushResult;

import java.io.File;
import java.sql.Connection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;

import static java.util.Objects.isNull;

public class CdcDataWarehouseSink implements DataWarehouseSink {

    private Map<String, DataWarehouseSink> warehouseSinkStore = new HashMap<>();

    private final java.util.function.Supplier<Connection> connProvider;
    private final DbDialect dbDialect;
    private final NexlaLogger logger;

    public CdcDataWarehouseSink(Supplier<Connection> connProvider, DbDialect dbDialect, NexlaLogger logger) {
        this.connProvider = connProvider;
        this.dbDialect = dbDialect;
        this.logger = logger;
    }

    public void invalidate() {
        warehouseSinkStore = new HashMap<>();
    }

    public void writeData(JdbcSinkConnectorConfig enriched,
                          AtomicLong lastProcessedMessageTs,
                          WarehouseCopyFileFormat fileFormat,
                          List<NexlaMessageContext> records,
                          Map<Long, Integer> streamSizeByRunId) {
        DataWarehouseSink dataWarehouseSink = getDataWarehouseSink(enriched, fileFormat);
        dataWarehouseSink.writeData(records, streamSizeByRunId);
        lastProcessedMessageTs.set(System.currentTimeMillis());
    }

    public void truncate(JdbcSinkConnectorConfig enriched,
                         WarehouseCopyFileFormat fileFormat,
                         int streamSize) {
        try {
            // there are cases when destination table was not created before truncate
            DataWarehouseSink dataWarehouseSink = getDataWarehouseSink(enriched, fileFormat);
            dataWarehouseSink.truncateTable(connProvider.get(), streamSize);
        } catch (Exception exc) {
            logger.error("{} table TRUNCATE failed for COPY flow: {}", enriched.table, exc.getMessage(), exc);
        }
    }

    private DataWarehouseSink getDataWarehouseSink(JdbcSinkConnectorConfig enriched,
                                                   WarehouseCopyFileFormat fileFormat){
        String key = getKey(enriched);
        DataWarehouseSink dataWarehouseSink = warehouseSinkStore.get(key);
        if (dataWarehouseSink == null) {
            logger.debug("CDC: DataWarehouseSink is empty, creating new one for the key - {}", key);
            Schema schema = SchemaUtils.selectSchema(this.connProvider, this.dbDialect, enriched);
            dataWarehouseSink = this.dbDialect.newDataWarehouseSink(enriched, fileFormat, schema, this.dbDialect, logger);
            dataWarehouseSink.onConnectorStart(this.connProvider);
            warehouseSinkStore.put(key, dataWarehouseSink);
        }

        return dataWarehouseSink;
    }

    @Override
    public void writeData(List<NexlaMessageContext> records, Map<Long, Integer> streamSizeByRunId) {
        throw new UnsupportedOperationException();
    }

    @Override
    public Map<Long, Integer> getStreamSizeByRunId() {
        throw new UnsupportedOperationException();
    }

    @Override
    public void onConnectorStop(Supplier<Connection> connProvider) {
        for (DataWarehouseSink sink : warehouseSinkStore.values()) {
            sink.onConnectorStop(connProvider);
        }
    }

    @Override
    public void onConnectorStart(Supplier<Connection> connProvider) {
        throw new UnsupportedOperationException();
    }

    @Override
    public int getBufferSize() {
        int streamSize = 0;
        for (DataWarehouseSink sink : warehouseSinkStore.values()) {
            streamSize += sink.getBufferSize();
        }

        return streamSize;
    }

    @Override
    public CopyOperationLocalBuffer getBuffer() {
        throw new UnsupportedOperationException();
    }

    @Override
    public void truncateTable(Connection connection, int streamSize) {
        throw new UnsupportedOperationException();
    }

    @Override
    public int getCurrentBatchSize() {
        int currentBatchSize = 0;
        for (DataWarehouseSink sink : warehouseSinkStore.values()) {
            currentBatchSize += sink.getCurrentBatchSize();
        }

        return currentBatchSize;
    }

    /**
     * 'multi_table' and 'single_table' modes support only UPSERT operations and return empty result
     * @param connection connection to DB
     * @return result of COPY operation
     */
    @Override
    public Optional<FlushResult> flushBatch(Connection connection) {
        FlushResult flushResult = null;
        for (DataWarehouseSink sink : warehouseSinkStore.values()) {
            try {
                Optional<FlushResult> resultOptional = sink.flushBatch(connection);

                if (resultOptional.isPresent()) {
                    flushResult = mergeFlushResult(resultOptional.get(), flushResult);
                }
            } catch(Exception e) {
                // if an exception is caught for a particular table, we should continue processing the other tables and
                // quarantine messages
                logger.warn("Error caught during cdc flush batch, exception message is " + e.getMessage(), e);
                flushResult = mergeFlushResult(flushResult, sink, e);
            }
        }
        return Optional.ofNullable(flushResult);
    }

    @Override
    public Optional<FlushResult> flushPipeline(Connection connection) {
        FlushResult merged = null;
        for (DataWarehouseSink sink : warehouseSinkStore.values()) {
            try {
                Optional<FlushResult> resultOptional = sink.flushPipeline(connection);

                if (resultOptional.isPresent()) {
                   merged = mergeFlushResult(resultOptional.get(), merged);
                }
            } catch(Exception e) {
                // if an exception is caught for a particular table, we should continue processing the other tables and
                // quarantine messages
                logger.warn("Error caught during cdc flush pipeline, exception message is " + e.getMessage(), e);
                merged = mergeFlushResult(merged, sink, e);
            }
        }

        return Optional.ofNullable(merged);
    }

    private FlushResult mergeFlushResult(FlushResult flushResult, DataWarehouseSink sink, Exception e) {
        Map<Long, RecordMetric> metricsByRunId = Optional.ofNullable(flushResult)
                .map(FlushResult::getMetricsByRunId)
                .orElse(new HashMap<>());

        var offsets = Optional.ofNullable(flushResult)
                .map(FlushResult::getBufferOffsets)
                .orElse(Maps.newLinkedHashMap());

        // Process each message's runId separately
        sink.getBuffer().getRecords().forEach(message -> {
            Long runId = 0L;
            if (message != null && message.getNexlaMetaData() != null && message.getNexlaMetaData().getTags() != null && message.getNexlaMetaData().getTags().get("originalMessageRunId") != null) {
                runId = (long) message.getNexlaMetaData().getTags().get("originalMessageRunId");
            }

            RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
            recordMetric.quarantineMessages.add(RecordMetric.quarantineMessage(message, e.getMessage()));
            recordMetric.errorRecords.incrementAndGet();
            
        });

        return FlushResult.builder()
                .metricsByRunId(metricsByRunId)
                .bufferOffsets(offsets)
                .build();
    }

    private FlushResult mergeFlushResult(FlushResult currentResult, FlushResult oldResult) {
        if (isNull(oldResult)) {
            return currentResult;
        }

        Map<TopicPartition, Long> bufferOffsets = mergeMaps(oldResult.getBufferOffsets(), currentResult.getBufferOffsets());
        
        // Merge metricsByRunId maps
        Map<Long, RecordMetric> mergedMetricsByRunId = new HashMap<>(oldResult.getMetricsByRunId());
        currentResult.getMetricsByRunId().forEach((runId, currentMetric) -> 
            mergedMetricsByRunId.merge(runId, currentMetric, RecordMetric::combine)
        );

        return FlushResult.builder()
                .metricsByRunId(mergedMetricsByRunId)
                .bufferOffsets(bufferOffsets)
                .build();
    }

    private Map<TopicPartition, Long> mergeMaps(Map<TopicPartition, Long> map1, Map<TopicPartition, Long> map2) {
        Map<TopicPartition, Long> mergedMap = new HashMap<>(map1);

        for (Map.Entry<TopicPartition, Long> entry : map2.entrySet()) {
            TopicPartition key = entry.getKey();
            Long value = entry.getValue();

            if (mergedMap.containsKey(key)) {
                Long existingValue = mergedMap.get(key);
                if (value > existingValue) {
                    mergedMap.put(key, value);
                }
            } else {
                mergedMap.put(key, value);
            }
        }

        return mergedMap;
    }

    @Override
    public Optional<FlushResult> directFlush(Connection connection, List<File> files, List<String> columnsPassed) {
        return Optional.empty();
    }

    private String getKey(JdbcSinkConnectorConfig enriched) {
        return enriched.sinkId + "_" + enriched.table;
    }
}
