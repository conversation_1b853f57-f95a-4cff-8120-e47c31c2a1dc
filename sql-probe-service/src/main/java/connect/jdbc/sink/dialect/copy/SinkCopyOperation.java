package connect.jdbc.sink.dialect.copy;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;

import java.io.File;
import java.sql.Connection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;

public interface SinkCopyOperation {

	WarehouseCopyFileFormat getFileFormat();

	SinkCopyOperation init(Schema schema,
	                       JdbcSinkConnectorConfig config,
	                       AdminApiClient adminApiClient,
	                       WarehouseCopyFileFormat fileFormat,
	                       NexlaLogger logger);

	void writeToBuffer(List<NexlaMessageContext> records, Map<Long, Integer> streamSizeByRunId);

	RecordMetric flushBuffer(CopyOperationLocalBuffer buffer,
							 Supplier<Connection> connectionProvider,
							 JdbcSinkConnectorConfig.InsertMode insertMode);

	Map<Long, RecordMetric> flushBufferByRunId(CopyOperationLocalBuffer buffer,
	                                          Supplier<Connection> connectionProvider,
	                                          JdbcSinkConnectorConfig.InsertMode insertMode);

	void uploadFile(File localFile);

	void uploadFiles(Supplier<Connection> connectionProvider,
	                 JdbcSinkConnectorConfig.InsertMode insertMode,
	                 Consumer<Connection> manageCopyErrors,
	                 File localFile,
	                 Optional<ReplicationContext> replicationContext);

	void createNewBuffers();

	List<CopyOperationLocalBuffer> getBuffers();

	int onUnprocessedBuffers(Consumer<NexlaMessage> consumer);

	void close(Supplier<Connection> connectionProvider);

	DbDialect dbDialect();
}
