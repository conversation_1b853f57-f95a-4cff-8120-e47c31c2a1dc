package connect.jdbc.sink.dialect;

import com.google.common.base.Supplier;
import com.google.common.collect.Sets;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.data.Schema;
import connect.jdbc.sink.dialect.copy.*;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyTempStorage;
import connect.jdbc.sink.dialect.type.OracleType;
import connect.jdbc.sink.warehouse.DataWarehouseSink;
import connect.jdbc.sink.warehouse.OracleAutonomousDataWarehouseSink;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;

import java.sql.Types;
import java.util.*;

import static com.nexla.connector.config.SinkConnectorConfig.DEFAULT_MAPPING;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.*;
import static connect.jdbc.sink.dialect.DialectFeature.COPY_INSERT;
import static connect.jdbc.sink.dialect.DialectFeature.COPY_UPSERT;

public class OracleAutonomousDialect extends OracleDialect {

	private static final Set<DialectFeature> ORACLE_AUTONOMOUS_DIALECT_FEATURES = EnumSet.of(COPY_INSERT, COPY_UPSERT);

	public OracleAutonomousDialect() {
		// java.sql.Date is serialized as epoch millis when uploading samples from listing,
		// however the oracle DBMS_CLOUD.export_data routine used during source execution will use an ISO 8601 string
		// in order to keep schema detection samples consistent with live data we use the string value
		// this still results in a difference
		// "yyyy-MM-dd HH:mm:ss" vs "yyyy-MM-ddTHH:mm:ss", however this should be purely cosmetic
		typeMappings.put(OracleType.DATE.type, Types.VARCHAR);
	}

	@Override
	public String getDatabaseTerm() {
		return SCHEMA;
	}

	@Override
	public Set<DialectFeature> getFeatures() {
		return ORACLE_AUTONOMOUS_DIALECT_FEATURES;
	}

	@Override
	public Set<WarehouseCopyFileFormat> sourceFileFormats() {
		return Sets.newHashSet(JSON, CSV, JSON_GZIP, CSV_GZIP);
	}

	@Override
	public Set<WarehouseCopyFileFormat> sinkFileFormats() {
		return Sets.newHashSet(JSON, JSON_GZIP, CSV, CSV_GZIP, CSV_BAR, CSV_BAR_GZIP);
	}

	@Override
	public Optional<WarehouseCopyFileFormat> defaultSourceFileFormat() {
		return Optional.of(JSON_GZIP);
	}

	@Override
	public Optional<WarehouseCopyFileFormat> defaultSinkFileFormat() {
		return Optional.of(JSON_GZIP);
	}

	@Override
	public SinkCopyOperation newSinkCopyOperation(JdbcSinkConnectorConfig config) {
		return new SinkOracleAutonomousCopyOperation();
	}

	@Override
	public SourceCopyOperation newSourceCopyOperation(JdbcSourceConnectorConfig config,
	                                                  WarehouseCopyFileFormat fileFormat,
	                                                  Logger logger,
	                                                  boolean isReplicationContext,
	                                                  WarehouseCopyTempStorage storage,
	                                                  FlowType flowType) {
		return new SourceOracleAutonomousCopyOperation(config, fileFormat, logger, storage, flowType);
	}

	@Override
	public String getTargetName(String originalName) {
		return TableNameMapper.getName(originalName.toUpperCase(), TableNameMapper.groupNamePattern3, false, 256, "[A-Z0-9_]", TableNameMapper.prefix.toUpperCase());
	}

	@Override
	public DataWarehouseSink newDataWarehouseSink(JdbcSinkConnectorConfig config,
												  WarehouseCopyFileFormat fileFormat,
												  Schema schema,
												  DbDialect dbDialect,
												  NexlaLogger logger) {
		return new OracleAutonomousDataWarehouseSink(new SinkCopyOperationCommon(config, fileFormat, schema, dbDialect, logger));
	}

	@Override
	public Schema.Type getSchemaTypeByDbType(String dbType) {
		switch (dbType) {
			case "NUMBER(3,0)":
				return Schema.Type.INT8;
			case "NUMBER(5,0)":
				return Schema.Type.INT16;
			case "DATE":
			case "NUMBER(10,0)":
				return Schema.Type.INT32;
			case "TIMESTAMP":
			case "NUMBER(*,null)":
			case "NUMBER(19,0)":
				return Schema.Type.INT64;
			case "BINARY_FLOAT":
				return Schema.Type.FLOAT32;
			case "BINARY_DOUBLE":
				return Schema.Type.FLOAT64;
			case "NUMBER(1,0)":
				return Schema.Type.BOOLEAN;
			case "VARCHAR2(3000)":
				return Schema.Type.STRING;
			case "BLOB":
				return Schema.Type.BYTES;
			default:
				return null;
		}
	}

	@Override
	public Map<Schema.Type, List<Schema.Type>> allowedColumnModifications() {
		return Map.of(
				Schema.Type.BYTES, List.of(),
				Schema.Type.STRING, List.of(),
				Schema.Type.BOOLEAN, List.of(Schema.Type.STRING),
				Schema.Type.FLOAT32, List.of(Schema.Type.FLOAT64, Schema.Type.STRING, Schema.Type.BYTES),
				Schema.Type.FLOAT64, List.of(Schema.Type.STRING, Schema.Type.BYTES),
				Schema.Type.INT8, List.of(Schema.Type.INT16, Schema.Type.INT32, Schema.Type.INT64, Schema.Type.STRING, Schema.Type.FLOAT32, Schema.Type.FLOAT64, Schema.Type.BYTES),
				Schema.Type.INT16, List.of(Schema.Type.INT32, Schema.Type.INT64, Schema.Type.STRING, Schema.Type.FLOAT32, Schema.Type.FLOAT64, Schema.Type.BYTES),
				Schema.Type.INT32, List.of(Schema.Type.INT64, Schema.Type.STRING, Schema.Type.FLOAT32, Schema.Type.FLOAT64, Schema.Type.BYTES),
				Schema.Type.INT64, List.of(Schema.Type.STRING, Schema.Type.FLOAT32,Schema.Type.FLOAT64, Schema.Type.BYTES));
	}
	@Override
	public List<String> getAlterModifySqls(
			String tableName,
			String nonKeyCol,
			Supplier<AutomaticBinding> automaticBinding,
			MappingConfig mappingConfig) {

		String sqlType = mappingConfig
				.getMapping()
				.values()
				.stream()
				.filter(map -> map.containsKey(nonKeyCol))
				.findFirst()
				.map(e -> e.get(nonKeyCol))
				.filter(t -> !t.equals(DEFAULT_MAPPING))
				.orElse(automaticBinding.get().getSqlType(nonKeyCol, this));

		//ALTER TABLE <t1> ADD COLUMN <new_column> <_correct_column_type_>;
		//UPDATE <t1> SET <new_column> = <column>;
		//ALTER TABLE <t1> DROP COLUMN <column>;
		//ALTER TABLE <t1> RENAME COLUMN <new_column> TO <column>;
		String tempCol = nonKeyCol + "_TEMP";
		String add = "ALTER TABLE " + tableName + " ADD " + q(tempCol) + " " + sqlType;
		String update = "UPDATE " + tableName + " SET " + q(tempCol) + " = " + q(nonKeyCol);
		String drop = "ALTER TABLE " + tableName + " DROP COLUMN " + q(nonKeyCol);
		String rename = "ALTER TABLE " + tableName + " RENAME COLUMN " + q(tempCol) + " TO " + q(nonKeyCol);

		List<String> sqls = new ArrayList<>();
		sqls.add(add);
		sqls.add(update);
		sqls.add(drop);
		sqls.add(rename);

		return sqls;
	}

	@Override
	public String getSelectRawDataTypesQuery(String table, String schema) {
		var schemaCondition = StringUtils.isNotBlank(schema) ? " AND owner = '" + schema.toUpperCase() + "'" : "";
		var query = "SELECT column_name, " +
				" CASE " +
				" WHEN data_type in ('VARCHAR2', 'VARCHAR', 'CHAR', 'NCHAR') AND char_length IS NOT NULL THEN data_type || '(' || char_length || ')' " +
				" WHEN data_precision IS NOT NULL AND data_scale IS NOT NULL THEN data_type || '(' || data_precision || ',' || data_scale || ')' " +
				" ELSE data_type " +
				" END AS data_type " +
				" FROM ALL_TAB_COLUMNS " +
				" WHERE table_name = '%s' %s";

		return String.format(query, table, schemaCondition);
	}
}
