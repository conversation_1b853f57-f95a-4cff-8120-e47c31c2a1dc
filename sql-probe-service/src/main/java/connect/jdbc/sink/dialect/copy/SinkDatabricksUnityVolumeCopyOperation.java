package connect.jdbc.sink.dialect.copy;

import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.DatabricksDialect;
import connect.jdbc.sink.dialect.copy.filewriter.JsonDataFileWriter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.databricks.sdk.WorkspaceClient;
import com.databricks.sdk.core.DatabricksConfig;

/**
 * Implementation of SinkCopyOperation for Databricks Unity Volume.
 * Supports both jdbc:databricks: and jdbc:spark: connection URLs.
 * Handles OAuth M2M authentication for databricks URLs and PAT authentication for spark URLs.
 */
public class SinkDatabricksUnityVolumeCopyOperation extends BaseSinkCopyOperation {

    private static final Logger logger = LoggerFactory.getLogger(SinkDatabricksUnityVolumeCopyOperation.class);
    private static final DatabricksDialect DIALECT = new DatabricksDialect();

    // Store the local file for upload to Unity Volume
    private File localFileToUpload;

    // Regex patterns for extracting information from JDBC URLs
    private static final Pattern DATABRICKS_HOST_PATTERN = Pattern.compile("jdbc:databricks://([^:/]+)");
    private static final Pattern SPARK_HOST_PATTERN = Pattern.compile("jdbc:spark://([^:/]+)");
    private static final Pattern SPARK_PWD_PATTERN = Pattern.compile("(?:;|^)PWD=([^;]+)");

    @Override
    @SneakyThrows
    protected void executeCopyCommand(String volumeLocation, Connection connection, Optional<ReplicationContext> replicationContext) {
        logger.info("[DATABRICKS-UNITY] Starting executeCopyCommand with volumeLocation: {}, Target table: {}", 
                   volumeLocation, getQualifiedTableName());

        createVolumeIfNotExists(connection);
        
        logger.info("[DATABRICKS-UNITY] Uploading local file: {} to volume location: {}", localFileToUpload.getName(), volumeLocation);
        uploadFileToUnityVolume(localFileToUpload, volumeLocation, connection);

        try {
            executeTableOperation(connection, volumeLocation, replicationContext);
        } catch (Exception e) {
            logger.error("[DATABRICKS-UNITY] Error in executeCopyCommand: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to execute copy command for table " + config.table, e);
        }
    }

    @Override
    @SneakyThrows
    protected void executeUpsert(Statement st, File localFile, String tempTable, Optional<ReplicationContext> replicationContext) {
        logger.info("[DATABRICKS-UNITY] Starting executeUpsert for file: {}", localFile.getName());

        try {
            Connection conn = st.getConnection();
            createVolumeIfNotExists(conn);

            String volumePath = tempFileLocation(localFile.getName());
            uploadFileToUnityVolume(localFile, volumePath, conn);

            executeTableOperation(conn, volumePath, replicationContext);
        } catch (Exception e) {
            logger.error("[DATABRICKS-UNITY] Error in executeUpsert: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to execute upsert operation for table " + config.table, e);
        }
    }

    /**
     * Unified method to handle both copy and upsert operations using MERGE
     * @param connection Database connection
     * @param volumePath Path to the file in Unity Volume
     * @param replicationContext Replication context
     */
    private void executeTableOperation(Connection connection, String volumePath,
                                     Optional<ReplicationContext> replicationContext) throws SQLException {
        final boolean tableExist = isTableExistQualified(connection);
        logger.info("[DATABRICKS-UNITY] Delta table {} exists: {}", config.table, tableExist);

        if (tableExist) {
            // Use MERGE for both insert and update operations
            String mergeCommand = buildMergeCommand(volumePath, replicationContext);
            logger.info("[DATABRICKS-UNITY] Executing MERGE command: {}", mergeCommand);
            executeStatement(connection, mergeCommand);
        } else {
            logger.info("[DATABRICKS-UNITY] Table {} does not exist. Creating table from volume path: {}", config.table, volumePath);
            String createCommand = createDeltaTableCommand(volumePath);
            executeStatement(connection, createCommand);
        }
    }

    /**
     * Builds a MERGE command that handles both insert and update scenarios
     * @param volumePathWithFile Path to the JSON file in Unity Volume
     * @param replicationContext Replication context
     * @return MERGE SQL command
     */
    private String buildMergeCommand(String volumePathWithFile, Optional<ReplicationContext> replicationContext) {
        String qualifiedTableName = getQualifiedTableName();
        logger.info("[DATABRICKS-UNITY] Building MERGE command - table: {}, primaryKeys: {}, volumePath: {}",
                   qualifiedTableName, config.primaryKey, volumePathWithFile);
        
        if (config.primaryKey == null || config.primaryKey.isEmpty()) {
            // No primary keys defined - treat as append-only (INSERT only)
            logger.info("[DATABRICKS-UNITY] No primary keys defined. Using append-only MERGE for table {}", qualifiedTableName);
            return String.format("MERGE INTO %s target\n" +
                                "USING (SELECT * FROM json.`%s`) source\n" +
                                "ON FALSE\n" +
                                "WHEN NOT MATCHED THEN INSERT *",
                                qualifiedTableName, volumePathWithFile);
        }

        // Primary keys defined - handle both INSERT and UPDATE
        final String condition = config.primaryKey.stream()
            .map(k -> String.format("target.%s = source.%s", DIALECT.q(k), DIALECT.q(k)))
            .collect(Collectors.joining(" AND "));

        String mergeCommand = String.format("MERGE INTO %s target\n" +
                              "USING (SELECT * FROM json.`%s`) source\n" +
                              "ON %s\n" +
                              "WHEN MATCHED THEN UPDATE SET *\n" +
                              "WHEN NOT MATCHED THEN INSERT *",
                              qualifiedTableName, volumePathWithFile, condition);
        
        logger.info("[DATABRICKS-UNITY] Generated MERGE command with primary key matching");
        return mergeCommand;
    }

    // Instance method that can use the qualified table name
    private boolean isTableExistQualified(Connection connection) throws java.sql.SQLException {
        String qualifiedTableName = getQualifiedTableName();

        try (Statement checkStatement = connection.createStatement()) {
            String testQuery = "SELECT 1 FROM " + qualifiedTableName + " LIMIT 1";
            checkStatement.executeQuery(testQuery);
            logger.info("[DATABRICKS-UNITY] Qualified table '{}' exists", qualifiedTableName);
            return true;
        } catch (SQLException e) {
            logger.error("[DATABRICKS-UNITY] Qualified table '{}' does not exist: {}", qualifiedTableName, e.getMessage());
            return false;
        }
    }

    @Override
    public DbDialect dbDialect() {
        return DIALECT;
    }

    @Override
    @SneakyThrows
    protected void runUpsert(Optional<List<String>> columnsOpt, File localFile, Connection conn, Optional<ReplicationContext> replicationContext) {
        // Since we have the connection directly, we can avoid creating an intermediate Statement
        // and just call our unified method directly
        try {
            createVolumeIfNotExists(conn);
            
            String volumePath = tempFileLocation(localFile.getName());
            uploadFileToUnityVolume(localFile, volumePath, conn);
            
            executeTableOperation(conn, volumePath, replicationContext);
        } catch (Exception e) {
            logger.error("[DATABRICKS-UNITY] Error in runUpsert: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to execute upsert operation for table " + config.table, e);
        }
    }

    @Override
    protected void initCloudObjectStoreClient() {
        // Unity Volume doesn't require a separate cloud object store client initialization here.
        // The WorkspaceClient is created on-demand.
    }

    @Override
    protected void deleteCloudFile(File localFile) {
        // Files on Unity Volumes are managed by Databricks.
        // If a cleanup of the specific file uploaded to the volume is needed,
        // it would require a Databricks SDK call to delete from the volume path.
        // For now, assuming Databricks or subsequent processes handle lifecycle.
        logger.info("File {} was uploaded to Unity Volume. Assuming its lifecycle is managed there.", localFile.getName());
    }

    @Override
    public void uploadFile(File localFile) {
        this.localFileToUpload = localFile;
        logger.info("[DATABRICKS-UNITY] Local file stored for later upload: {}", localFile.getAbsolutePath());
        // The actual upload happens later in executeCopyCommand or executeUpsert via uploadFileToUnityVolume
    }

    @SneakyThrows
    private void uploadFileToUnityVolume(File localFile, String volumePathWithFile, Connection connection) {
        logger.info("[DATABRICKS-UNITY] File details - size: {} bytes, exists: {}, readable: {}",
                   localFile.length(), localFile.exists(), localFile.canRead());

        try {
            // Use Databricks SDK for reliable file upload
            WorkspaceClient workspaceClient = createWorkspaceClient(connection);
            
            try (InputStream fileInputStream = Files.newInputStream(localFile.toPath())) {
                // Ensure the target path for upload is just the path, not including dbfs:/ prefix if WorkspaceClient handles it.
                // The volumePathWithFile is like /Volumes/catalog/schema/volume/file.json, which is correct for workspaceClient.files().upload()
                workspaceClient.files().upload(volumePathWithFile, fileInputStream); // true for overwrite
                logger.info("[DATABRICKS-UNITY] File uploaded successfully to Unity Volume using Databricks SDK: {}", volumePathWithFile);
                return;
            }
        } catch (Exception sdkException) {
            logger.warn("[DATABRICKS-UNITY] Databricks SDK upload failed for {}: {}. Attempting fallback.", volumePathWithFile, sdkException.getMessage(), sdkException);
            throw new RuntimeException("File upload to Unity Volume " + volumePathWithFile + " failed using Databricks SDK. SDK Error: " + sdkException.getMessage(), sdkException);
        }
    }
    
    private WorkspaceClient createWorkspaceClient(Connection connection) throws SQLException {
        String jdbcUrl = connection.getMetaData().getURL();
        logger.info("[DATABRICKS-UNITY] Creating WorkspaceClient for JDBC URL: {}", jdbcUrl);

        DatabricksConfig databricksConfig = new DatabricksConfig();
        String host;

        if (jdbcUrl.startsWith("jdbc:databricks:")) {
            logger.info("[DATABRICKS-UNITY] Detected jdbc:databricks URL, using M2M authentication");
            host = extractHost(jdbcUrl, DATABRICKS_HOST_PATTERN);
            String clientId = extractClientIdFromConfig(); // Uses config.authConfig.jdbcParameters
            String clientSecret = extractClientSecretFromConfig(); // Uses config.authConfig.jdbcParameters

            if (StringUtils.isAnyBlank(host, clientId)) { // Secret can sometimes be optional depending on auth
                logger.error("[DATABRICKS-UNITY] Missing host or clientId for jdbc:databricks URL. Host: {}, ClientId provided: {}", host, !StringUtils.isBlank(clientId));
                throw new IllegalArgumentException("Missing host or clientId for Databricks M2M authentication via jdbc:databricks URL.");
            }
            if (StringUtils.isBlank(clientSecret)) {
                 logger.warn("[DATABRICKS-UNITY] ClientSecret is not configured for jdbc:databricks URL. Authentication might fail if required.");
            }
            databricksConfig.setHost(host).setClientId(clientId).setClientSecret(clientSecret);
            logger.info("[DATABRICKS-UNITY] Configured WorkspaceClient for Databricks JDBC URL (M2M Auth). Host: {}, ClientID: {}", host, clientId);

        } else if (jdbcUrl.startsWith("jdbc:spark:")) {
            logger.info("[DATABRICKS-UNITY] Detected jdbc:spark URL, using PAT authentication");
            host = extractHost(jdbcUrl, SPARK_HOST_PATTERN);
            String token = extractTokenFromSparkJdbcUrl(jdbcUrl);

            if (StringUtils.isAnyBlank(host, token)) {
                throw new IllegalArgumentException("Missing host or PAT token for Databricks authentication via jdbc:spark URL.");
            }
            databricksConfig.setHost(host).setToken(token);

        } else {
            logger.error("[DATABRICKS-UNITY] Unsupported JDBC URL for WorkspaceClient creation: {}", jdbcUrl);
            throw new IllegalArgumentException("Unsupported JDBC URL for Databricks WorkspaceClient: " + jdbcUrl);
        }
        
        logger.info("[DATABRICKS-UNITY] Creating WorkspaceClient with configuration");
        return new WorkspaceClient(databricksConfig);
    }
    
    private String extractHost(String jdbcUrl, Pattern pattern) {
        Matcher matcher = pattern.matcher(jdbcUrl);
        if (matcher.find()) {
            String rawHost = matcher.group(1);
            return "https://" + rawHost;
        }
        logger.error("Cannot extract host from JDBC URL: {} using pattern: {}", jdbcUrl, pattern.pattern());
        throw new IllegalArgumentException("Cannot extract host from JDBC URL: " + jdbcUrl);
    }

    private String extractTokenFromSparkJdbcUrl(String jdbcUrl) {
        // Example: ****************************;...
        // Look for PWD=value part
        Matcher matcher = SPARK_PWD_PATTERN.matcher(jdbcUrl);
        if (matcher.find()) {
            String token = matcher.group(1);
            if (!StringUtils.isBlank(token)) {
                return token;
            }
        }
        logger.warn("Could not extract PAT token (PWD field) from Spark JDBC URL: {}", jdbcUrl);
        return null; // Or throw exception if token is strictly required and not found
    }

    @Override
    protected String getTempFilePrefix() {
        return "nexla_unity_volume_";
    }

    @Override
    String tempFileLocation(String fileName) {
        // Path for Unity Catalog Volumes: /Volumes/<catalog_name>/<schema_name>/<volume_name>/<path_to_file>
        String catalog = config.authConfig.databaseName; // Assuming databaseName maps to catalog
        String schema = config.authConfig.schemaName;
        String volumeName = config.table; // Using table name as volume name, as per original logic

        if (StringUtils.isAnyBlank(catalog, schema, volumeName)) {
            throw new IllegalArgumentException("Catalog, schema, or volume name is not configured for Unity Volume path construction.");
        }

        // Ensure filename has .json extension, as data is written in JSON format by JsonDataFileWriter
        String finalFileName = fileName;
        if (dataFileWriter instanceof JsonDataFileWriter && !finalFileName.toLowerCase().endsWith(".json")) {
            finalFileName += ".json";
        }

        // For file paths, use raw names without SQL quoting (no backticks)
        // The Databricks SDK expects paths like: /Volumes/catalog/schema/volume/file.json
        String volumePath = String.format("/Volumes/%s/%s/%s/%s",
                             catalog, schema, volumeName, finalFileName);
        
        logger.info("[DATABRICKS-UNITY] Generated Unity Volume path: {}", volumePath);
        return volumePath;
    }

    @Override
    public String copyCommand(Schema schema, String volumeLocationWithFile, String table, Optional<ReplicationContext> replicationContext) {
        // This method is kept for interface compatibility but we now use MERGE consistently
        // Convert to MERGE command for consistency
        return buildMergeCommand(volumeLocationWithFile, replicationContext);
    }

    private String createDeltaTableCommand(String volumeLocationWithFile) {
        // Creates a new Delta table by selecting data from the JSON file in the volume.
        return String.format("CREATE TABLE IF NOT EXISTS %s\nUSING delta\nTBLPROPERTIES ('delta.autoMerge.enabled' = 'true')\nAS SELECT *\nFROM json.`%s`",
                             getQualifiedTableName(), volumeLocationWithFile);
    }

    @Override
    protected String createCommand(String tempTable, String qualifiedTableName) {
        // Not used for Databricks Unity Volume with MERGE/COPY INTO.
        // Temporary SQL tables are not part of this workflow.
        return "";
    }

    @Override
    public void dropTempTable(Statement stmt, String tempTable) {
        // No temp table, we use databricks merge for upsert
    }

    private String getQualifiedTableName() {
        String catalog = config.authConfig.databaseName; // Assuming databaseName is catalog
        String schema = config.authConfig.schemaName;
        String table = config.table;

        logger.debug("[DATABRICKS-UNITY] Building qualified table name with - Catalog: '{}', Schema: '{}', Table: '{}'",
                     catalog, schema, table);

        if (StringUtils.isAnyBlank(catalog, schema, table)) {
            logger.error("[DATABRICKS-UNITY] Missing configuration for qualified table name construction - Catalog: '{}', Schema: '{}', Table: '{}'",
                        catalog, schema, table);
            throw new IllegalArgumentException("Catalog, schema, or table name is not configured for qualified name construction.");
        }
        // Quote each part of the name
        String qualifiedName = String.format("%s.%s.%s",
                             DIALECT.q(catalog),
                             DIALECT.q(schema),
                             DIALECT.q(table));
        
        logger.info("[DATABRICKS-UNITY] Generated qualified table name: {}", qualifiedName);
        return qualifiedName;
    }

    @SneakyThrows
    private void executeStatement(Connection connection, String sql) {
        try (Statement statement = connection.createStatement()) { // Using Statement for DDL/DML like COPY, MERGE
            long startTime = System.currentTimeMillis();
            statement.execute(logSql(sql));
            long endTime = System.currentTimeMillis();
            logger.info("[DATABRICKS-UNITY] SQL executed successfully in {} ms", (endTime - startTime));
        } catch (Exception e) {
            logger.error("[DATABRICKS-UNITY] Failed to execute SQL statement: {}. Error: {}", sql, e.getMessage(), e);
            throw new RuntimeException("Failed to execute SQL statement on Databricks: " + sql, e);
        }
    }

    /**
     * Creates Unity Volume if it doesn't exist
     * Uses databaseName as catalog, schemaName as schema, tableName as volume
     */
    @SneakyThrows
    private void createVolumeIfNotExists(Connection connection) {
        String catalog = config.authConfig.databaseName;
        String schema = config.authConfig.schemaName;
        String volumeName = config.table; // Using table name as volume name

        if (StringUtils.isAnyBlank(catalog, schema, volumeName)) {
            logger.error("[DATABRICKS-UNITY] Missing required configuration for volume creation - Catalog: '{}', Schema: '{}', Volume: '{}'",
                        catalog, schema, volumeName);
            throw new IllegalArgumentException("Catalog, schema, or volume name is not configured for Unity Volume path construction.");
        }

        String createVolumeSql = String.format("CREATE VOLUME IF NOT EXISTS %s.%s.%s",
                                               DIALECT.q(catalog), DIALECT.q(schema), DIALECT.q(volumeName));
        executeStatement(connection, createVolumeSql);
        logger.info("[DATABRICKS-UNITY] Volume '{}.{}.{}' is ready.", catalog, schema, volumeName);
    }

    private String extractClientIdFromConfig() {
        String clientId = null;
        // 1. Check JDBC parameters (most specific)
        if (config.authConfig.jdbcParameters != null) {
            clientId = config.authConfig.jdbcParameters.get("OAuth2ClientId");
            if (!StringUtils.isBlank(clientId)) {
                logger.debug("Found Client ID in jdbcParameters using key 'OAuth2ClientId'");
                return clientId;
            }
        }

        // 2. Check original configuration map
        try {
            Map<String, ?> originals = config.authConfig.originals();
            Object clientIdObj = originals.get("database.field.OAuth2ClientId");
            if (clientIdObj != null && !StringUtils.isBlank(String.valueOf(clientIdObj))) {
                logger.debug("Found Client ID in originals using key 'database.field.OAuth2ClientId'");
                return String.valueOf(clientIdObj);
            }
            // Fallback to other common keys in originals if the specific one isn't found
            clientIdObj = originals.get("clientId"); // Common generic key
            if (clientIdObj != null && !StringUtils.isBlank(String.valueOf(clientIdObj))) {
                 logger.debug("Found Client ID in originals using key 'clientId'");
                return String.valueOf(clientIdObj);
            }
            clientIdObj = originals.get("client_id"); // Another common variation
             if (clientIdObj != null && !StringUtils.isBlank(String.valueOf(clientIdObj))) {
                 logger.debug("Found Client ID in originals using key 'client_id'");
                return String.valueOf(clientIdObj);
            }
        } catch (Exception e) {
            logger.debug("Could not check original config for client ID: {}", e.getMessage());
        }

        logger.warn("Client ID not found using keys 'OAuth2ClientId' in jdbcParameters or 'database.field.OAuth2ClientId' (and fallbacks) in original configuration.");
        return null;
    }

    private String extractClientSecretFromConfig() {
        String clientSecret = null;
        // 1. Check JDBC parameters (most specific)
        if (config.authConfig.jdbcParameters != null) {
            clientSecret = config.authConfig.jdbcParameters.get("OAuth2Secret");
            if (!StringUtils.isBlank(clientSecret)) {
                logger.debug("Found Client Secret in jdbcParameters using key 'OAuth2Secret'");
                return clientSecret;
            }
        }

        // 2. Check original configuration map
        try {
            Map<String, ?> originals = config.authConfig.originals();
            Object clientSecretObj = originals.get("database.field.OAuth2Secret");
            if (clientSecretObj != null && !StringUtils.isBlank(String.valueOf(clientSecretObj))) {
                logger.debug("Found Client Secret in originals using key 'database.field.OAuth2Secret'");
                return String.valueOf(clientSecretObj);
            }
            // Fallback to other common keys in originals
            clientSecretObj = originals.get("clientSecret"); // Common generic key
            if (clientSecretObj != null && !StringUtils.isBlank(String.valueOf(clientSecretObj))) {
                logger.debug("Found Client Secret in originals using key 'clientSecret'");
                return String.valueOf(clientSecretObj);
            }
             clientSecretObj = originals.get("client_secret"); // Another common variation
            if (clientSecretObj != null && !StringUtils.isBlank(String.valueOf(clientSecretObj))) {
                logger.debug("Found Client Secret in originals using key 'client_secret'");
                return String.valueOf(clientSecretObj);
            }
        } catch (Exception e) {
            logger.debug("Could not check original config for client secret: {}", e.getMessage());
        }

        logger.warn("Client Secret not found using keys 'OAuth2Secret' in jdbcParameters or 'database.field.OAuth2Secret' (and fallbacks) in original configuration.");
        return null;
    }

    @Override
    public void close(Supplier<Connection> connectionProvider) {
        // No specific resources like a dedicated client to close here,
        // as WorkspaceClient is created and used per operation.
    }
}