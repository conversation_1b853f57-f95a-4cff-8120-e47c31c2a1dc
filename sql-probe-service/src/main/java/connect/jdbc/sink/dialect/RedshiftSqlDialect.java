package connect.jdbc.sink.dialect;

import com.google.common.base.Supplier;
import com.google.common.collect.Sets;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.tracker.Tracker;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.data.Date;
import connect.data.*;
import connect.jdbc.sink.dialect.copy.*;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyTempStorage;
import connect.jdbc.sink.dialect.type.RedshiftType;
import connect.jdbc.sink.warehouse.DataWarehouseSink;
import connect.jdbc.sink.warehouse.RedshiftDataWarehouseSink;
import connect.jdbc.util.WarehouseUtils;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Types;
import java.util.*;
import java.util.stream.Collectors;

import static com.nexla.connector.config.SinkConnectorConfig.DEFAULT_MAPPING;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.*;
import static connect.jdbc.sink.dialect.DialectFeature.COPY_INSERT;
import static connect.jdbc.sink.dialect.DialectFeature.COPY_UPSERT;
import static connect.jdbc.sink.dialect.StringBuilderUtil.joinToBuilder;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

public class RedshiftSqlDialect extends DbDialect {

	private static final Set<DialectFeature> DIALECT_FEATURES = EnumSet.of(COPY_INSERT, COPY_UPSERT);

	private static final String SCHEMA_PARAMETER = "search_path";
	private static final String DEFAULT_SCHEMA_NAME = "public";

	protected RedshiftSqlDialect(
		String escapeStart,
		String escapeEnd,
		Optional<String> schemaParam,
		Optional<String> warehouseParam,
		Optional<String> defaultSchemaName
	) {
		super(escapeStart, escapeEnd, schemaParam, warehouseParam, defaultSchemaName, false);
	}

	public RedshiftSqlDialect() {
		super("\"", "\"", of(SCHEMA_PARAMETER), empty(), of(DEFAULT_SCHEMA_NAME), false);
	}

	@Override
	public Set<WarehouseCopyFileFormat> sourceFileFormats() {
		return Sets.newHashSet(CSV, JSON, CSV_GZIP, JSON_GZIP);
	}

	@Override
	public Set<WarehouseCopyFileFormat> sinkFileFormats() {
		return Sets.newHashSet(CSV, CSV_COMMA, CSV_COMMA_GZIP, JSON, CSV_GZIP, JSON_GZIP);
	}

	@Override
	@SneakyThrows
	public java.sql.Timestamp getTimestampRoundUp(ResultSet rs, int column) {
		return this.roundUpTimestamp(super.getTimestamp(rs, column));
	}

	@Override
	public Optional<WarehouseCopyFileFormat> defaultSourceFileFormat() {
		return Optional.of(CSV_GZIP);
	}

	@Override
	public Optional<WarehouseCopyFileFormat> defaultSinkFileFormat() {
		return Optional.of(JSON_GZIP);
	}

	@Override
	public String getDatabaseTerm() {
		return SCHEMA;
	}

	public StreamEx<String> getSqlTypes() {
		return StreamEx
			.of(RedshiftType.values())
			.map(x -> x.type);
	}

	@Override
	public String getQualifiedSchemaName(String schemaName, String databaseName) {
		return schemaName;
	}

	@Override
	public Schema.Type getSchemaTypeByDbType(String dbType) {
		if ("VARCHAR(65535)".equals(dbType)) {
			return Schema.Type.STRING;
		}

		var availableTypes = Sets.newHashSet(RedshiftType.values())
				.stream()
				.map(Enum::name)
				.collect(Collectors.toSet());

		if (!availableTypes.contains(dbType)){
			return null;
		}

		switch (RedshiftType.valueOf(dbType)) {
			case VARCHAR:
				return Schema.Type.STRING;
			case DATE:
				return Schema.Type.INT32;
			case BIGINT:
			case TIMESTAMP:
				return Schema.Type.INT64;
			case FLOAT4:
				return Schema.Type.FLOAT32;
			case FLOAT8:
				return Schema.Type.FLOAT64;
			case BOOLEAN:
				return Schema.Type.BOOLEAN;
			default:
				return null;
		}
	}

	public String getDbType(Schema.Type type) {
		// Redshift does not support storage of binary data.
		// We use text format to store binary data instead.
		switch (type) {
			case INT8:
			case INT16:
			case INT32:
			case INT64:
				return RedshiftType.BIGINT.type;
			case FLOAT32:
				return RedshiftType.FLOAT4.type;
			case FLOAT64:
				return RedshiftType.FLOAT8.type;
			case BOOLEAN:
				return RedshiftType.BOOLEAN.type;
			case STRING:
				return "VARCHAR(65535)";
			case BYTES:
				return "VARCHAR(65535)";
			default:
				return null;
		}
	}

	public String getDbTypeByKafka(String schemaName, Map<String, String> parameters) {
		switch (schemaName) {
			case Decimal.LOGICAL_NAME:
				return RedshiftType.DECIMAL.type;
			case Date.LOGICAL_NAME:
				return RedshiftType.DATE.type;
			case Time.LOGICAL_NAME:
			case Timestamp.LOGICAL_NAME:
				return RedshiftType.TIMESTAMP.type;
			default:
				return null;
		}
	}

	@Override
	public Set<DialectFeature> getFeatures() {
		return DIALECT_FEATURES;
	}

	@Override
	public String getUpsertQuery(String table, Collection<String> keyCols, Collection<String> cols) {
		throw new UnsupportedOperationException();
	}

	@Override
	public void resolveSqlType(ResultSetMetaData metadata, int col, SchemaBuilder builder, boolean mapNumerics, String fieldName, int sqlType, boolean optional) throws SQLException {
		if ("bool".equals(metadata.getColumnTypeName(col))) {
			sqlType = Types.BOOLEAN;
		}
		if (sqlType == Types.NUMERIC) {
			if (mapNumerics) {
				processNumeric(metadata, col, builder, fieldName, optional);
			} else {
				if (optional) {
					builder.field(fieldName, Schema.OPTIONAL_FLOAT64_SCHEMA);
				} else {
					builder.field(fieldName, Schema.FLOAT64_SCHEMA);
				}
			}
		} else {
			super.resolveSqlType(metadata, col, builder, mapNumerics, fieldName, sqlType, optional);
		}
	}

	@Override
	@SneakyThrows
	public void convertFieldValue(
		ResultSet resultSet,
		int col,
		int colType,
		Struct struct,
		String fieldName,
		boolean mapNumerics,
		String typeName
	) {
		if ("bool".equals(resultSet.getMetaData().getColumnTypeName(col))) {
			colType = Types.BOOLEAN;
		}
		if ("numeric".equals(resultSet.getMetaData().getColumnTypeName(col))) {
			colType = Types.DOUBLE;
		}
		super.convertFieldValue(resultSet, col, colType, struct, fieldName, mapNumerics, typeName);
	}

	@Override
	public SinkCopyOperation newSinkCopyOperation(JdbcSinkConnectorConfig config) {
		boolean isServerless = WarehouseUtils.isRedshiftServerless(config);
		return new SinkRedshiftCopyOperation(WarehouseUtils.getCopyOperationTempStorage(config), isServerless);
	}

	@Override
	public SourceCopyOperation newSourceCopyOperation(JdbcSourceConnectorConfig config,
	                                                  WarehouseCopyFileFormat fileFormat,
	                                                  Logger logger,
	                                                  boolean isReplicationContext,
													  WarehouseCopyTempStorage bucket,
													  FlowType flowType) {
		return new SourceRedshiftCopyOperation(config, fileFormat, logger, bucket, flowType);
	}

	@Override
	public DataWarehouseSink newDataWarehouseSink(JdbcSinkConnectorConfig config,
	                                              WarehouseCopyFileFormat fileFormat,
	                                              Schema schema,
	                                              DbDialect dbDialect,
	                                              NexlaLogger logger) {
		return new RedshiftDataWarehouseSink(new SinkCopyOperationCommon(config, fileFormat, schema, dbDialect, logger));
	}

	@Override
	public List<String> getAlterModifySqls(
			String tableName,
			String nonKeyCol,
			Supplier<AutomaticBinding> automaticBinding,
			MappingConfig mappingConfig) {

		String sqlType = mappingConfig
				.getMapping()
				.values()
				.stream()
				.filter(map -> map.containsKey(nonKeyCol))
				.findFirst()
				.map(e -> e.get(nonKeyCol))
				.filter(t -> !t.equals(DEFAULT_MAPPING))
				.orElse(automaticBinding.get().getSqlType(nonKeyCol, this));

		//ALTER TABLE <t1> ADD COLUMN <new_column> <correct_column_type>;
		//UPDATE <t1> SET <new_column> = cast(<column> as <correct_column_type>);
		//ALTER TABLE <t1> DROP COLUMN <column>;
		//ALTER TABLE <t1> RENAME COLUMN <new_column> TO <column>;
		String tempCol = nonKeyCol + "_TEMP";
		String add = "ALTER TABLE " + tableName + " ADD COLUMN " + q(tempCol) + " " + sqlType + ";";
		String update = "UPDATE " + tableName + " SET " + q(tempCol) + " = cast(" + q(nonKeyCol) + " as " + sqlType + ");";
		String drop = "ALTER TABLE " + tableName + " DROP COLUMN " + q(nonKeyCol) + ";";
		String rename = "ALTER TABLE " + tableName + " RENAME COLUMN " + q(tempCol) + " TO " + q(nonKeyCol) + ";";

		List<String> sqls = new ArrayList<>();
		sqls.add(add);
		sqls.add(update);
		sqls.add(drop);
		sqls.add(rename);

		return sqls;
	}

	@Override
	public String getCreateSql(
			String tableName,
			Collection<String> keyColumns,
			Collection<String> nonKeyColumns,
			Supplier<AutomaticBinding> automaticBinding,
			MappingConfig mappingConfig,
			JdbcSinkConnectorConfig sinkConnectorConfig) {

		StringBuilder builder = new StringBuilder("CREATE TABLE ");
		builder.append(tableName);
		builder.append(" (");
		if (mappingConfig.getTrackerMode() != Tracker.TrackerMode.NONE) {
			nonKeyColumns.add(mappingConfig.getTrackerFieldName());
		}
		joinToBuilder(builder, ",", keyColumns, nonKeyColumns, (b, val) -> {
			String sqlType = mappingConfig
					.getMapping()
					.values()
					.stream()
					.filter(map -> map.containsKey(val))
					.findFirst()
					.map(e -> e.get(val))
					.filter(t -> !t.equals(DEFAULT_MAPPING))
					.orElseGet(() -> automaticBinding.get().getSqlType(val, this));

			b.append(q(val)).append(" ").append(sqlType);
		});
		builder.append(",");

		if (isNotEmpty(keyColumns)) {
			builder.append("PRIMARY KEY(");
			joinToBuilder(builder, ",", keyColumns, escaper());
			builder.append(")");
		} else {
			//Deleting the final ,
			builder.deleteCharAt(builder.length() - 1);
		}

		builder.append(")");

		List<String> sortKeys = isNotEmpty(sinkConnectorConfig.clusteringingColumns)
				? sinkConnectorConfig.clusteringingColumns
				: sinkConnectorConfig.sortKeys;

		String distKey = sinkConnectorConfig.partitioningColumn.orElseGet(() -> sinkConnectorConfig.distKey);

		if (StringUtils.isNotBlank(distKey)) {
			builder.append(" DISTKEY(\"");
			builder.append(distKey);
			builder.append("\")");
		}

		if (isNotEmpty(sortKeys)) {
			builder.append(" SORTKEY(");
			joinToBuilder(builder, ",", sortKeys, escaper());
			builder.append(")");
		}

		return builder.toString();
	}

	@Override
	public String getTargetName(String originalName) {
		return TableNameMapper.getName(originalName.toLowerCase(), TableNameMapper.groupNamePattern1, false, 126, "[a-z0-9_$]", TableNameMapper.prefix.toLowerCase());
	}

	@Override
	public Map<Schema.Type, List<Schema.Type>> allowedColumnModifications() {
		return Map.of(
				Schema.Type.JSON, List.of(),
				Schema.Type.BOOLEAN, List.of(),
				Schema.Type.STRING, List.of(),
				Schema.Type.FLOAT64, List.of(Schema.Type.STRING, Schema.Type.JSON),
				Schema.Type.INT64, List.of(Schema.Type.STRING, Schema.Type.JSON, Schema.Type.FLOAT64));
	}

	@Override
	public String getSelectRawDataTypesQuery(String table, String schema) {
		var schemaCondition = org.apache.commons.lang.StringUtils.isNotBlank(schema) ? " AND table_schema = '" + schema.toUpperCase() + "'" : "";
		var query = "SELECT column_name, " +
					" CASE " +
					"  WHEN data_type IN ('text', 'character varying', 'character') AND character_maximum_length IS NOT NULL THEN 'VARCHAR(' || character_maximum_length || ')' " +
					"  WHEN data_type IN ('text', 'character varying', 'character') THEN 'VARCHAR' " +
					"  WHEN numeric_precision IS NOT NULL AND numeric_scale IS NOT null THEN data_type || '(' || numeric_precision || ',' || numeric_scale || ')' " +
					"  ELSE data_type " +
					" END AS data_type " +
					" FROM INFORMATION_SCHEMA.COLUMNS " +
					" WHERE table_name = '%s' %s";

		return String.format(query, table, schemaCondition);
	}

}
