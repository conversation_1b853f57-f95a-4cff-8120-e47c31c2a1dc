package connect.jdbc.sink.dialect.copy.storage;

import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaFile;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import com.nexla.probe.s3.S3ConnectorService;
import lombok.Getter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.net.URI;
import java.util.List;
import java.util.stream.Collectors;

import static com.nexla.common.ListingResourceType.FILE;
import static com.nexla.connector.config.file.AWSAuthConfig.toBucketPrefix;

@Getter
public class WarehouseCopyS3Storage implements WarehouseCopyTempStorage {

	protected final AWSAuthConfig config;

	protected final String tempUploadBucket;

	protected final String tempUploadPrefix;

	protected final Boolean deleteTempBucket;

	protected final S3ConnectorService connectorService;

	public WarehouseCopyS3Storage(
			AWSAuthConfig config,
			String tempUploadBucket,
			String tempUploadPrefix,
			boolean deleteTempBucket
	) {
		this.config = config;
		this.tempUploadBucket = tempUploadBucket;
		this.deleteTempBucket = deleteTempBucket;
		this.tempUploadPrefix = tempUploadPrefix;
		this.connectorService = new S3ConnectorService();
	}

	@Override
	public String getSourceCopyQuery(
			WarehouseCopyFileFormat format,
			String destination,
			String source,
			String compression,
			String single,
			boolean optionallyEnclosed
	) {
		throw new UnsupportedOperationException("Source copy query not supported for default s3 bucket!");
	}

	@Override
	public String getSinkCopyQuery(
			String table,
			String columns,
			String destination,
			String format,
			String compression,
			String skipHeaders,
			String columnMatchMode,
			boolean stopOnError
	) {
		throw new UnsupportedOperationException("Sink copy query not supported for default s3 bucket!");
	}

	@Override
	public String getUrlPrefix() {
		return "s3://";
	}

	@Override
	@SneakyThrows
	public List<NexlaFile> listObjects(FileSourceConnectorConfig fileSourceConnectorConfig, String bucketDest) {
		var fileDestinationUri = new URI(bucketDest);
		var bucketPath = StringUtils.removeStart(fileDestinationUri.getPath(), "/");

		var bucketPrefix = toBucketPrefix(bucketPath, true);

		return connectorService.listS3Objects(fileSourceConnectorConfig, bucketPrefix.bucket, bucketPrefix.prefix)
				.map(blob -> new NexlaFile(blob.key(), 0L, null, null, null, null, FILE))
				.collect(Collectors.toList());
	}

	@Override
	public ConnectionType getConnectionType() {
		return ConnectionType.S3;
	}

	@Override
	public void deleteFile(String fileName) {
		if (deleteTempBucket) {
			String s3FileName = tempUploadPrefix + "/" + fileName;
			connectorService.deleteObject(config, tempUploadBucket, s3FileName);
		}
	}

	@Override
	public void uploadFile(File localFile) {
		connectorService.putObject(config, tempUploadBucket, tempUploadPrefix + "/" + localFile.getName(), localFile);
	}
}
