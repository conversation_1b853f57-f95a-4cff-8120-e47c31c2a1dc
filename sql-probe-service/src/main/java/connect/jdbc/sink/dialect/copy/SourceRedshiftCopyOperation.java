package connect.jdbc.sink.dialect.copy;

import software.amazon.awssdk.services.s3.S3Client;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import com.nexla.probe.s3.S3ConnectorService;
import connect.jdbc.sink.dialect.RedshiftSqlDialect;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyTempStorage;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.nio.file.Paths;
import java.sql.Connection;

public class SourceRedshiftCopyOperation extends SourceCopyOperation {

	public static final RedshiftSqlDialect DIALECT = new RedshiftSqlDialect();

	private final JdbcSourceConnectorConfig config;
	private final S3Client s3Client;
	private final Logger logger;

	public SourceRedshiftCopyOperation(JdbcSourceConnectorConfig config,
	                                   WarehouseCopyFileFormat fileFormat,
	                                   Logger logger,
	                                   WarehouseCopyTempStorage storage,
	                                   FlowType flowType) {
		super(fileFormat, storage, flowType);
		this.config = config;
		this.s3Client = S3ConnectorService.createS3ClientFromCreds(config.s3AuthConfig, null);
		this.logger = logger;
	}

	@SneakyThrows
	public void executeCopyCommand(
		Connection connection,
		String query,
		String dest
	) {
		AWSAuthConfig s3AuthConfig = config.s3AuthConfig;
		String dbSource = StringUtils.removeEnd(query, ";");
		String sql = copyCommand(dbSource, dest, s3AuthConfig);
		logSql(sql);
		connection.prepareStatement(sql).execute();
	}

	private String copyCommand(String source, String dest, AWSAuthConfig s3AuthConfig) {
		String formatString;
		String compression;
		if (fileFormat.compression == WarehouseCopyFileFormat.Compression.GZIP) {
			compression = " GZIP";
		} else {
			compression = "";
		}
		if (fileFormat.format == WarehouseCopyFileFormat.Format.CSV_FORMAT) {
			formatString = "CSV ALLOWOVERWRITE HEADER" + compression + " DELIMITER AS ';'";
		} else {
			formatString = "JSON ALLOWOVERWRITE" + compression;
		}

		return "UNLOAD ($$\n" + source + "\n$$)" +
			"  TO '" + dest + "'" +
			"  CREDENTIALS	'aws_access_key_id=" + s3AuthConfig.accessKeyId + ";aws_secret_access_key=" + s3AuthConfig.secretKey + "'" +
			"  FORMAT " + formatString + " REGION '" + s3AuthConfig.region + "'";
	}

    @Override
	public String destinationPath(int sourceId, Long runId) {
		return "s3://" + Paths.get(storage.getTempUploadBucket(), "source-" + sourceId, String.valueOf(runId)) + "/";
	}

	void logSql(String sql) {
		config.ifLogVerbose(() -> logger.info("Executing query={}", sql));
	}

	@Override
	public void close() {
		this.s3Client.close();
	}
}
