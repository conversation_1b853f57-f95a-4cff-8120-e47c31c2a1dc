package connect.jdbc.sink.dialect.copy;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.common.annotations.VisibleForTesting;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.logging.TimeLogger;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connect.common.cdc.DebeziumConstants;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.data.Field;
import connect.data.Schema;
import connect.jdbc.sink.dialect.copy.filewriter.CsvDataFileWriter;
import connect.jdbc.sink.dialect.copy.filewriter.DataFileWriter;
import connect.jdbc.sink.dialect.copy.filewriter.JsonDataFileWriter;
import connect.jdbc.util.JdbcUtils;
import lombok.Getter;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.metrics.RecordMetric.quarantineMessage;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Format.CSV_FORMAT;
import static connect.jdbc.sink.dialect.copy.CopyOperationHelper.deleteRowsCommand;
import static connect.jdbc.sink.dialect.copy.CopyOperationHelper.dropCommand;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;

public abstract class BaseSinkCopyOperation implements SinkCopyOperation {

	protected NexlaLogger logger;

	protected Schema schema;

	protected JdbcSinkConnectorConfig config;

	protected AdminApiClient adminApiClient;

	List<CopyOperationLocalBuffer> localBuffers = Lists.newArrayList();

	@Getter
	protected WarehouseCopyFileFormat fileFormat;

	@Getter
	protected DataFileWriter dataFileWriter;
	private String tempTableName;

	protected String insertCommand(String tempTable, String qualifiedTableName, String columns) {
		var query = "INSERT INTO " + qualifiedTableName + "(" + columns + ") " +
				"SELECT " + columns +
				" FROM " + tempTable;

		if(config.cdcEnabled) {
			var cdcColumn = dbDialect().q(DebeziumConstants.NEXLA_OPERATION_DELETE);
			var cdcCondition = " WHERE " + String.format("%s = FALSE", cdcColumn);
			query = query + cdcCondition;
		}

		return query;
	}

	public BaseSinkCopyOperation init(Schema schema,
	                                  JdbcSinkConnectorConfig config,
	                                  AdminApiClient adminApiClient,
	                                  WarehouseCopyFileFormat fileFormat,
	                                  NexlaLogger logger) {
		this.schema = schema;
		this.config = config;
		this.tempTableName = config.table + "_temp_" + UUID.randomUUID();
		this.logger = logger;
		this.adminApiClient = adminApiClient;
		this.fileFormat = fileFormat;
		if (fileFormat.format == CSV_FORMAT) {
			this.dataFileWriter = new CsvDataFileWriter(fileFormat, schema, dbDialect(), this.logger, List.of(), config);
		} else {
			this.dataFileWriter = new JsonDataFileWriter(fileFormat);
		}
		initCloudObjectStoreClient();
		return this;
	}

	protected abstract void initCloudObjectStoreClient();

	protected abstract void executeCopyCommand(String blobLocation,
	                                           Connection connection,
	                                           Optional<ReplicationContext> replicationContext) throws SQLException;

	protected void manageCopyErrors(List<NexlaMessage> records, RecordMetric recordMetric, Connection conn, String fileName) {

	}

	protected abstract String createCommand(String tempTable, String qualifiedTableName);

	protected abstract String copyCommand(Schema schema, String s3FileLocation, String table, Optional<ReplicationContext> replicationContext);

	protected abstract void deleteCloudFile(File localFile);

	public abstract void uploadFile(File localFile);

	protected abstract String getTempFilePrefix();

	@SneakyThrows
	public RecordMetric flushBuffer(CopyOperationLocalBuffer buffer,
									Supplier<Connection> connectionProvider,
									JdbcSinkConnectorConfig.InsertMode insertMode) {
		RecordMetric recordMetric = new RecordMetric();
		File localFile = createTempFile(fileFormat.extension);
		try (TimeLogger ctx = new TimeLogger(logger, "Flush buffer")) {
			// Step 0. Get records from stream
			List<NexlaMessage> records = buffer.getRecords().toList();

			// Step 1. Filter null and deduplicate primary keys
			if (config.insertMode == UPSERT) {
				records = filterNullPrimaryKeys(records, config.primaryKey, recordMetric);
				records = dedupByPk(records, new ArrayList<>(config.primaryKey));
			}

			// Step 1.1. NEX-15037 - Stop processing when all the records were removed by step 1.
			if (records.isEmpty()) {
				recordMetric.sentBytesTotal.set(0);
				recordMetric.sentRecordsTotal.set(0);
				return recordMetric;
			}

			// Step 2. Write records to a local file
			try (TimeLogger ctx1 = new TimeLogger(logger, "Write local file")) {
				ArrayList<NexlaMessage> writtenRecords = new ArrayList<>();
				dataFileWriter.writeDataFile(
						localFile,
						StreamEx.of(records),
						(exc, nexlaMessage) -> {
							recordMetric.errorRecords.incrementAndGet();
							recordMetric.quarantineMessages.add(quarantineMessage(nexlaMessage, exc.getMessage()));
						},
						writtenRecords::add);
				records = writtenRecords;
			}

			// Step 3. Upload local data file to Stage storage
			try (TimeLogger ctx1 = new TimeLogger(logger, "Upload local data file to Stage storage")) {
				uploadFile(localFile);
			}

			// Step 4. Upload data file from Stage storage to target destination
			try (TimeLogger ctx1 = new TimeLogger(logger, "Upload data file from Stage storage to target destination")) {
				final List<NexlaMessage> finalRecords = records;
				uploadFiles(
						connectionProvider,
						insertMode,
						connection -> manageCopyErrors(finalRecords, recordMetric, connection, localFile.getName()),
						localFile,
						Optional.empty());
			}

			// Step 5. Adjust metrics.
			// NEX-3761 number of input records for JDBC sink should always be the same size as input dataset
			// That means, take number before deduplication
			recordMetric.sentBytesTotal.set(localFile.length());
			recordMetric.sentRecordsTotal.set(buffer.streamSize() - recordMetric.errorRecords.get());
			return recordMetric;
		} finally {
			cleanupTempFiles(localFile);
		}
	}

	@SneakyThrows
	public Map<Long, RecordMetric> flushBufferByRunId(CopyOperationLocalBuffer buffer,
	                                                 Supplier<Connection> connectionProvider,
	                                                 JdbcSinkConnectorConfig.InsertMode insertMode) {
		Map<Long, RecordMetric> metricsByRunId = new HashMap<>();
		File localFile = createTempFile(fileFormat.extension);
		
		try (TimeLogger ctx = new TimeLogger(logger, "Flush buffer by runId")) {
			// Step 0. Get records from stream
			List<NexlaMessage> records = buffer.getRecords().toList();

			// Step 1. Filter null and deduplicate primary keys
			if (config.insertMode == UPSERT) {
				records = filterNullPrimaryKeys(records, config.primaryKey, new RecordMetric());
				records = dedupByPk(records, new ArrayList<>(config.primaryKey));
			}

			if (records.isEmpty()) {
				return metricsByRunId;
			}

			// Step 2. Write records to a local file
			try (TimeLogger ctx1 = new TimeLogger(logger, "Write local file")) {
				ArrayList<NexlaMessage> writtenRecords = new ArrayList<>();
				dataFileWriter.writeDataFile(
						localFile,
						StreamEx.of(records),
						(exc, nexlaMessage) -> {
							Long runId = 0L;
							if (nexlaMessage != null && nexlaMessage.getNexlaMetaData() != null && nexlaMessage.getNexlaMetaData().getRunId() != null) {
								runId = nexlaMessage.getNexlaMetaData().getRunId();
							}
							RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
							recordMetric.errorRecords.incrementAndGet();
							recordMetric.quarantineMessages.add(quarantineMessage(nexlaMessage, exc.getMessage()));
						},
						writtenRecords::add);
				records = writtenRecords;
			}

			// Step 3. Upload local data file to Stage storage
			try (TimeLogger ctx1 = new TimeLogger(logger, "Upload local data file to Stage storage")) {
				uploadFile(localFile);
			}

			// Step 4. Upload data file from Stage storage to target destination
			try (TimeLogger ctx1 = new TimeLogger(logger, "Upload data file from Stage storage to target destination")) {
				final List<NexlaMessage> finalRecords = records;
				uploadFiles(
						connectionProvider,
						insertMode,
						connection -> {
							// Process each record and update metrics by runId
							for (NexlaMessage record : finalRecords) {
								Long runId = 0L;
								if (record != null && record.getNexlaMetaData() != null && record.getNexlaMetaData().getRunId() != null) {
									runId = record.getNexlaMetaData().getRunId();
								}								RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
								manageCopyErrors(Collections.singletonList(record), recordMetric, connection, localFile.getName());
							}
						},
						localFile,
						Optional.empty());
			}

			// Step 5. Adjust metrics for each runId
			for (NexlaMessage record : records) {
				Long runId = 0L;
				if (record != null && record.getNexlaMetaData() != null && record.getNexlaMetaData().getRunId() != null) {
					runId = record.getNexlaMetaData().getRunId();
				}
				RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
				// Calculate bytes for this specific record using MetricUtils
				recordMetric.sentBytesTotal.addAndGet(calcBytes(toJsonString(record.getRawMessage())));
				recordMetric.sentRecordsTotal.incrementAndGet();
			}

			return metricsByRunId;
		} finally {
			cleanupTempFiles(localFile);
		}
	}

	public static List<NexlaMessage> dedupByPk(List<NexlaMessage> dataStream, List<String> keyFields) {
		LinkedHashMap<List<Object>, NexlaMessage> dedupMap = new LinkedHashMap<>();
		dataStream.forEach(value -> dedupMap.put(extractPrimaryKey(keyFields, value.getRawMessage()), value));
		return new ArrayList<>(dedupMap.values());
	}

	private static List<Object> extractPrimaryKey(List<String> keyFields, Map<String, Object> dataToOutput) {
		return keyFields.stream().map(dataToOutput::get).collect(toList());
	}

	@VisibleForTesting
	List<NexlaMessage> filterNullPrimaryKeys(
			final List<NexlaMessage> messageList,
			final List<String> primaryKey,
			final RecordMetric recordMetric) {
		return messageList
				.stream()
				.filter(message -> {
					List<Object> primaryKeyValues = extractPrimaryKey(config.primaryKey, message.getRawMessage());
					if (primaryKeyValues.contains(null) || primaryKeyValues.contains("")) {
						recordMetric.quarantineMessages.add(RecordMetric.quarantineMessage(message, "Null or empty value in primary key"));
						recordMetric.errorRecords.incrementAndGet();
						return false;
					}
					return true;
				})
				.collect(toList());
	}

	@SneakyThrows
	public void uploadFiles(Supplier<Connection> connectionProvider,
	                        JdbcSinkConnectorConfig.InsertMode insertMode,
	                        Consumer<Connection> manageCopyErrors,
	                        File localFile,
	                        Optional<ReplicationContext> replicationContext) {
		Connection connection = connectionProvider.get();
		if (!dbDialect().sinkFileFormats().contains(fileFormat)) {
			throw new RuntimeException("File format " + fileFormat +
				" is not supported. Possible options are: " +
				JsonUtils.toJsonString(dbDialect().sinkFileFormats()));
		}
		try {
			Optional<List<String>> columns = replicationContext.flatMap(ReplicationContext::getColumns);
			if (insertMode == JdbcSinkConnectorConfig.InsertMode.INSERT) {
				runInsert(localFile, connection, replicationContext);
			} else {
				runUpsert(columns, localFile, connection, replicationContext);
			}
			manageCopyErrors.accept(connection);
			replicationContext.ifPresent(rc -> {
				rc.getReplicateMetric().sentBytesTotal.set(localFile.length());
				logger.info("Replication metrics: {}", rc.getReplicateMetric());
			});

			if (!connection.getAutoCommit()) {
				connection.commit();
			}
		} catch (Exception e) {
			if (!connection.getAutoCommit()) {
				connection.rollback();
			}
			throw e;
		} finally {
			closeSilently(connection);
		}
	}

	public int onUnprocessedBuffers(Consumer<NexlaMessage> consumer) {
		return StreamEx.of(localBuffers)
			.filter(x -> !x.isClosed())
			.mapToInt(buffer -> {
				buffer.getRecords().forEach(consumer);
				buffer.close();
				return buffer.streamSize();
			})
			.sum();
	}


	public void createNewBuffers() {
		this.localBuffers.forEach(CopyOperationLocalBuffer::close);
		this.localBuffers = new ArrayList<>();
	}

	@SneakyThrows
	private void runInsert(File localFile,
	                       Connection connection,
	                       Optional<ReplicationContext> replicationContext) {
		String blobLocation = tempFileLocation(localFile.getName());
		executeCopyCommand(blobLocation, connection, replicationContext);
	}

	@SneakyThrows
	protected void runUpsert(Optional<List<String>> columnsOpt, File localFile, Connection conn, Optional<ReplicationContext> replicationContext) {
		String tempTable = getTempTableName();
		String qualifiedTableName = qualifiedName(config.table);
		try (Statement st = conn.createStatement()) {
			createTempTableOrTruncate(tempTable, qualifiedTableName, st);
			
			try {
				executeUpsert(st, localFile, tempTable, replicationContext);

				// deleting rows from target table, that will be updated from temp table
				logUpdated(executeUpdate(st,
						logSql(deleteRowsCommand(config.primaryKey, dbDialect(), config.authConfig.schemaName, tempTable, qualifiedTableName))));

				List<String> columnsList = columnsOpt.orElseGet(() -> columnNames(schema).collect(Collectors.toList()));
				String columns = StreamEx.of(columnsList).collect(joining(","));

				int rowCount = executeUpdate(st, logSql(
						insertCommand(tempTable, qualifiedTableName, columns)));
				logUpdated(rowCount);

				conn.commit();
				replicationContext.ifPresent(rc ->  rc.getReplicateMetric().sentRecordsTotal.set(rowCount));

			} finally {
				dropTempTable(st, tempTable);
			}
		}


	}

	protected void createTempTableOrTruncate(String tempTable, String qualifiedTableName, Statement st) throws SQLException {
		try {
			createTempTable(st, createCommand(tempTable, qualifiedTableName));
			if(config.cdcEnabled) {
				var delColAlter = String.format("ALTER TABLE %s ADD %s BOOLEAN", tempTable, dbDialect().q(DebeziumConstants.NEXLA_OPERATION_DELETE));
				st.executeUpdate(logSql(delColAlter));
			}
		} catch (Exception e) {
			executeUpdate(st, logSql("TRUNCATE TABLE " + tempTable));
		}
	}

	@SneakyThrows
	protected void executeUpsert(Statement st, File localFile, String tempTable, Optional<ReplicationContext> replicationContext){
		logUpdated(
				executeUpdate(st, logSql(copyCommand(schema, tempFileLocation(localFile.getName()), tempTable, replicationContext))));
	}

	protected int executeUpdate(Statement st, String command) throws SQLException {
		return st.executeUpdate(command);
	}

	@SneakyThrows
	protected void createTempTable(Statement st, String createCommand) {
		st.execute(logSql(createCommand));
	}

	@SneakyThrows
	public void dropTempTable(Statement stmt, String tempTable) {
		executeUpdate(stmt, logSql(dropCommand(tempTable)));
	}

	public String getTempTableName() {
		return dbDialect().q(makeStableTempTableName());
	}

	/**
	 * Called multiple times, so should return same table name for this instance of sink op.
	 *
	 * @return table name, which stays unchnaged for this instance of sink op.
	 */
	protected String makeStableTempTableName() {
		if (tempTableName == null) {
			throw new IllegalStateException("The tempTableName is not initialized. CopyOperation#init method was not called.");
		}

		return tempTableName;
	}

	@SneakyThrows
	File createTempFile(String extension) {
		File localFile = File.createTempFile(getTempFilePrefix() + config.sinkId, extension);
		localFile.deleteOnExit();
		return localFile;
	}

	void cleanupTempFiles(File localFile) {
		deleteLocalFile(localFile);
		deleteCloudFile(localFile);
	}

	private void deleteLocalFile(File localFile) {
		if (localFile != null) {
			localFile.delete();
		}
	}

	String tempFileLocation(String fileName) {
		return "s3://" + Paths.get(config.tempS3UploadBucket, config.tempS3UploadPrefix, fileName);
	}

	String logSql(String sql) {
		config.ifLogVerbose(() -> logger.info("Executing query={}", sql));
		return sql;
	}

	void logUpdated(int rowsUpdated) {
		config.ifLogVerbose(() -> logger.info("Rows updated={}", rowsUpdated));
	}

	String columns(Schema schema, Optional<List<String>> columns) {
		Stream<String> stringStream = columns.map(StreamEx::of).orElseGet(() -> columnNames(schema));

		if(config.cdcEnabled) {
			stringStream = Stream.concat(stringStream, Stream.of(dbDialect().q(DebeziumConstants.NEXLA_OPERATION_DELETE)));
		}

		return stringStream.collect(joining(",", " (", ") "));
	}

	StreamEx<String> columnNames(Schema schema) {
		return StreamEx.of(schema.fields()).map(Field::name).map(col -> dbDialect().q(col));
	}

	String qualifiedName(String table) {
		return dbDialect().getQualifiedTableName(table, config.authConfig.schemaName, JdbcUtils.getDatabaseName(config));
	}

	@Override
	public void writeToBuffer(List<NexlaMessageContext> records, Map<Long, Integer> streamSizeByRunId) {
		if (localBuffers.isEmpty() || localBuffers.get(localBuffers.size() - 1).streamSize() >= config.dwhBatchSize) {

			if (config.cdcEnabled) {
				localBuffers.add(new CdcCopyOperationLocalBuffer(this, config));
			} else {
				localBuffers.add(new CopyOperationLocalBuffer(config));
			}
		}
		CopyOperationLocalBuffer writer = localBuffers.get(localBuffers.size() - 1);
		writer.write(records, records, streamSizeByRunId);
	}

	@Override
	public List<CopyOperationLocalBuffer> getBuffers() {
		return Collections.unmodifiableList(localBuffers);
	}
}
