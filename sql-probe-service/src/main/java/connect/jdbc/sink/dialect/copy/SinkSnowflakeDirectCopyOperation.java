package connect.jdbc.sink.dialect.copy;

import com.google.common.annotations.VisibleForTesting;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connector.config.MappingConfig;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.SnowflakeSqlDialect;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Stream;

import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Compression.GZIP;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Format.CSV_FORMAT;
import static connect.jdbc.sink.dialect.copy.CopyOperationHelper.createCommandSnowflake;
import static java.util.stream.Collectors.joining;

/**
 * @see <a https://docs.snowflake.com/en/user-guide/script-data-load-transform-json.html">COPY docs</a>
 */
public class SinkSnowflakeDirectCopyOperation extends BaseSinkCopyOperation {

    private static String FORMAT_NAME = "nexla_upload_";
    private static String STAGE_NAME = "nexla_upload_stage_";
    public static final SnowflakeSqlDialect DIALECT = new SnowflakeSqlDialect();

    String filePath;

    @SneakyThrows
    private void updateRecordMetric(String blobLocation,
                                    RecordMetric replicateMetric,
                                    Statement statement) {

        String query = CopyOperationHelper.uploadResultSql(blobLocation, config.table, true);

        ResultSet rs = statement.executeQuery(query);
        if (rs.next()) {
            int rowCount = rs.getInt("ROW_COUNT");
            int errorCount = rs.getInt("ERROR_COUNT");
            String errMsg = rs.getString("FIRST_ERROR_MESSAGE");
            String errColName = rs.getString("FIRST_ERROR_COL_NAME");

            replicateMetric.sentRecordsTotal.set(rowCount);
            replicateMetric.errorRecords.set(errorCount);

            if(StringUtils.isNotBlank(errMsg) && StringUtils.isNotBlank(errColName)) {
                replicateMetric.quarantineMessages.add(RecordMetric.quarantineMessage(new NexlaMessage(new LinkedHashMap<>(), new NexlaMetaData()),
                        String.format("error message='%s', error column='%s'", errMsg, errColName)));
            }

        }
        rs.close();
    }

    @Override
    @SneakyThrows
    protected void executeUpsert(Statement st, File localFile, String tempTable, Optional<ReplicationContext> replicationContext) {
        String filePath = tempFileLocation(localFile.getName());
        executeCopyCommand(st, filePath, tempTable, replicationContext, false);
    }

    @Override
    @SneakyThrows
    protected void executeCopyCommand(String blobLocation,
                                      Connection connection,
                                      Optional<ReplicationContext> replicationContext) {
        try (Statement st = connection.createStatement()) {
            String stageLocation = executeCopyCommand(st, blobLocation, qualifiedName(config.table), replicationContext, true);
//            connection.commit();
            replicationContext.ifPresent(rc -> updateRecordMetric(stageLocation, rc.getReplicateMetric(), st));
        }
    }

    @SneakyThrows
    private String executeCopyCommand(Statement statement,
                                      String blobLocation,
                                      String table,
                                      Optional<ReplicationContext> replicationContext,
                                      boolean isInsert) {

        String stage = createStage(statement);
        String dest = putFileToStage(statement, UUID.randomUUID().toString(), blobLocation, stage);

        String[] pathParts = blobLocation.split("/");
        String fileName = pathParts[pathParts.length - 1];
        String stageLocation = dest + "/" + fileName.replace(".gz", "") + ".gz";

        String sql = copyCommand(schema, stageLocation, table, replicationContext);
        if (isInsert) {
            statement.execute(logSql(sql));
        } else {
            logUpdated(executeUpdate(statement, logSql(sql)));
        }

        String removeSql = "remove @" + stageLocation;
        statement.execute(logSql(removeSql));

        //remove stage name to get stats
        return stageLocation.replace(stage, "");
    }

    private String createStage(Statement statement) throws SQLException {

        String fileFormatName = fileFormat.toString().toLowerCase();
        String uploadFormatName = FORMAT_NAME + fileFormatName;

        String sql = CopyOperationHelper.createFileFormat(fileFormat, uploadFormatName);

        logSql(sql);
        statement.execute(sql);

        String stageName = STAGE_NAME + fileFormatName;
        String sqlStage = CopyOperationHelper.createStage(stageName, uploadFormatName);

        logSql(sqlStage);
        statement.execute(sqlStage);

        return stageName;
    }

    private String putFileToStage(Statement statement,
                                  String uuid,
                                  String location,
                                  String stage) throws SQLException {
        String dest = stage + "/" + uuid;
        String putSql = String.format("put file:///%s @%s;", location, dest);
        logSql(putSql);
        statement.execute(putSql);
        return dest;
    }

    @VisibleForTesting
    public String copyCommand(Schema schema,
                              String stage,
                              String table,
                              Optional<ReplicationContext> replicationContext) {
        String onError = config.stopOnError ? "" : " ON_ERROR = 'CONTINUE'";

        if (fileFormat.format.equals(CSV_FORMAT)) {
            Optional<List<String>> columnsProvided = replicationContext.flatMap(ReplicationContext::getColumns);
            String columns = columns(schema, columnsProvided);
            String compression = fileFormat.compression == GZIP ? "COMPRESSION = gzip" : "";
            String skipHeaders = config.skipHeader ? " SKIP_HEADER = 1 " : "";
            return "COPY INTO " + (table + columns) + " FROM '@" + stage + "'" +
                    "   FILE_FORMAT = (TYPE = csv FIELD_DELIMITER = '" + fileFormat.delimiter + "' FIELD_OPTIONALLY_ENCLOSED_BY = '\"' ESCAPE = '\\\\' " + skipHeaders + compression + ") " + onError;
        } else {
            Tuple2<String, String> columns = columnsTuple();
            String dbColumns = columns.getT1();
            String jsonColumns = columns.getT2();
            return "COPY INTO " + (table + dbColumns) + " FROM (SELECT " + jsonColumns + " FROM @" + stage + " t) " + onError;
        }
    }

    private Tuple2<String, String> columnsTuple() {
        Optional<MappingConfig> mappingConfig = config.mappingConfig;

        // use Supplier for reusing Stream
        Supplier<Stream<LinkedHashMap<String, Map<String, String>>>> stream = () -> mappingConfig.map(MappingConfig::getMapping)
                .map(StreamEx::of)
                .orElse(StreamEx.empty());

        String dbColumns = stream.get().map(LinkedHashMap::values)
                .flatMap(c -> c.stream()
                        .map(Map::keySet))
                .flatMap(StreamEx::of)
                .map(col -> dbDialect().q(col))
                .collect(joining(",", " (", ") "));

        String jsonColumns = stream.get()
                .map(LinkedHashMap::keySet)
                .flatMap(StreamEx::of)
                .map(col -> "parse_json($1):" + dbDialect().q(col))
                .collect(joining(","));

        return Tuples.of(dbColumns, jsonColumns);
    }

    @Override
    public DbDialect dbDialect() {
        return DIALECT;
    }

    @Override
    protected void initCloudObjectStoreClient() {

    }

    @Override
    protected void deleteCloudFile(File localFile) {

    }

    @Override
    @SneakyThrows
    public void uploadFile(File localFile) {
        filePath = localFile.getAbsolutePath();
    }

    @Override
    String tempFileLocation(String fileName) {
        return filePath;
    }

    @Override
    public void close(Supplier<Connection> connectionProvider) {
    }

    @Override
    protected String getTempFilePrefix() {
        return "direct_";
    }

    protected String createCommand(String tempTable, String qualifiedTableName) {
        return createCommandSnowflake(tempTable, qualifiedTableName);
    }

}
