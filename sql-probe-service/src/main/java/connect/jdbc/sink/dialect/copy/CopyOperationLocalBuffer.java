package connect.jdbc.sink.dialect.copy;

import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.StreamUtils;
import com.nexla.common.sink.TopicPartition;
import com.nexla.common.tracker.Tracker;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.SinkConnectorConfig;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileWriter;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class CopyOperationLocalBuffer {
	private static final Logger logger = LoggerFactory.getLogger(CopyOperationLocalBuffer.class);

	private File file;
	private FileWriter writer;
	private int streamSize = 0;
	private Map<Long, Integer> streamSizeByRunId = new HashMap<>();
	private boolean closed = false;
	private Optional<Tracker> maybeEmptyTracker = Optional.empty();
	private Map<TopicPartition, Long> offsets = Maps.newConcurrentMap();

	@SneakyThrows
	public CopyOperationLocalBuffer() {
		this.file = File.createTempFile("jdbc", "sink");
		this.writer = new FileWriter(file);
	}

	@SneakyThrows
	public CopyOperationLocalBuffer(SinkConnectorConfig cfg) {
		this();
		if (cfg != null) {
			cfg.fixedTrackerValue.ifPresent(pattern -> {
				// "u" + sourceId + ":dataset1:1:dataset2:dataset3:dataset4;" + sinkId + ":1:" + sinkId
				Tracker justSourceAndSinkId = Tracker.parse(pattern);
				this.maybeEmptyTracker = Optional.of(justSourceAndSinkId);
			});
		}
	}

	public Map<Long, Integer> streamSizeByRunId() { return streamSizeByRunId; }

	public int streamSize() {
		return streamSize;
	}

	@SneakyThrows
	public void write(List<NexlaMessageContext> recordsToWrite, List<NexlaMessageContext> recordsToOffset,
										Map<Long, Integer> streamSizeByRunId) {
		recordsToWrite.forEach(x -> writeNexlaMessage(x.mapped));
		recordsToOffset.forEach(x -> offsets.compute(x.topicPartition, (tp, off) -> {
			if (off == null || x.kafkaOffset > off) {
				return x.kafkaOffset;
			} else {
				return off;
			}
		}));
		writer.flush();
		streamSizeByRunId.forEach((runId, streamSize) -> {
			this.streamSizeByRunId.merge(runId, streamSize, Integer::sum);
			this.streamSize += streamSize;
		});

	}

	@SneakyThrows
	private void writeNexlaMessage(NexlaMessage m) {
		if (maybeEmptyTracker.isPresent()) {
			NexlaMessage msgNoTrackerId = new NexlaMessage();
			msgNoTrackerId.setRawMessage(m.getRawMessage());
			NexlaMetaData noTrackerMetadata = m.getNexlaMetaData();
			noTrackerMetadata.setTrackerId(maybeEmptyTracker.get());
			msgNoTrackerId.setNexlaMetaData(noTrackerMetadata);
			writer.write(StreamUtils.jsonUtil().toJsonString(msgNoTrackerId) + "\n");
		} else {
			writer.write(StreamUtils.jsonUtil().toJsonString(m) + "\n");
		}
	}

	public boolean isClosed() {
		return closed;
	}

	@SneakyThrows
	public void close() {
		this.writer.close();
		this.file.delete();
		this.closed = true;
	}

	// TODO Consider to remove this method
	@SneakyThrows
	public void reset() {
		closed = false;
		streamSize = 0;
		offsets.clear();
		writer.close();
		file.delete();
		this.file = File.createTempFile("jdbc", "sink");
		this.writer = new FileWriter(file);
	}

	@SneakyThrows
	public StreamEx<NexlaMessage> getRecords() {
		if (isClosed() || !Files.exists(file.toPath())) {
			logger.error("Trying to get records from closed CopyOperationLocalBuffer: {}", file.toPath());
			return StreamEx.empty();
		}
		return StreamEx.of(StreamUtils.autoClosedStream(Files.lines(file.toPath())))
					   .map(str -> StreamUtils.jsonUtil().stringToType(str, NexlaMessage.class));
	}

	public Map<TopicPartition, Long> getOffsets() {
		return offsets;
	}
}
