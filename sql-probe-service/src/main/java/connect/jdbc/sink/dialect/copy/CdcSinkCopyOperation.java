package connect.jdbc.sink.dialect.copy;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import com.nexla.probe.sql.SchemaUtils;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;

import java.io.File;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.function.Supplier;

public class CdcSinkCopyOperation implements SinkCopyOperation {

    private Map<String, SinkCopyOperation> copyOperationStore = new HashMap<>();

    private final java.util.function.Supplier<Connection> connProvider;
    private final DbDialect dbDialect;
    private final WarehouseCopyFileFormat fileFormat;
    private final NexlaLogger logger;

    public CdcSinkCopyOperation(Supplier<Connection> connProvider, DbDialect dbDialect, WarehouseCopyFileFormat fileFormat, NexlaLogger logger) {
        this.connProvider = connProvider;
        this.dbDialect = dbDialect;
        this.fileFormat = fileFormat;
        this.logger = logger;
    }

    @Override
    public WarehouseCopyFileFormat getFileFormat() {
        return fileFormat;
    }

    @Override
    public SinkCopyOperation init(Schema schema, JdbcSinkConnectorConfig config, AdminApiClient adminApiClient, WarehouseCopyFileFormat fileFormat, NexlaLogger logger) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void writeToBuffer(List<NexlaMessageContext> records, Map<Long, Integer> streamSizeByRunId) {
        throw new UnsupportedOperationException();
    }

    public void invalidate() {
        copyOperationStore = new HashMap<>();
    }

    public void writeToBuffer(JdbcSinkConnectorConfig enriched,
                              AdminApiClient adminApiClient,
                              List<NexlaMessageContext> dataStream,
                              Map<Long, Integer> streamSizeByRunId) {
        String key = enriched.sinkId + "_" + enriched.table;
        SinkCopyOperation copyOperation = copyOperationStore.get(key);
        if (copyOperation == null) {
            logger.debug("CDC: SinkCopyOperation is empty, creating new one for the key - {}", key);
            Schema schema = SchemaUtils.selectSchema(this.connProvider, this.dbDialect, enriched);
            copyOperation = dbDialect
                    .newSinkCopyOperation(enriched)
                    .init(schema, enriched, adminApiClient, fileFormat, logger);
        }

        copyOperation.writeToBuffer(dataStream, streamSizeByRunId);
        copyOperationStore.put(key, copyOperation);
    }

    @Override
    public Map<Long, RecordMetric> flushBufferByRunId(CopyOperationLocalBuffer buffer, Supplier<Connection> connectionProvider, JdbcSinkConnectorConfig.InsertMode insertMode) {
        return ((CdcCopyOperationLocalBuffer) buffer).getLocalCopyOperation().flushBufferByRunId(buffer, connectionProvider, insertMode);
    }

    @Override
    public RecordMetric flushBuffer(CopyOperationLocalBuffer buffer, Supplier<Connection> connectionProvider, JdbcSinkConnectorConfig.InsertMode insertMode) {
        return ((CdcCopyOperationLocalBuffer) buffer).getLocalCopyOperation().flushBuffer(buffer, connectionProvider, insertMode);
    }

    @Override
    public void uploadFile(File localFile) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void uploadFiles(Supplier<Connection> connectionProvider, JdbcSinkConnectorConfig.InsertMode insertMode, Consumer<Connection> manageCopyErrors, File localFile, Optional<ReplicationContext> replicationContext) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void createNewBuffers() {
        for (SinkCopyOperation sink : copyOperationStore.values()) {
            sink.createNewBuffers();
        }
    }

    @Override
    public List<CopyOperationLocalBuffer> getBuffers() {
        List<CopyOperationLocalBuffer> buffers = new ArrayList<>();
        for (SinkCopyOperation sink : copyOperationStore.values()) {
            buffers.addAll(sink.getBuffers());
        }
        return buffers;
    }

    @Override
    public int onUnprocessedBuffers(Consumer<NexlaMessage> consumer) {
        int streamSize = 0;
        for (SinkCopyOperation sink : copyOperationStore.values()) {
            streamSize += sink.onUnprocessedBuffers(consumer);
        }

        return streamSize;
    }

    @Override
    public void close(Supplier<Connection> connectionProvider) {
        for (SinkCopyOperation sink : copyOperationStore.values()) {
            sink.close(connectionProvider);
        }
    }

    @Override
    public DbDialect dbDialect() {
        return dbDialect;
    }
}
