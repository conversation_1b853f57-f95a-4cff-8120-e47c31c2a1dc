package connect.jdbc.sink.dialect.copy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.exception.NexlaErrorMessage;
import com.nexla.common.exception.NexlaQuarantineMessage;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import connect.data.Field;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.OracleAutonomousDialect;
import connect.jdbc.sink.dialect.copy.exception.OracleAutonomousErrorMessage;
import connect.jdbc.sink.dialect.copy.exception.OracleAutonomousUtilityEntitiesToNexlaMessageMapper;
import connect.jdbc.sink.dialect.copy.exception.OracleAutonomousUtilityTablesService;
import connect.jdbc.sink.dialect.copy.exception.OracleAutonomousWholeBatchFailedException;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.javatuples.Pair;
import org.springframework.util.CollectionUtils;

import java.nio.file.Paths;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.EnumSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;

import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Compression.GZIP;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Format.CSV_FORMAT;
import static com.nexla.connector.config.jdbc.WarehouseCopyFileFormat.Format.JSON_FORMAT;
import static connect.jdbc.sink.dialect.copy.SourceOracleAutonomousCopyOperation.CREDENTIALS_ALREADY_EXISTS_ERROR_CODE;
import static java.lang.String.format;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.StringUtils.isEmpty;

public class SinkOracleAutonomousCopyOperation extends SinkS3CopyOperation {

	private static final EnumSet<WarehouseCopyFileFormat.Format> SELF_DESCRIBING_FORMATS = EnumSet.of(JSON_FORMAT);
	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

	public static final OracleAutonomousDialect DIALECT = new OracleAutonomousDialect();

	private final OracleAutonomousUtilityTablesService utilityTablesService = new OracleAutonomousUtilityTablesService();
	private final OracleAutonomousUtilityEntitiesToNexlaMessageMapper bodiesToNexlaMessageMapper
			= new OracleAutonomousUtilityEntitiesToNexlaMessageMapper();

	@Override
	@SneakyThrows
	protected void executeCopyCommand(String blobLocation,
	                                  Connection connection,
	                                  Optional<ReplicationContext> replicationContext) {
		String sql = copyCommand(schema, blobLocation, DIALECT.q(config.table), replicationContext);

		try (CallableStatement createCredentials = connection.prepareCall(logSql(createCredentials(config.s3AuthConfig)));
		     CallableStatement copyData = connection.prepareCall(logSql(sql))) {

			try {
				createCredentials.execute();
			} catch (SQLException e) {
				if (e.getErrorCode() == 20022) {
					// credentials already exists, do nothing.
				} else {
					throw e;
				}
			}
			try {
				copyData.execute();
			} catch (SQLException e) {
				if (e.getErrorCode() == 20003) {
					logger.info("rejectLimit: {} reached for the query: {}.", config.rejectLimit, sql);
					// config.rejectLimit reached. Ignoring this since exception will be handled later in manageCopyErrors
				} else {
					throw e;
				}
			}
			replicationContext.ifPresent(rc -> updateReplicateMetric(blobLocation, rc, connection));

			connection.commit();
		}
	}

	/**
	 * In case if rejectLimit not reached, Oracle will silently insert only valid messages, while non-valid ones will be
	 * discarded. This method retrieves OracleAutonomous' representations on such messages and maps them to NexlaMessage
	 * if possible, so then it will be possible to update RecordMetric afterwards.
	 *
	 * @param records All NexlaMessages used in the copy operation.
	 * @param recordMetric to update error and quarantine records.
	 * @param conn Connection to query Oracle utility tables.
	 * @param fileName tempFile name.
	 */
	@SneakyThrows
	@Override
	protected void manageCopyErrors(List<NexlaMessage> records,
									RecordMetric recordMetric,
									Connection conn,
									String fileName) {
		final String table = config.table;
		int recordNum = records.size();
		int rowsLoaded = utilityTablesService.rowsLoaded(conn, fileName, table, logger);
		if (recordNum == rowsLoaded) {
			return;
		}
		Optional<Pair<String, String>> logAndBadFileTablesOptional = utilityTablesService.queryLogAndBadFileTables(
				fileName, conn, logger, table);
		if (logAndBadFileTablesOptional.isEmpty()) {
			return;
		}

		List<String> badBodies = utilityTablesService.getFormattedBadRecords(conn,
				logAndBadFileTablesOptional.get().getValue1(), logger);
		if (badBodies.size() == 0) {
			return;
		}

		boolean rejectLimitReached = recordNum > 0 && rowsLoaded == 0 && (badBodies.size() > config.rejectLimit);
		List<OracleAutonomousErrorMessage> errorDetails = utilityTablesService.createOracleAutonomousErrorMessageList(
				fileName, conn, logger, table, config.oracleAutonomousObservableExceptions);
		if (CollectionUtils.isEmpty(errorDetails)) {
			logger.error(format("Unable to retrieve errorDetails. Bad records are: %s LogFile: %s, badFile: %s",
					badBodies, logAndBadFileTablesOptional.get().getValue0(),
					logAndBadFileTablesOptional.get().getValue1()));
			return;
		}

		if (rejectLimitReached) {
			String errorAsJson = OBJECT_MAPPER.writeValueAsString(errorDetails);
			String errorMessage = format("All %d records failed since reject.limit %d reached. \n" +
					"Errors that caused failure: %s", recordNum, config.rejectLimit, errorAsJson);
			logger.info("rejectLimit reached with exception: " + errorMessage);
			throw new OracleAutonomousWholeBatchFailedException(errorMessage);
		} else {
			logger.info(format("rejectLimit of %s not reached. Loaded %d out of %d. Updating quarantine records.",
					config.rejectLimit, rowsLoaded, recordNum));
			this.updateQuarantineRecords(records, recordMetric, badBodies, errorDetails);
		}
	}

	private void updateQuarantineRecords(
			List<NexlaMessage> records,
			RecordMetric recordMetric,
			List<String> badBodies,
			List<OracleAutonomousErrorMessage> errorDetails) {
		recordMetric.errorRecords.addAndGet(badBodies.size());
		final List<Pair<String, Optional<NexlaMessage>>> badBodyToNexlaMessageMapping
				= bodiesToNexlaMessageMapper.createBadBodyToNexlaMessageMapping(records, badBodies);
		final String badTable = errorDetails.get(0).getBadTable();
		final String logTable = errorDetails.get(0).getLogTable();

		for (Pair<String, Optional<NexlaMessage>> pair : badBodyToNexlaMessageMapping) {
			Optional<OracleAutonomousErrorMessage> errorMessage = errorDetails.stream()
					.filter(el -> el.getBodyInOracle().equals(pair.getValue0())).findAny();
			String error = bodiesToNexlaMessageMapper.mapToSingleError(errorMessage, badTable, logTable);
			if (pair.getValue1().isPresent()) {
				NexlaMessage message = pair.getValue1().get();

				NexlaQuarantineMessage quarantineMessage = new NexlaQuarantineMessage(
						message.getNexlaMetaData(),
						new NexlaErrorMessage(error, ""),
						message.getRawMessage(),
						nowUTC().getMillis());
				recordMetric.quarantineMessages.add(quarantineMessage);
			} else {
				recordMetric.quarantineMessages.add(RecordMetric.quarantineMessage(
						new NexlaMessage(
								new LinkedHashMap<>(),
								new NexlaMetaData()),
								error));
			}
		}
	}
	@SneakyThrows
	private void updateReplicateMetric(String blobLocation,
	                                   ReplicationContext replicationContext,
	                                   Connection connection) {
		try (Statement statement = connection.createStatement()) {
			ResultSet rs = statement.executeQuery("select * from user_load_operations\n" +
				"where file_uri_list = '" +  blobLocation + "'\n" +
				"and table_name = '" + config.table + "'\n" +
				"order by id desc");

			if (rs.next()) {
				int rowsCount = rs.getInt("ROWS_LOADED");
				replicationContext.getReplicateMetric().sentRecordsTotal.set(rowsCount);
			}
		}
	}

	@Override
	public DbDialect dbDialect() {
		return DIALECT;
	}

	@Override
	String tempFileLocation(String fileName) {
		return "https://" + Paths.get(config.tempS3UploadBucket + ".s3.amazonaws.com", config.tempS3UploadPrefix, fileName);
	}

	@VisibleForTesting
	public String copyCommand(Schema schema, String s3FileLocation, String table, Optional<ReplicationContext> replicationContext) {
		AWSAuthConfig s3AuthConfig = config.s3AuthConfig;
		String schemaName = config.authConfig.schemaName != null ? config.authConfig.schemaName : "";


		String delimiter = nonNull(config.intermediateFileDelimiter) ? config.intermediateFileDelimiter : fileFormat.delimiter;
		String quoteChar = nonNull(config.intermediateFileQuoteChar) ? config.intermediateFileQuoteChar : "''";
		String delimiterStr = fileFormat.format == CSV_FORMAT ? "'delimiter' value '" + delimiter + "', " : "";
		String compressionStr = fileFormat.compression == GZIP ? "'compression' value 'gzip', " : "";

		String fieldList = "";
		if (!SELF_DESCRIBING_FORMATS.contains(fileFormat.format)) {
			String columns = toColumnNames(schema, replicationContext)
					.joining(" CHAR(10000), ") + " CHAR(10000)";

			fieldList = String.format("field_list => '%s', ", columns);
		}

		String format = String.format("json_object(" +
						"'ignoremissingcolumns' value 'true', " +
						"'quote' value '%s', " +
						"'type' value '%s', " +
						"%s " +
						"%s " +
						"'rejectlimit' value %d, " +
						"'dateformat' value '%s', " +
						"'timestampformat' value '%s'," +
						"'escape' value 'true'" +
						")",
				quoteChar,
				fileFormat.format.name,
				delimiterStr,
				compressionStr,
				config.rejectLimit,
				config.oracleDateFormat,
				config.oracleTimestampFormat
		);

		if (fileFormat.format == JSON_FORMAT) {
			String columnPath = StreamEx.of(schema.fields()).map(Field::name)
					.map(s -> String.format("\"$.%s\"", s))
					.joining(",", "[", "]");

			format = String.format("json_object(" +
							"'type' value '%s', " +
							"%s" +
							"'rejectlimit' value %d, " +
							"'columnpath' value '%s'" +
							")",
					fileFormat.format.name,
					compressionStr,
					config.rejectLimit,
					columnPath
					);
		}

		return String.format("{ call DBMS_CLOUD.copy_data("
			+ "    table_name  => '%s', "
			+ "    schema_name => '%s', "
			+ "    credential_name => '%s', "
			+ "    file_uri_list => '%s', "
			+ "    %s" // fieldList
			+ "    format => %s"
			+ ")}",
			table, schemaName, s3AuthConfig.accessKeyId, s3FileLocation, fieldList, format);
	}

	private StreamEx<String> toColumnNames(Schema schema, Optional<ReplicationContext> replicationContext) {
		return replicationContext
				.flatMap(ReplicationContext::getColumns)
				.map(StreamEx::of)
				.orElseGet(() -> columnNames(schema));
	}

	@Override
	protected String createCommand(String tempTable, String qualifiedTableName) {
		return "CREATE GLOBAL TEMPORARY TABLE " + qualifiedName(makeStableTempTableName()) +
				" ON COMMIT PRESERVE ROWS AS SELECT * FROM " + qualifiedTableName + " WHERE rownum = -1";
	}

	@Override
	protected String insertCommand(String tempTable, String qualifiedTableName, String columns) {
		return "INSERT INTO " + qualifiedTableName + "(" + columns + ") " +
				"SELECT " + columns +
				" FROM " + withSchema(tempTable);
	}

	private String withSchema(String tempTable) {
		return isEmpty(config.authConfig.schemaName)
				? tempTable
				: dbDialect().q(config.authConfig.schemaName) + "." + tempTable;
	}

	@Override
	protected int executeUpdate(Statement st, String command) throws SQLException {
		boolean autoCommit = st.getConnection().getAutoCommit();
		if (!autoCommit) {
			st.getConnection().setAutoCommit(true);
		}

		try {
			return st.executeUpdate(command);
		} catch (SQLException e) {
			if (e.getErrorCode() == 20000 & e.getMessage() != null & e.getMessage().contains("KUP-04043")) {
				logger.error("Oracle Autonomous: table column not found in external source. Possible a new column was added to the target table that cause update failure.", e);
				// We should not ignore this error as manageCopyErrors will not handle it (bad, log table are empty)
				String reason = e.getMessage().split("\n")[0];
				throw new OracleAutonomousWholeBatchFailedException(String.format("The update failed because the target table has a column not present in the dataset: %s", reason));
			}
			if (e.getErrorCode() == 20003) {
				logger.error("rejectLimit: {} reached for the query: {}.", config.rejectLimit, st);
				// config.rejectLimit reached. Ignoring this since exception will be handled later in manageCopyErrors
			}
			return 0;
		} finally {
			st.getConnection().setAutoCommit(autoCommit);
		}
	}

	@SneakyThrows
	@Override
	public void dropTempTable(Statement stmt, String tempTable) {
		truncateTempTable(stmt, tempTable);
		super.dropTempTable(stmt, withSchema(tempTable));
	}

	@Override
	public void createTempTableOrTruncate(String tempTable, String qualifiedTableName, Statement st) throws SQLException {
		try {
			createCredentials(st);
			createTempTable(st, createCommand(tempTable, qualifiedTableName));
		} catch (Exception e) {
			logger.warn("M=createTempTableOrTruncate, exception creating temp table, error is = {}", e.getMessage());
			truncateTempTable(st, tempTable);
		}
	}

	private void truncateTempTable(Statement stmt, String tempTable) throws SQLException {
		executeUpdate(stmt, logSql("TRUNCATE TABLE " + withSchema(tempTable)));
		super.dropTempTable(stmt, withSchema(tempTable));
	}

	@SneakyThrows
	public void createCredentials(Statement statement) {
		String sql = logSql(createCredentials(config.s3AuthConfig));

		try {
			statement.execute(sql);
		} catch (SQLException e) {
			logger.error("M=createCredentials, errorCode={}, errorMessage={}", e.getErrorCode(), e.getMessage());
			if (e.getErrorCode() != CREDENTIALS_ALREADY_EXISTS_ERROR_CODE) {
				throw e;
			}
		}
	}

	public static String createCredentials(AWSAuthConfig s3AuthConfig) {
		return String.format("{ call DBMS_CLOUD.create_credential (credential_name => '%s',"
				+ " username => '%s',"
				+ " password => '%s')}", s3AuthConfig.accessKeyId, s3AuthConfig.accessKeyId, s3AuthConfig.secretKey);
	}
}
