package connect.jdbc.sink.dialect;

import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.common.collect.Maps;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaConstants;
import com.nexla.common.datatype.Datatype;
import com.nexla.connector.ConnectorService;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.properties.SqlConfigAccessor;
import com.nexla.common.schema.SchemaType;
import com.nexla.test.UnitTests;
import connect.jdbc.sink.dialect.type.RedshiftType;
import connect.jdbc.sink.dialect.type.SnowflakeType;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;

import static com.nexla.common.StreamUtils.map;

import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.CLUSTERING_COLUMNS;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.PARTITIONING_COLUMN;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.REDSHIFT_DIST_KEY;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.REDSHIFT_SORT_KEYS;
import static java.util.Collections.emptyMap;
import static org.junit.Assert.assertEquals;

@Category(UnitTests.class)
public class DbDialectTest {
	private static final Map<String, SchemaType> TYPE_BINDING = Maps.newLinkedHashMap();
	private static final Map<String, String> FORMAT_BINDING = Maps.newLinkedHashMap();
	private static final Map<String, String> DEFAULT_CONFIGS = Maps.newLinkedHashMap();
	private String table;
	private String schema;
	private List<String> keyColumns;
	private List<String> nonKeyColumns;

	@BeforeClass
	public static void onBeforeClass() {
		TYPE_BINDING.put("id", SchemaType.INTEGER);
		TYPE_BINDING.put("data_set_id", SchemaType.INTEGER);
		TYPE_BINDING.put("name", SchemaType.STRING);
		TYPE_BINDING.put("description", SchemaType.STRING);
		FORMAT_BINDING.put("description", Datatype.DATE.get());

		DEFAULT_CONFIGS.put(NexlaConstants.CREDENTIALS_TYPE, ConnectionType.MYSQL.name());
		DEFAULT_CONFIGS.put(NexlaConstants.SINK_ID, "1");
		DEFAULT_CONFIGS.put(ConnectorService.UNIT_TEST, "true");
		DEFAULT_CONFIGS.put(SqlConfigAccessor.INSERT_MODE, "insert");
	}

	@Before
	public void onBefore() {
		keyColumns = new ArrayList<>();
		nonKeyColumns = new ArrayList<>();
	}

	private Supplier<AutomaticBinding> automaticBinding(
		Map<String, SchemaType> typeBinding,
		Map<String, String> formatBinding
	) {
		return Suppliers.memoize(() -> new AutomaticBinding(typeBinding, formatBinding));
	}

	@Test
	public void getCreateSqlMysql() {
		DbDialect dbDialect = getDialect(false, ConnectionType.MYSQL);
		String qualifiedName = dbDialect.getQualifiedTableName(table, "", "");
		String createSql = dbDialect.getCreateSql(qualifiedName, keyColumns, nonKeyColumns, automaticBinding(TYPE_BINDING, FORMAT_BINDING), new MappingConfig(), new JdbcSinkConnectorConfig(DEFAULT_CONFIGS));
		String expectedQuery = "CREATE TABLE `table` (`id` INT,`data_set_id` INT,`name` VARCHAR(3000)," +
							   "`description` DATE,PRIMARY KEY(`id`,`data_set_id`))";
		assertEquals(expectedQuery, createSql);

		createSql = dbDialect.getCreateSql(qualifiedName, new ArrayList<>(), nonKeyColumns, automaticBinding(emptyMap(), emptyMap()), new MappingConfig(), new JdbcSinkConnectorConfig(DEFAULT_CONFIGS));
		expectedQuery = "CREATE TABLE `table` (`name` VARCHAR(3000),`description` VARCHAR(3000))";
		assertEquals(expectedQuery, createSql);
	}

	@Test
	public void getUpsertQueryMysql() {
		DbDialect dbDialect = getDialect(true, ConnectionType.MYSQL);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");
		String createSql = dbDialect.getUpsertQuery(qualifiedName, keyColumns, nonKeyColumns);
		String expectedQuery = "INSERT INTO `schema`.`table`(`id`,`data_set_id`,`name`,`description`) VALUES (?,?,?,?) " +
							   "ON DUPLICATE KEY UPDATE `name` = VALUES (`name`),`description` = VALUES (`description`)";
		assertEquals(expectedQuery, createSql);
	}

	@Test
	public void getCreateSqlPostgres() {
		DbDialect dbDialect = getDialect(true, ConnectionType.POSTGRES);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");
		String createSql = dbDialect.getCreateSql(qualifiedName, keyColumns, nonKeyColumns, automaticBinding(TYPE_BINDING, FORMAT_BINDING), new MappingConfig(), new JdbcSinkConnectorConfig(DEFAULT_CONFIGS));
		String expectedQuery = "CREATE TABLE \"schema\".\"table\" (\"id\" INT,\"data_set_id\" INT,\"name\" TEXT," +
							   "\"description\" DATE,PRIMARY KEY(\"id\",\"data_set_id\"))";
		assertEquals(expectedQuery, createSql);
	}

	@Test
	public void getUpsertQueryPostgres() {
		DbDialect dbDialect = getDialect(true, ConnectionType.POSTGRES);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");
		String createSql = dbDialect.getUpsertQuery(qualifiedName, keyColumns, nonKeyColumns);
		String expectedQuery = "INSERT INTO \"schema\".\"table\" (\"id\",\"data_set_id\",\"name\",\"description\")" +
							   " VALUES (?,?,?,?) ON CONFLICT (\"id\",\"data_set_id\") DO UPDATE SET \"name\"=EXCLUDED." +
							   "\"name\",\"description\"=EXCLUDED.\"description\"";
		assertEquals(expectedQuery, createSql);
	}

	@Test
	public void getCreateSqlSqlite() {
		DbDialect dbDialect = getDialect(true, ConnectionType.SQLITE);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");
		String createSql = dbDialect.getCreateSql(qualifiedName, keyColumns, nonKeyColumns, automaticBinding(TYPE_BINDING, FORMAT_BINDING), new MappingConfig(), new JdbcSinkConnectorConfig(DEFAULT_CONFIGS));
		String expectedQuery = "CREATE TABLE `schema`.`table` (`id` INTEGER,`data_set_id` INTEGER,`name` TEXT,`description`" +
							   " NUMERIC,PRIMARY KEY(`id`,`data_set_id`))";
		assertEquals(expectedQuery, createSql);
	}

	@Test
	public void getUpsertQuerySqlite() {
		DbDialect dbDialect = getDialect(true, ConnectionType.SQLITE);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");
		String createSql = dbDialect.getUpsertQuery(qualifiedName, keyColumns, nonKeyColumns);
		String expectedQuery = "INSERT OR REPLACE INTO `schema`.`table`(`id`,`data_set_id`,`name`,`description`) VALUES(?,?,?,?)";
		assertEquals(expectedQuery, createSql);
	}

	@Test
	public void getCreateSqlHana() {
		DbDialect dbDialect = getDialect(true, ConnectionType.HANA_JDBC);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");
		String createSql = dbDialect.getCreateSql(qualifiedName, keyColumns, nonKeyColumns, automaticBinding(TYPE_BINDING, FORMAT_BINDING), new MappingConfig(), new JdbcSinkConnectorConfig(DEFAULT_CONFIGS));
		String expectedQuery = "CREATE TABLE \"schema\".\"table\" (\"id\" INTEGER,\"data_set_id\" INTEGER," +
							   "\"name\" VARCHAR(1000),\"description\" DATE,PRIMARY KEY(\"id\",\"data_set_id\"))";
		assertEquals(expectedQuery, createSql);
	}

	@Test
	public void getUpsertQueryHana() {
		DbDialect dbDialect = getDialect(true, ConnectionType.HANA_JDBC);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");
		String createSql = dbDialect.getUpsertQuery(qualifiedName, keyColumns, nonKeyColumns);
		String expectedQuery = "UPSERT \"schema\".\"table\"(\"id\",\"data_set_id\",\"name\",\"description\") " +
							   "VALUES(?,?,?,?) WITH PRIMARY KEY";
		assertEquals(expectedQuery, createSql);
	}

	@Test
	public void getCreateSqlSnowflake() {
		DbDialect dbDialect = getDialect(true, ConnectionType.SNOWFLAKE);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");
		String createSql = dbDialect.getCreateSql(qualifiedName, keyColumns, nonKeyColumns, automaticBinding(TYPE_BINDING, FORMAT_BINDING), new MappingConfig(), new JdbcSinkConnectorConfig(DEFAULT_CONFIGS));
		String expectedQuery = "CREATE TABLE \"schema\".\"table\" (\"id\" BIGINT,\"data_set_id\" BIGINT,\"name\" TEXT," +
							   "\"description\" DATE,PRIMARY KEY(\"id\",\"data_set_id\"))";
		assertEquals(expectedQuery, createSql);
	}

	@Test
	public void getCreateSqlSnowflake_withClustering() {
		DbDialect dbDialect = getDialect(true, ConnectionType.SNOWFLAKE);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");
		DEFAULT_CONFIGS.put(CLUSTERING_COLUMNS, "name");

		String createSql = dbDialect.getCreateSql(qualifiedName, keyColumns, nonKeyColumns,
				automaticBinding(TYPE_BINDING, FORMAT_BINDING), new MappingConfig(),
				new JdbcSinkConnectorConfig(DEFAULT_CONFIGS));

		System.out.println(createSql);

		String expectedQuery = "CREATE TABLE \"schema\".\"table\" (\"id\" BIGINT,\"data_set_id\" BIGINT,\"name\" TEXT," +
				"\"description\" DATE,PRIMARY KEY(\"id\",\"data_set_id\")) CLUSTER BY (\"name\")";

		System.out.println(expectedQuery);

		assertEquals(expectedQuery, createSql);
		DEFAULT_CONFIGS.remove(CLUSTERING_COLUMNS);
	}

	@Test
	public void getCreateSqlSnowflake_withBindings() {
		DbDialect dbDialect = getDialect(true, ConnectionType.SNOWFLAKE);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");

		LinkedHashMap<String, Map<String, String>> map = Maps.newLinkedHashMap();
		Map<String, String> idMappingValue = map("id", SnowflakeType.INTEGER.toString());
		map.put("id", idMappingValue);

		Map<String, String> datasetIdMappingValue = map("data_set_id", SnowflakeType.BIGINT.toString());
		map.put("data_set_id", datasetIdMappingValue);

		Map<String, String> nameMappingValue = map("name", SnowflakeType.VARCHAR.toString());
		map.put("name", nameMappingValue);

		Map<String, String> descriptionMappingValue = map("description", SnowflakeType.STRING.toString());
		map.put("description", descriptionMappingValue);

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMapping(map);

		String createSql = dbDialect.getCreateSql(qualifiedName, keyColumns, nonKeyColumns, automaticBinding(TYPE_BINDING, FORMAT_BINDING), mappingConfig, new JdbcSinkConnectorConfig(DEFAULT_CONFIGS));
		String expectedQuery = "CREATE TABLE \"schema\".\"table\" (\"id\" INTEGER,\"data_set_id\" BIGINT,\"name\" VARCHAR," +
							   "\"description\" STRING,PRIMARY KEY(\"id\",\"data_set_id\"))";
		assertEquals(expectedQuery, createSql);
	}

	@Test
	public void getCreateSqlRedshift_withBindings() {
		DbDialect dbDialect = getDialect(true, ConnectionType.REDSHIFT);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");

		LinkedHashMap<String, Map<String, String>> map = Maps.newLinkedHashMap();
		Map<String, String> idMappingValue = map("id", RedshiftType.INTEGER.toString());
		map.put("id", idMappingValue);

		Map<String, String> datasetIdMappingValue = map("data_set_id", RedshiftType.BIGINT.toString());
		map.put("data_set_id", datasetIdMappingValue);

		Map<String, String> nameMappingValue = map("name", RedshiftType.VARCHAR.toString());
		map.put("name", nameMappingValue);

		Map<String, String> descriptionMappingValue = map("description",
				RedshiftType.CHARACTER_VARYING.toString());
		map.put("description", descriptionMappingValue);

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMapping(map);

		String createSql = dbDialect.getCreateSql(
				qualifiedName,
				keyColumns,
				nonKeyColumns,
				automaticBinding(TYPE_BINDING, FORMAT_BINDING),
				mappingConfig,
				new JdbcSinkConnectorConfig(DEFAULT_CONFIGS));
		String expectedQuery = "CREATE TABLE \"schema\".\"table\" (\"id\" INTEGER,\"data_set_id\" BIGINT,\"name\" VARCHAR," +
							   "\"description\" CHARACTER_VARYING,PRIMARY KEY(\"id\",\"data_set_id\"))";
		assertEquals(expectedQuery, createSql);
	}

	@Test
	public void getCreateSqlRedshift_withDataKeyAndSortKeys() {
		DbDialect dbDialect = getDialect(true, ConnectionType.REDSHIFT);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");

		LinkedHashMap<String, Map<String, String>> map = Maps.newLinkedHashMap();
		Map<String, String> idMappingValue = map("id", RedshiftType.INTEGER.toString());
		map.put("id", idMappingValue);

		Map<String, String> datasetIdMappingValue = map("data_set_id", RedshiftType.BIGINT.toString());
		map.put("data_set_id", datasetIdMappingValue);

		Map<String, String> nameMappingValue = map("name", RedshiftType.VARCHAR.toString());
		map.put("name", nameMappingValue);

		Map<String, String> descriptionMappingValue = map("description",
				RedshiftType.CHARACTER_VARYING.toString());
		map.put("description", descriptionMappingValue);

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMapping(map);

		DEFAULT_CONFIGS.put(REDSHIFT_DIST_KEY, "name");
		DEFAULT_CONFIGS.put(REDSHIFT_SORT_KEYS, "id,name");

		String createSql = dbDialect.getCreateSql(
				qualifiedName,
				keyColumns,
				nonKeyColumns,
				automaticBinding(TYPE_BINDING, FORMAT_BINDING),
				mappingConfig,
				new JdbcSinkConnectorConfig(DEFAULT_CONFIGS));
		String expectedQuery = "CREATE TABLE \"schema\".\"table\" (\"id\" INTEGER,\"data_set_id\" BIGINT,\"name\" VARCHAR,\"description\" CHARACTER_VARYING,PRIMARY KEY(\"id\",\"data_set_id\")) DISTKEY(\"name\") SORTKEY(\"id\",\"name\")";
		assertEquals(expectedQuery, createSql);
		DEFAULT_CONFIGS.remove(REDSHIFT_DIST_KEY);
		DEFAULT_CONFIGS.remove(REDSHIFT_SORT_KEYS);
	}

	@Test
	public void getCreateSqlRedshift_withClusteringColumnAndPartitioningKey() {
		Map<String, String> configs = Maps.newHashMap(DEFAULT_CONFIGS);
		configs.put(PARTITIONING_COLUMN, "name");
		configs.put(CLUSTERING_COLUMNS, "id,name");

		DbDialect dbDialect = getDialect(true, ConnectionType.REDSHIFT);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");

		LinkedHashMap<String, Map<String, String>> map = Maps.newLinkedHashMap();
		Map<String, String> idMappingValue = map("id", RedshiftType.INTEGER.toString());
		map.put("id", idMappingValue);

		Map<String, String> datasetIdMappingValue = map("data_set_id", RedshiftType.BIGINT.toString());
		map.put("data_set_id", datasetIdMappingValue);

		Map<String, String> nameMappingValue = map("name", RedshiftType.VARCHAR.toString());
		map.put("name", nameMappingValue);

		Map<String, String> descriptionMappingValue = map("description",
				RedshiftType.CHARACTER_VARYING.toString());
		map.put("description", descriptionMappingValue);

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMapping(map);

		String createSql = dbDialect.getCreateSql(
				qualifiedName,
				keyColumns,
				nonKeyColumns,
				automaticBinding(TYPE_BINDING, FORMAT_BINDING),
				mappingConfig,
				new JdbcSinkConnectorConfig(configs));

		String expectedQuery = "CREATE TABLE \"schema\".\"table\" (\"id\" INTEGER,\"data_set_id\" BIGINT,\"name\" VARCHAR,\"description\" CHARACTER_VARYING,PRIMARY KEY(\"id\",\"data_set_id\")) DISTKEY(\"name\") SORTKEY(\"id\",\"name\")";
		assertEquals(expectedQuery, createSql);
	}

	@Test
	public void getUpsertQueryTeradata() {
		DbDialect dbDialect = getDialect(true, ConnectionType.TERADATA);
		String qualifiedName = dbDialect.getQualifiedTableName(table, schema, "");
		String upsertSql = dbDialect.getUpsertQuery(qualifiedName, keyColumns, nonKeyColumns);
		String expectedQuery = "UPDATE \"schema\".\"table\" SET \"name\"=?, \"description\"=? WHERE (\"id\"=? AND \"data_set_id\"=?) ELSE INSERT INTO \"schema\".\"table\" (?,?,?,?)";
		assertEquals(expectedQuery, upsertSql);
	}

	@Test
	public void getDatabricksSparkDialect() {
		Map<String, String> dbricksMinimalConfig = Map.of(
				"url", "**************************************************************************************************************************************;",
				"credentials_type", "databricks",
				"databricks.cloud.type", "databricks");
		JdbcAuthConfig config = new JdbcAuthConfig(dbricksMinimalConfig, -1);
		DbDialect dbDialect = DialectRegistry.getInstance().fromConnectionString(config);
		Assertions.assertInstanceOf(DatabricksSparkDialect.class, dbDialect);
	}

	@Test
	public void getDatabricksModernDialect() {
		Map<String, String> dbricksMinimalConfig = Map.of(
				"url", "*******************************************************************************************************************************************;",
				"credentials_type", "databricks",
				"databricks.cloud.type", "databricks");
		JdbcAuthConfig config = new JdbcAuthConfig(dbricksMinimalConfig, -1);
		DbDialect dbDialect = DialectRegistry.getInstance().fromConnectionString(config);
		Assertions.assertInstanceOf(DatabricksDialect.class, dbDialect);
	}

	private DbDialect getDialect(boolean schemaSet, ConnectionType connectionType) {
		table = "table";
		schema = schemaSet ? "schema" : "";
		keyColumns.add("id");
		keyColumns.add("data_set_id");
		nonKeyColumns.add("name");
		nonKeyColumns.add("description");
		return DialectRegistry.getInstance().fromConnectionType(connectionType);
	}
}