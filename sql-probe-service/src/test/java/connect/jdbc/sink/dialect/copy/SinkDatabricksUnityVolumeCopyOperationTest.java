package connect.jdbc.sink.dialect.copy;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.common.ConnectionType;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.connector.config.databricks.DatabricksCloudType;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import com.nexla.test.UnitTests;
import connect.data.Schema;
import connect.data.SchemaBuilder;
import connect.jdbc.sink.dialect.DatabricksDialect;
import org.junit.Before;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.NexlaConstants.*;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.*;
import static com.nexla.connector.properties.SqlConfigAccessor.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Unit test for SinkDatabricksUnityVolumeCopyOperation
 * Tests the core business logic and critical functionality
 */
@Category(UnitTests.class)
public class SinkDatabricksUnityVolumeCopyOperationTest {

    private static final String TEST_CATALOG = "test_catalog";
    private static final String TEST_SCHEMA = "test_schema";
    private static final String TEST_TABLE = "test_table";
    private static final String TEST_VOLUME_PATH = "/Volumes/test_catalog/test_schema/test_table/test_file.json";
    private static final String TEST_QUALIFIED_TABLE = "`test_catalog`.`test_schema`.`test_table`";

    private SinkDatabricksUnityVolumeCopyOperation copyOperation;
    private Schema schema;
    private NexlaLogger logger;

    @Before
    public void setUp() {
        copyOperation = new SinkDatabricksUnityVolumeCopyOperation();
        schema = createTestSchema();
        logger = new NexlaLogger(LoggerFactory.getLogger(this.getClass()));
    }

    /**
     * Test 1: Test WorkspaceClient creation with different JDBC URL patterns
     * This tests the core authentication logic for both Databricks and Spark JDBC URLs
     */
    @Test
    public void testWorkspaceClientCreationWithDifferentJdbcUrls() throws SQLException {
        // Test Databricks JDBC URL with M2M authentication
        Connection databricksConnection = mock(Connection.class);
        DatabaseMetaData databricksMetaData = mock(DatabaseMetaData.class);
        when(databricksConnection.getMetaData()).thenReturn(databricksMetaData);
        when(databricksMetaData.getURL()).thenReturn("*****************************************************************");

        Map<String, String> databricksConfig = getJdbcConfigWithAuth();
        databricksConfig.put("database.field.OAuth2ClientId", "test-client-id");
        databricksConfig.put("database.field.OAuth2Secret", "test-client-secret");
        
        JdbcSinkConnectorConfig config = new JdbcSinkConnectorConfig(databricksConfig);
        copyOperation.init(schema, config, mock(AdminApiClient.class), WarehouseCopyFileFormat.JSON, logger);

        // Test Spark JDBC URL with PAT authentication
        Connection sparkConnection = mock(Connection.class);
        DatabaseMetaData sparkMetaData = mock(DatabaseMetaData.class);
        when(sparkConnection.getMetaData()).thenReturn(sparkMetaData);
        when(sparkMetaData.getURL()).thenReturn("******************************************************************************");

        Map<String, String> sparkConfig = getJdbcConfig();
        sparkConfig.put(URL, "******************************************************************************");
        
        JdbcSinkConnectorConfig sparkJdbcConfig = new JdbcSinkConnectorConfig(sparkConfig);
        copyOperation.init(schema, sparkJdbcConfig, mock(AdminApiClient.class), WarehouseCopyFileFormat.JSON, logger);

        // Test invalid JDBC URL
        Connection invalidConnection = mock(Connection.class);
        DatabaseMetaData invalidMetaData = mock(DatabaseMetaData.class);
        when(invalidConnection.getMetaData()).thenReturn(invalidMetaData);
        when(invalidMetaData.getURL()).thenReturn("********************************");

        // This should throw an exception for unsupported JDBC URL
        try {
            java.lang.reflect.Method method = SinkDatabricksUnityVolumeCopyOperation.class
                .getDeclaredMethod("createWorkspaceClient", Connection.class);
            method.setAccessible(true);
            method.invoke(copyOperation, invalidConnection);
            fail("Expected RuntimeException for unsupported JDBC URL");
        } catch (Exception e) {
            // Expected - should throw exception for unsupported URL
            assertTrue("Should throw RuntimeException for unsupported JDBC URL", 
                e.getCause() instanceof RuntimeException || e instanceof RuntimeException);
        }
    }

    /**
     * Test 2: Test MERGE command generation with different primary key scenarios
     * This tests the core business logic for generating SQL MERGE commands
     */
    @Test
    public void testMergeCommandGenerationLogic() {
        // Test 1: MERGE with single primary key
        Map<String, String> configWithSinglePK = getJdbcConfig();
        configWithSinglePK.put(PRIMARY_KEY, "id");
        
        JdbcSinkConnectorConfig singlePKConfig = new JdbcSinkConnectorConfig(configWithSinglePK);
        copyOperation.init(schema, singlePKConfig, mock(AdminApiClient.class), WarehouseCopyFileFormat.JSON, logger);

        String singlePKMerge = copyOperation.copyCommand(schema, TEST_VOLUME_PATH, TEST_TABLE, Optional.empty());
        
        assertTrue("Should contain qualified table name", 
            singlePKMerge.contains("MERGE INTO `test_catalog`.`test_schema`.`test_table` target"));
        assertTrue("Should reference JSON file in volume", 
            singlePKMerge.contains("USING (SELECT * FROM json.`" + TEST_VOLUME_PATH + "`) source"));
        assertTrue("Should have primary key condition", 
            singlePKMerge.contains("ON target.`id` = source.`id`"));
        assertTrue("Should have UPDATE clause", 
            singlePKMerge.contains("WHEN MATCHED THEN UPDATE SET *"));
        assertTrue("Should have INSERT clause", 
            singlePKMerge.contains("WHEN NOT MATCHED THEN INSERT *"));

        // Test 2: MERGE with multiple primary keys
        Map<String, String> configWithMultiplePK = getJdbcConfig();
        configWithMultiplePK.put(PRIMARY_KEY, "id,name");
        
        JdbcSinkConnectorConfig multiplePKConfig = new JdbcSinkConnectorConfig(configWithMultiplePK);
        copyOperation.init(schema, multiplePKConfig, mock(AdminApiClient.class), WarehouseCopyFileFormat.JSON, logger);

        String multiplePKMerge = copyOperation.copyCommand(schema, TEST_VOLUME_PATH, TEST_TABLE, Optional.empty());
        
        assertTrue("Should contain first primary key condition", 
            multiplePKMerge.contains("target.`id` = source.`id`"));
        assertTrue("Should contain second primary key condition", 
            multiplePKMerge.contains("target.`name` = source.`name`"));
        assertTrue("Should contain AND operator between conditions", 
            multiplePKMerge.contains(" AND "));

        // Test 3: MERGE without primary keys (append-only)
        Map<String, String> configWithoutPK = getJdbcConfig();
        // Don't set PRIMARY_KEY
        
        JdbcSinkConnectorConfig noPKConfig = new JdbcSinkConnectorConfig(configWithoutPK);
        copyOperation.init(schema, noPKConfig, mock(AdminApiClient.class), WarehouseCopyFileFormat.JSON, logger);

        String appendOnlyMerge = copyOperation.copyCommand(schema, TEST_VOLUME_PATH, TEST_TABLE, Optional.empty());
        
        assertTrue("Should use ON FALSE for append-only", 
            appendOnlyMerge.contains("ON FALSE"));
        assertFalse("Should not have UPDATE clause", 
            appendOnlyMerge.contains("WHEN MATCHED THEN UPDATE SET *"));
        assertTrue("Should have INSERT clause", 
            appendOnlyMerge.contains("WHEN NOT MATCHED THEN INSERT *"));
    }

    /**
     * Test 3: Test Unity Volume path construction and validation
     * This tests the critical path generation logic and configuration validation
     */
    @Test
    public void testUnityVolumePathConstructionAndValidation() {
        // Test 1: Valid configuration - should generate correct path
        initializeCopyOperation();
        
        String jsonPath = copyOperation.tempFileLocation("data.json");
        assertEquals("Should generate correct Unity Volume path for JSON file",
            "/Volumes/test_catalog/test_schema/test_table/data.json", jsonPath);
        
        String csvPath = copyOperation.tempFileLocation("data.csv");
        assertEquals("Should append .json extension for non-JSON files",
            "/Volumes/test_catalog/test_schema/test_table/data.csv.json", csvPath);
        
        String noExtensionPath = copyOperation.tempFileLocation("data");
        assertEquals("Should add .json extension for files without extension",
            "/Volumes/test_catalog/test_schema/test_table/data.json", noExtensionPath);

        // Test 2: Missing catalog configuration
        Map<String, String> missingCatalogConfig = getJdbcConfig();
        missingCatalogConfig.remove(DATABASE_NAME);
        
        JdbcSinkConnectorConfig invalidConfig1 = new JdbcSinkConnectorConfig(missingCatalogConfig);
        copyOperation.init(schema, invalidConfig1, mock(AdminApiClient.class), WarehouseCopyFileFormat.JSON, logger);
        
        try {
            copyOperation.tempFileLocation("test.json");
            fail("Should throw exception when catalog is missing");
        } catch (IllegalArgumentException e) {
            assertTrue("Exception should mention missing configuration",
                e.getMessage().contains("Catalog, schema, or volume name is not configured"));
        }

        // Test 3: Missing schema configuration
        Map<String, String> missingSchemaConfig = getJdbcConfig();
        missingSchemaConfig.remove(SCHEMA_NAME);
        
        JdbcSinkConnectorConfig invalidConfig2 = new JdbcSinkConnectorConfig(missingSchemaConfig);
        copyOperation.init(schema, invalidConfig2, mock(AdminApiClient.class), WarehouseCopyFileFormat.JSON, logger);
        
        try {
            copyOperation.tempFileLocation("test.json");
            fail("Should throw exception when schema is missing");
        } catch (IllegalArgumentException e) {
            assertTrue("Exception should mention missing configuration",
                e.getMessage().contains("Catalog, schema, or volume name is not configured"));
        }

        // Test 4: Missing table configuration
        Map<String, String> missingTableConfig = getJdbcConfig();
        missingTableConfig.remove(TABLE);
        
        JdbcSinkConnectorConfig invalidConfig3 = new JdbcSinkConnectorConfig(missingTableConfig);
        copyOperation.init(schema, invalidConfig3, mock(AdminApiClient.class), WarehouseCopyFileFormat.JSON, logger);
        
        try {
            copyOperation.tempFileLocation("test.json");
            fail("Should throw exception when table is missing");
        } catch (IllegalArgumentException e) {
            assertTrue("Exception should mention missing configuration",
                e.getMessage().contains("Catalog, schema, or volume name is not configured"));
        }

        // Test 5: Special characters in names (should be handled correctly)
        Map<String, String> specialCharsConfig = getJdbcConfig();
        specialCharsConfig.put(DATABASE_NAME, "test-catalog");
        specialCharsConfig.put(SCHEMA_NAME, "test_schema");
        specialCharsConfig.put(TABLE, "test-table");
        
        JdbcSinkConnectorConfig specialConfig = new JdbcSinkConnectorConfig(specialCharsConfig);
        copyOperation.init(schema, specialConfig, mock(AdminApiClient.class), WarehouseCopyFileFormat.JSON, logger);
        
        String specialPath = copyOperation.tempFileLocation("test.json");
        assertEquals("Should handle special characters in catalog and table names",
            "/Volumes/test-catalog/test_schema/test-table/test.json", specialPath);
    }

    // Helper methods
    private void initializeCopyOperation() {
        JdbcSinkConnectorConfig config = new JdbcSinkConnectorConfig(getJdbcConfig());
        copyOperation.init(schema, config, mock(AdminApiClient.class), WarehouseCopyFileFormat.JSON, logger);
    }

    private Map<String, String> getJdbcConfig() {
        Map<String, String> config = new HashMap<>();
        config.put(SINK_ID, "1");
        config.put(CREDS_ENC, "1");
        config.put(CREDS_ENC_IV, "1");
        config.put(UNIT_TEST, "true");
        config.put(INSERT_MODE, "upsert");
        config.put(CREDENTIALS_TYPE, ConnectionType.DATABRICKS.name());
        config.put(DATABASE_NAME, TEST_CATALOG);
        config.put(SCHEMA_NAME, TEST_SCHEMA);
        config.put(URL, "*****************************************************************");
        config.put(TABLE, TEST_TABLE);
        config.put("databricks.cloud.type", DatabricksCloudType.DATABRICKS.name());
        return config;
    }

    private Map<String, String> getJdbcConfigWithAuth() {
        Map<String, String> config = getJdbcConfig();
        // Add authentication parameters that would be used for M2M auth
        config.put("OAuth2ClientId", "test-client-id");
        config.put("OAuth2Secret", "test-client-secret");
        return config;
    }

    private Schema createTestSchema() {
        return SchemaBuilder.struct()
                .field("id", Schema.INT32_SCHEMA)
                .field("name", Schema.STRING_SCHEMA)
                .field("value", Schema.STRING_SCHEMA)
                .build();
    }
}