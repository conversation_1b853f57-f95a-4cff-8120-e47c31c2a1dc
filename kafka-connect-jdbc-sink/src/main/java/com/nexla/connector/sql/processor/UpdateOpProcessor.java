package com.nexla.connector.sql.processor;

import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.sql.sink.CdcSinkTask;
import com.nexla.probe.sql.SchemaUtils;
import connect.data.Field;
import connect.data.Schema;
import lombok.SneakyThrows;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.nexla.common.ResourceType.SINK;
import static java.util.Arrays.stream;
import static java.util.stream.Collectors.toList;

public class UpdateOpProcessor extends BaseOpProcessor {

    public UpdateOpProcessor(JdbcSinkConnectorConfig config, TableProcessor tableProcessor) {
        super(config, tableProcessor);
        init(LoggerFactory.getLogger(this.getClass()));
    }

    @Override
    @SneakyThrows
    public void processInternal(Collection<NexlaMessage> records,
                                String tableName,
                                List<String> keyFields,
                                List<String> nonKeyFields,
                                Map<Long, RecordMetric> metricsByRunId,
                                Schema schema,
                                BaseSinkTask.ExceptionHandler handler,
                                JdbcSinkConnectorConfig config) {

        List<String> fieldsForUpdate = new ArrayList<>(nonKeyFields);
        fieldsForUpdate.addAll(keyFields);

        getLogger().info("CDC: Trying to UPDATE data in '{}'...", config.table);
        if(!keyFields.isEmpty()) {
            getTableProcessor().doUpsertEmulation(records,
                    tableName,
                    keyFields,
                    nonKeyFields,
                    fieldsForUpdate,
                    metricsByRunId,
                    schema,
                    handler,
                    config);
        } else {
            getTableProcessor().doUpdateWithoutPK(records,
                    tableName,
                    nonKeyFields,
                    metricsByRunId,
                    schema,
                    handler,
                    config);
        }
    }
}
