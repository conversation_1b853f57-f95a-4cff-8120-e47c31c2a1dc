package com.nexla.connector.sql.processor;

import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.sql.sink.CdcSinkTask;
import com.nexla.probe.sql.SchemaUtils;
import connect.data.Field;
import connect.data.Schema;
import connect.jdbc.util.JdbcUtils;
import lombok.Getter;
import org.slf4j.Logger;

import java.util.*;

import static com.nexla.common.ResourceType.SINK;
import static java.util.stream.Collectors.toList;

public abstract class BaseOpProcessor {

    @Getter
    private NexlaLogger logger;
    @Getter
    private final JdbcSinkConnectorConfig config;
    @Getter
    private final TableProcessor tableProcessor;

    public BaseOpProcessor(JdbcSinkConnectorConfig config, TableProcessor tableProcessor) {
        this.config = config;
        this.tableProcessor = tableProcessor;
    }

    protected void init(Logger logger) {
        this.logger = new NexlaLogger(logger, new NexlaLogKey(SINK, config.sinkId, Optional.empty()));
    }

    /**
     * Process INSERT, UPDATE, DELETE, TRUNCATE operations using appropriate child class
     *
     * @param records      segregated records to process
     * @param metricsByRunId records metrics by Run ID
     * @param handler      exception handler
     */
    public void process(Collection<NexlaMessage> records,
                        Map<Long, RecordMetric> metricsByRunId,
                        CdcSinkTask.ExceptionHandler handler) {
        String tableName = tableProcessor.getDbDialect().getQualifiedTableName(config.table,
                config.authConfig.schemaName,
                JdbcUtils.getDatabaseName(config));

        // there are cases when destination table was not created before truncate
        Schema schema = null;
        try {
            schema = SchemaUtils.selectSchema(tableProcessor.getConnProvider(), tableProcessor.getDbDialect(), config);
        } catch (Exception exc) {
            logger.error("Can't get schema from table={}", tableName, exc);
        }

        List<String> keyFields = config.primaryKey == null ? Collections.emptyList() : config.primaryKey;
        List<String> nonKeyFields = getNonKeyFields(keyFields, schema, tableName);

        // here you should already have the schema and table name
        processInternal(records,
                tableName,
                keyFields,
                nonKeyFields,
                metricsByRunId,
                schema,
                handler,
                config);
    }

    /**
     * Get non Key Fields according to schema and key fields from config.
     * Method is overriden in Truncate operation and returns empty list
     * @param keyFields key fields from config
     * @param schema target table schema
     * @param tableName target table name
     * @return list of non Key Fields
     */
    protected List<String> getNonKeyFields(List<String> keyFields, Schema schema, String tableName) {
        if (schema == null) {
            throw new RuntimeException("Can't get schema from table=" + tableName);
        }

        return schema.fields().stream()
                .map(Field::name)
                .filter(f -> !keyFields.contains(f))
                .collect(toList());
    }

    protected abstract void processInternal(Collection<NexlaMessage> records,
                                            String tableName,
                                            List<String> keyFields,
                                            List<String> nonKeyFields,
                                            Map<Long, RecordMetric> metricsByRunId,
                                            Schema schema,
                                            BaseSinkTask.ExceptionHandler handler,
                                            JdbcSinkConnectorConfig config);
}