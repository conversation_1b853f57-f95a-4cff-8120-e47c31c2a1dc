package com.nexla.connector.sql.processor;

import com.nexla.common.NexlaMessage;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import connect.data.Schema;
import lombok.SneakyThrows;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public class DeleteOpProcessor extends BaseOpProcessor {

    public DeleteOpProcessor(JdbcSinkConnectorConfig config, TableProcessor tableProcessor) {
        super(config, tableProcessor);
        init(LoggerFactory.getLogger(this.getClass()));
    }

    @Override
    @SneakyThrows
    public void processInternal(Collection<NexlaMessage> records,
                                String tableName,
                                List<String> keyFields,
                                List<String> nonKeyFields,
                                Map<Long, RecordMetric> metricsByRunId,
                                Schema schema,
                                BaseSinkTask.ExceptionHandler handler,
                                JdbcSinkConnectorConfig config) {

        getLogger().info("CDC: Trying to DELETE data from {}...", config.table);
        getTableProcessor().doDelete(records,
                tableName,
                keyFields,
                nonKeyFields,
                metricsByRunId,
                schema,
                handler,
                config);
    }
}
