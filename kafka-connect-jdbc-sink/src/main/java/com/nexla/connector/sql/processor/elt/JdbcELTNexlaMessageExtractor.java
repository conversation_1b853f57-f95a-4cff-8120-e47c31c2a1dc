package com.nexla.connector.sql.processor.elt;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.nexla.common.NexlaMessage;
import com.nexla.connector.ExtractedMessage;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.MappingConfig;
import connect.data.Field;
import connect.data.Schema;
import connect.data.Timestamp;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.stream.Collectors;

import static com.nexla.connect.common.elt.ELTConstants.*;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;

public class JdbcELTNexlaMessageExtractor {

  public static Pair<Map<List<String>, List<NexlaMessageContext>>, Map<List<String>, List<NexlaMessageContext>>> extractPrimaryKeys(List<NexlaMessageContext> extractedMessages) {
    Map<List<String>, List<NexlaMessageContext>> groupedMessages = extractedMessages
        .stream()
        .map(it -> Pair.of((List<String>) it.getOriginal().getRawMessage().get(NEXLA_OPERATION_PRIMARY_KEY), it))
        .collect(Collectors.groupingBy(Pair::getKey, mapping(Pair::getValue, toList())));

    Map<List<String>, List<NexlaMessageContext>> invalidPrimaryKeys = groupedMessages.entrySet()
        .stream()
        .filter(entry -> !isValidPrimaryKey(entry.getKey()))
        .collect(Collectors.toMap(
            Map.Entry::getKey,
            Map.Entry::getValue,
            (list1, list2) -> {
              list1.addAll(list2);
              return list1;
            }
        ));

    invalidPrimaryKeys.forEach((k, v) -> groupedMessages.remove(k));

    return Pair.of(groupedMessages, invalidPrimaryKeys);
  }

  private static boolean isValidPrimaryKey(List<String> key) {
    return key.stream().filter(StringUtils::isNotBlank).count() == key.size();
  }

  private static ExtractedMessage extractRecordMessage(NexlaMessage message) {
    Object record = message.getRawMessage().get(NEXLA_OPERATION_RECORD);
    String dataStr = JsonUtils.toJsonString(record);
    LinkedHashMap<String, Object> recordMap = JsonUtils.stringToType(dataStr, new TypeReference<>() {});
    LinkedHashMap<String, Object> filteredRawMessage = new LinkedHashMap<>();
    recordMap.forEach((key, value) -> {
      if (Objects.nonNull(value)) {
        filteredRawMessage.put(key, value);
      }
    });
    NexlaMessage nexlaMessage = new NexlaMessage(filteredRawMessage, message.getNexlaMetaData());
    return new ExtractedMessage(message, nexlaMessage);
  }

  public static Optional<MappingConfig> extractMappingConfigFromMetadata(List<NexlaMessageContext> messages){
    return Optional.ofNullable(messages)
        .filter(CollectionUtils::isNotEmpty)
        .map(it -> it.get(0))
        .map(NexlaMessageContext::getOriginal)
        .map(NexlaMessage::getRawMessage)
        .map(it -> it.get(NEXLA_OPERATION_METADATA))
        .map(JsonUtils::toJsonString)
        .map(it -> JsonUtils.stringToType(it, MappingConfig.class));
  }

  public static List<NexlaMessageContext> extractNexlaRecord(List<NexlaMessageContext> messages) {
    return StreamEx.of(messages)
        .map(originalMessage -> {
          ExtractedMessage extractedMessage = extractRecordMessage(originalMessage.getOriginal());
          return new NexlaMessageContext(originalMessage.getOriginal(),
              extractedMessage.getMapped(),
              originalMessage.getTopicPartition(),
              originalMessage.getKafkaOffset());
        }).toList();
  }

  /**
   * Most of the warehouses don't deal well with timestamp with timezone precision,
   * we're removing it from the dat string and saving always as default
   */
  public static List<NexlaMessageContext> stripTimezoneFromTimestampAttributes(List<NexlaMessageContext> messageContexts, Schema schema) {
    List<String> timestampFields = schema.fields()
        .stream()
        .filter(it -> Timestamp.LOGICAL_NAME.equals(it.schema().name()))
        .map(Field::name)
        .collect(Collectors.toList());

    if (CollectionUtils.isNotEmpty(timestampFields)) {
      return messageContexts.stream()
          .peek(messageContext -> stripTimezoneFromMessageFields(timestampFields, messageContext))
          .collect(Collectors.toList());
    }

    return messageContexts;
  }

  private static void stripTimezoneFromMessageFields(List<String> timestampFields, NexlaMessageContext messageContext) {
    LinkedHashMap<String, Object> rawMessage = messageContext.getMapped().getRawMessage();
    timestampFields.forEach(fieldName -> {
      Object value = rawMessage.get(fieldName);
      if (Objects.nonNull(value)) {
        String strippedTimezone = stripTimezone(value.toString());
        rawMessage.put(fieldName, strippedTimezone);
      }
    });
  }

  /**
   * Removes the timezone part from a datetime string.
   * E.g. "2023-12-22T22:21:49.123+0000" -> "2023-12-22T22:21:49.123"
   *      "2023-12-22T22:21:49Z"        -> "2023-12-22T22:21:49"
   *
   * @param input the original datetime string
   * @return the datetime string without timezone
   */
  private static String stripTimezone(String input) {
    if (StringUtils.isBlank(input)) {
      return input;
    }

    // Regex pattern to strip offset or 'Z'
    return input.replaceFirst("([\\+\\-]\\d{2}:?\\d{2}|Z)$", "")
        .replaceFirst("([\\+\\-]\\d{4})$", "");
  }
}
