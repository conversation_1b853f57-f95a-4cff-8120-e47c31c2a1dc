package com.nexla.connector.sql.sink;

import com.nexla.common.ConnectionType;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connect.common.postponedFlush.KafkaProgressTracker;
import com.nexla.connect.common.postponedFlush.PipelineProgressTracker;
import com.nexla.connector.ExtractedMessage;
import com.nexla.connector.MessageMapper;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.sql.processor.ELTFlushProcessor;
import com.nexla.connector.sql.processor.elt.JdbcELTSchemaMappingResolver;
import com.nexla.connector.sql.processor.elt.JdbcELTTableProcessor;
import com.nexla.probe.sql.SqlConnectorService;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DialectRegistry;
import connect.jdbc.sink.warehouse.JdbcELTDataWarehouseSink;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.common.config.ConfigException;
import org.apache.kafka.connect.errors.RetriableException;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.nexla.common.ConnectionType.ORACLE_AUTONOMOUS;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.connect.common.elt.ELTConstants.NEXLA_OPERATION_OBJECT;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.INSERT;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static com.nexla.connector.properties.SqlConfigAccessor.*;
import static com.nexla.connector.sql.processor.elt.JdbcELTNexlaMessageExtractor.*;
import static com.nexla.probe.sql.SqlConnectorService.shouldRethrow;

public class JdbcELTSinkTask extends JdbcSinkTask {

    private static final Set<ConnectionType> TYPES_ENABLED_FOR_ELT_FLOW = Set.of(ConnectionType.REDSHIFT, ConnectionType.SNOWFLAKE, ORACLE_AUTONOMOUS);

    @Override
    public void doStart() {
        if (!typeEnabledForEltFlow()) {
            throw new IllegalArgumentException(String.format("Only %s types are supported!", TYPES_ENABLED_FOR_NEW_FLOW));
        }

        this.sinkTelemetryReporter = Optional.of(new ConnectorTelemetryReporter(config.sinkId, config.authConfig.dbType.name(), SINK, isDedicatedNode));
        this.probeService = new SqlConnectorService(nexlaCredentialsStore, adminApiClient);
        this.probeService.initLogger(SINK, config.sinkId, taskId);
        this.dbDialect = DialectRegistry.getInstance().fromConnectionString(config.authConfig);
        this.connProvider = () -> probeService.getConnection(config.authConfig);
        this.tableProcessor = new JdbcELTTableProcessor(dbDialect, connProvider, probeService, config.sinkId);
        this.dataWarehouseSink = new JdbcELTDataWarehouseSink(tableProcessor.getConnProvider(), tableProcessor.getDbDialect(), logger);
        this.flusher = new ELTFlushProcessor(tableProcessor, config.sinkId, getExceptionHandler(), getMetricHandler(), lastProcessedMessageTs);
        PipelineProgressTracker ppt = pipelineProgressTracker.orElseGet(() -> new KafkaProgressTracker(getKafkaLagCalculator(), adminApiClient, ctrlClient, logger));
        this.postponedFlush = flusher.getPostponedFlush(this, ppt, dataWarehouseSink, lastProcessedMessageTs, logger);
        this.copyMode = true;

        if (config.parallelism > 1) {
            this.executor = Executors.newWorkStealingPool(config.parallelism);
        }

        validateConfig();

        open(context.assignment());
    }

    @Override
    protected void processBatch(StreamEx<NexlaMessageContext> nexlaMessages, int streamSize) {
        List<NexlaMessageContext> messageList = nexlaMessages.toList();
        try {
            groupMessagesByTableName(messageList)
                    .forEach((tableName, messages) -> {
                        // extract nexla_op_record
                        List<NexlaMessageContext> extractedMessages = extractNexlaRecord(messages);

                        // extract primary keys
                        Pair<Map<List<String>, List<NexlaMessageContext>>, Map<List<String>, List<NexlaMessageContext>>> primaryKeyPair = extractPrimaryKeys(extractedMessages);
                        Map<List<String>, List<NexlaMessageContext>> validMessages = primaryKeyPair.getLeft();
                        Map<List<String>, List<NexlaMessageContext>> invalidMessages = primaryKeyPair.getRight();

                        validMessages.forEach((primaryKeys, messagesToProcess) -> {
                            Optional<MappingConfig> metadataMappingConfig = extractMappingConfigFromMetadata(messagesToProcess);

                            JdbcELTSchemaMappingResolver mappingResolver = createMappingResolver(metadataMappingConfig);

                            // detect schema from records
                            Schema schema = mappingResolver.detectSchema(messagesToProcess, tableName);

                            JdbcSinkConnectorConfig enrichedConfig = enrichConfig(tableName, primaryKeys, schema, metadataMappingConfig, mappingResolver);

                            logger.info("M=processBatch, table={}, enrichedConfigMapping={}", enrichedConfig.table, enrichedConfig.mappingConfig.get().getMapping());

                            List<NexlaMessageContext> strippedMessagesToProcess = stripTimezoneFromTimestampAttributes(messagesToProcess, schema);
                            processRecords(strippedMessagesToProcess, enrichedConfig);
                        });

                        invalidMessages.forEach((primaryKeys, invalidMessagesToProcess) -> {
                                logger.info("M=processBatch, processing {} messages with invalid primary key definition {}", invalidMessagesToProcess.size(), primaryKeys);
                                handleErrorNotification(invalidMessagesToProcess, new RuntimeException("Invalid primary key definition: " + primaryKeys));
                            }
                        );
                    });
        } catch (Exception e) {
            logger.error("Stopping ELT sink task {}.... ", this.config.sinkId, e);
            throw e;
        }
    }

    private JdbcELTSchemaMappingResolver createMappingResolver(Optional<MappingConfig> metadataMappingConfig) {
        return new JdbcELTSchemaMappingResolver(this.config.mappingConfig.get(), metadataMappingConfig, this.dbDialect, this.logger);
    }

    private LinkedHashMap<String, List<NexlaMessageContext>> groupMessagesByTableName(List<NexlaMessageContext> messageList) {
        return StreamEx.of(messageList)
                .groupingBy(nmc -> nmc.getOriginal().getRawMessage().get(NEXLA_OPERATION_OBJECT).toString(),
                        LinkedHashMap::new,
                        Collectors.toList());
    }

    private void validateConfig() {
        if (this.config.mappingConfig.isEmpty()) {
            throw new ConfigException("ELT: Mapping Config should not be empty");
        }
    }

    private JdbcSinkConnectorConfig enrichConfig(String tableName, List<String> primaryKeys, Schema schema, Optional<MappingConfig> metadataMappingConfig, JdbcELTSchemaMappingResolver mappingResolver) {
        Optional<MappingConfig.TableMapping> overriddenMapping = Optional.ofNullable(config.mappingConfig.get()
                .getOverriddenMappings()
                .get(tableName));

        LinkedHashMap<String, Map<String, String>> mappingConfig = mappingResolver.resolveMappingConfig(tableName, schema);

        logger.info("M=enrichConfig, primaryKeys={}, mappingConfig={}", primaryKeys, mappingConfig);

        String finalTableName = overriddenMapping.map(MappingConfig.TableMapping::getName)
            .orElse(metadataMappingConfig
                .map(MappingConfig::getOverriddenMappings)
                .map(it -> it.get(tableName))
                .map(MappingConfig.TableMapping::getName)
                .orElse(tableName));

        Map<String, String> originals = this.config.originalsStrings();
        String formattedPrimaryKeys = primaryKeys.stream()
                .map(key -> dbDialect.getTargetName(key))
                .collect(Collectors.joining(","));
        originals.put(TABLE, tableProcessor.getFullTableName(this.config.mappingConfig.get(), finalTableName));
        if (StringUtils.isBlank(formattedPrimaryKeys) || INSERT == config.insertMode) {
            originals.put(INSERT_MODE, INSERT.name());
        } else {
            originals.put(PRIMARY_KEY, formattedPrimaryKeys);
            originals.put(INSERT_MODE, UPSERT.name());
        }

        if (ORACLE_AUTONOMOUS == config.authConfig().dbType){
            // setting ISO date time format as default for oracle
            // this should be a temp solution, best way is to make source send the date time format using nexla_op_metadata
            originals.put(JdbcSinkConnectorConfig.ORACLE_TIMESTAMP_FORMAT, "YYYY-MM-DD\"T\"HH24:MI:SS.FF3");
        }

        JdbcSinkConnectorConfig enrichedConfig = new JdbcSinkConnectorConfig(originals);
        enrichedConfig.mappingConfig.get().setMapping(mappingConfig);
        return enrichedConfig;
    }

    private void processRecords(List<NexlaMessageContext> messages, JdbcSinkConnectorConfig enriched) {
        try {
            ((JdbcELTTableProcessor) tableProcessor).processTable(enriched, alterTableSupplier());

            List<NexlaMessageContext> mappedMessages = mapMessages(messages, enriched);

            final Map<Long, Integer> streamSizeByRunId = new HashMap<>();
            mappedMessages.forEach(message -> {
                Long runId = 0L;
                if (message.getOriginal() != null && message.getOriginal().getNexlaMetaData() != null && message.getOriginal().getNexlaMetaData().getRunId() != null) {
                    runId = message.getOriginal().getNexlaMetaData().getRunId();
                }
                streamSizeByRunId.compute(runId, (key, value) -> value == null ? 1 : value + 1);
            });
            addRunIdToHeartbeat(mappedMessages);

            ((JdbcELTDataWarehouseSink) dataWarehouseSink).writeData(enriched,
                    getFileFormat(),
                    mappedMessages,
                    streamSizeByRunId);

            lastProcessedMessageTs.set(System.currentTimeMillis());
        } catch (Exception e) {
            if (shouldRethrow(e)) {
                throw new RetriableException(e);
            }
            logger.error("ELT: Error while writing records to sinkId={}, records={}", this.config.sinkId, messages.size(), e);
            handleErrorNotification(messages, e);
            if (this.config.stopOnError) {
                throw new RuntimeException(e);
            }
        }
    }

    private void handleErrorNotification(List<NexlaMessageContext> messages, Exception e) {
        RecordMetric recordMetric = new RecordMetric();
        messages.forEach(m -> sendToQuarantine(m.getOriginal(), e, recordMetric));

        String name = StringUtils.isNotBlank(this.config.table) ? this.config.table : this.config.authConfig.databaseName;
        sendMetric(name, Optional.empty(), 0L, 0L, recordMetric.errorRecords.get());

        ExceptionHandler handler = getExceptionHandler();
        handler.handleException(e, recordMetric.errorRecords.get(), name);
        handler.handleMonitoring(e);
    }

    // convert messages to exact DB field name based on mapping config
    private List<NexlaMessageContext> mapMessages(List<NexlaMessageContext> messages, JdbcSinkConnectorConfig enriched) {
        MessageMapper mapper = new MessageMapper(enriched.mappingConfig, enriched, false, encryptionUtils);
        return messages
                .stream()
                .map(message -> {
                    ExtractedMessage extractedMessage = mapper.extractMessage(message.getMapped());
                    return new NexlaMessageContext(message.getOriginal(),
                            extractedMessage.getMapped(),
                            message.topicPartition,
                            message.kafkaOffset);
                })
                .collect(Collectors.toList());
    }

    private void addRunIdToHeartbeat(List<NexlaMessageContext> extractedMessages) {
        extractedMessages
                .stream()
                .filter(m -> Objects.nonNull(m.getMapped()) && Objects.nonNull(m.getMapped().getNexlaMetaData()))
                .forEach(m -> runIdsToHeartbeat.add(m.getMapped().getNexlaMetaData().getRunId()));
    }

    private Supplier<Boolean> alterTableSupplier() {
        return () -> {
            postponedFlush.setForceFlush(true);

            boolean result = doFlush(postponedFlush.readyToFlush());

            ((JdbcELTDataWarehouseSink) dataWarehouseSink).invalidate();

            return result;
        };
    }

    private boolean typeEnabledForEltFlow() {
        return TYPES_ENABLED_FOR_ELT_FLOW.contains(config.authConfig.dbType);
    }
}
