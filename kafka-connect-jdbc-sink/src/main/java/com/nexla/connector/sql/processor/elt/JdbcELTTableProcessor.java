package com.nexla.connector.sql.processor.elt;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.sql.processor.CaseInsensitiveMap;
import com.nexla.connector.sql.processor.TableProcessor;
import com.nexla.probe.sql.SchemaUtils;
import com.nexla.probe.sql.SqlConnectorService;
import connect.data.Field;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.google.common.collect.ImmutableMap.of;
import static com.nexla.common.tracker.Tracker.TrackerMode.NONE;

public class JdbcELTTableProcessor extends TableProcessor {

    protected final Cache<String, Map<String, String>> rawColumnDataTypeCache;

    public JdbcELTTableProcessor(DbDialect dbDialect,
                                 Supplier<Connection> connProvider,
                                 SqlConnectorService probeService,
                                 int sinkId) {
        super(dbDialect, connProvider, probeService, sinkId);
        this.rawColumnDataTypeCache = CacheBuilder
            .newBuilder()
            .expireAfterWrite(15, TimeUnit.MINUTES)
            .build();
    }

    public void processTable(JdbcSinkConnectorConfig config, Supplier<Boolean> flusher) {
        String table = config.table;
        int sinkId = config.sinkId;
        String cacheKey = sinkId + "_" + table;

        try {
            boolean tableExists = existingTables.get(cacheKey, () -> {
                logger.info("checking if destination table '{}' exists directly in db '{}'...", table, config.authConfig.databaseName);
                return tableExists(config);
            });
            logger.info("destination table '{}' EXISTS: {}", config.table, tableExists);
            if (!tableExists) {
                probeService.createDestination(config, Optional.empty());
                existingTables.put(cacheKey, true);
            } else {
                alterIfNeeded(cacheKey, config, flusher);
            }
        } catch (Exception e) {
            throw new RuntimeException("Can't process table '" + table + "' in sink " + sinkId, e);
        }
    }

    @SneakyThrows
    private void alterIfNeeded(String cacheKey, JdbcSinkConnectorConfig config, Supplier<Boolean> flusher) {
        MappingConfig mappingConfig = config.mappingConfig.get();

        CaseInsensitiveMap<String> cachedMapping = tablePerMapping.get(cacheKey, () -> {
            logger.info("M=alterIfNeeded, getting full schema from table '{}'...", config.table);
            Schema schema = SchemaUtils.selectDBSchema(this.connProvider, this.dbDialect, config);

            return StreamEx.of(schema.fields())
                    .collect(Collectors.toMap(
                            Field::name,
                            f -> this.dbDialect.getSqlType(f.schema().name(), Map.of(), f.schema().type()),
                            (existingValue, newValue) -> existingValue, CaseInsensitiveMap::new));
        });

        CaseInsensitiveMap<String> currentMapping = new CaseInsensitiveMap<>();
        for (Map<String, String> innerMap : mappingConfig.getMapping().values()) {
            currentMapping.putAll(innerMap);
        }

        logger.info("M=alterIfNeeded, target table '{}' - {} schema", config.table, Collections.singletonList(cachedMapping));
        logger.info("M=alterIfNeeded, target table '{}' - {} config mapping", config.table, Collections.singletonList(currentMapping));

        // check if we need to add or update columns
        Set<String> columnsToAdd = currentMapping.keySet()
                .stream()
                .filter(key -> !cachedMapping.containsKey(key)).collect(Collectors.toSet());

        Set<String> columnsToModify = currentMapping
            .keySet()
            .stream()
            .filter(k -> shouldModifyColumn(k, currentMapping, cachedMapping, config.table, config.authConfig.schemaName, cacheKey))
            .collect(Collectors.toSet());

        // add tracker_name if required
        if (mappingConfig.getTrackerMode() != NONE &&
                !cachedMapping.containsKey(mappingConfig.getTrackerFieldName())) {
            columnsToAdd.add(mappingConfig.getTrackerFieldName());
            config.mappingConfig
                    .get()
                    .getMapping()
                    .put(mappingConfig.getTrackerFieldName(),
                            new LinkedHashMap<>(of(mappingConfig.getTrackerFieldName(), this.dbDialect.getDbType(Schema.Type.STRING))));
        }

        logger.debug("M=alterIfNeeded, columns to ADD to '{}': {}", config.table, columnsToAdd);
        logger.debug("M=alterIfNeeded, columns to MODIFY to '{}': {}", config.table, columnsToModify);

        if (!columnsToAdd.isEmpty() || !columnsToModify.isEmpty()) {
            logger.info("M=alterIfNeeded, flushing to modify or add columns");
            flusher.get();
            probeService.alterTableAdd(config, columnsToAdd);
            probeService.alterTableModify(config, columnsToModify);
            // clean cache after ALTERING
            logger.info("M=alterIfNeeded, cleaning schema cache after ALTER operation: '{}'", config.table);
            tablePerMapping.invalidate(cacheKey);
            rawColumnDataTypeCache.invalidate(cacheKey);
        }
    }

    private boolean shouldModifyColumn(String column,
                                       CaseInsensitiveMap<String> newMapping,
                                       CaseInsensitiveMap<String> currentMapping,
                                       String table,
                                       String schemaName,
                                       String cacheKey) {
        if (!currentMapping.containsKey(column)) {
            // new columns won't exist in the cache at this point
            return false;
        }
        var currentDbType = currentMapping.get(column);
        var newDbType = newMapping.get(column);
        var currentType = dbDialect.getSchemaTypeByDbType(currentDbType);
        var newType = dbDialect.getSchemaTypeByDbType(newDbType);

        if (Objects.nonNull(currentType) && Objects.nonNull(newType)) {
            if (currentType != newType) {
                List<Schema.Type> types = dbDialect.allowedColumnModifications().get(currentType);
                if (types.contains(newType)) {
                    return true;
                }
                logger.warn("M=shouldModifyColumn, ignoring attempt to perform an invalid column data type change from {} to {}", newType, currentType);
            }
        } else {
            // if the types are null means that it's using a customized config, (i.e overridden_mapping,overridden_db_mapping) so we must fetch the type from DB to compare
            var currentFieldTypeFromDB = getCurrentFieldTypeFromDB(table, schemaName, column, cacheKey);
            logger.debug("M=shouldModifyColumn, currentFieldTypeFromDB={}, newDbType={}", currentFieldTypeFromDB, newDbType);
            return !ColumnTypeComparator.areColumnTypesEqual(newDbType, currentFieldTypeFromDB);
        }
        return false;
    }

    @SneakyThrows
    private String getCurrentFieldTypeFromDB(String table, String schema, String column, String cacheKey) {
        return rawColumnDataTypeCache.get(cacheKey, () -> {
            var selectRawDataTypesQuery = dbDialect.getSelectRawDataTypesQuery(table, schema);
            var columnTypeMap = Maps.<String, String>newHashMap();

            try (Connection connection = connProvider.get();
                 Statement statement = connection.createStatement()) {

                ResultSet resultSet = statement.executeQuery(selectRawDataTypesQuery);

                while(resultSet.next()) {
                    var columnName = resultSet.getString(1);
                    var dataType = resultSet.getString(2);
                    columnTypeMap.put(columnName.toUpperCase(), dataType);
                }
            }
            return columnTypeMap;
        }).get(column.toUpperCase());
    }
}
