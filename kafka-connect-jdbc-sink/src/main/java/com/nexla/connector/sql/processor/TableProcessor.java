package com.nexla.connector.sql.processor;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import com.nexla.common.MetricUtils;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connect.common.cdc.DebeziumConstants;
import com.nexla.connect.common.cdc.DebeziumData;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import com.nexla.probe.sql.SchemaUtils;
import com.nexla.probe.sql.SqlConnectorService;
import connect.data.Field;
import connect.data.Schema;
import connect.jdbc.sink.PreparedStatementBinder;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.util.CdcMode;
import lombok.Getter;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.google.common.collect.ImmutableMap.of;
import static com.nexla.common.NexlaConstants.SOURCE_ID;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.metrics.RecordMetric.quarantineMessage;
import static com.nexla.common.tracker.Tracker.TrackerMode.NONE;
import static java.util.Arrays.stream;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toList;

public class TableProcessor {
    @Getter
    protected final NexlaLogger logger;
    @Getter
    protected final DbDialect dbDialect;
    @Getter
    protected final java.util.function.Supplier<Connection> connProvider;
    protected final SqlConnectorService probeService;

    protected final Cache<String, CaseInsensitiveMap<String>> tablePerMapping;

    protected final Cache<String, Boolean> existingTables;

    public TableProcessor(DbDialect dbDialect,
                          Supplier<Connection> connProvider,
                          SqlConnectorService probeService,
                          int sinkId) {
        this.dbDialect = dbDialect;
        this.connProvider = connProvider;
        this.probeService = probeService;
        this.logger = new NexlaLogger(LoggerFactory.getLogger(this.getClass()), new NexlaLogKey(SINK, sinkId, Optional.empty()));
        this.tablePerMapping = CacheBuilder
                .newBuilder()
                .expireAfterWrite(15, TimeUnit.MINUTES)
                .build();
        this.existingTables = CacheBuilder
                .newBuilder()
                .expireAfterWrite(12, TimeUnit.HOURS)
                .build();
    }

    /**
     * Create or alter destination table if required
     *
     * @param enriched config with correct table name
     */
    public void processTable(JdbcSinkConnectorConfig enriched, Supplier<Boolean> flusher, CdcMode cdcMode) {
        String cacheKey = enriched.sinkId + "_" + enriched.table;

        try {
            boolean tableExists = existingTables.get(cacheKey, () -> {
                logger.info("checking if destination table '{}' exists directly in db '{}'...", enriched.table, enriched.authConfig.databaseName);
                return tableExists(enriched);
            });
            logger.info("destination table '{}' EXISTS: {}", enriched.table, tableExists);
            if (!tableExists) {
                //we will INSERT data if it is UPDATE operation in UpdateOpProcessor.doUpdate
                probeService.createDestination(enriched, Optional.empty());
                existingTables.put(cacheKey, true);
            } else {
                alterIfNeeded(cacheKey, enriched, flusher, cdcMode);
            }
        } catch (Exception e) {
            throw new RuntimeException("Can't process table '" + enriched.table + "' in sink " + enriched.sinkId, e);
        }
    }

    public String getFullTableName(MappingConfig mappingConfig, String table) {
        return  dbDialect.getTargetName(mappingConfig.getTableNamePrefix() + table + mappingConfig.getTableNameSuffix());
    }

    protected boolean tableExists(JdbcSinkConnectorConfig enriched) {
        Map<String, String> originals = enriched.originalsStrings();
        originals.put(SOURCE_ID, "1");

        return probeService.listTables(null, Optional.empty(),
                new JdbcSourceConnectorConfig(originals)).has(enriched.table);
    }

    @SneakyThrows
    private void alterIfNeeded(String cacheKey, JdbcSinkConnectorConfig enriched, Supplier<Boolean> flusher, CdcMode cdcMode) {

        if (enriched.mappingConfig.isEmpty()) {
            logger.warn("Mapping config is empty for table {}", enriched.table);
            return;
        }

        MappingConfig mappingConfig = enriched.mappingConfig.get();
        // check if target table has specified custom config
        MappingConfig.TableMapping tableMapping = mappingConfig
                .getOverriddenMappings()
                .values()
                .stream()
                .filter(tm -> getFullTableName(mappingConfig, tm.getName()).equals(enriched.table))
                .findFirst()
                .orElse(null);

        // skipp ALTER if there is a custom configuration
        if (tableMapping != null || cdcMode.equals(CdcMode.SINGLE_TABLE)) {
            logger.debug("Mapping mode={}, CDC mode={}. No need to ALTER table {} with custom config.", mappingConfig.getMode(), cdcMode, enriched.table);
            return;
        }

        Set<String> toAdd = new LinkedHashSet<>();
        Set<String> toModify = new LinkedHashSet<>();
        Set<String> toDelete = new LinkedHashSet<>();

        CaseInsensitiveMap<String> cachedMapping = tablePerMapping.get(cacheKey, () -> {
            logger.info("getting full schema from table '{}'...", enriched.table);
            Schema schema = SchemaUtils.selectDBSchema(this.connProvider, this.dbDialect, enriched);

            return StreamEx.of(schema.fields())
                    .collect(Collectors.toMap(
                            Field::name,
                            f -> this.dbDialect.convertSemanticToDBType(f.schema().name())
                                    .orElseGet(() -> {
                                        Schema.Type kafkaSchemaType = Schema.Type.valueOf(f.schema().type().name());
                                        return this.dbDialect.getDbType(kafkaSchemaType);
                                    }),
                            (existingValue, newValue) -> existingValue, CaseInsensitiveMap::new));
        });

        CaseInsensitiveMap<String> currentMapping = new CaseInsensitiveMap<>();
        for (Map<String, String> innerMap : mappingConfig.getMapping().values()) {
            currentMapping.putAll(innerMap);
        }

        logger.info("target table '{}' - {} schema", enriched.table, Collections.singletonList(cachedMapping));
        logger.info("target table '{}' - {} config mapping", enriched.table, Collections.singletonList(currentMapping));

        // check if we need to add or update columns
        currentMapping.keySet().forEach(k -> {
            if (cachedMapping.containsKey(k)) {
                if (!currentMapping.get(k).equalsIgnoreCase(cachedMapping.get(k))) {
                    toModify.add(k);
                }
            } else {
                toAdd.add(k);
            }
        });

        // add tracker_name if required
        if (mappingConfig.getTrackerMode() != NONE &&
                !cachedMapping.containsKey(mappingConfig.getTrackerFieldName())) {
            toAdd.add(mappingConfig.getTrackerFieldName());
            enriched.mappingConfig
                    .get()
                    .getMapping()
                    .put(mappingConfig.getTrackerFieldName(), new LinkedHashMap<>(of(mappingConfig.getTrackerFieldName(), this.dbDialect.getDbType(Schema.Type.STRING))));
        }

        // check if we need to delete columns
        cachedMapping.keySet().stream()
                .filter(k -> mappingConfig.getTrackerMode() == NONE || !k.equalsIgnoreCase(mappingConfig.getTrackerFieldName())) // skip tracker_name
                .forEach(k -> {
                    if (!currentMapping.containsKey(k)) {
                        toDelete.add(k);
                    }
                });

        logger.debug("columns to ADD to '{}': {}", enriched.table, toAdd);
        logger.debug("columns to DROP from '{}': {}", enriched.table, toDelete);
        logger.debug("columns to MODIFY in '{}': {}", enriched.table, toModify);

        if (!toAdd.isEmpty() || !toDelete.isEmpty() || !toModify.isEmpty()) {
            flusher.get();
            probeService.alterTableAdd(enriched, toAdd);
            probeService.alterTableDrop(enriched, toDelete);
            probeService.alterTableModify(enriched, toModify);
            // clean cache after ALTERING
            logger.info("cleaning schema cache after ALTER operation: '{}'", enriched.table);
            tablePerMapping.invalidate(cacheKey);
        }
    }

    public void doUpsert(
            Collection<NexlaMessage> records,
            String tableName,
            List<String> keyFields,
            List<String> nonKeyFields,
            List<String> allFieldsOrdered,
            Map<Long, RecordMetric> metricsByRunId,
            Schema schema,
            BaseSinkTask.ExceptionHandler handler,
            JdbcSinkConnectorConfig config
    ) {
        // if db supports MERGE-like operators
        String upsertQuery = dbDialect.getUpsertQuery(tableName, keyFields, nonKeyFields);
        logger.info("UPSERT query: {}", upsertQuery);
        final List<String> upsertQueryFields = dbDialect.getUpsertQueryFields(keyFields, nonKeyFields, allFieldsOrdered);
        tryUpsert(upsertQuery, records, upsertQueryFields, metricsByRunId, schema, handler, config);
    }

    @SneakyThrows
    public void doUpsertWithoutNull(
            Collection<NexlaMessage> records,
            String tableName,
            List<String> keyFields,
            List<String> nonKeyFields,
            List<String> allFieldsOrdered,
            Map<Long, RecordMetric> metricsByRunId,
            Schema schema,
            BaseSinkTask.ExceptionHandler handler,
            JdbcSinkConnectorConfig config
    ) {
        List<Map<String, Object>> rowsFromDb = getDataFromDb(records, keyFields, nonKeyFields, tableName, schema);
        String upsertQuery = dbDialect.getUpsertQuery(tableName, keyFields, nonKeyFields);
        logger.info("UPSERT query (no null upsert set): {}", upsertQuery);

        List<NexlaMessage> messages = rowsFromDb.stream()
                .map(entry -> new NexlaMessage((LinkedHashMap<String, Object>) entry))
                .collect(toList());

        final List<String> upsertQueryFields = dbDialect.getUpsertQueryFields(keyFields, nonKeyFields, allFieldsOrdered);
        tryUpsert(upsertQuery, messages, upsertQueryFields, metricsByRunId, schema, handler, config);
    }

    @SneakyThrows
    private void tryUpsert(
            String upsertQuery,
            Collection<NexlaMessage> messages,
            List<String> allFieldsOrdered,
            Map<Long, RecordMetric> metricsByRunId,
            Schema schema,
            BaseSinkTask.ExceptionHandler handler,
            JdbcSinkConnectorConfig config
    ) {
        try (Connection conn = connProvider.get()) {
            bindAndExecBatch(conn, schema, messages, upsertQuery, allFieldsOrdered, metricsByRunId);
            if (!dbDialect.isAutoCommit()) {
                conn.commit();
            }
        } catch (Exception e) {
            logger.error("Error during upsert process.", e);
            checkAndRethrow(e, config.stopOnError);

            if (tryFallbackToSingleRecordMode(e, config)) {
                bindAndExecSingle(messages, upsertQuery, allFieldsOrdered, metricsByRunId, schema, handler, config);
            } else {
                handleBatchException(messages, metricsByRunId, handler, config, e);
            }
        }
    }

    @SneakyThrows
    private List<Map<String, Object>> getDataFromDb(
            Collection<NexlaMessage> records,
            List<String> keyFields,
            List<String> nonKeyFields,
            String tableName,
            Schema schema
    ) {
        List<Map<String, Object>> dataFromMessages = getDataFromMessages(records, keyFields, nonKeyFields);
        Map<Map<String, Object>, Map<String, Object>> pkToRowMap = getRowsFromDbForPk(records, tableName, keyFields, nonKeyFields, schema);

        return dataFromMessages.stream()
                .map(map -> {
                    Map<String, Object> pkMap = EntryStream.of(map).filterKeys(keyFields::contains).toMap();
                    Map<String, Object> dbValues = pkToRowMap.getOrDefault(pkMap, new HashMap<>());
                    Map<String, Object> row = Maps.newLinkedHashMap();
                    map.forEach((key, value) -> {
                        if (value == null && dbValues.containsKey(key)) {
                            row.put(key, dbValues.get(key));
                        } else {
                            row.put(key, value);
                        }
                    });
                    return row;
                })
                .collect(toList());
    }

    private List<Map<String, Object>> getDataFromMessages(
            Collection<NexlaMessage> records,
            List<String> keyFields,
            List<String> nonKeyFields
    ) {
        return records.stream()
                .map(record -> {
                    Map<String, Object> map = Maps.newLinkedHashMap();
                    keyFields.forEach(field -> map.put(field, record.getRawMessage().get(field)));
                    nonKeyFields.forEach(field -> map.put(field, record.getRawMessage().get(field)));
                    return map;
                })
                .collect(toList());
    }

    private List<Map<String, Object>> getValueForPks(
            Collection<NexlaMessage> records,
            List<String> keyFields
    ) {
        return records.stream()
                .map(record -> {
                    Map<String, Object> pkValues = Maps.newLinkedHashMap();
                    Map<String, Object> message = record.getRawMessage();
                    keyFields.forEach(field -> pkValues.put(field, message.get(field)));
                    return pkValues;
                })
                .collect(toList());
    }

    @SneakyThrows
    private Map<Map<String, Object>, Map<String, Object>> getRowsFromDbForPk(
            Collection<NexlaMessage> records,
            String tableName,
            List<String> keyFields,
            List<String> nonKeyFields,
            Schema schema
    ) {
        Map<Map<String, Object>, Map<String, Object>> pkToRowMap = Maps.newLinkedHashMap();
        List<Map<String, Object>> pkValueMapList = getValueForPks(records, keyFields);
        String selectSql = dbDialect.getSelectQuery(tableName, keyFields, nonKeyFields, pkValueMapList.size());
        logger.info("Select SQL: {}", selectSql);
        try (
                Connection conn = connProvider.get();
                PreparedStatement psSelect = conn.prepareStatement(selectSql)
        ) {
            PreparedStatementBinder binder = new PreparedStatementBinder(psSelect).withLoggerPrefix(logger.getPrefix());
            AtomicInteger bindIndex = new AtomicInteger(1);
            pkValueMapList.forEach(pkValueMap -> binder.bindMultipleRecords(
                    pkValueMap, schema, keyFields, bindIndex.getAndAdd(keyFields.size())));
            binder.addBatch();
            try (ResultSet rs = psSelect.executeQuery()) {
                while (rs.next()) {
                    Map<String, Object> row = Maps.newLinkedHashMap();
                    Map<String, Object> pkMap = Maps.newLinkedHashMap();
                    for (String field : keyFields) {
                        Object value = rs.getObject(field);
                        if (value != null) {
                            pkMap.put(field, value);
                            row.put(field, value);
                        }
                    }
                    for (String field : nonKeyFields) {
                        Object value = rs.getObject(field);
                        if (value != null) {
                            row.put(field, value);
                        }
                    }
                    pkToRowMap.put(pkMap, row);
                }
            }
        }
        return pkToRowMap;
    }

    @SneakyThrows
    public void doUpdateWithoutPK(
            Collection<NexlaMessage> records,
            String tableName,
            List<String> nonKeyFields,
            Map<Long, RecordMetric> metricsByRunId,
            Schema schema,
            BaseSinkTask.ExceptionHandler handler,
            JdbcSinkConnectorConfig config
    ) {
        try {
            doUpdateInternal(records, tableName, nonKeyFields, metricsByRunId, schema);
        } catch (Exception e) {
            logger.error("Error during update without pk process.", e);
            checkAndRethrow(e, config.stopOnError);

            if (tryFallbackToSingleRecordMode(e, config)) {
                Exception exception = null;
                int exceptionCount = 0;

                for (NexlaMessage record : records) {
                    try {
                        doUpdateInternal(singletonList(record), tableName, nonKeyFields, metricsByRunId, schema);
                    } catch (Exception re) {
                        exception = re;
                        exceptionCount++;
                        Long runId = 0L;
                        if (record.getNexlaMetaData() != null && record.getNexlaMetaData().getTags() != null && record.getNexlaMetaData().getTags().get("originalMessageRunId") != null)
                        {
                            runId = (Long) record.getNexlaMetaData().getTags().get("originalMessageRunId");
                        }
                        RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
                        recordMetric.quarantineMessages.add(quarantineMessage(record, re.getMessage()));
                        recordMetric.errorRecords.incrementAndGet();
                    }
                }
                if (exceptionCount > 0) {
                    handler.handleException(exception, exceptionCount, config.table);
                    handler.handleMonitoring(exception);
                }
            } else {
                handleBatchException(records, metricsByRunId, handler, config, e);
            }
        }
    }

    private void doUpdateInternal(
            Collection<NexlaMessage> dataStream,
            String tableName,
            List<String> nonKeyFields,
            Map<Long, RecordMetric> metricsByRunId,
            Schema schema
    ) throws Exception {

        String updateSql = dbDialect.getUpdateSql(tableName, new ArrayList<>(), nonKeyFields);
        logger.info("Update SQL without PK: {}", updateSql);

        final int[] updateResult;

        try (Connection conn = connProvider.get()) {

            try (PreparedStatement psUpdate = conn.prepareStatement(updateSql)) {
                PreparedStatementBinder binderUpdate = new PreparedStatementBinder(psUpdate).withLoggerPrefix(logger.getPrefix());

                dataStream.forEach(dataToOutput -> {
                    DebeziumData debeziumData = (DebeziumData) dataToOutput.getRawMessage().get(DebeziumConstants.NEXLA_CDC_INFO);
                    LinkedHashMap<String, Object> before = debeziumData.getBeforeData();
                    LinkedHashMap<String, Object> after = debeziumData.getAfterData();
                    binderUpdate.bindUpdateRecord(before, after, schema, nonKeyFields);
                });

                updateResult = psUpdate.executeBatch();
                logger.info("SQL without PK - {} resulted in {} updates", updateSql, updateResult);
            }

            if (!dbDialect.isAutoCommit()) {
                conn.commit();
            }
            updateRecordMetric(dataStream, metricsByRunId);
        }
    }

    @SneakyThrows
    public void doUpsertEmulation(
            Collection<NexlaMessage> records,
            String tableName,
            List<String> keyFields,
            List<String> nonKeyFields,
            List<String> fieldsForUpdate,
            Map<Long, RecordMetric> metricsByRunId,
            Schema schema,
            BaseSinkTask.ExceptionHandler handler,
            JdbcSinkConnectorConfig config
    ) {
        try {
            doUpsertEmulationInternal(records, tableName, keyFields, nonKeyFields, fieldsForUpdate, metricsByRunId, schema);
        } catch (Exception e) {
            logger.error("Error during upsert emulation process.", e);
            checkAndRethrow(e, config.stopOnError);

            if (tryFallbackToSingleRecordMode(e, config)) {
                Exception exception = null;
                int exceptionCount = 0;

                for (NexlaMessage record : records) {
                    try {
                        doUpsertEmulationInternal(singletonList(record), tableName, keyFields, nonKeyFields, fieldsForUpdate, metricsByRunId, schema);
                    } catch (Exception re) {
                        exception = re;
                        exceptionCount++;
                        Long runId = 0L;
                        if (record.getNexlaMetaData() != null && record.getNexlaMetaData().getTags() != null && record.getNexlaMetaData().getTags().get("originalMessageRunId") != null)
                        {
                            runId = (Long) record.getNexlaMetaData().getTags().get("originalMessageRunId");
                        }
                        RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
                        recordMetric.quarantineMessages.add(quarantineMessage(record, re.getMessage()));
                        recordMetric.errorRecords.incrementAndGet();
                    }
                }
                if (exceptionCount > 0) {
                    handler.handleException(exception, exceptionCount, config.table);
                    handler.handleMonitoring(exception);
                }
            } else {
                handleBatchException(records, metricsByRunId, handler, config, e);
            }
        }
    }

    private void doUpsertEmulationInternal(
            Collection<NexlaMessage> dataStream,
            String tableName,
            List<String> keyFields,
            List<String> nonKeyFields,
            List<String> fieldsForUpdate,
            Map<Long, RecordMetric> metricsByRunId,
            Schema schema
    ) throws Exception {
        List<String> fieldsForInsert = new ArrayList<>(keyFields);
        fieldsForInsert.addAll(nonKeyFields);

        // emulating upsert mode for databases, that don't support it natively
        // try to update data first, then insert the records that were not updated
        String updateSql = dbDialect.getUpdateSql(tableName, keyFields, nonKeyFields);
        logger.info("Update SQL: {}", updateSql);

        LinkedHashMap<List<Object>, Map<String, Object>> batchRows = new LinkedHashMap<>();
        HashMap<List<Object>, Integer> batchRowsIds = new HashMap<>();

        final int[] updateResult;

        try (Connection conn = connProvider.get()) {

            try (PreparedStatement psUpdate = conn.prepareStatement(updateSql)) {
                PreparedStatementBinder binderUpdate = new PreparedStatementBinder(psUpdate).withLoggerPrefix(logger.getPrefix());
                AtomicInteger countUpdate = new AtomicInteger();

                dataStream.forEach(dataToOutput -> {
                    LinkedHashMap<String, Object> dataToOutputMap = dataToOutput.getRawMessage();
                    List<Object> primaryKey = extractPrimaryKey(keyFields, dataToOutputMap);
                    batchRows.put(primaryKey, dataToOutputMap);
                    batchRowsIds.put(primaryKey, countUpdate.get());
                    binderUpdate.bindRecord(dataToOutputMap, schema, fieldsForUpdate);
                    countUpdate.incrementAndGet();
                });

                updateResult = psUpdate.executeBatch();
                logger.info("SQL - {} resulted in {} updates", updateSql, updateResult);
            }

            // if there are not updated fields, let's insert them
            if (stream(updateResult).anyMatch(value -> value == 0)) {
                String insertSql = dbDialect.getInsertSql(tableName, keyFields, nonKeyFields);
                logger.info("update - insert SQL {}", insertSql);

                try (PreparedStatement psInsert = conn.prepareStatement(insertSql)) {
                    PreparedStatementBinder binderInsert = new PreparedStatementBinder(psInsert).withLoggerPrefix(logger.getPrefix());
                    batchInsert(schema, batchRows, batchRowsIds, fieldsForInsert, psInsert, binderInsert, updateResult);
                }
            }

            if (!dbDialect.isAutoCommit()) {
                conn.commit();
            }
            updateRecordMetric(dataStream, metricsByRunId);
        }
    }

    @SneakyThrows
    private void batchInsert(
            Schema schema,
            LinkedHashMap<List<Object>, Map<String, Object>> batchRows,
            HashMap<List<Object>, Integer> batchRowsIds,
            List<String> fieldsForInsert,
            PreparedStatement psInsert,
            PreparedStatementBinder binderInsert,
            int[] updateResult
    ) {
        int countInsert = 0;
        for (Map.Entry<List<Object>, Map<String, Object>> entry : batchRows.entrySet()) {
            Integer rowId = batchRowsIds.get(entry.getKey());
            boolean needInsert = (updateResult[rowId] == 0);
            if (needInsert) {
                Map<String, Object> rowData = batchRows.get(entry.getKey());
                binderInsert.bindRecord(rowData, schema, fieldsForInsert);
                countInsert++;
            }
        }
        batchRows.clear();
        batchRowsIds.clear();
        if (countInsert > 0) {
            psInsert.executeBatch();
        }
    }

    public static List<Object> extractPrimaryKey(List<String> keyFields, Map<String, Object> dataToOutput) {
        return keyFields.stream().map(dataToOutput::get).collect(toList());
    }

    @SneakyThrows
    public void doTruncate(
            Collection<NexlaMessage> records,
            String tableName,
            Map<Long, RecordMetric> metricsByRunId,
            BaseSinkTask.ExceptionHandler handler,
            JdbcSinkConnectorConfig config
    ) {
        String truncateSql = dbDialect.getTruncateSql(tableName);
        logger.info("TRUNCATE SQL: {}", truncateSql);

        try (Connection conn = connProvider.get()) {
            try (PreparedStatement ps = conn.prepareStatement(truncateSql)) {
                ps.execute();
            }
            if (!dbDialect.isAutoCommit()) {
                conn.commit();
            }
            updateRecordMetric(records, metricsByRunId);
        } catch (Exception e) {
            logger.error("Error during truncate process.", e);
            checkAndRethrow(e, config.stopOnError);

            if (tryFallbackToSingleRecordMode(e, config)) {
                Exception exception = null;
                int exceptionCount = 0;

                for (NexlaMessage record : records) {
                    try (Connection conn = connProvider.get()) {
                        try (PreparedStatement ps = conn.prepareStatement(truncateSql)) {
                            ps.execute();
                        }
                        if (!dbDialect.isAutoCommit()) {
                            conn.commit();
                        }
                        updateRecordMetric(singletonList(record), metricsByRunId);
                    } catch (Exception re) {
                        exception = re;
                        exceptionCount++;
                        Long runId = 0L;
                        if (record.getNexlaMetaData() != null && record.getNexlaMetaData().getTags() != null && record.getNexlaMetaData().getTags().get("originalMessageRunId") != null)
                        {
                            runId = (Long) record.getNexlaMetaData().getTags().get("originalMessageRunId");
                        }
                        RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());

                        recordMetric.quarantineMessages.add(quarantineMessage(record, re.getMessage()));
                        recordMetric.errorRecords.incrementAndGet();
                    }
                }
                if (exceptionCount > 0) {
                    handler.handleException(exception, exceptionCount, config.table);
                    handler.handleMonitoring(exception);
                }
            } else {
                handleBatchException(records, metricsByRunId, handler, config, e);
            }
        }
    }

    @SneakyThrows
    public void doInsert(
            Collection<NexlaMessage> records,
            String tableName,
            List<String> keyFields,
            List<String> nonKeyFields,
            List<String> allFieldsOrdered,
            Map<Long, RecordMetric> metricsByRunId,
            Schema schema,
            BaseSinkTask.ExceptionHandler handler,
            JdbcSinkConnectorConfig config
    ) {
        String insertSql = dbDialect.getInsertSql(tableName, keyFields, nonKeyFields);
        logger.info("Insert - insert SQL {}", insertSql);

        try (Connection conn = connProvider.get()) {
            bindAndExecBatch(conn, schema, records, insertSql, allFieldsOrdered, metricsByRunId);
            if (!dbDialect.isAutoCommit()) {
                conn.commit();
            }
        } catch (Exception e) {
            logger.error("Error during insert process.", e);
            checkAndRethrow(e, config.stopOnError);

            if (tryFallbackToSingleRecordMode(e, config)) {
                bindAndExecSingle(records, insertSql, allFieldsOrdered, metricsByRunId, schema, handler, config);
            } else {
                handleBatchException(records, metricsByRunId, handler, config, e);
            }
        }
    }

    @SneakyThrows
    private void bindAndExecBatch(
            Connection connection,
            Schema schema,
            Collection<NexlaMessage> records,
            String sql,
            List<String> allFieldsOrdered,
            Map<Long, RecordMetric> metricsByRunId
    ) {
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            PreparedStatementBinder binder = new PreparedStatementBinder(ps).withLoggerPrefix(logger.getPrefix());
            records.forEach(dataToOutput -> binder.bindRecord(dataToOutput.getRawMessage(), schema, allFieldsOrdered));
            ps.executeBatch();
            updateRecordMetric(records, metricsByRunId);
        }
    }

    @SneakyThrows
    private void bindAndExecSingle(
            Collection<NexlaMessage> records,
            String query,
            List<String> allFieldsOrdered,
            Map<Long, RecordMetric> metricsByRunId,
            Schema schema,
            BaseSinkTask.ExceptionHandler handler,
            JdbcSinkConnectorConfig config
    ) {
        Exception exception = null;
        int exceptionCount = 0;
        for (NexlaMessage record : records) {
            try (Connection conn = connProvider.get()) {
                bindAndExecBatch(conn, schema, singletonList(record), query, allFieldsOrdered, metricsByRunId);
                if (!dbDialect.isAutoCommit()) {
                    conn.commit();
                }
            } catch (Exception re) {
                exception = re;
                exceptionCount++;
                Long runId = 0L;
                if (record.getNexlaMetaData() != null && record.getNexlaMetaData().getTags() != null && record.getNexlaMetaData().getTags().get("originalMessageRunId") != null)
                {
                    runId = (Long) record.getNexlaMetaData().getTags().get("originalMessageRunId");
                }
                RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());

                recordMetric.quarantineMessages.add(RecordMetric.quarantineMessage(record, re.getMessage()));
                recordMetric.errorRecords.incrementAndGet();
            }
        }
        if (exceptionCount > 0) {
            handler.handleException(exception, exceptionCount, config.table);
            handler.handleMonitoring(exception);
        }
    }

    @SneakyThrows
    public void doDelete(
            Collection<NexlaMessage> records,
            String tableName,
            List<String> keyFields,
            List<String> nonKeyFields,
            Map<Long, RecordMetric> metricsByRunId,
            Schema schema,
            BaseSinkTask.ExceptionHandler handler,
            JdbcSinkConnectorConfig config
    ) {
        try {
            doDeleteInternal(schema, records, tableName, keyFields, nonKeyFields, metricsByRunId);
        } catch (Exception e) {
            logger.error("Error during delete process.", e);
            checkAndRethrow(e, config.stopOnError);

            if (tryFallbackToSingleRecordMode(e, config)) {
                Exception exception = null;
                int exceptionCount = 0;

                for (NexlaMessage record : records) {
                    try {
                        doDeleteInternal(schema, singletonList(record), tableName, keyFields, nonKeyFields, metricsByRunId);
                    } catch (Exception re) {
                        exception = re;
                        exceptionCount++;
                        Long runId = 0L;
                        if (record.getNexlaMetaData() != null && record.getNexlaMetaData().getTags() != null && record.getNexlaMetaData().getTags().get("originalMessageRunId") != null)
                        {
                            runId = (Long) record.getNexlaMetaData().getTags().get("originalMessageRunId");
                        }
                        RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());

                        recordMetric.quarantineMessages.add(quarantineMessage(record, re.getMessage()));
                        recordMetric.errorRecords.incrementAndGet();
                    }
                }
                if (exceptionCount > 0) {
                    handler.handleException(exception, exceptionCount, config.table);
                    handler.handleMonitoring(exception);
                }
            } else {
                handleBatchException(records, metricsByRunId, handler, config, e);
            }
        }
    }

    private void doDeleteInternal(
            Schema schema,
            Collection<NexlaMessage> dataStream,
            String tableName,
            List<String> keyFields,
            List<String> nonKeyFields,
            Map<Long, RecordMetric> metricsByRunId
    ) throws Exception {

        String deleteSql = keyFields.isEmpty() ? dbDialect.getDeleteSql(tableName, nonKeyFields) : dbDialect.getDeleteSql(tableName, keyFields);
        logger.info("DELETE SQL: {}", deleteSql);

        final int[] deleteResult;

        try (Connection conn = connProvider.get()) {

            try (PreparedStatement ps = conn.prepareStatement(deleteSql)) {
                PreparedStatementBinder binder = new PreparedStatementBinder(ps).withLoggerPrefix(logger.getPrefix());

                dataStream.forEach(dataToOutput -> {
                    LinkedHashMap<String, Object> dataToOutputMap = dataToOutput.getRawMessage();
                    if (keyFields.isEmpty()) {
                        binder.bindRecord(dataToOutputMap, schema, nonKeyFields);
                    } else {
                        binder.bindRecord(dataToOutputMap, schema, keyFields);
                    }
                });

                deleteResult = ps.executeBatch();
                logger.info("SQL - {} resulted in {} deletes", deleteSql, deleteResult);
            }

            if (!dbDialect.isAutoCommit()) {
                conn.commit();
            }
            updateRecordMetric(dataStream, metricsByRunId);
        }
    }

    private void updateRecordMetric(Collection<NexlaMessage> records, Map<Long, RecordMetric> metricsByRunId) {
        for (NexlaMessage record : records) {
            Long runId = 0L;
            if (record.getNexlaMetaData() != null && record.getNexlaMetaData().getTags() != null && record.getNexlaMetaData().getTags().get("originalMessageRunId") != null)
            {
                runId = (Long) record.getNexlaMetaData().getTags().get("originalMessageRunId");
            }
            RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());

            recordMetric.sentBytesTotal.addAndGet(MetricUtils.calcBytes(record.toJsonString()));
            recordMetric.sentRecordsTotal.incrementAndGet();
        }
    }

    private void checkAndRethrow(Exception e, boolean stopOnError) throws Exception {
        boolean shouldRethrow = SqlConnectorService.shouldRethrow(e);
        if (stopOnError || shouldRethrow) {
            String warnMessage = String.format("Rethrowing exception '%s'. stopOnError: %s. shouldRethrow: %s",
              e.getMessage(), stopOnError, shouldRethrow);
            logger.warn(warnMessage);
            throw e;
        }
    }

    private boolean tryFallbackToSingleRecordMode(Exception exp, JdbcSinkConnectorConfig config) {
        if (config.fallbackSingleRecordMode) {
            logger.warn("Error occurred in batch mode, falling back to single record mode", exp);
            return true;
        }
        return false;
    }

    private void handleBatchException(Collection<NexlaMessage> messages, Map<Long, RecordMetric> metricsByRunId,
                                      BaseSinkTask.ExceptionHandler handler, JdbcSinkConnectorConfig config,
                                      Exception exception) {
        for (NexlaMessage message: messages){
            Long runId = 0L;
            if (message.getNexlaMetaData() != null && message.getNexlaMetaData().getTags() != null && message.getNexlaMetaData().getTags().get("originalMessageRunId") != null)
            {
                runId = (Long) message.getNexlaMetaData().getTags().get("originalMessageRunId");
            }
            RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());

            recordMetric.quarantineMessages.add(RecordMetric.quarantineMessage(message, exception.getMessage()));
            recordMetric.errorRecords.incrementAndGet();
        }
        handler.handleException(exception, messages.size(), config.table);
        handler.handleMonitoring(exception);
    }
}
