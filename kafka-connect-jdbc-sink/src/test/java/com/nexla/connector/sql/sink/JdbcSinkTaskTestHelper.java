package com.nexla.connector.sql.sink;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.admin.client.pipeline.PDataSource;
import com.nexla.admin.client.pipeline.Pipeline;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaConstants;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.tracker.Tracker;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.probe.sql.SqlConnectorService;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.DialectRegistry;
import lombok.SneakyThrows;
import org.mockito.Mockito;
import org.testcontainers.containers.JdbcDatabaseContainer;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.google.common.collect.ImmutableMap.of;
import static com.nexla.common.NexlaConstants.CREDS_ENC;
import static com.nexla.common.NexlaConstants.CREDS_ENC_IV;
import static com.nexla.common.NexlaConstants.LISTING_APP_SERVER_URL;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.PASSWORD;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.URL;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.USERNAME;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.MAPPING;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.PARALLELISM;
import static com.nexla.connector.properties.SqlConfigAccessor.INSERT_MODE;
import static com.nexla.connector.properties.SqlConfigAccessor.PRIMARY_KEY;
import static com.nexla.connector.properties.SqlConfigAccessor.TABLE;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;

class JdbcSinkTaskTestHelper {
	private final static String SCHEMA_NAME = "schemaname";
	private final static String DATABASE_NAME = "databasename";
	final static String TABLE_NAME = "output";

	public static final int SINK_ID_VALUE = 1;

	@SneakyThrows
	static void recreateTable(boolean schemaSet, MappingConfig mappingConfig, JdbcDatabaseContainer db,
							  String credsType, Map<String, String> props) {

		prepareTable(schemaSet, db, credsType, props);

		props.put(MAPPING, toJsonString(mappingConfig));
		JdbcSinkConnectorConfig abstractConfig = new JdbcSinkConnectorConfig(props);

		AdminApiClient client = mock(AdminApiClient.class);
		Map<String, Object> map = Maps.newHashMap();
		PDataSource dataSource = new PDataSource();

		dataSource.setConnectionType(ConnectionType.SQLSERVER);
		dataSource.setId(1);

		var dataSink = new DataSink();
		dataSink.setId(1);
		dataSink.setConnectionType(ConnectionType.SQLSERVER);
		dataSink.setSinkConfig(Map.of(PARALLELISM, "1"));
		dataSink.setStatus(ResourceStatus.ACTIVE);

		Pipeline pipeline = new Pipeline(Lists.newArrayList(), dataSource, dataSink);

		Mockito.when(client.getPipeline(anyInt())).thenReturn(Optional.of(pipeline));
		Mockito.when(client.getDataSink(1)).thenReturn(Optional.of(dataSink));

		SqlConnectorService sqlConnectorService = new SqlConnectorService(null, client);
		sqlConnectorService.createDestination(abstractConfig, Optional.empty());
	}

	@SneakyThrows
	static void prepareTable(boolean schemaSet, JdbcDatabaseContainer db, String credsType, Map<String, String> props) {
		String qualifiedName = TABLE_NAME;
		if (schemaSet) {
			createSchema(db, ConnectionType.fromString(credsType.toUpperCase()));
			props.put(JdbcAuthConfig.SCHEMA_NAME, SCHEMA_NAME);
			qualifiedName = getQualifiedName(credsType);
		}

		try (Connection connection = db.createConnection("")) {
			connection.setAutoCommit(false);
			connection.createStatement().execute("DROP TABLE IF EXISTS " + qualifiedName);
			connection.commit();
		}
	}

	@SneakyThrows
	static List<Map<String, String>> selectRows(JdbcDatabaseContainer db, boolean schemaSet, Set<String> columns, String credsType) {
		return selectRows(db, TABLE_NAME, schemaSet, columns, credsType);
	}

	@SneakyThrows
	static List<Map<String, String>> selectRows(JdbcDatabaseContainer db, String tableName, boolean schemaSet, Set<String> columns, String credsType) {
		List<Map<String, String>> rows = new ArrayList<>();
		String qualifiedName = schemaSet ? getQualifiedName(credsType) : tableName;
		try (
			Connection connection = db.createConnection("");
			ResultSet resultSet = connection.createStatement().executeQuery("SELECT * FROM " + qualifiedName)
		) {
			connection.setAutoCommit(false);
			try {
				while (resultSet.next()) {
					Map<String, String> row = new LinkedHashMap<>();
					for (String column : columns) {
						row.put(column, resultSet.getObject(column).toString());
					}
					rows.add(row);
				}
			} finally {
				connection.rollback();
			}
		}
		return rows;
	}

	static NexlaMessage createMessage(LinkedHashMap<String, Object> rawMessage) {
		NexlaMessage message = new NexlaMessage(rawMessage);
		NexlaMetaData nexlaMetaData = new NexlaMetaData();
		nexlaMetaData.setRunId(1L);
		message.setNexlaMetaData(nexlaMetaData);
		nexlaMetaData.setTrackerId(Tracker.parse("5010:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010"));
		return message;
	}

	private static String getQualifiedName(String credsType) {
		ConnectionType connectionType = ConnectionType.fromString(credsType.toUpperCase());
		return DialectRegistry.getInstance().fromConnectionString(connectionType, connectionType.getConnectionStringPrefix())
				.getQualifiedTableName(TABLE_NAME, SCHEMA_NAME, DATABASE_NAME);
	}

	@SneakyThrows
	private static void createSchema(JdbcDatabaseContainer db, ConnectionType connectionType) {
		DbDialect dbDialect = DialectRegistry.getInstance().fromConnectionString(connectionType, connectionType.getConnectionStringPrefix());
		try (Connection connection = db.createConnection("")) {
			connection.setAutoCommit(false);
			connection.createStatement().execute("CREATE SCHEMA IF NOT EXISTS " + dbDialect.q(SCHEMA_NAME));
			connection.commit();
		}
	}

	static Map<String, String> getProperties(JdbcDatabaseContainer db, String credsType) {
		Map<String, String> properties = new HashMap<>(
			of(TABLE, TABLE_NAME,
				PRIMARY_KEY, "id",
				NexlaConstants.CREDENTIALS_TYPE, credsType,
				DATABASE_NAME, db.getDatabaseName(),
				URL, db.getJdbcUrl())
		);
		properties.put(PARALLELISM, "1");
		properties.put(USERNAME, db.getUsername());
		properties.put(PASSWORD, db.getPassword());
		properties.put(SINK_ID, SINK_ID_VALUE + "");
		properties.put(CREDS_ENC, "1");
		properties.put(CREDS_ENC_IV, "1");
		properties.put(UNIT_TEST, "true");
		properties.put(INSERT_MODE, "INSERT");
		properties.put(LISTING_APP_SERVER_URL, "123");
		return properties;
	}
}
