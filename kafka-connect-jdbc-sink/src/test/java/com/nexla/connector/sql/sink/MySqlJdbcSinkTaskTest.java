package com.nexla.connector.sql.sink;

import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.pipeline.PDataSource;
import com.nexla.admin.client.pipeline.PDataset;
import com.nexla.admin.client.pipeline.Pipeline;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.exception.NexlaQuarantineMessage;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.common.schema.SchemaType;
import com.nexla.common.sink.TopicPartition;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connect.common.offsets.OffsetsTracker;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.listing.client.ListingClient;
import com.nexla.test.IntegrationTests;
import connect.jdbc.sink.dialect.AutomaticBinding;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.MySqlDialect;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.connect.errors.RetriableException;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.junit.*;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.MySQLContainer;
import org.testcontainers.utility.DockerImageName;
import org.junit.After;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.google.common.collect.ImmutableMap.of;
import static com.google.common.collect.Sets.newHashSet;
import static com.nexla.common.NexlaConstants.EXCEPTION_TRACE;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.common.NexlaNamingUtils.getQuarantineTopic;
import static com.nexla.common.Resource.sink;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.StreamUtils.lhm;
import static com.nexla.common.StreamUtils.map;
import static com.nexla.common.metrics.NexlaRawMetric.HASH;
import static com.nexla.common.metrics.NexlaRawMetric.NAME;
import static com.nexla.common.metrics.NexlaRawMetric.RECORDS;
import static com.nexla.common.metrics.NexlaRawMetric.SIZE;
import static com.nexla.connect.common.DbTestUtils.newDb;
import static com.nexla.connector.config.BaseConnectorConfig.METRICS_TOPIC;
import static com.nexla.connector.config.MappingConfig.MODE_MANUAL;
import static com.nexla.connector.config.SinkConnectorConfig.DEFAULT_MAPPING;
import static com.nexla.connector.config.SinkConnectorConfig.MAPPING;
import static com.nexla.connector.config.SinkConnectorConfig.TRACKER_ENCRYPTION_ENABLED;
import static com.nexla.connector.config.SinkConnectorConfig.TRACKER_ENCRYPTION_KEY;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.USERNAME;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.*;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.INSERT;
import static com.nexla.connector.properties.SqlConfigAccessor.INSERT_MODE;
import static com.nexla.connector.properties.SqlConfigAccessor.PRIMARY_KEY;
import static com.nexla.connector.sql.sink.JdbcSinkTaskTestHelper.SINK_ID_VALUE;
import static com.nexla.connector.sql.sink.JdbcSinkTaskTestHelper.TABLE_NAME;
import static com.nexla.connector.sql.sink.JdbcSinkTaskTestHelper.createMessage;
import static com.nexla.connector.sql.sink.JdbcSinkTaskTestHelper.getProperties;
import static com.nexla.connector.sql.sink.JdbcSinkTaskTestHelper.prepareTable;
import static com.nexla.connector.sql.sink.JdbcSinkTaskTestHelper.recreateTable;
import static com.nexla.connector.sql.sink.JdbcSinkTaskTestHelper.selectRows;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyMap;
import static java.util.Collections.emptySet;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Category(IntegrationTests.class)
public class MySqlJdbcSinkTaskTest extends BaseKafkaTest {

	private static final String CREDS_TYPE = "mysql";
	private JdbcSinkTask task;
    public static KafkaContainer kafka = new KafkaContainer("7.2.11");
	// to deal with the dreaded Specified key was too long; max key length is 767 bytes, we use mysql 5.7 here
	public static final DockerImageName MYSQL_57 = DockerImageName.parse("debezium/example-mysql:1.2").asCompatibleSubstituteFor("mysql");
	public static MySQLContainer<?> mysql = new MySQLContainer<>(MYSQL_57)
			.withUsername("mysqluser")
			.withPassword("debezium");

    private Map<String, String> properties;

    @BeforeClass
    public static void startUp() {
		mysql.withReuse(true);
        kafka.withReuse(true);
		mysql.start();
        kafka.start();
        init(kafka);
    }

    @AfterClass
    public static void tearDown() {
		mysql.stop();
        kafka.stop();
    }

	@Before
	public void onBefore() {
		AdminApiClient adminApiClient = mock(AdminApiClient.class);
		AdminApiClientBuilder.INSTANCE = adminApiClient;
		List<PDataset> dataSets = List.of(new PDataset(24, 32, null));
		PDataSource ds = new PDataSource();
		DataSink sink = new DataSink();
		when(adminApiClient.getPipeline(anyInt())).thenReturn(Optional.of(new Pipeline(dataSets, ds, sink)));

		when(adminApiClient.getPipeline(any(Integer.class))).thenReturn(Optional.of(
				new Pipeline(null, null, null)));
		DataSink dataSink = new DataSink();
		dataSink.setId(1);
		dataSink.setConnectionType(ConnectionType.MYSQL);
		dataSink.setSinkConfig(Map.of(PARALLELISM, "1"));
		dataSink.setStatus(ResourceStatus.ACTIVE);
		when(adminApiClient.getDataSink(any(Integer.class))).thenReturn(Optional.of(dataSink));

		this.task = new JdbcSinkTask() {
			@Override
			public void doStart() {
				super.doStart();
				this.listingClient = mock(ListingClient.class);
				this.offsetsSender = Optional.of(mock(OffsetsTracker.class));

			}
		};
		SinkTaskContext taskContext = mock(SinkTaskContext.class);
		when(taskContext.assignment()).thenReturn(emptySet());
		task.initialize(taskContext);

		this.runBeforeTopicReading = Optional.of(() -> task.getControlMessageProducer().flush());
		this.properties = getProperties(mysql, CREDS_TYPE);
	}

	@After
	@SneakyThrows
	public void after() {
		if (task != null
				&& task.getControlMessageProducer() != null) {
			task.getControlMessageProducer().close();
			task.stop();
		}
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void nullPrimaryKeyTest() {
		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("description", map("description", DEFAULT_MAPPING));

		recreateTable(false, mappingConfig, mysql, CREDS_TYPE, properties);

		mappingConfig.setMode(MappingConfig.MODE_AUTO);

		properties.put(INSERT_MODE, "insert");
		properties.put(MAPPING, toJsonString(mappingConfig));
		properties.put(STOP_ON_ERROR, "true");
		task.start(properties);

		LinkedHashMap<String, Object> messageMap = Maps.newLinkedHashMap();
		messageMap.put("id", null);
		messageMap.put("description", "test");
		NexlaMessage message = createMessage(messageMap);

		try {
			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));
		} catch (Exception e) {
			assertEquals("java.sql.BatchUpdateException: Column 'id' cannot be null", e.getCause().getMessage());
			task.stop();
			return;
		}

		fail("Did not throw an exception on null primary key");
		task.stop();
	}

	@SneakyThrows
	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putInsert() {

		withConsumer((metricsTopic, metricsConsumer) -> {

			MappingConfig mappingConfig = new MappingConfig();
			mappingConfig.setMode(MODE_MANUAL);
			mappingConfig.getMapping().put("ids", map("id", DEFAULT_MAPPING));
			mappingConfig.getMapping().put("descriptions", map("description", DEFAULT_MAPPING, "desc", DEFAULT_MAPPING));
			mappingConfig.setTrackerMode(Tracker.TrackerMode.RECORD);
			mappingConfig.setTrackerFieldName("mytracker");

			recreateTable(false, mappingConfig, mysql, CREDS_TYPE, properties);

			mappingConfig.setMode(MappingConfig.MODE_AUTO);
			mappingConfig.getExcludes().add("excluded");

			properties.put(INSERT_MODE, "insert");
			properties.put(MAPPING, toJsonString(mappingConfig));
			properties.put(METRICS_TOPIC, metricsTopic);

			task.start(properties);

			NexlaMessage message = createMessage(new LinkedHashMap<>(
				of("ids", "1", "descriptions", "test", "excluded", "test excluded")));

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));

			List<Map<String, String>> result = selectRows(mysql, false, newHashSet("id", "description", "desc", "mytracker"), CREDS_TYPE);
			String tracker = result.get(0).get("mytracker");

			assertTrue(tracker.length() > 15);

			assertEquals(singletonList(of("id", "1", "description", "test", "desc", "test", "mytracker", tracker)), result);
			List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);

			assertNexlaMetric(metrics.get(0), "output", 695, 1, 1);

			task.stop();

		});
	}

	@SneakyThrows
	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putInsertCaps() {
		withConsumer((metricsTopic, metricsConsumer) -> {

			MappingConfig mappingConfig = new MappingConfig();
			mappingConfig.setMode(MODE_MANUAL);
			mappingConfig.getMapping().put("ids", map("id", DEFAULT_MAPPING));
			mappingConfig.getMapping().put("descriptions", map("DESCRIPTION", DEFAULT_MAPPING, "DESC", DEFAULT_MAPPING));

			recreateTable(false, mappingConfig, mysql, CREDS_TYPE, properties);

			properties.put(INSERT_MODE, INSERT.toString());
			properties.put(MAPPING, toJsonString(mappingConfig));
			properties.put(TRACKER_ENCRYPTION_ENABLED, "true");
			properties.put(TRACKER_ENCRYPTION_KEY, "9Rz+VE1VzxS7wvUpg8hhsbrt8vrPLUKBtDNd8N/wKbg=");
			properties.put(METRICS_TOPIC, metricsTopic);

			task.start(properties);

			NexlaMessage message = createMessage(new LinkedHashMap<>(
				of("ids", "1",
					"descriptions", "park")));

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(message), 1)));
			assertEquals(singletonList(of("id", "1", "DESCRIPTION", "park", "DESC", "park")),
				selectRows(mysql, false, newHashSet("id", "DESCRIPTION", "DESC"), CREDS_TYPE));
			List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);

			assertNexlaMetric(metrics.get(0), "output", 587, 1, 1);

			task.stop();

		});
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putUpsert() {
		properties.put(INSERT_MODE, "upsert");

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("description", map("description", DEFAULT_MAPPING));
		recreateTable(false, mappingConfig, mysql, CREDS_TYPE, properties);
		task.start(properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "test")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));
		assertEquals(singletonList(of("id", "1", "description", "test")),
			selectRows(mysql, false, newHashSet("id", "description"), CREDS_TYPE));

		verify(task.offsetsSender.get()).updateSinkOffsets(eq(SINK_ID_VALUE), eq(lhm(new TopicPartition("test-topic", 1), 1L)));

		message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "changed")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 2)));
		assertEquals(singletonList(of("id", "1", "description", "changed")),
			selectRows(mysql, false, newHashSet("id", "description"), CREDS_TYPE));

		verify(task.offsetsSender.get()).updateSinkOffsets(eq(SINK_ID_VALUE), eq(lhm(new TopicPartition("test-topic", 1), 2L)));

		task.stop();
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putUpsertPkCaps() {
		properties.put(INSERT_MODE, "upsert");
		properties.put(PRIMARY_KEY, "ID");

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("ID", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("description", map("DESCRIPTION", DEFAULT_MAPPING));
		recreateTable(false, mappingConfig, mysql, CREDS_TYPE, properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "test")));
		task.start(properties);

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));
		assertEquals(singletonList(of("id", "1", "description", "test")),
			selectRows(mysql, false, newHashSet("id", "description"), CREDS_TYPE));

		message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "changed")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 2)));
		assertEquals(singletonList(of("ID", "1", "DESCRIPTION", "changed")),
			selectRows(mysql, false, newHashSet("ID", "DESCRIPTION"), CREDS_TYPE));
		task.stop();
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putUpsertWithNullsDisabled() {
		properties.put(INSERT_MODE, "upsert");
		properties.put(UPSERT_NULLS, "false");

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("description", map("description", DEFAULT_MAPPING));
		recreateTable(false, mappingConfig, mysql, CREDS_TYPE, properties);
		task.start(properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "test")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));
		assertEquals(singletonList(of("id", "1", "description", "test")),
			selectRows(mysql, false, newHashSet("id", "description"), CREDS_TYPE));

		message = createMessage(new LinkedHashMap<>(
			map("id", "1",
				"description", null)));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 2)));
		assertEquals(singletonList(of("id", "1", "description", "test")),
			selectRows(mysql, false, newHashSet("id", "description"), CREDS_TYPE));
		task.stop();
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void dedupUpsert() {
		properties.put(INSERT_MODE, "upsert");
		properties.put(PRIMARY_KEY, "id");
		task.start(properties);
		NexlaMetaData metaData = new NexlaMetaData();
		LinkedHashMap<String, Object> map1 = new LinkedHashMap<>(of("id", "1", "description", "test1"));
		LinkedHashMap<String, Object> map2 = new LinkedHashMap<>(of("id", "2", "description", "test2"));
		LinkedHashMap<String, Object> map3 = new LinkedHashMap<>(of("id", "4", "description", "test3"));
		LinkedHashMap<String, Object> map4 = new LinkedHashMap<>(of("id", "4", "description", "test4"));

		List<NexlaMessageContext> messages = asList(
			nexlaMessage(metaData, map1),
			nexlaMessage(metaData, map2),
			nexlaMessage(metaData, map3),
			nexlaMessage(metaData, map4)
		);

		List<LinkedHashMap<String, Object>> messagesExpected = asList(
			map1,
			map2,
			map4
		);

		List<LinkedHashMap<String, Object>> collect = task
			.deduplicate(StreamEx.of(messages), task.config)
			.map(x -> x.getMapped().getRawMessage())
			.collect(toList());

		assertEquals(messagesExpected, collect);
		task.stop();
	}

	public static NexlaMessageContext nexlaMessage(NexlaMetaData metaData, LinkedHashMap<String, Object> map1) {
		var nm = new NexlaMessage(map1, metaData);
		return new NexlaMessageContext(nm, nm, new TopicPartition("1", 1), 1L);
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void dedupUpsertWithCaps() {
		properties.put(INSERT_MODE, "upsert");
		properties.put(PRIMARY_KEY, "ID,id");
		task.start(properties);

		NexlaMetaData metaData = new NexlaMetaData();
		LinkedHashMap<String, Object> map1 = new LinkedHashMap<>(of("ID", "1", "description", "test1"));
		LinkedHashMap<String, Object> map2 = new LinkedHashMap<>(of("ID", "2", "description", "test2"));
		LinkedHashMap<String, Object> map3 = new LinkedHashMap<>(of("ID", "4", "description", "test3"));
		LinkedHashMap<String, Object> map4 = new LinkedHashMap<>(of("ID", "4", "description", "test4"));
		LinkedHashMap<String, Object> map5 = new LinkedHashMap<>(of("id", "1", "description", "test5"));
		LinkedHashMap<String, Object> map6 = new LinkedHashMap<>(of("id", "2", "description", "test6"));
		LinkedHashMap<String, Object> map7 = new LinkedHashMap<>(of("id", "4", "description", "test7"));
		LinkedHashMap<String, Object> map8 = new LinkedHashMap<>(of("id", "4", "description", "test8"));


		List<NexlaMessageContext> messages = asList(
			nexlaMessage(metaData, map1),
			nexlaMessage(metaData, map2),
			nexlaMessage(metaData, map3),
			nexlaMessage(metaData, map4),
			nexlaMessage(metaData, map5),
			nexlaMessage(metaData, map6),
			nexlaMessage(metaData, map7),
			nexlaMessage(metaData, map8)
		);

		List<LinkedHashMap<String, Object>> messagesExpected = asList(
			map1,
			map2,
			map4,
			map5,
			map6,
			map8
		);

		List<LinkedHashMap<String, Object>> collect = task
			.deduplicate(StreamEx.of(messages), task.config)
			.map(x -> x.getMapped().getRawMessage())
			.collect(toList());

		assertEquals(messagesExpected, collect);
		task.stop();
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void closedConnectionBeforeStart() {
		properties.put(INSERT_MODE, INSERT.toString());
		properties.put(USERNAME, ""); // simulate connection drop by giving incorrect username
		task.start(properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "park")));

		try {
			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(message), 1)));
		} catch (RetriableException e) {
			task.stop();
		} catch (Exception e) {
			task.stop();
			fail("Wrong exception");
		}
	}

	@SneakyThrows
	//@Test FIXME Deactivating tests in release/v3.2.0
	public void quarantineRecordsTest() {
		int sinkId = new Random().nextInt(10000);
		String quarantineTopic = getQuarantineTopic(sink(sinkId), Optional.empty());

		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer(quarantineTopic, quarantineConsumer -> {

				MappingConfig mappingConfig = new MappingConfig();
				mappingConfig.setMode(MODE_MANUAL);
				mappingConfig.getMapping().put("ids", map("id", DEFAULT_MAPPING));
				mappingConfig.getMapping().put("descriptions", map("description", DEFAULT_MAPPING));

				recreateTable(false, mappingConfig, mysql, CREDS_TYPE, properties);

				mappingConfig.setMode(MappingConfig.MODE_AUTO);
				mappingConfig.getExcludes().add("excluded");

				properties.put(INSERT_MODE, "insert");
				properties.put(MAPPING, toJsonString(mappingConfig));
				properties.put(METRICS_TOPIC, metricsTopic);
				properties.put(SINK_ID, sinkId + "");

				task.start(properties);

				NexlaMessage message = createMessage(new LinkedHashMap<>(
					of("descriptions", "test", "excluded", "test excluded")));

				task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));

				List<NexlaQuarantineMessage> quarantine = readQuarantine(quarantineConsumer);
				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);

				assertEquals(1, quarantine.size());
				assertEquals(0, metrics.get(0).getFields().get(RECORDS));
				task.stop();
			});

		});
	}

	@SneakyThrows
	//@Test FIXME Deactivating tests in release/v3.2.0
	public void noQuarantineRecordsTest() {
		int sinkId = new Random().nextInt(10000);
		String quarantineTopic = getQuarantineTopic(sink(sinkId), Optional.empty());

		withConsumer(quarantineTopic, quarantineConsumer -> {

			MappingConfig mappingConfig = new MappingConfig();
			mappingConfig.setMode(MODE_MANUAL);
			mappingConfig.getMapping().put("ids", map("id", DEFAULT_MAPPING));
			mappingConfig.getMapping().put("descriptions", map("description", DEFAULT_MAPPING));

			recreateTable(false, mappingConfig, mysql, CREDS_TYPE, properties);

			mappingConfig.setMode(MappingConfig.MODE_AUTO);
			mappingConfig.getExcludes().add("excluded");

			properties.put(INSERT_MODE, "insert");
			properties.put(MAPPING, toJsonString(mappingConfig));
			properties.put(USERNAME, "");
			properties.put(SINK_ID, sinkId + "");

			task.start(properties);

			NexlaMessage message = createMessage(new LinkedHashMap<>(
				of("id", "1",
					"description", "park")));

			try {
				task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));
			} catch (RetriableException e) {
				List<NexlaQuarantineMessage> quarantine = readQuarantine(quarantineConsumer);
				assertEquals(0, quarantine.size());
				task.stop();
			}
		});
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void checkException() throws SQLException {

		prepareTable(false, mysql, CREDS_TYPE, properties);
		DbDialect dbDialect = new MySqlDialect();
		String qualifiedName = dbDialect.getQualifiedTableName(TABLE_NAME, null, null);

		Map<String, SchemaType> typeBinding = Maps.newHashMap();
		typeBinding.put("id", SchemaType.INTEGER);
		typeBinding.put("description", SchemaType.STRING);

		String createSql = dbDialect.getCreateSql(qualifiedName, singletonList("id"), singletonList("description"),
				() -> new AutomaticBinding(typeBinding, emptyMap()), new MappingConfig(), new JdbcSinkConnectorConfig(properties));

		try (Connection connection = mysql.createConnection("")) {
			connection.setAutoCommit(false);
			connection.prepareStatement(createSql).execute();
			connection.commit();
		}

		properties.put(INSERT_MODE, "insert");
		properties.put(STOP_ON_ERROR, "true");
		task.start(properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("id", "jim",
				"description", "park")));

		try {
			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(message), 1)));
			fail("Did not throw exception");
		} catch (Exception e) {
			assertTrue(e.getCause().getMessage().contains("expected column \"id\" to be of format \"int32\""));
		}
	}

	private void assertNexlaMetric(NexlaRawMetric metric, String tableName, Integer size, Integer records, Integer sinkId) {
		Map<String, Object> expectedMetrics = new HashMap<>(
			of("Path", tableName,
				"Size", size,
				"Records", records,
				"Resource ID", sinkId,
				"Resource Type", SINK)
		);
		expectedMetrics.put("Error Message", null);
		expectedMetrics.put("Hash", null);

		Map<String, Object> actualMetrics = new HashMap<>(
			of("Path", metric.getTags().get(NAME),
				"Size", metric.getFields().get(SIZE),
				"Records", metric.getFields().get(RECORDS),
				"Resource ID", metric.getResourceId(),
				"Resource Type", metric.getResourceType())
		);
		actualMetrics.put("Error Message", metric.getFields().get(EXCEPTION_TRACE));
		actualMetrics.put("Hash", metric.getFields().get(HASH));

		assertEquals(expectedMetrics, actualMetrics);
	}
}
