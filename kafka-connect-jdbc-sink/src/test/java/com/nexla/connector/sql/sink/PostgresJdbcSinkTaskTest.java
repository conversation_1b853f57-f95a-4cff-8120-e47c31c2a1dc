package com.nexla.connector.sql.sink;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.admin.client.pipeline.PDataSource;
import com.nexla.admin.client.pipeline.PDataset;
import com.nexla.admin.client.pipeline.Pipeline;
import com.nexla.common.NexlaConstants;
import com.nexla.common.NexlaMessage;
import com.nexla.common.exception.NexlaQuarantineMessage;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connect.common.offsets.OffsetsTracker;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.listing.client.ListingClient;
import com.nexla.test.IntegrationTests;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DialectRegistry;
import lombok.SneakyThrows;
import org.apache.kafka.connect.errors.RetriableException;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.junit.*;
import org.junit.experimental.categories.Category;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.utility.DockerImageName;

import java.sql.Connection;
import java.util.*;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.google.common.collect.ImmutableMap.of;
import static com.google.common.collect.Sets.newHashSet;
import static com.nexla.common.ConnectionType.POSTGRES;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.common.NexlaNamingUtils.getQuarantineTopic;
import static com.nexla.common.Resource.sink;
import static com.nexla.common.StreamUtils.map;
import static com.nexla.connector.config.MappingConfig.MODE_MANUAL;
import static com.nexla.connector.config.SinkConnectorConfig.DEFAULT_MAPPING;
import static com.nexla.connector.config.SinkConnectorConfig.MAPPING;
import static com.nexla.connector.config.jdbc.JdbcAuthConfig.PASSWORD;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.*;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.INSERT;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static com.nexla.connector.properties.SqlConfigAccessor.INSERT_MODE;
import static com.nexla.connector.properties.SqlConfigAccessor.PRIMARY_KEY;
import static com.nexla.connector.sql.sink.JdbcSinkTaskTestHelper.*;
import static com.nexla.probe.sql.SchemaUtils.selectSchema;
import static java.util.Arrays.asList;
import static java.util.Collections.emptySet;
import static java.util.Collections.singletonList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@Category(IntegrationTests.class)
//@Test FIXME Deactivating tests in release/v3.2.0containers
public class PostgresJdbcSinkTaskTest extends BaseKafkaTest {

	private static final String CREDSTYPE = "postgres";

	private JdbcSinkTask task;

	public static KafkaContainer kafka = new KafkaContainer("7.2.11");
	public static DockerImageName myImage = DockerImageName.parse("debezium/postgres:11").asCompatibleSubstituteFor("postgres");
	public static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>(myImage);

	private Map<String, String> properties;

	@BeforeClass
	public static void startUp() {
		postgres.withReuse(true);
		kafka.withReuse(true);
		postgres.start();
		kafka.start();
		init(kafka);
	}

	@AfterClass
	public static void tearDown() {
		postgres.stop();
		kafka.stop();
	}

	@Before
	public void onBefore() {
		AdminApiClient adminApiClient = mock(AdminApiClient.class);
		AdminApiClientBuilder.INSTANCE = adminApiClient;
		List<PDataset> dataSets = List.of(new PDataset(24, 32, null));
		PDataSource ds = new PDataSource();
		DataSink sink = new DataSink();
		when(adminApiClient.getPipeline(anyInt())).thenReturn(Optional.of(new Pipeline(dataSets, ds, sink)));

		when(adminApiClient.getPipeline(any(Integer.class))).thenReturn(Optional.of(
				new Pipeline(null, null, null)));
		DataSink dataSink = new DataSink();
		dataSink.setId(1);
		dataSink.setConnectionType(POSTGRES);
		dataSink.setSinkConfig(Map.of(PARALLELISM, "1"));
		dataSink.setStatus(ResourceStatus.ACTIVE);
		when(adminApiClient.getDataSink(any(Integer.class))).thenReturn(Optional.of(dataSink));

		this.task = new JdbcSinkTask() {
			@Override
			public void doStart() {
				super.doStart();
				this.listingClient = mock(ListingClient.class);
				this.offsetsSender = Optional.of(mock(OffsetsTracker.class));
			}
		};
		SinkTaskContext taskContext = mock(SinkTaskContext.class);
		when(taskContext.assignment()).thenReturn(emptySet());
		task.initialize(taskContext);

		this.runBeforeTopicReading = Optional.of(() -> task.getControlMessageProducer().flush());
		this.properties = getProperties(postgres, CREDSTYPE);
	}

	@After
	public void after() {
		// forcibly flush kafka stuff to release producers
		task.getControlMessageProducer().getTransport().flush();
		task.stop();
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putUpsertEmulation() {
		properties.put(INSERT_MODE, "upsert");
		properties.put(NexlaConstants.CREDENTIALS_TYPE, "redshift"); // redshift is just to use dialect, where MERGE is not supported natively
		properties.put(COPY_ALLOWED, "false");

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("description", map("description", DEFAULT_MAPPING));

		recreateTable(false, mappingConfig, postgres, CREDSTYPE, properties);
		task.start(properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "test")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));
		assertEquals(singletonList(of("id", "1", "description", "test")),
			selectRows(postgres, false, newHashSet("id", "description"), CREDSTYPE));

		message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"description", "changed")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 2)));
		assertEquals(singletonList(of(
			"id", "1",
			"description", "changed")), selectRows(postgres, false, newHashSet("id", "description"), CREDSTYPE));
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putUpsertEmulationSkipErrors() {
		properties.put(INSERT_MODE, "upsert");
		properties.put(NexlaConstants.CREDENTIALS_TYPE, "redshift");
		properties.put(COPY_ALLOWED, "false");
		properties.put(STOP_ON_ERROR, "false");

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("description", map("description", DEFAULT_MAPPING));

		recreateTable(false, mappingConfig, postgres, CREDSTYPE, properties);
		task.start(properties);

		NexlaMessage messageGood = createMessage(new LinkedHashMap<>(
			map("id", "1",
				"description", "testGood")));

		NexlaMessage messageMalformed = createMessage(new LinkedHashMap<>(
			map("id", null,
				"description", "testMalformed")));

		task.put(asList(
			new SinkRecord("test-topic", 1, null, null, null, toJsonString(messageMalformed), 1),
			new SinkRecord("test-topic", 1, null, null, null, toJsonString(messageGood), 2)));

		assertEquals(singletonList(
			of("id", "1", "description", "testGood")
		), selectRows(postgres, false, newHashSet("id", "description"), CREDSTYPE));
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putSkipErrors() {

		for (InsertMode mode : InsertMode.values()) {
			for (boolean stopOnError : asList(true, false)) {

				onBefore();

				MappingConfig mappingConfig = new MappingConfig();
				mappingConfig.setMode(MODE_MANUAL);
				mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
				mappingConfig.getMapping().put("description", map("description", DEFAULT_MAPPING));
				mappingConfig.getMapping().put("int_field", map("int_field", "int"));

				recreateTable(false, mappingConfig, postgres, CREDSTYPE, properties);

				try {
					properties.put(INSERT_MODE, mode.name());
					properties.put(COPY_ALLOWED, "false");
					properties.put(STOP_ON_ERROR, stopOnError + "");
					task.start(properties);

					NexlaMessage messageGood = createMessage(new LinkedHashMap<>(
							map("id", "1",
									"description", "testGood",
									"int_field", "1")));

					NexlaMessage messageMalformed = createMessage(new LinkedHashMap<>(
							map("id", "2",
									"description", "testMalformed",
									"int_field", "a")));

					task.put(asList(
						new SinkRecord("test-topic", 1, null, null, null, toJsonString(messageMalformed), 1),
						new SinkRecord("test-topic", 1, null, null, null, toJsonString(messageGood), 2)));

					assertEquals(singletonList(
						of("id", "1", "description", "testGood")
					), selectRows(postgres, false, newHashSet("id", "description"), CREDSTYPE));
					task.stop();

					if (stopOnError) {
						fail("it should fail");
					}

				} catch (Exception e) {
					if (!stopOnError) {
						fail("it should not fail");
					}
				}
			}
		}
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putInsertCaps() {
		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("ids", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("descriptions", map("DESCRIPTION", DEFAULT_MAPPING));

		recreateTable(false, mappingConfig, postgres, CREDSTYPE, properties);

		properties.put(INSERT_MODE, INSERT.toString());
		properties.put(MAPPING, toJsonString(mappingConfig));
		task.start(properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("ids", "1",
				"descriptions", "park")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
			toJsonString(message), 1)));
		assertEquals(singletonList(of("id", "1", "DESCRIPTION", "park")),
			selectRows(postgres, false, newHashSet("id", "DESCRIPTION"), CREDSTYPE));
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putUpsertWithNullsDisabled() {
		properties.put(INSERT_MODE, "upsert");
		properties.put(UPSERT_NULLS, "false");

		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("description", map("description", DEFAULT_MAPPING));

		recreateTable(false, mappingConfig, postgres, CREDSTYPE, properties);
		task.start(properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			map("id", "1",
				"description", "test")));

		NexlaMessage messageWithNull = createMessage(new LinkedHashMap<>(
			map("id", "1",
				"description", null)));

		task.put(singletonList(
			new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));

		task.put(singletonList(
			new SinkRecord("test-topic", 1, null, null, null, toJsonString(messageWithNull), 2)));

		assertEquals(singletonList(
			of("id", "1", "description", "test")
		), selectRows(postgres, false, newHashSet("id", "description"), CREDSTYPE));
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putUpsertPkCaps() {
		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("ID", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("ids", map("id", DEFAULT_MAPPING));
		properties.put(PRIMARY_KEY, "ID");

		recreateTable(false, mappingConfig, postgres, CREDSTYPE, properties);

		properties.put(INSERT_MODE, UPSERT.toString());
		properties.put(MAPPING, toJsonString(mappingConfig));
		task.start(properties);

		NexlaMessage message = createMessage(new LinkedHashMap<>(
			of("id", "1",
				"ids", "1")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
			toJsonString(message), 1)));
		assertEquals(singletonList(of("id", "1", "ID", "1")),
			selectRows(postgres, false, newHashSet("id", "ID"), CREDSTYPE));
	}

	@SneakyThrows
	//@Test FIXME Deactivating tests in release/v3.2.0
	public void putUpsertWrongPk() {
		int sinkId = new Random().nextInt(10000);
		String quarantineTopic = getQuarantineTopic(sink(sinkId), Optional.empty());

		withConsumer(quarantineTopic, quarantineConsumer -> {

			MappingConfig mappingConfig = new MappingConfig();
			mappingConfig.setMode(MODE_MANUAL);
			mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));

			recreateTable(false, mappingConfig, postgres, CREDSTYPE, properties);

			properties.put(SINK_ID, sinkId + "");
			properties.put(PRIMARY_KEY, "ID");
			properties.put(INSERT_MODE, UPSERT.toString());
			properties.put(MAPPING, toJsonString(mappingConfig));

			task.start(properties);

			NexlaMessage message = createMessage(new LinkedHashMap<>(of("id", "1")));

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));

			assertEquals(1, readQuarantine(quarantineConsumer).size());
		});
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	@SneakyThrows
	public void removeFieldFromSchema() {
		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("ids", map("ID", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("description", map("description", DEFAULT_MAPPING));

		recreateTable(false, mappingConfig, postgres, CREDSTYPE, properties);

		mappingConfig.getMapping().remove("ids");
		properties.put(PRIMARY_KEY, "id");
		properties.put(INSERT_MODE, INSERT.toString());
		properties.put(MAPPING, toJsonString(mappingConfig));
		task.start(properties);
		Connection conn = postgres.createConnection("");
		Schema schema = selectSchema(() -> conn,
				DialectRegistry.getInstance().fromConnectionType(POSTGRES), new JdbcSinkConnectorConfig(properties));
		assertEquals(schema.fields().size(), 2);
	}

	@SneakyThrows
	//@Test FIXME Deactivating tests in release/v3.2.0
	public void quarantineRecordsTest() {
		int sinkId = new Random().nextInt(10000);
		String quarantineTopic = getQuarantineTopic(sink(sinkId), Optional.empty());

		withConsumer(quarantineTopic, quarantineConsumer -> {

			properties.put(INSERT_MODE, "upsert");
			properties.put(NexlaConstants.CREDENTIALS_TYPE, "redshift"); // redshift is just to use dialect, where MERGE is not supported natively
			properties.put(COPY_ALLOWED, "false");
			properties.put(SINK_ID, sinkId + "");

			MappingConfig mappingConfig = new MappingConfig();
			mappingConfig.setMode(MODE_MANUAL);
			mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
			mappingConfig.getMapping().put("description", map("description", DEFAULT_MAPPING));

			recreateTable(false, mappingConfig, postgres, CREDSTYPE, properties);
			task.start(properties);

			NexlaMessage message = createMessage(new LinkedHashMap<>(of("description", "test")));

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 1)));
			assertEquals(1, readQuarantine(quarantineConsumer).size());
			task.stop();
		});
	}

	@SneakyThrows
	//@Test FIXME Deactivating tests in release/v3.2.0
	public void noQuarantineRecordsFailsTest() {
		int sinkId = new Random().nextInt(10000);
		String quarantineTopic = getQuarantineTopic(sink(sinkId), Optional.empty());

		withConsumer(quarantineTopic, quarantineConsumer -> {

			MappingConfig mappingConfig = new MappingConfig();
			mappingConfig.setMode(MODE_MANUAL);
			mappingConfig.getMapping().put("id", map("ID", DEFAULT_MAPPING));
			mappingConfig.getMapping().put("ids", map("id", DEFAULT_MAPPING));

			recreateTable(false, mappingConfig, postgres, CREDSTYPE, properties);

			properties.put(PRIMARY_KEY, "ID");
			properties.put(INSERT_MODE, "upsert");
			properties.put(PASSWORD, "");
			properties.put(MAPPING, toJsonString(mappingConfig));

			task.start(properties);

			NexlaMessage message = createMessage(new LinkedHashMap<>(
				of("id", "1",
					"ids", "1")));

			try {
				task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
					toJsonString(message), 1)));
			} catch (RetriableException e) {
				List<NexlaQuarantineMessage> quarantine = readQuarantine(quarantineConsumer);
				assertEquals(0, quarantine.size());
				task.stop();
			}
		});
	}
}