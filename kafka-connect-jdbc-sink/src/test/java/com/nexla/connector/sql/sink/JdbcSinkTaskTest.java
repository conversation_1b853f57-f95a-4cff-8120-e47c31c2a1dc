package com.nexla.connector.sql.sink;

import com.nexla.common.NexlaMessage;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.test.IntegrationTests;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.Assert.*;

@Category(IntegrationTests.class)
public class JdbcSinkTaskTest {

    //@Test FIXME Deactivating tests in release/v3.2.0
    public void filterNullPrimaryKeysTest() {
        final List<NexlaMessageContext> inputMessages =
                Stream.<Map<String, Object>>of(
                              Map.of("id", 1, "name", "n1", "value", "val1"),
                              Map.of("name", "n2", "value", "val2"),
                              Map.of("id", 3, "value", "val3"),
                              Map.of("value", "val4"))
                      .map(map -> new NexlaMessage(new LinkedHashMap<>(map), null))
                      .map(message -> new NexlaMessageContext(null, message, null, null))
                      .collect(Collectors.toList());
        final List<String> primaryKeys = Arrays.asList("id", "name");
        final Map<Long, RecordMetric> metricsByRunId = new HashMap<>();

        final List<NexlaMessageContext> filtered =
                new JdbcSinkTask().filterNullPrimaryKeys(inputMessages, primaryKeys, metricsByRunId);

        assertEquals(1, filtered.size());
        assertEquals(1,filtered.get(0).getMapped().getRawMessage().get("id"));

        assertEquals(3, metricsByRunId.get(0L).errorRecords.get());
        assertEquals(3, metricsByRunId.get(0L).quarantineMessages.size());
    }
}