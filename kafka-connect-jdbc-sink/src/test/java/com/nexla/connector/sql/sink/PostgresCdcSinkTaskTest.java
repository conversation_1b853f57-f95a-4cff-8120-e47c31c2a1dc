package com.nexla.connector.sql.sink;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.admin.client.pipeline.Pipeline;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connect.common.offsets.OffsetsTracker;
import com.nexla.connector.config.MappingConfig;
import com.nexla.listing.client.ListingClient;
import com.nexla.test.IntegrationTests;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.utility.DockerImageName;

import java.util.*;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.google.common.collect.ImmutableMap.of;
import static com.google.common.collect.Sets.newHashSet;
import static com.nexla.common.StreamUtils.map;
import static com.nexla.connector.config.MappingConfig.MODE_AUTO;
import static com.nexla.connector.config.MappingConfig.MODE_MANUAL;
import static com.nexla.connector.config.SinkConnectorConfig.DEFAULT_MAPPING;
import static com.nexla.connector.config.SinkConnectorConfig.MAPPING;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.BATCH_SIZE;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.INSERT;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.PARALLELISM;
import static com.nexla.connector.properties.SqlConfigAccessor.*;
import static com.nexla.connector.sql.sink.JdbcSinkTaskTestHelper.*;
import static java.util.Collections.emptySet;
import static java.util.Collections.singletonList;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@Category(IntegrationTests.class)
public class PostgresCdcSinkTaskTest extends BaseKafkaTest {

	private static final String CREDSTYPE = "postgres";

	private CdcSinkTask task;
	private Map<String, String> properties;
	public static KafkaContainer kafka = new KafkaContainer("7.2.11");
	public static DockerImageName myImage = DockerImageName.parse("debezium/postgres:11").asCompatibleSubstituteFor("postgres");
	public static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>(myImage);

	@BeforeClass
	public static void startUp() {
		postgres.withReuse(false);
		kafka.withReuse(false);
		postgres.start();
		kafka.start();
		init(kafka);
	}

	@AfterClass
	public static void tearDown() {
		postgres.stop();
		kafka.stop();
	}

	@Before
	public void onBefore() {
		AdminApiClient adminApiClient = mock(AdminApiClient.class);
		AdminApiClientBuilder.INSTANCE = adminApiClient;

		when(adminApiClient.getPipeline(any(Integer.class))).thenReturn(Optional.of(
				new Pipeline(null, null, null)));
		DataSink dataSink = new DataSink();
		dataSink.setId(1);
		dataSink.setConnectionType(ConnectionType.POSTGRES);
		dataSink.setSinkConfig(Map.of(PARALLELISM, "1"));
		dataSink.setStatus(ResourceStatus.ACTIVE);
		when(adminApiClient.getDataSink(any(Integer.class))).thenReturn(Optional.of(dataSink));

		this.task = new CdcSinkTask() {
			@Override
			public void doStart() {
				super.doStart();
				this.listingClient = mock(ListingClient.class);
				this.offsetsSender = Optional.of(mock(OffsetsTracker.class));
			}
		};
		SinkTaskContext taskContext = mock(SinkTaskContext.class);
		when(taskContext.assignment()).thenReturn(emptySet());
		task.initialize(taskContext);

		this.runBeforeTopicReading = Optional.of(() -> task.getControlMessageProducer().flush());
		this.properties = getProperties(postgres, CREDSTYPE);
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void put_MultiTable_Manual_WithoutPK_Test() {
		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.setReplayRowDeletions(true);
		mappingConfig.setTableNamePrefix("prefix_");
		mappingConfig.setTableNameSuffix("_sufix");

		MappingConfig.ColumnMapping columnMapping1 = new MappingConfig.ColumnMapping();
		columnMapping1.setName("dest_id");
		columnMapping1.setType("integer");
		columnMapping1.setEnableHashing(false);

		MappingConfig.ColumnMapping columnMapping2 = new MappingConfig.ColumnMapping();
		columnMapping2.setName("dest_fullname");
		columnMapping2.setType("text");
		columnMapping2.setEnableHashing(false);

		MappingConfig.ColumnMapping columnMapping3 = new MappingConfig.ColumnMapping();
		columnMapping3.setName("dest_email");
		columnMapping3.setType("text");
		columnMapping3.setEnableHashing(false);

		MappingConfig.ColumnMapping columnMapping4 = new MappingConfig.ColumnMapping();
		columnMapping4.setName("dest_new_column_name_1");
		columnMapping4.setType("text");
		columnMapping4.setEnableHashing(false);

		MappingConfig.TableMapping tableMapping = new MappingConfig.TableMapping();
		tableMapping.setName("dest_table_name");
		tableMapping.getColumns().put("id", columnMapping1);
		tableMapping.getColumns().put("fullname", columnMapping2);
		tableMapping.getColumns().put("email", columnMapping3);
		tableMapping.getColumns().put("new_column_name_1", columnMapping4);

		String tblName = TABLE_NAME + UUID.randomUUID().toString().replace("-","");
		mappingConfig.getOverriddenMappings().put(tblName, tableMapping);

		properties.put(CDC_ENABLED, "true");
		properties.put(MAPPING, toJsonString(mappingConfig));
		properties.remove(TABLE);
		properties.remove(PRIMARY_KEY);
		task.start(properties);

		// CREATE check
		NexlaMessage createMessage = createMessage(new LinkedHashMap<>(
			of("nexla_op", "CREATE",
					"nexla_op_database", postgres.getDatabaseName(),
					"nexla_op_table", tblName,
					"nexla_cdc_info", "{\"primary_key\":[],\"before\":{},\"after\":{\"id\":2,\"fullname\":\"John Doe\",\"email\":\"<EMAIL>\",\"new_column_name_1\":\"test2\"},\"schema\":{\"id\":{\"INTEGER\":null},\"fullname\":{\"STRING\":null},\"email\":{\"STRING\":null}},\"new_column_name_1\":{\"STRING\":null}}}")));

 		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
			toJsonString(createMessage), 1)));

		assertEquals(singletonList(of("dest_id", "2", "dest_fullname", "John Doe", "dest_email", "<EMAIL>", "dest_new_column_name_1", "test2")),
			selectRows(postgres, "prefix_dest_table_name_sufix", false, newHashSet("dest_id", "dest_fullname", "dest_email", "dest_new_column_name_1"), CREDSTYPE));

		// UPDATE check
		NexlaMessage updateMessage = createMessage(new LinkedHashMap<>(
				of("nexla_op", "UPDATE",
						"nexla_op_database", postgres.getDatabaseName(),
						"nexla_op_table", tblName,
						"nexla_cdc_info", "{\"primary_key\":[],\"before\":{\"id\":2,\"fullname\":\"John Doe\",\"email\":\"<EMAIL>\",\"new_column_name_1\":\"test2\"},\"after\":{\"id\":2,\"fullname\":\"John Doe\",\"email\":\"<EMAIL>\",\"new_column_name_1\":\"test2_updated\"},\"schema\":{\"id\":{\"INTEGER\":null},\"fullname\":{\"STRING\":null},\"email\":{\"STRING\":null}},\"new_column_name_1\":{\"STRING\":null}}}")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(updateMessage), 1)));

		assertEquals(singletonList(of("dest_id", "2", "dest_fullname", "John Doe", "dest_email", "<EMAIL>", "dest_new_column_name_1", "test2_updated")),
				selectRows(postgres, "prefix_dest_table_name_sufix", false, newHashSet("dest_id", "dest_fullname", "dest_email", "dest_new_column_name_1"), CREDSTYPE));

		// DELETE check
		NexlaMessage deleteMessage = createMessage(new LinkedHashMap<>(
				of("nexla_op", "DELETE",
						"nexla_op_database", postgres.getDatabaseName(),
						"nexla_op_table", tblName,
						"nexla_cdc_info", "{\"primary_key\":[],\"before\":{\"id\":2,\"fullname\":\"John Doe\",\"email\":\"<EMAIL>\",\"new_column_name_1\":\"test2_updated\"},\"after\":{\"id\":2,\"fullname\":\"John Doe\",\"email\":\"<EMAIL>\",\"new_column_name_1\":\"test2_updated\"},\"schema\":{\"id\":{\"INTEGER\":null},\"fullname\":{\"STRING\":null},\"email\":{\"STRING\":null}},\"new_column_name_1\":{\"STRING\":null}}}")));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(deleteMessage), 1)));

		assertEquals(0,
				selectRows(postgres, "prefix_dest_table_name_sufix",false, newHashSet("dest_id", "dest_fullname", "dest_email", "dest_new_column_name_1"), CREDSTYPE).size());
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void put_MultiTable_Auto_WithPK_Test() {
		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_AUTO);
		mappingConfig.setReplayRowDeletions(true);
		mappingConfig.getExcludeTables().add("excluded_table");

		properties.put(CDC_ENABLED, "true");
		properties.put(MAPPING, toJsonString(mappingConfig));
		properties.put(BATCH_SIZE, "1");
		properties.remove(TABLE);
		properties.remove(PRIMARY_KEY);
		task.start(properties);

		String tblName = TABLE_NAME + UUID.randomUUID().toString().replace("-","");

		// CREATE check
		Map<String, Object> createMap = JsonUtils.jsonToMap("{\"primary_key\":[\"id\"],\"before\":{},\"after\":{\"id\":2,\"fullname\":\"John Doe\",\"email\":\"<EMAIL>\",\"new_column_name_1\":\"test2\"},\"schema\":{\"id\":{\"INTEGER\":null},\"fullname\":{\"STRING\":null},\"email\":{\"STRING\":null}},\"new_column_name_1\":{\"STRING\":null}}}");
		NexlaMessage createMessage = createMessage(new LinkedHashMap<>(
				of("nexla_op", "CREATE",
						"nexla_op_database", postgres.getDatabaseName(),
						"nexla_op_table", tblName,
						"nexla_cdc_info", createMap)));

		NexlaMessage excludedMessage = createMessage(new LinkedHashMap<>(
				of("nexla_op", "CREATE",
						"nexla_op_database", postgres.getDatabaseName(),
						"nexla_op_table", "excluded_table",
						"nexla_cdc_info", createMap)));

		task.put(Arrays.asList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(createMessage), 1),
				new SinkRecord("test-topic", 1, null, null, null, toJsonString(excludedMessage), 1)));

		assertEquals(singletonList(of("id", "2", "fullname", "John Doe", "email", "<EMAIL>", "new_column_name_1", "test2")),
				selectRows(postgres, tblName, false, newHashSet("id", "fullname", "email", "new_column_name_1"), CREDSTYPE));

		// UPDATE check with ID in uppercase
		Map<String, Object> updateMap = JsonUtils.jsonToMap("{\"primary_key\":[\"id\"],\"before\":{\"id\":2,\"fullname\":\"John Doe\",\"email\":\"<EMAIL>\",\"new_column_name_1\":\"test2\"},\"after\":{\"id\":2,\"fullname\":\"John Doe\",\"email\":\"<EMAIL>\",\"new_column_name_1\":\"test2_updated\"},\"schema\":{\"id\":{\"INTEGER\":null},\"fullname\":{\"STRING\":null},\"email\":{\"STRING\":null}},\"new_column_name_1\":{\"STRING\":null}}}");
		NexlaMessage updateMessage = createMessage(new LinkedHashMap<>(
				of("nexla_op", "UPDATE",
						"nexla_op_database", postgres.getDatabaseName(),
						"nexla_op_table", tblName,
						"nexla_cdc_info", updateMap)));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(updateMessage), 1)));

		assertEquals(singletonList(of("id", "2", "fullname", "John Doe", "email", "<EMAIL>", "new_column_name_1", "test2_updated")),
				selectRows(postgres, tblName, false, newHashSet("id", "fullname", "email", "new_column_name_1"), CREDSTYPE));

		// DELETE check
		NexlaMessage deleteMessage = createMessage(new LinkedHashMap<>(
				of("nexla_op", "DELETE",
						"nexla_op_database", postgres.getDatabaseName(),
						"nexla_op_table", tblName,
						"nexla_cdc_info", updateMap)));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(deleteMessage), 1)));

		assertEquals(0,
				selectRows(postgres, tblName, false, newHashSet("id", "fullname", "email", "new_column_name_1"), CREDSTYPE).size());
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void put_MultiTable_Auto_WithPK_Alter_Test() {
		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_AUTO);
		mappingConfig.setReplayRowDeletions(true);
		mappingConfig.getExcludeTables().add("excluded_table");

		properties.put(CDC_ENABLED, "true");
		properties.put(MAPPING, toJsonString(mappingConfig));
		properties.put(BATCH_SIZE, "1");
		properties.remove(TABLE);
		properties.remove(PRIMARY_KEY);
		task.start(properties);

		String tblName = TABLE_NAME + UUID.randomUUID().toString().replace("-","");

		// CREATE check
		Map<String, Object> createMap = JsonUtils.jsonToMap("{\"primary_key\":[\"id\"],\"before\":{},\"after\":{\"id\":3,\"fullname\":\"John Doe\",\"email\":\"<EMAIL>\",\"new_column_name_1\":\"2\"},\"schema\":{\"id\":{\"INT32\":null},\"fullname\":{\"STRING\":null},\"email\":{\"STRING\":null},\"new_column_name_1\":{\"STRING\":null}}}");
		NexlaMessage createMessage = createMessage(new LinkedHashMap<>(
				of("nexla_op", "CREATE",
						"nexla_op_database", postgres.getDatabaseName(),
						"nexla_op_table", tblName,
						"nexla_cdc_info", createMap)));

		task.put(Arrays.asList(new SinkRecord("test-topic", 1, null, null, null, toJsonString(createMessage), 1)));

		// ALTER ADD/DROP/MODIFY type check
		Map<String, Object> updateMap = JsonUtils.jsonToMap("{\"primary_key\":[\"id\"],\"before\":{\"id\":3,\"fullname\":\"John Doe\",\"email\":\"<EMAIL>\",\"new_column_name_1\":\"test2\"},\"after\":{\"id\":3,\"fullname\":\"John Doe\",\"email_altered\":\"<EMAIL>\",\"new_column_name_1\":20},\"schema\":{\"ID\":{\"INT32\":null},\"fullname\":{\"STRING\":null},\"email_altered\":{\"STRING\":null},\"new_column_name_1\":{\"INT32\":null}}}");
		NexlaMessage updateMessage = createMessage(new LinkedHashMap<>(
				of("nexla_op", "UPDATE",
						"nexla_op_database", postgres.getDatabaseName(),
						"nexla_op_table", tblName,
						"nexla_cdc_info", updateMap)));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(updateMessage), 1)));

		assertEquals(singletonList(of("id", "3", "fullname", "John Doe", "email_altered", "<EMAIL>", "new_column_name_1", "20")),
				selectRows(postgres, tblName, false, newHashSet("id", "fullname", "email_altered", "new_column_name_1"), CREDSTYPE));
	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void put_SingleTable_Manual_WithoutPK_Test() {
		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_MANUAL);
		mappingConfig.getMapping().put("ids", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("descriptions", map("description", DEFAULT_MAPPING));

		recreateTable(false, mappingConfig, postgres, CREDSTYPE, properties);

		properties.put(CDC_ENABLED, "true");
		properties.put(INSERT_MODE, INSERT.toString());
		properties.put(MAPPING, toJsonString(mappingConfig));
		properties.remove(PRIMARY_KEY);
		task.start(properties);

		// CREATE check
		Map<String, Object> createMap = JsonUtils.jsonToMap("{\"ids\":11,\"descriptions\":\"John Doe\",\"nexla_op\":\"CREATE\",\"nexla_before\":{}}");
		NexlaMessage message = createMessage(new LinkedHashMap<>(createMap));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(message), 1)));
		assertEquals(singletonList(of("id", "11", "description", "John Doe")),
				selectRows(postgres, false, new LinkedHashSet<>(Arrays.asList("id", "description")), CREDSTYPE));

		// UPDATE check
		Map<String, Object> updateMap = JsonUtils.jsonToMap("{\"ids\":11,\"descriptions\":\"John Doe Updated\",\"nexla_op\":\"UPDATE\",\"nexla_before\":{\"ids\":11,\"descriptions\":\"John Doe\"}}");
		NexlaMessage updateMessage = createMessage(new LinkedHashMap<>(updateMap));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(updateMessage), 1)));

		assertEquals(singletonList(of("id", "11", "description", "John Doe Updated")),
				selectRows(postgres, false, new LinkedHashSet<>(Arrays.asList("id", "description")), CREDSTYPE));

		// DELETE check
		Map<String, Object> deleteMap = JsonUtils.jsonToMap("{\"ids\":11,\"descriptions\":\"John Doe Updated\",\"nexla_op\":\"DELETE\",\"nexla_before\":{\"ids\":11,\"descriptions\":\"John Doe\"}}");
		NexlaMessage deleteMessage = createMessage(new LinkedHashMap<>(deleteMap));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(deleteMessage), 1)));

		assertEquals(0,
				selectRows(postgres, false, new LinkedHashSet<>(Arrays.asList("id", "description")), CREDSTYPE).size());

	}

	//@Test FIXME Deactivating tests in release/v3.2.0
	public void put_SingleTable_Auto_WithPK_Test() {
		MappingConfig mappingConfig = new MappingConfig();
		mappingConfig.setMode(MODE_AUTO);
		mappingConfig.getMapping().put("id", map("id", DEFAULT_MAPPING));
		mappingConfig.getMapping().put("descriptions", map("descriptions", DEFAULT_MAPPING));

		properties.put(CDC_ENABLED, "true");
		properties.put(INSERT_MODE, INSERT.toString());
		properties.put(MAPPING, toJsonString(mappingConfig));

		recreateTable(false, mappingConfig, postgres, CREDSTYPE, properties);
		task.start(properties);

		// CREATE check
		Map<String, Object> createMap = JsonUtils.jsonToMap("{\"id\":11,\"descriptions\":\"John Doe\",\"nexla_op\":\"CREATE\",\"nexla_before\":{}}");
		NexlaMessage message = createMessage(new LinkedHashMap<>(createMap));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(message), 1)));
		assertEquals(singletonList(of("id", "11", "descriptions", "John Doe")),
				selectRows(postgres, false, new LinkedHashSet<>(Arrays.asList("id", "descriptions")), CREDSTYPE));

		// UPDATE check
		Map<String, Object> updateMap = JsonUtils.jsonToMap("{\"id\":11,\"descriptions\":\"John Doe Updated\",\"nexla_op\":\"UPDATE\",\"nexla_before\":{\"id\":11,\"descriptions\":\"John Doe\"}}");
		NexlaMessage updateMessage = createMessage(new LinkedHashMap<>(updateMap));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(updateMessage), 1)));

		assertEquals(singletonList(of("id", "11", "descriptions", "John Doe Updated")),
				selectRows(postgres, false, new LinkedHashSet<>(Arrays.asList("id", "descriptions")), CREDSTYPE));

		// DELETE check
		Map<String, Object> deleteMap = JsonUtils.jsonToMap("{\"id\":11,\"descriptions\":\"John Doe Updated\",\"nexla_op\":\"DELETE\",\"nexla_before\":{\"id\":11,\"descriptions\":\"John Doe\"}}");
		NexlaMessage deleteMessage = createMessage(new LinkedHashMap<>(deleteMap));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(deleteMessage), 1)));

		assertEquals(0,
				selectRows(postgres, false, new LinkedHashSet<>(Arrays.asList("id", "descriptions")), CREDSTYPE).size());

	}

}