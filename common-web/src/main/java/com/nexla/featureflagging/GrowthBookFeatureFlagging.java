package com.nexla.featureflagging;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import com.nexla.common.StreamUtils;
import growthbook.sdk.java.*;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class GrowthBookFeatureFlagging implements NexlaFeatureFlagging {

  private final GBFeaturesRepository repository;
  private final Cache<Map<String, String>, GrowthBook> growthBookCache;

  @SneakyThrows
  public GrowthBookFeatureFlagging(GBFeaturesRepository repository) {
    this.repository = repository;
    this.growthBookCache =
        CacheBuilder.newBuilder().maximumSize(10_000).expireAfterWrite(1, TimeUnit.MINUTES).build();
  }

  @SneakyThrows
  public GrowthBookFeatureFlagging(String clientKey) {
    this.repository =
        GBFeaturesRepository.builder()
            .apiHost("https://cdn.growthbook.io")
            .clientKey(clientKey)
            .refreshStrategy(FeatureRefreshStrategy.STALE_WHILE_REVALIDATE)
            .build();

    this.repository.initialize();

    this.repository.onFeaturesRefresh(
        new FeatureRefreshCallback() {
          @Override
          public void onRefresh(String featuresJson) {
            invalidateCache();
          }

          @Override
          public void onError(Throwable throwable) {
            log.error("Error refreshing features", throwable);
          }
        });

    this.growthBookCache =
        CacheBuilder.newBuilder().maximumSize(10_000).expireAfterWrite(1, TimeUnit.MINUTES).build();
  }

  @Override
  public Boolean isOn(String featureName, Map<String, String> attributes) {
    GrowthBook growthBook = getGrowthBook(attributes);
    return growthBook.isOn(featureName);
  }

  @Override
  public Boolean isOff(String featureName, Map<String, String> attributes) {
    GrowthBook growthBook = getGrowthBook(attributes);
    return growthBook.isOff(featureName);
  }

  @Override
  public Boolean getFeatureValue(
      String featureName, Map<String, String> attributes, Boolean defaultValue) {
    GrowthBook growthBook = getGrowthBook(attributes);
    return growthBook.getFeatureValue(featureName, defaultValue);
  }

  @Override
  public String getFeatureValue(
      String featureName, Map<String, String> attributes, String defaultValue) {
    GrowthBook growthBook = getGrowthBook(attributes);
    return growthBook.getFeatureValue(featureName, defaultValue);
  }

  @Override
  public Float getFeatureValue(
      String featureName, Map<String, String> attributes, Float defaultValue) {
    GrowthBook growthBook = getGrowthBook(attributes);
    return growthBook.getFeatureValue(featureName, defaultValue);
  }

  @Override
  public Integer getFeatureValue(
      String featureName, Map<String, String> attributes, Integer defaultValue) {
    GrowthBook growthBook = getGrowthBook(attributes);
    return growthBook.getFeatureValue(featureName, defaultValue);
  }

  @Override
  public Double getFeatureValue(
      String featureName, Map<String, String> attributes, Double defaultValue) {
    GrowthBook growthBook = getGrowthBook(attributes);
    return growthBook.getFeatureValue(featureName, defaultValue);
  }

  @Override
  public Object getFeatureValue(
      String featureName, Map<String, String> attributes, Object defaultValue) {
    GrowthBook growthBook = getGrowthBook(attributes);
    return growthBook.getFeatureValue(featureName, defaultValue);
  }

  @Override
  public <ValueType> ValueType getFeatureValue(
      String featureName,
      Map<String, String> attributes,
      ValueType defaultValue,
      Class<ValueType> valueTypeClass) {
    GrowthBook growthBook = getGrowthBook(attributes);
    return growthBook.getFeatureValue(featureName, defaultValue, valueTypeClass);
  }

  @Override
  public Boolean isOn(FeatureAttributes featureAttributes) {
    return isOn(featureAttributes.getFeatureName(), featureAttributes.attributesMap());
  }

  @Override
  public Boolean isOff(FeatureAttributes featureAttributes) {
    return isOn(featureAttributes.getFeatureName(), featureAttributes.attributesMap());
  }

  @Override
  public Boolean getFeatureValue(FeatureAttributes featureAttributes, Boolean defaultValue) {
    return getFeatureValue(
        featureAttributes.getFeatureName(), featureAttributes.attributesMap(), defaultValue);
  }

  @Override
  public String getFeatureValue(FeatureAttributes featureAttributes, String defaultValue) {
    return getFeatureValue(
        featureAttributes.getFeatureName(), featureAttributes.attributesMap(), defaultValue);
  }

  @Override
  public Float getFeatureValue(FeatureAttributes featureAttributes, Float defaultValue) {
    return getFeatureValue(
        featureAttributes.getFeatureName(), featureAttributes.attributesMap(), defaultValue);
  }

  @Override
  public Integer getFeatureValue(FeatureAttributes featureAttributes, Integer defaultValue) {
    return getFeatureValue(
        featureAttributes.getFeatureName(), featureAttributes.attributesMap(), defaultValue);
  }

  @Override
  public Double getFeatureValue(FeatureAttributes featureAttributes, Double defaultValue) {
    return getFeatureValue(
        featureAttributes.getFeatureName(), featureAttributes.attributesMap(), defaultValue);
  }

  @Override
  public Object getFeatureValue(FeatureAttributes featureAttributes, Object defaultValue) {
    return getFeatureValue(
        featureAttributes.getFeatureName(), featureAttributes.attributesMap(), defaultValue);
  }

  @Override
  public <ValueType> ValueType getFeatureValue(
      FeatureAttributes featureAttributes,
      ValueType defaultValue,
      Class<ValueType> valueTypeClass) {
    return getFeatureValue(
        featureAttributes.getFeatureName(),
        featureAttributes.attributesMap(),
        defaultValue,
        valueTypeClass);
  }

  @Override
  public Boolean isFeatureEnabled(FeatureAttributes featureAttributes) {
    return isFeatureEnabled(featureAttributes.getFeatureName(), featureAttributes.attributesMap());
  }

  @Override
  public Boolean isFeatureEnabled(String featureName, Map<String, String> attributes) {
    GrowthBook growthBook = getGrowthBook(attributes);
    return growthBook.isFeatureEnabled(featureName);
  }

  @SneakyThrows
  private GrowthBook getGrowthBook(Map<String, String> attributes) {
    final Map<String, String> lookupKey = attributes != null ? attributes : Maps.newHashMap();

    GrowthBook result = growthBookCache.getIfPresent(lookupKey);
    if (result != null) {
      return result;
    }

    Map<String, String> immutableKey = Map.copyOf(lookupKey);
    return growthBookCache.get(
        immutableKey,
        () ->
            new GrowthBook(
                GBContext.builder()
                    .featuresJson(this.repository.getFeaturesJson())
                    .attributesJson(StreamUtils.jsonUtil().toJsonString(immutableKey))
                    .build()));
  }

  public void invalidateCache() {
    growthBookCache.invalidateAll();
  }

  public void close() {
    this.repository.shutdown();
  }
}
