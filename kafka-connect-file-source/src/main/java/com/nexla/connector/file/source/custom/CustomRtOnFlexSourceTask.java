package com.nexla.connector.file.source.custom;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.CodeContainer;
import com.nexla.admin.client.DataSet;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaConstants;
import com.nexla.common.Resource;
import com.nexla.common.RestTemplateBuilder;
import com.nexla.common.StreamUtils;
import com.nexla.common.datetime.NexlaBackoff;
import com.nexla.connect.common.BaseSourceTask;
import com.nexla.connect.common.CollectRecordsResult;
import com.nexla.connect.common.ConnectWorkarounds;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.file.AdvancedSettings;
import com.nexla.connector.config.file.CustomRtMode;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.file.source.CustomRtOnFlexTransportFileReader;
import com.nexla.connector.file.source.FileSourceContext;
import com.nexla.connector.file.source.FileSourceNotificationSender;
import com.nexla.connector.file.source.FileSourceOffsetWriter;
import com.nexla.connector.file.source.ITransportFileReader;
import com.nexla.connector.file.source.KafkaFileMessageResultReader;
import com.nexla.connector.file.source.KafkaFileSourceMessageGrouper;
import com.nexla.connector.file.source.KafkaFileSourceOffsetWriter;
import com.nexla.connector.file.source.ReadBatchResult;
import com.nexla.connector.file.source.TransportFile;
import com.nexla.connector.file.source.TransportFileReader;
import com.nexla.connector.file.source.custom.bus.KafkaBasedTaskBusImpl;
import com.nexla.connector.file.source.custom.bus.TaskBus;
import com.nexla.connector.file.source.custom.config.PostProcessorConfig;
import com.nexla.connector.file.source.custom.config.Utils;
import com.nexla.connector.file.source.custom.flow.ParallelFlow;
import com.nexla.connector.file.source.custom.flow.Flow;
import com.nexla.connector.file.source.custom.stage.IngestionStage;
import com.nexla.connector.file.source.custom.stage.MezzanineUploadStage;
import com.nexla.connector.file.source.custom.stage.PostProcessingAckStage;
import com.nexla.file.service.FileConnectorService;
import com.nexla.kafka.control.listener.conf.NexlaKafkaConfigApplier;
import com.nexla.listing.client.FileVaultClient;
import com.nexla.listing.client.ListingClient;
import com.nexla.probe.ftp.FileConnectorServiceBuilder;
import lombok.SneakyThrows;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.source.SourceRecord;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import static com.nexla.common.NexlaConstants.CUSTOM_RT_CREDS_ENC;
import static com.nexla.common.NexlaConstants.CUSTOM_RT_CREDS_ENCIV;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.connector.config.FileEncryptConfig.ENCRYPT_PRIVATE_KEY;
import static com.nexla.connector.config.FileEncryptConfig.ENCRYPT_PRIVATE_PASSWORD;
import static com.nexla.connector.config.file.FileSourceConnectorConfig.OVERRIDDEN_EXTENSIONS;
import static com.nexla.connector.config.vault.CredentialsStore.CONNECTOR_POLICY;
import static com.nexla.connector.config.vault.CredentialsStore.SECRET;
import static com.nexla.connector.properties.FileConfigAccessor.FILE_LIST;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;

public class CustomRtOnFlexSourceTask extends BaseSourceTask<FileSourceConnectorConfig> {
	private final Supplier<ConnectWorkarounds> workarounds =
			Suppliers.memoize(() -> new ConnectWorkarounds(context.offsetStorageReader()));

	private final static ScheduledExecutorService SCHEDULED_POOL = Executors.newScheduledThreadPool(1);


	private TaskRegistry registry;
	private List<ProcessingStage> stages;
	private Flow flow;

	public CustomRtOnFlexSourceTask() {
	}

	@Override
	public ConfigDef configDef() {
		return FileSourceConnectorConfig.configDef();
	}

	@Override
	public void doStart(Map<String, String> props) throws ConnectException {
		this.sourceTelemetryReporter = Optional.of(new ConnectorTelemetryReporter(
				config.sourceId, config.sourceType.name(), SOURCE, true
		));

		this.registry = new TaskRegistry(listingClient, config.sourceId);

		this.stages = plan(props);

		this.flow = new ParallelFlow(stages);
	}

	private List<ProcessingStage> plan(Map<String, String> props) {
		Map<String, Object> sourceConfig = dataSource.getSourceConfig();

		List<PostProcessorConfig> ppc = Utils.convert(sourceConfig.get("post_processor"));

		List<ProcessingStage> stages = new ArrayList<>(List.of(
				preProcessingStage(props, ppc) // pre-processing stage is always required
		));

		// if schema detection once, we only need to run the pre-processing stage,
		// custom runtime will push the data via webhook.
		if (!config.schemaDetectionOnce) {
			stages.add(postProcessingStage(props));
		} else {
			stages.add(postProcessingAckStage(props));
		}

		return stages;
	}

	private ProcessingStage preProcessingStage(Map<String, String> props, List<PostProcessorConfig> ppc) {
		final FileSourceContext fileSourceContext = new FileSourceContext(
				config,
				getDataSetForSource(dataSource.getDatasets()),
				runId,
				dataSource.getOrgId(),
				dataSource.getOwnerId()
		);

		FileSourceNotificationSender kafkaMessageSender = new FileSourceNotificationSender(controlMessageProducer, dataMessageProducer, FlowType.STREAMING, fileSourceContext, logger);
		FileSourceOffsetWriter offsetWriter = createOffsetWriter(kafkaMessageSender);
		KafkaFileSourceMessageGrouper messageGrouper = new KafkaFileSourceMessageGrouper(fileSourceContext, logger, runId);


		Optional<ListingClient> listingClient = config.listingEnabled
				? of(new ListingClient(config.listingAppServer, config.nexlaUsername, config.nexlaPassword, new RestTemplateBuilder().withSSL(config.nexlaSslConfig).build()))
				: empty();

		CustomRtMode mode = AdvancedSettings.RAW_DATA_FILE.equalsIgnoreCase(config.advancedSettings)
				? CustomRtMode.REPLICATION
				: CustomRtMode.PARSING;

		logger.info("Using CustomRtOnFlexTransportFileReader");
		CustomRtOnFlexTransportFileReader fileReader = new CustomRtOnFlexTransportFileReader(
				kafkaMessageSender,
                initNewProbe(),
				schemaDetection,
				offsetWriter,
				messageGrouper,
				logger,
				of(context.offsetStorageReader()),
				listingClient,
				empty(),
				fileSourceContext,
				of(this::heartbeat),
				config.listingEnabled,
				mode
		);

		fileReader.setCustomParserEnabled(true);

		if (!config.listingEnabled) {
			List<TransportFile> transportFiles = StreamUtils.jsonUtil().stringToType(props.get(FILE_LIST), new TypeReference<List<TransportFile>>() {
			});

			fileReader.addFiles(transportFiles);
		}

		Integer codeContainerId = ppc.stream()
				.filter(x -> Const.RUN_ENGINE_RAY.equalsIgnoreCase(x.getRunEngine()))
				.map(PostProcessorConfig::getCodeContainerId)
				.filter(Objects::nonNull)
				.findFirst()
				.orElseThrow(() -> new IllegalArgumentException("Code container ID not found for Ray Task: " + fileSourceContext.config.sourceId));

		CodeContainer codeContainer = adminApiClient.getCodeContainer(codeContainerId);

		logger.info("Using CustomRtOnFlexTaskMapper");
		CustomRtOnFlexTaskMapper consumer = new CustomRtOnFlexTaskMapper(
				this.registry,
				offsetWriter,
				fileSourceContext,
				logger,
				Const.DEFAULT_SOURCE_FILE_PROCESSING_TASKS_TOPIC,
				dataSource, codeContainer, mode
		);

		return new MezzanineUploadStage(fileReader, consumer, adminApiClient);
	}

	private FileVaultClient newFileVaultClient() {
		return new FileVaultClient(
				config.fileVaultServer,
				config.nexlaUsername,
				config.nexlaPassword,
				new RestTemplateBuilder()
						.withSSL(config.nexlaSslConfig)
						.build()
		);
	}

	private ProcessingStage postProcessingAckStage(Map<String, String> props) {
		FileSourceConnectorConfig fileSourceConfig = new FileSourceConnectorConfig(props);
		TaskBus taskBus = new KafkaBasedTaskBusImpl(createTaskBusConsumer(fileSourceConfig), fileSourceConfig.sourceId)
				.start();

		return new PostProcessingAckStage(
				new ListingClient(
						config.listingAppServer,
						config.nexlaUsername, config.nexlaPassword,
						new RestTemplateBuilder().withSSL(config.nexlaSslConfig).build()
				),
				taskBus,
				this.registry
		);
	}

	private ProcessingStage postProcessingStage(Map<String, String> props) {
		HashMap<String, String> postProcessingProps = new HashMap<>(props);

//		XXX refactor this
		postProcessingProps.put(OVERRIDDEN_EXTENSIONS, "*:json");

		postProcessingProps.put(NexlaConstants.CREDS_ID, "-1");
		postProcessingProps.put(NexlaConstants.CREDS_ENC, props.get(CUSTOM_RT_CREDS_ENC));
		postProcessingProps.put(NexlaConstants.CREDS_ENC_IV, props.get(CUSTOM_RT_CREDS_ENCIV));

		final FileSourceContext fileSourceContext = new FileSourceContext(
				new FileSourceConnectorConfig(postProcessingProps),
				getDataSetForSource(dataSource.getDatasets()),
				runId,
				dataSource.getOrgId(),
				dataSource.getOwnerId()
		);

		FileSourceNotificationSender kafkaMessageSender = new FileSourceNotificationSender(controlMessageProducer, dataMessageProducer, FlowType.STREAMING, fileSourceContext, logger);
		FileSourceOffsetWriter offsetWriter = createOffsetWriter(kafkaMessageSender);
		KafkaFileSourceMessageGrouper messageGrouper = new KafkaFileSourceMessageGrouper(fileSourceContext, logger, runId);

        TransportFileReader fileReader = new TransportFileReader(
				kafkaMessageSender,
                initNewProbe(),
				schemaDetection,
				offsetWriter,
				messageGrouper,
				logger,
				of(context.offsetStorageReader()),
                empty(),
				empty(),
				fileSourceContext,
				of(this::heartbeat),
				config.listingEnabled
		);

		fileReader.setCustomParserEnabled(true);

		KafkaFileMessageResultReader nexlaMessageConsumer = new KafkaFileMessageResultReader(
				offsetWriter,
				messageGrouper,
				fileSourceContext,
				kafkaMessageSender,
				logger,
                initNewProbe()
		);

		TaskBus taskBus = new KafkaBasedTaskBusImpl(createTaskBusConsumer(fileSourceContext.config), fileSourceContext.config.sourceId)
				.start();

		return new IngestionStage(
                new ListingClient(
						config.listingAppServer,
						config.nexlaUsername, config.nexlaPassword,
						new RestTemplateBuilder().withSSL(config.nexlaSslConfig).build()
				),
				fileReader,
				nexlaMessageConsumer,
				taskBus,
				this.registry,
				adminApiClient
		);
	}

	private FileConnectorService initNewProbe() {
		FileConnectorService probeService = getProbeService(
				this.adminApiClient,
				this.listingClient,
				config.decryptKey,
				config.sourceType,
				newFileVaultClient()
		);

		probeService.initLogger(SOURCE, config.sourceId, Optional.empty());

		return probeService;
	}

	private KafkaConsumer<String, String> createTaskBusConsumer(FileSourceConnectorConfig config) {
		Map<String, Object> originals = config.originals();

		Map<String, Object> props = new HashMap<>();
		props.put(CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG, originals.getOrDefault("bootstrap.servers", originals.get("connect.bootstrap.servers")));
		props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
		props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
		props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
		props.put(ConsumerConfig.GROUP_ID_CONFIG, "post-processing-" + config.sourceId + "-" + this.runId + "-" + System.currentTimeMillis());
		props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "true"); // default, but just to be explicit
		props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "5000"); // default, but just to be explicit

		KafkaConsumer<String, String> consumer = new KafkaConsumer<>(
				new NexlaKafkaConfigApplier(config.dataKafkaContext).apply(props)
		);

		consumer.subscribe(List.of(Const.DEFAULT_SOURCE_FILE_PROCESSING_TASKS_TOPIC));

		return consumer;
	}

	private Optional<Integer> getDataSetForSource(List<DataSet> datasets) {
		if (datasets.isEmpty() || datasets.size() > 1) {
			return empty();
		}
		return ofNullable(datasets.get(0).getId());
	}

	public Map<String, String> customConnectorProperties(int sourceId) {
		Map<String, String> result = Maps.newHashMap();

		try {
			String connectorPrefix = SECRET + "_" + CONNECTOR_POLICY + "_" + "source_" + sourceId;
			Optional<Map<String, String>> vaultProps = this.nexlaCredentialsStore
					.getValuesMap(connectorPrefix);

			vaultProps
					.map(x -> x.get(ENCRYPT_PRIVATE_KEY))
					.ifPresent(x -> result.put(ENCRYPT_PRIVATE_KEY, x));

			vaultProps
					.map(x -> x.get(ENCRYPT_PRIVATE_PASSWORD))
					.ifPresent(x -> result.put(ENCRYPT_PRIVATE_PASSWORD, x));
		} catch (Exception e) {
			logger.error("Failed to load customConnectorProperties from CredentialsStore");
		}

		return result;
	}

	static FileConnectorService getProbeService(
		AdminApiClient adminApiClient,
		ListingClient listingClient,
		String decryptKey,
		ConnectionType sourceType,
		FileVaultClient fileVault
	) {
		FileConnectorServiceBuilder fileConnectorServiceBuilder = new FileConnectorServiceBuilder(adminApiClient, listingClient, decryptKey);
		fileConnectorServiceBuilder.setFileVault(fileVault);
		return fileConnectorServiceBuilder.createFileConnectorService(sourceType);
	}

	FileSourceOffsetWriter createOffsetWriter(FileSourceNotificationSender kafkaMessageSender) {
		return new KafkaFileSourceOffsetWriter(kafkaMessageSender, workarounds, logger, runId, config.listingEnabled);
	}

	@Override
	protected FileSourceConnectorConfig parseConfig(Map<String, String> props) {
		return new FileSourceConnectorConfig(props);
	}

	@SneakyThrows
	@Override
	public CollectRecordsResult collectRecords() {
		long startedQuery = System.currentTimeMillis();
		ScheduledFuture<?> heartbeat = SCHEDULED_POOL.scheduleWithFixedDelay(() ->
						ctrlClient
								.filter(x -> System.currentTimeMillis() - startedQuery < config.queryHeartbeatPeriodMs)
								.ifPresent(jpc -> {
									logger.info("Heartbeat. trace=false");
									jpc.heartbeatConnector(new Resource(config.sourceId, SOURCE), runId, false);
								}),
				1, 1, TimeUnit.MINUTES);
		try {
			logger.info("Collecting records...");
			ReadBatchResult<SourceRecord> sourceRecordReadBatchResult = flow.poll();
			logger.info("Collected records in {} ms", System.currentTimeMillis() - startedQuery);

			return new CollectRecordsResult(sourceRecordReadBatchResult.messages) {
				@Override
				public boolean hasMoreData() {
					return !sourceRecordReadBatchResult.listingIsEmpty && !registry.isEmpty();
				}
			};
		} finally {
			heartbeat.cancel(false);
		}
	}

	public ITransportFileReader getFileReader() {
		throw new UnsupportedOperationException("getFileReader is not supported in V2SourceTask");
	}

	@Override
	public void stop() throws ConnectException {
		stages.forEach(ProcessingStage::stop);

		flow.stop();

		super.stop();
	}

	@Override
	protected NexlaBackoff constructBackoff() {
		return new NexlaBackoff(logger, TimeUnit.SECONDS.toMillis(5));
	}
}
