package com.nexla.connector.file.source.custom.stage;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.connector.file.source.FileReadResult;
import com.nexla.connector.file.source.ReadBatchResult;
import com.nexla.connector.file.source.SchemaContext;
import com.nexla.connector.file.source.TransportFileReader;
import com.nexla.connector.file.source.custom.ProcessingStage;
import org.apache.kafka.connect.source.SourceRecord;

public class MezzanineUploadStage implements ProcessingStage {
    private final TransportFileReader fileReader;
    private final FileReadResult<SourceRecord> consumer;
    private final AdminApiClient adminApiClient;
    private boolean listingIsEmpty = false;

    public MezzanineUploadStage(TransportFileReader fileReader, FileReadResult<SourceRecord> consumer, AdminApiClient adminApiClient) {
        this.fileReader = fileReader;
        this.consumer = consumer;
        this.adminApiClient = adminApiClient;
    }

    @Override
    public ReadBatchResult<SourceRecord> poll() {
        // if there are no files to process, do not call listing-app#take-file (which in turn queries the db)
        if (listingIsEmpty) {
            return new ReadBatchResult<>(null, new SchemaContext(), false, true);
        }

        ReadBatchResult<SourceRecord> readResult = fileReader
                .readNextBatch(consumer, adminApiClient);

        if (readResult.listingIsEmpty) {
            listingIsEmpty = true;
        }

        return readResult;
    }

    @Override
    public void stop() {
        this.fileReader.stop();
    }
}
