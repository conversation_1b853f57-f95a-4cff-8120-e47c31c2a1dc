package com.nexla.connector.rest.sink;

import com.bazaarvoice.jolt.JsonUtils;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.Options;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.github.tomakehurst.wiremock.matching.AnythingPattern;
import com.github.tomakehurst.wiremock.matching.EqualToPattern;
import com.github.tomakehurst.wiremock.verification.LoggedRequest;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.Org;
import com.nexla.admin.client.Owner;
import com.nexla.admin.client.flownode.FlowNodeDatasource;
import com.nexla.admin.client.flownode.NexlaFlow;
import com.nexla.admin.client.pipeline.PDataSource;
import com.nexla.admin.client.pipeline.PDataset;
import com.nexla.admin.client.pipeline.Pipeline;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaConstants;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.exception.NexlaQuarantineMessage;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.rest.BatchResponseStrategy;
import com.nexla.connector.config.rest.RestSinkMode;
import com.nexla.listing.client.ListingClient;
import com.nexla.test.IntegrationTests;
import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.ConfigException;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.junit.*;
import org.junit.experimental.categories.Category;
import org.springframework.http.MediaType;
import org.testcontainers.containers.KafkaContainer;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.*;
import java.util.stream.IntStream;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToXml;
import static com.github.tomakehurst.wiremock.client.WireMock.findAll;
import static com.github.tomakehurst.wiremock.client.WireMock.patch;
import static com.github.tomakehurst.wiremock.client.WireMock.patchRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static com.google.common.collect.ImmutableMap.of;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.StreamUtils.map;
import static com.nexla.common.metrics.NexlaRawMetric.ERROR_COUNT;
import static com.nexla.common.metrics.NexlaRawMetric.HASH;
import static com.nexla.common.metrics.NexlaRawMetric.NAME;
import static com.nexla.common.metrics.NexlaRawMetric.RECORDS;
import static com.nexla.common.parse.ParserConfigs.Xml.XML_ROOT_TAG;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.BaseConnectorConfig.*;
import static com.nexla.connector.config.SinkConnectorConfig.MAPPING;
import static com.nexla.connector.config.rest.RestAuthConfig.HMAC_FUNC;
import static com.nexla.connector.config.rest.RestAuthConfig.JWT_CLAIM_AUDIENCE;
import static com.nexla.connector.config.rest.RestAuthConfig.JWT_CLAIM_EXPIRATION_SEC;
import static com.nexla.connector.config.rest.RestAuthConfig.JWT_CLAIM_ISSUER;
import static com.nexla.connector.config.rest.RestAuthConfig.JWT_CLAIM_SCOPE;
import static com.nexla.connector.config.rest.RestAuthConfig.JWT_CONTENT_TYPE;
import static com.nexla.connector.config.rest.RestAuthConfig.JWT_ENABLED;
import static com.nexla.connector.config.rest.RestAuthConfig.JWT_HTTP_URL;
import static com.nexla.connector.config.rest.RestAuthConfig.JWT_RESPONSE_TOKEN_PATH;
import static com.nexla.connector.config.rest.RestAuthConfig.JWT_RESPONSE_TOKEN_TYPE_PATH;
import static com.nexla.connector.config.rest.RestAuthConfig.JWT_TOKEN_SECRET;
import static com.nexla.connector.config.rest.RestAuthConfig.TOKEN_AUTH_HTTP_BODY;
import static com.nexla.connector.config.rest.RestAuthConfig.TOKEN_AUTH_HTTP_URL;
import static com.nexla.connector.config.rest.RestAuthConfig.TOKEN_AUTH_RESPONSE_TOKEN_PATH;
import static com.nexla.connector.config.rest.RestIterationConfig.REST_SINK_MODE;
import static com.nexla.connector.config.rest.RestSinkCallerConfig.BATCH_RESPONSE_ERROR_MESSAGE_FILTER;
import static com.nexla.connector.config.rest.RestSinkCallerConfig.BATCH_RESPONSE_FAILED_ITEMS_JSON_PATH;
import static com.nexla.connector.config.rest.RestSinkCallerConfig.BATCH_RESPONSE_ERROR_MESSAGES_JSON_PATH;
import static com.nexla.connector.config.rest.RestSinkCallerConfig.BATCH_RESPONSE_FAILURE_ENUM;
import static com.nexla.connector.config.rest.RestSinkCallerConfig.BATCH_RESPONSE_REQUEST_ID_FIELD;
import static com.nexla.connector.config.rest.RestSinkCallerConfig.BATCH_RESPONSE_RESPONSE_ID_FIELD;
import static com.nexla.connector.config.rest.RestSinkCallerConfig.BATCH_RESPONSE_STATUS_JSON_PATH;
import static com.nexla.connector.config.rest.RestSinkCallerConfig.BATCH_RESPONSE_STRATEGY;
import static com.nexla.connector.config.rest.RestSinkConnectorConfig.BATCH_GROUP_KEY;
import static com.nexla.connector.config.rest.RestSinkConnectorConfig.BATCH_MODE;
import static com.nexla.connector.config.rest.RestSinkConnectorConfig.BATCH_SIZE;
import static com.nexla.connector.config.rest.RestSinkConnectorConfig.BODY_TRANSFORM_FUNCTION;
import static com.nexla.connector.config.rest.RestSinkConnectorConfig.IGNORE_EMPTY_VALUES;
import static com.nexla.connector.config.rest.RestSinkConnectorConfig.REQUEST_PARALLELISM_COUNT;
import static com.nexla.connector.properties.RestConfigAccessor.*;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.util.MimeTypeUtils.APPLICATION_JSON;
import static org.springframework.util.MimeTypeUtils.APPLICATION_XML_VALUE;

@Category(IntegrationTests.class)
public class RestSinkTaskTest extends BaseKafkaTest {

	public static KafkaContainer kafka = new KafkaContainer("7.2.11");

	@BeforeClass
	public static void setUp() {
		kafka.withReuse(true)
			.start();
		init(kafka);
	}

	@AfterClass
	public static void tearDown() {
		kafka.stop();
	}

	@After
	@SneakyThrows
	public void after() {
		if (task != null && task.getControlMessageProducer() != null) {
			task.getControlMessageProducer().close();
		}
	}

	private static final HashMap<String, String> BASE_PARAMS = new HashMap<>() {{
		put(UNIT_TEST, "true");
		put(AUTH_TYPE, AuthType.NONE.name());

		put(CREDS_ENC, "1");
		put(CREDS_ENC_IV, "1");
		put(CREDENTIALS_DECRYPT_KEY, "1");

		put(SINK_ID, "1");
		put(FAST_MODE, "true"); // disable listing calls
	}};

	private static HashMap<String, String> getBaseParams() {
		HashMap<String, String> baseParams = new HashMap<>(BASE_PARAMS);
		baseParams.put(NexlaConstants.CONTROL_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		baseParams.put(NexlaConstants.DATA_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		baseParams.put(NexlaConstants.CONTROL_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);
		baseParams.put(NexlaConstants.DATA_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);
		return baseParams;
	}

	@ClassRule
	public static WireMockRule wireMockServer = new WireMockRule(Options.DYNAMIC_PORT);

	private RestSinkTask task;

	@Before
	public void onBefore() {
		AdminApiClient adminApiClient = mock(AdminApiClient.class);
		AdminApiClientBuilder.INSTANCE = adminApiClient;
		List<PDataset> dataSets = List.of(new PDataset(24, 32, null));
		PDataSource ds = new PDataSource();
		DataSink sink = new DataSink();
		when(adminApiClient.getPipeline(anyInt())).thenReturn(Optional.of(new Pipeline(dataSets, ds, sink)));

		when(adminApiClient.getDataSink(any(Integer.class))).thenReturn(Optional.of(
				new DataSink()));
		DataSink dataSink = new DataSink();
		dataSink.setId(1);
		dataSink.setConnectionType(ConnectionType.MYSQL);
		dataSink.setSinkConfig(Map.of("parallelism", "1"));
		dataSink.setStatus(ResourceStatus.ACTIVE);
		dataSink.setOwner(Owner.getOwnerById(22));
		dataSink.setOrg(Org.getOrgById(22));
		when(adminApiClient.getDataSink(any(Integer.class))).thenReturn(Optional.of(dataSink));
		when(adminApiClient.getFlowByResource(any())).thenReturn(Optional.of(new NexlaFlow(Collections.emptyList(),
			Collections.singletonList(new FlowNodeDatasource(1, 2, 3, 4, 5, "", "", ResourceStatus.ACTIVE, Collections.emptyList(),
				7, 8, 9, ConnectionType.MONGO, ConnectionType.MONGO, ConnectionType.MONGO)), Collections.emptyList(), Collections.emptyList())));

		wireMockServer.resetAll();
		this.task = new RestSinkTask() {

			@Override
			protected ListingClient createListingClient() {
				return mock(ListingClient.class);
			}
		};

		task.initialize(mock(SinkTaskContext.class));
		this.runBeforeTopicReading = Optional.of(() -> task.getControlMessageProducer().flush());
	}

	@SneakyThrows
	@Test
	public void put_patch() {

		withConsumer((metricsTopic, metricsConsumer) -> {

			stubFor(patch(urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToJson("{\"newid\":1}"))
				.willReturn(
					aResponse()
						.withBody("{\"ingestId\": 123}")
						.withStatus(200)));

			Map<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
				put(METHOD, "PATCH");
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut?id={id}&country={location.country}&city={location.city}");
				put(BODY_TEMPLATE, "{\"newid\":{newid}}");
				put(POLL_MS, "1");
				put(METRICS_TOPIC, metricsTopic);
				put(IGNORE_EMPTY_VALUES, "true");
				put(MAPPING, "{\"mode\":\"manual\",\"mapping\":{\"id\": [\"newid\"] } }");
			}};

			task.start(baseProps);

			LinkedHashMap<String, Object> data = new LinkedHashMap<>();
			data.put("id", "1");
			data.put("location.city", "SF");
			data.put("comment", "Don‚Äôt like color");
			data.put("location", map("country", "USA"));
			data.put("thisshouldbeabsent1", null);
			data.put("thisshouldbeabsent2", "");

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(new NexlaMessage(data, metadata())), 1)));

			verify(patchRequestedFor(
				urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToJson("{\"newid\":1}")));

		});
	}

	@Test
	@Ignore // for manual testing purposes
	public void putMultipart() {

		String jsonMessage = "{\n" +
			"  \"incoming\": false,\n" +
			"  \"private\": true,\n" +
			"  \"ticket_id\": 27,\n" +
			"  \"body\": \"Note on FreshService - <div>Added Grand River Woods 2 NVR is unreachable</div>\",\n" +
			"  \"notify_emails\": [],\n" +
			"  \"attachments\": [\n" +
			"    {\n" +
			"      \"attachment_name\": \"test_image.jpg\",\n" +
			"      \"attachment_url\": \"https://mik.tv/upload/iblock/067/mik.jpg\",\n" +
			"      \"content_type\": \"image/jpeg\",\n" +
			"      \"size\": 28979\n" +
			"    }\n" +
			"  ]\n" +
			"}";

		LinkedHashMap<String, Object> data = JsonUtils.stringToType(jsonMessage, LinkedHashMap.class);

		Map<String, String> baseProps = new HashMap<>(getBaseParams()) {{
			put(METHOD, "POST");
			put(URL_TEMPLATE, "http://localhost:8282/aaa");
			put(BODY_TEMPLATE, "{\"id\":{id},\"city\":\"{location.city}\",\"country\":\"{location.country}\"}");
			put(INGEST_URL_PARAM, "http://localhost:" + wireMockServer.port() + "/ingestUrl");
			put(INGEST_DATASOURCE_PARAM, "123");
			put(POLL_MS, "1");
			put(REST_SINK_MODE, RestSinkMode.FILE_UPLOAD.name());
		}};

		task.start(baseProps);

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
			toJsonString(new NexlaMessage(data, metadata())), 1)));

	}

	@SneakyThrows
	@Test
	public void put() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {

			String ingestRequestBody =
				"{\n" +
				"  \"input\" : {\n" +
				"    \"rawMessage\" : {\n" +
				"      \"id\" : \"1\",\n" +
				"      \"location.city\" : \"SF\",\n" +
				"      \"comment\" : \"Don‚Äôt like color\",\n" +
				"      \"location\" : {\n" +
				"        \"country\" : \"USA\",\n" +
				"        \"thisshouldbeabsent3\" : \"\"\n" +
				"      },\n" +
				"      \"thisshouldbeabsent1\" : null,\n" +
				"      \"thisshouldbeabsent2\" : \"\"\n" +
				"    },\n" +
				"    \"nexlaMetaData\" : {\n" +
				"      \"sourceType\" : null,\n" +
				"      \"ingestTime\" : null,\n" +
				"      \"sourceOffset\" : null,\n" +
				"      \"sourceKey\" : null,\n" +
				"      \"topic\" : null,\n" +
				"      \"datasetId\" : null,\n" +
				"      \"resourceType\" : null,\n" +
				"      \"resourceId\" : null,\n" +
				"      \"trackerId\" : null,\n" +
				"      \"eof\" : false,\n" +
				"      \"lastModified\" : null,\n" +
				"      \"runId\" : 1,\n" +
				"      \"tags\" : null\n" +
				"    }\n" +
				"  },\n" +
				"  \"request\" : {\n" +
				"    \"id\" : 1,\n" +
				"    \"city\" : \"SF\",\n" +
				"    \"country\" : \"USA\"\n" +
				"  },\n" +
				"  \"response\" : {\n" +
				"    \"ingestId\" : 123\n" +
				"  }\n" +
				"}";

			stubFor(post(urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}"))
				.willReturn(
					aResponse()
						.withBody("{\"ingestId\": 123}")
						.withStatus(200)));

			stubFor(post(urlEqualTo("/ingestUrl/123"))
				.withRequestBody(equalToJson(ingestRequestBody))
				.willReturn(
					aResponse()
						.withBody(ingestRequestBody)
						.withStatus(200)));

			Map<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
				put(SINK_ID, String.valueOf(sinkId));
				put(METHOD, "POST");
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut?id={id}&country={location.country}&city={location.city}");
				put(BODY_TEMPLATE, "{\"id\":{id},\"city\":\"{location.city}\",\"country\":\"{location.country}\"}");
				put(INGEST_URL_PARAM, "http://localhost:" + wireMockServer.port() + "/ingestUrl");
				put(INGEST_DATASOURCE_PARAM, "123");
				put(POLL_MS, "1");
				put(METRICS_TOPIC, metricsTopic);
				put(IGNORE_EMPTY_VALUES, "true");
			}};

			task.start(baseProps);

			LinkedHashMap<String, Object> data = new LinkedHashMap<>();
			data.put("id", "1");
			data.put("location.city", "SF");
			data.put("comment", "Don‚Äôt like color");
			data.put("location", map("country", "USA", "thisshouldbeabsent3", ""));
			data.put("thisshouldbeabsent1", null);
			data.put("thisshouldbeabsent2", "");

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(new NexlaMessage(data, metadata())), 1)));

			verify(postRequestedFor(
				urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}")));

			verify(postRequestedFor(
				urlEqualTo("/ingestUrl/123"))
				.withRequestBody(equalToJson(ingestRequestBody)));

			assertMetrics(metricsConsumer, "/testPut?id=1&country=USA&city=SF", 1, sinkId);
		}, "metrics");
	}

	@SneakyThrows
	@Test
	public void put_xml() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {

			stubFor(post(urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToXml("<root><id>1</id><location.city>SF</location.city><comment>comment</comment><location><country>USA</country></location></root>")));

			Map<String, String> baseProps = new HashMap<>(getBaseParams()) {{
				put(SINK_ID, String.valueOf(sinkId));
				put(METHOD, "POST");
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut?id={id}&country={location.country}&city={location.city}");
				put(BODY_TEMPLATE, "{message.json}");
				put(POLL_MS, "1");
				put(METRICS_TOPIC, metricsTopic);
				put(IGNORE_EMPTY_VALUES, "true");
				put(CONTENT_TYPE, APPLICATION_XML_VALUE);
			}};

			task.start(baseProps);

			LinkedHashMap<String, Object> data = new LinkedHashMap<>();
			data.put("id", "1");
			data.put("location.city", "SF");
			data.put("comment", "comment");
			data.put("location", map("country", "USA"));

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(new NexlaMessage(data, metadata())), 1)));

			verify(postRequestedFor(
				urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToXml("<root><id>1</id><location.city>SF</location.city><comment>comment</comment><location><country>USA</country></location></root>")));

			assertMetrics(metricsConsumer, "/testPut?id=1&country=USA&city=SF", 1, sinkId);
		}, "metrics");
	}

	@SneakyThrows
	@Test
	public void put_xml_suppress_root() {

		withConsumer((metricsTopic, metricsConsumer) -> {

			stubFor(post(urlEqualTo("/testPut"))
				.withRequestBody(equalToXml("<location><country>USA</country></location>")));

			Map<String, String> baseProps = new HashMap<>(getBaseParams()) {{
				put(METHOD, "POST");
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut");
				put(BODY_TEMPLATE, "{message.json}");
				put(POLL_MS, "1");
				put(METRICS_TOPIC, metricsTopic);
				put(IGNORE_EMPTY_VALUES, "true");
				put(CONTENT_TYPE, APPLICATION_XML_VALUE);
				put(XML_ROOT_TAG, "");
			}};

			task.start(baseProps);

			LinkedHashMap<String, Object> data = new LinkedHashMap<>();
			data.put("location", map("country", "USA"));

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(new NexlaMessage(data, metadata())), 1)));

			verify(postRequestedFor(
				urlEqualTo("/testPut"))
				.withRequestBody(equalToXml("<location><country>USA</country></location>")));
		});
	}

	@SneakyThrows
	@Test
	public void put_tracker_id() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {

			String ingestRequestBody = "{\n" +
									   "  \"input\": {\n" +
									   "    \"rawMessage\": {\n" +
									   "      \"id\": \"1\",\n" +
									   "      \"location.city\": \"SF\",\n" +
									   "      \"comment\": \"Don‚Äôt like color\",\n" +
									   "      \"location\": {\n" +
									   "        \"country\": \"USA\"\n," +
									   "        \"thisshouldbeabsent3\": null,\n" +
									   "        \"thisshouldbeabsent4\": \"\"\n" +
									   "      },\n" +
									   "      \"thisshouldbeabsent1\": null,\n" +
									   "      \"thisshouldbeabsent2\": \"\"\n" +
									   "    },\n" +
									   "    \"nexlaMetaData\": {\n" +
									   "      \"sourceType\": null,\n" +
									   "      \"ingestTime\": null,\n" +
									   "      \"sourceOffset\": null,\n" +
									   "      \"sourceKey\": null,\n" +
				             "      \"topic\": null,\n" +
										 "      \"datasetId\": null,\n" +
									   "      \"resourceType\": null,\n" +
									   "      \"resourceId\": null,\n" +
									   "      \"trackerId\": {\n" +
									   "        \"source\": {\n" +
									   "          \"sourceId\": 5010,\n" +
									   "          \"dataSetId\": 123,\n" +
									   "          \"source\": \"1\",\n" +
									   "          \"recordNumber\": 123,\n" +
									   "          \"version\": 123,\n" +
									   "          \"initialIngestTimestamp\": 123\n" +
									   "        },\n" +
									   "        \"sets\": [],\n" +
									   "        \"sink\": {\n" +
									   "          \"id\": " + sinkId + ",\n" +
									   "          \"version\": 1,\n" +
									   "          \"offsetFromIngest\": null\n" +
									   "        },\n" +
									   "        \"trackerMode\": \"FULL\"\n" +
									   "      },\n" +
									   "      \"eof\": false,\n" +
									   "      \"lastModified\": null,\n" +
									   "      \"runId\": 1,\n" +
									   "      \"tags\": null\n" +
									   "    }\n" +
									   "  },\n" +
									   "  \"request\": {\n" +
									   "    \"id\": 1,\n" +
									   "    \"city\": \"SF\",\n" +
									   "    \"country\": \"USA\"\n" +
									   "  },\n" +
									   "  \"response\": {\n" +
									   "    \"ingestId\": 123\n" +
									   "  }\n" +
									   "}";

			stubFor(post(urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}"))
				.willReturn(
					aResponse()
						.withBody("{\"ingestId\": 123}")
						.withStatus(200)));

			stubFor(post(urlEqualTo("/ingestUrl/123"))
				.willReturn(
					aResponse()
						.withStatus(200)));

			MappingConfig mappingConfig = new MappingConfig();
			mappingConfig.setTrackerMode(Tracker.TrackerMode.RECORD);
			mappingConfig.setTrackerFieldName("mytracker");
			mappingConfig.setMode(MappingConfig.MODE_AUTO);

			Map<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
				put(SINK_ID, String.valueOf(sinkId));
				put(METHOD, "POST");
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut?id={id}&country={location.country}&city={location.city}");
				put(BODY_TEMPLATE, "{\"id\":{id},\"city\":\"{location.city}\",\"country\":\"{location.country}\"}");
				put(INGEST_URL_PARAM, "http://localhost:" + wireMockServer.port() + "/ingestUrl");
				put(INGEST_DATASOURCE_PARAM, "123");
				put(POLL_MS, "1");
				put(METRICS_TOPIC, metricsTopic);
				put(IGNORE_EMPTY_VALUES, "true");
				put(MAPPING, toJsonString(mappingConfig));
			}};

			task.start(baseProps);

			LinkedHashMap<String, Object> data = new LinkedHashMap<>();
			data.put("id", "1");
			data.put("location.city", "SF");
			data.put("comment", "Don‚Äôt like color");
			data.put("location", map("country", "USA", "thisshouldbeabsent3", null, "thisshouldbeabsent4", ""));
			data.put("thisshouldbeabsent1", null);
			data.put("thisshouldbeabsent2", "");

			NexlaMetaData nexlaMetaData = new NexlaMetaData();
			nexlaMetaData.setTrackerId(Tracker.parse("u5010:123:1:123:123:123;4444:1:1010"));
			nexlaMetaData.setRunId(1L);
			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(new NexlaMessage(data, nexlaMetaData)), 1)));

			verify(postRequestedFor(
				urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}")));

			List<LoggedRequest> ingested = findAll(postRequestedFor(
				urlEqualTo("/ingestUrl/123")));

			Map<String, Object> ingestedMap = JsonUtils.jsonToMap(new String(ingested.get(0).getBody()));

			Map<String, String> request = (Map<String, String>) ingestedMap.get("request");
			Map<String, Object> inputMap = (Map<String, Object>) ingestedMap.get("input");
			Map<String, Object> nexlaMetaDataMap = (Map<String, Object>) inputMap.get("nexlaMetaData");
			Map<String, Object> trackerIdMap = (Map<String, Object>) nexlaMetaDataMap.get("trackerId");
			Map<String, Long> sinkMap = (Map<String, Long>) trackerIdMap.get("sink");

			assertEquals(ingestedMap.get("input"), JsonUtils.jsonToMap(ingestRequestBody).get("input"));

			assertMetrics(metricsConsumer, "/testPut?id=1&country=USA&city=SF", 1, sinkId);
		}, "metrics");
	}

	private void assertMetrics(KafkaConsumer<String, String> metricsConsumer, String urlSuffix,
														 int records, int sinkId) {
		List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
		assertNexlaMetric(metrics.get(0), urlSuffix, records, sinkId);
	}

	@SneakyThrows
	@Test
	public void put_hmac() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			stubFor(post(urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}"))
				.willReturn(
					aResponse()
						.withBody("{\"ingestId\": 123}")
						.withStatus(200)));

			stubFor(post(urlEqualTo("/hmacSigner"))
				.willReturn(
					aResponse()
						.withBody("{\n" +
							"    \"url\": \"http://localhost:" + wireMockServer.port() + "/testPut?id=1&country=USA&city=SF\",\n" +
							"    \"method\": \"POST\",\n" +
							"    \"body\": \"{\\\"id\\\":1,\\\"city\\\":\\\"SF\\\",\\\"country\\\":\\\"USA\\\"}\",\n" +
							"    \"restHeaders\": {\n" +
							"        \"requestHeaders\": {\n" +
							"            \"key\": \"keykey\",\n" +
							"            \"accept\": \"application/json\",\n" +
							"            \"signature\": \"Kax6hmbxnYdS60E070WAgbKOo+c=\"\n" +
							"        },\n" +
							"        \"contentType\": \"application/json\"\n" +
							"    }\n" +
							"}")
						.withStatus(200)));

			String hmacFuncRaw =
				"import java.util.Base64\n" +
				"import scala.collection.mutable.Map\n" +
				"import scala.collection.JavaConverters._\n" +
				"import com.nexla.common.datetime.DateTimeUtils.nowUTC\n" +
				"import com.nexla.connector.config.rest.HttpCallParameters\n" +
				"import org.apache.commons.codec.digest.{HmacAlgorithms, HmacUtils}\n" +
				"import org.apache.commons.httpclient.util.URIUtil\n" +
				"val method = rest.getMethod.name.toLowerCase\n" +
				"val contentType = rest.getContentType.toLowerCase\n" +
				"val url = URIUtil.getQuery(rest.getUrl)\n" +
				"val key = authConfig.originals.get(\"key\").toString\n" +
				"val canonical = method + contentType + url + key\n" +
				"val binarySign = new HmacUtils(HmacAlgorithms.HMAC_SHA_1, key).hmac(canonical)\n" +
				"val sign = Base64.getEncoder.encodeToString(binarySign)\n" +
				"val headers = Map[String, String]()\n" +
				"headers.put(\"accept\", \"application/json\")\n" +
				"headers.put(\"key\", key)\n" +
				"headers.put(\"signature\", sign)\n" +
				"new HttpCallParameters(rest.getUrl, rest.getMethod, rest.getBody, rest.getContentType, headers.asJava)";

			String hmacFunc = Base64.getEncoder().encodeToString(hmacFuncRaw.getBytes());

			HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
				put(SINK_ID, String.valueOf(sinkId));
				put(METHOD, "POST");
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut?id={id}&country={location.country}&city={location.city}");
				put(BODY_TEMPLATE, "{\"id\":{id},\"city\":\"{location.city}\",\"country\":\"{location.country}\"}");
				put(POLL_MS, "1");
				put("key", "keykey");
				put(HMAC_FUNC, hmacFunc);
				put(METRICS_TOPIC, metricsTopic);
				put(PROBE_APP_URL, "http://localhost:" + wireMockServer.port());
			}};

			task.start(baseProps);

			LinkedHashMap<String, Object> data = new LinkedHashMap<>();
			data.put("id", "1");
			data.put("location.city", "SF");
			data.put("location",
				map("country", "USA"));

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(new NexlaMessage(data, metadata())), 1)));

			verify(postRequestedFor(
				urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withHeader("key", new EqualToPattern("keykey"))
				.withHeader("signature", new EqualToPattern("Kax6hmbxnYdS60E070WAgbKOo+c="))
				.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}")));

			assertMetrics(metricsConsumer, "/testPut?id=1&country=USA&city=SF", 1, sinkId);
		}, "metrics");
	}

	@Test
	public void put_plain_answer() {
		String ingestRequestBody =
			"{\n" +
			"  \"input\" : {\n" +
			"    \"rawMessage\" : {\n" +
			"      \"id\" : \"1\",\n" +
			"      \"location.city\" : \"SF\",\n" +
			"      \"location\" : {\n" +
			"        \"country\" : \"USA\"\n" +
			"      }\n" +
			"    },\n" +
			"    \"nexlaMetaData\" : {\n" +
			"      \"sourceType\" : null,\n" +
			"      \"ingestTime\" : null,\n" +
			"      \"sourceOffset\" : null,\n" +
			"      \"sourceKey\" : null,\n" +
			"      \"topic\" : null,\n" +
			"      \"datasetId\" : null,\n" +
			"      \"resourceType\" : null,\n" +
			"      \"resourceId\" : null,\n" +
			"      \"trackerId\" : null,\n" +
			"      \"eof\" : false,\n" +
			"      \"lastModified\" : null,\n" +
			"      \"runId\" : 1,\n" +
			"      \"tags\" : null\n" +
			"    }\n" +
			"  },\n" +
			"  \"request\" : {\n" +
			"    \"id\" : 1,\n" +
			"    \"city\" : \"SF\",\n" +
			"    \"country\" : \"USA\"\n" +
			"  },\n" +
			"  \"response\" : {\n" +
			"    \"value\" : \"ingestId = 123\"\n" +
			"  }\n" +
			"}";

		stubFor(post(urlEqualTo("/testPut?id=1&country=USA&city=SF"))
			.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}"))
			.willReturn(
				aResponse()
					.withBody("ingestId = 123")
					.withStatus(200)));

		stubFor(post(urlEqualTo("/ingestUrl/123"))
			.withRequestBody(equalToJson(ingestRequestBody))
			.willReturn(
				aResponse()
					.withBody(ingestRequestBody)
					.withStatus(200)));

		HashMap<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
			put(METHOD, "POST");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut?id={id}&country={location.country}&city={location.city}");
			put(BODY_TEMPLATE, "{\"id\":{id},\"city\":\"{location.city}\",\"country\":\"{location.country}\"}");
			put(INGEST_URL_PARAM, "http://localhost:" + wireMockServer.port() + "/ingestUrl");
			put(INGEST_DATASOURCE_PARAM, "123");
			put(POLL_MS, "1");
		}};

		task.start(baseProps);

		LinkedHashMap<String, Object> data = new LinkedHashMap<>();
		data.put("id", "1");
		data.put("location.city", "SF");
		data.put("location",
			map("country", "USA"));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
			toJsonString(new NexlaMessage(data, metadata())), 1)));

		verify(postRequestedFor(
			urlEqualTo("/testPut?id=1&country=USA&city=SF"))
			.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}")));

		verify(postRequestedFor(
			urlEqualTo("/ingestUrl/123"))
			.withRequestBody(equalToJson(ingestRequestBody)));
	}

	public static byte[] iv() {
		byte[] iv = new byte[12];
		new SecureRandom().nextBytes(iv);
		return iv;
	}

	public static byte[] encrypt(Map<String, String> credsMap, SecretKey secret, byte[] iv) throws Exception {
		Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");

		GCMParameterSpec gcmSpecWithIV = new GCMParameterSpec(128, iv);
		cipher.init(Cipher.ENCRYPT_MODE, secret, gcmSpecWithIV);

		return cipher.doFinal(JsonUtils.toJsonString(credsMap).getBytes(UTF_8));
	}

	private static Map<String, String> baseConfigWithCreds(Map<String, String> credentials) throws Exception {
		byte[] iv = iv();

		String decryptKey = "e2bdf6f807bgf66316c38acb0cc01919";
		SecretKey key = new SecretKeySpec(decryptKey.getBytes(UTF_8), "AES");

		String credsEnc = new String(Base64.getEncoder().encode(encrypt(credentials, key, iv)), UTF_8);
		String credsEncIv = new String(Base64.getEncoder().encode(iv), UTF_8);

		Map<String, String> baseConfig = new HashMap<>(getBaseParams());
//		baseConfig.put(UNIT_TEST, "false");
		baseConfig.put(CREDS_ENC, credsEnc);
		baseConfig.put(CREDS_ENC_IV, credsEncIv);
		baseConfig.put(CREDENTIALS_DECRYPT_KEY, decryptKey);

		baseConfig.putAll(credentials);

		return baseConfig;
	}

	@SneakyThrows
	@Test
	public void put_substituteCredentials() {
		String token = UUID.randomUUID().toString();

		String xAuth = "x-auth: " + token;
		String cursor = "cursor: 5";

		stubFor(
				post(urlEqualTo("/test?auth=" + token)) //
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody("{\"status\": \"ok\"}")
						)
		);


		Map<String, String> base = baseConfigWithCreds(Map.of(
				"request.headers", xAuth + ", " + cursor
		));


		HashMap<String, String> baseProps = new HashMap<String, String>(base) {{
			put(METHOD, "POST");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test?auth={data_credential[\"request.headers\"][\"x-auth\"]}");
			put(BODY_TEMPLATE, "{\"id\":{id},\"city\":\"{location.city}\",\"country\":\"{location.country}\"}");
			put(POLL_MS, "1");
		}};

		task.start(baseProps);

		LinkedHashMap<String, Object> data = new LinkedHashMap<>();
		data.put("id", "1");
		data.put("location.city", "SF");
		data.put("location", map("country", "USA"));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
			toJsonString(new NexlaMessage(data, metadata())), 1)));

		verify(postRequestedFor(
			urlEqualTo("/test?auth=" + token))
			.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}")));
	}

	@Test
	public void put_xml_answer_and_mapping_ingest() {
		String ingestRequestBody =
			"{\n" +
			"  \"input\" : {\n" +
			"    \"rawMessage\" : {\n" +
			"      \"id\" : \"1\",\n" +
			"      \"location.city\" : \"SF\"\n" +
			"    },\n" +
			"    \"nexlaMetaData\" : {\n" +
			"      \"sourceType\" : null,\n" +
			"      \"ingestTime\" : null,\n" +
			"      \"sourceOffset\" : null,\n" +
			"      \"sourceKey\" : null,\n" +
			"      \"topic\" : null,\n" +
			"      \"datasetId\" : null,\n" +
			"      \"resourceType\" : null,\n" +
			"      \"resourceId\" : null,\n" +
			"      \"trackerId\" : null,\n" +
			"      \"eof\" : false,\n" +
			"      \"lastModified\" : null,\n" +
			"      \"runId\" : 1,\n" +
			"      \"tags\" : null\n" +
			"    }\n" +
			"  },\n" +
			"  \"request\" : {\n" +
			"    \"id\" : 1,\n" +
			"    \"city\" : \"SF\"\n" +
			"  },\n" +
			"  \"response\" : {\n" +
			"    \"ingestId\" : \"123\"\n" +
			"  }\n" +
			"}";

		stubFor(post(urlEqualTo("/testPut?id=1&city=SF"))
			.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\"}"))
			.willReturn(
				aResponse()
					.withBody("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
							  "<ingestId>123</ingestId>\n")
					.withHeader("Content-Type", "application/xhtml+xml")
					.withStatus(200)));

		stubFor(post(urlEqualTo("/ingestUrl/123"))
			.withRequestBody(equalToJson(ingestRequestBody))
			.willReturn(
				aResponse()
					.withBody(ingestRequestBody)
					.withStatus(200)));

		HashMap<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
			put(METHOD, "POST");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut?id={renamed_id}&city={location.city}");
			put(BODY_TEMPLATE, "{\"id\":{renamed_id},\"city\":\"{location.city}\"}");
			put(INGEST_URL_PARAM, "http://localhost:" + wireMockServer.port() + "/ingestUrl");
			put(INGEST_DATASOURCE_PARAM, "123");
			put(POLL_MS, "1");
			put(MAPPING, "{\"trackerMode\":\"NONE\",\"mode\":\"manual\",\"mapping\":{\"id\":[\"renamed_id\"],\"location.city\":[\"location.city\"]}}");
		}};

		task.start(baseProps);

		LinkedHashMap<String, Object> data = new LinkedHashMap<>();
		data.put("id", "1");
		data.put("location.city", "SF");

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
			toJsonString(new NexlaMessage(data, metadata())), 1)));

		verify(postRequestedFor(
			urlEqualTo("/testPut?id=1&city=SF"))
			.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\"}")));

		verify(postRequestedFor(
			urlEqualTo("/ingestUrl/123"))
			.withRequestBody(equalToJson(ingestRequestBody)));
	}

	@SneakyThrows
	@Test
	public void put_jwt() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {

			stubFor(post(urlEqualTo("/access_token"))
				.withBasicAuth("test_user", "qwerty")
				.withRequestBody(new AnythingPattern())
				.willReturn(aResponse()
					.withStatus(200)
					.withHeader("Content-Type", "application/json")
					.withBody(
						"{\n" +
						"\"access_token\": 11111, " +
						"\"expires_in\": 100, " +
						"\"token_type\": \"Bearer\"\n" +
						"}\n")));

			stubFor(post(urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}"))
				.withHeader("Authorization", equalTo("Bearer 11111"))
				.willReturn(aResponse().withStatus(200)));

			HashMap<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
				put(SINK_ID, String.valueOf(sinkId));
				put(METHOD, "POST");
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut?id={id}&country={location.country}&city={location.city}");
				put(BODY_TEMPLATE, "{\"id\":{id},\"city\":\"{location.city}\",\"country\":\"{location.country}\"}");
				put(POLL_MS, "1");

				put(JWT_ENABLED, "true");
				put(JWT_HTTP_URL, "http://localhost:" + wireMockServer.port() + "/access_token");
				put(JWT_RESPONSE_TOKEN_PATH, "$.access_token");
				put(JWT_RESPONSE_TOKEN_TYPE_PATH, "$.token_type");
				put(JWT_TOKEN_SECRET, "secret");
				put(JWT_CLAIM_AUDIENCE, "audience");
				put(JWT_CLAIM_EXPIRATION_SEC, "100");
				put(JWT_CLAIM_ISSUER, "issuer");
				put(JWT_CLAIM_SCOPE, "scope");
				put(JWT_CONTENT_TYPE, APPLICATION_JSON.toString());

				put(AUTH_TYPE, AuthType.BASIC.name());
				put(BASIC_USERNAME, "test_user");
				put(BASIC_PASSWORD, "qwerty");

				put(METRICS_TOPIC, metricsTopic);
			}};

			task.start(baseProps);

			LinkedHashMap<String, Object> data = new LinkedHashMap<>();
			data.put("id", "1");
			data.put("location.city", "SF");
			data.put("location", map("country", "USA"));

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(new NexlaMessage(data, metadata())), 1)));

			verify(postRequestedFor(
				urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}")));

			assertMetrics(metricsConsumer, "/testPut?id=1&country=USA&city=SF", 1, sinkId);
		}, "metrics");
	}

	@SneakyThrows
	@Test
	public void put_auth_token() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {

			stubFor(post(urlEqualTo("/access_token"))
				.withHeader("Authorization", new EqualToPattern("Basic dGVzdF91c2VyOnF3ZXJ0eQ=="))
				.withRequestBody(new EqualToPattern("grant_type=password&username=<EMAIL>&password=passwordtoken"))
				.willReturn(aResponse()
					.withStatus(200)
					.withBody("{ \"token\": \"accesstoken\" }")));

			stubFor(post(urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}"))
				.withHeader("Authorization", equalTo("Bearer accesstoken"))
				.willReturn(aResponse().withStatus(200)));

			HashMap<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
				put(SINK_ID, String.valueOf(sinkId));
				put(METHOD, "POST");
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut?id={id}&country={location.country}&city={location.city}");
				put(BODY_TEMPLATE, "{\"id\":{id},\"city\":\"{location.city}\",\"country\":\"{location.country}\"}");
				put(POLL_MS, "1");

				put(AUTH_TYPE, AuthType.TOKEN.name());
				put(TOKEN_AUTH_HTTP_URL, "http://localhost:" + wireMockServer.port() + "/access_token");
				put(TOKEN_AUTH_HTTP_BODY, "grant_type=password&username=<EMAIL>&password=passwordtoken");
				put(TOKEN_AUTH_RESPONSE_TOKEN_PATH, "$.token");
				put(BASIC_USERNAME, "test_user");
				put(BASIC_PASSWORD, "qwerty");

				put(METRICS_TOPIC, metricsTopic);
			}};

			task.start(baseProps);

			LinkedHashMap<String, Object> data = new LinkedHashMap<>();
			data.put("id", "1");
			data.put("location.city", "SF");
			data.put("location", map("country", "USA"));

			task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(new NexlaMessage(data, metadata())), 1)));

			verify(postRequestedFor(
				urlEqualTo("/testPut?id=1&country=USA&city=SF"))
				.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}")));

			verify(postRequestedFor(
				urlEqualTo("/access_token"))
				.withHeader("Authorization", new EqualToPattern("Basic dGVzdF91c2VyOnF3ZXJ0eQ=="))
				.withRequestBody(equalTo("grant_type=password&username=<EMAIL>&password=passwordtoken")));

			assertMetrics(metricsConsumer, "/testPut?id=1&country=USA&city=SF", 1, sinkId);
		}, "metrics");
	}

	@SneakyThrows
	@Test
	public void put_parallel() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {

			IntStream.range(1, 5 + 1).boxed().forEach(i ->
				stubFor(post(urlEqualTo("/testPut?id=" + i)).willReturn(aResponse().withStatus(200))));

			HashMap<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
				put(SINK_ID, String.valueOf(sinkId));
				put(METHOD, "POST");
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut?id={id}");
				put(REQUEST_PARALLELISM_COUNT, "5");

				put(METRICS_TOPIC, metricsTopic);
			}};

			task.start(baseProps);

			task.put(
				IntStream.range(1, 5 + 1).boxed()
					.map(i -> {
						Map<String, Object> data = map("id", i);
						return new SinkRecord("test-topic", 1, null, null, null,
							toJsonString(new NexlaMessage(new LinkedHashMap<>(data), metadata())), i);
					})
					.collect(toList()));

			IntStream.range(1, 5 + 1).boxed().forEach(i -> verify(postRequestedFor(urlEqualTo("/testPut?id=" + i))));

			assertMetrics(metricsConsumer, "/testPut?id=1", 5, sinkId);

		}, "metrics");
	}

	private void assertNexlaMetric(NexlaRawMetric metric, String urlSuffix, Integer records, Integer sinkId) {
		assertTrue(metric.getTags().get(NAME).endsWith(urlSuffix));
		assertEquals("Invalid records", records, metric.getFields().get(RECORDS));
		assertEquals("Invalid resource Id", sinkId, metric.getResourceId());
		assertEquals("Invalid resource Type", SINK, metric.getResourceType());
		assertNull("Invalid Error Message", metric.getFields().get(EXCEPTION_TRACE));
		assertNull("Invalid Hash", metric.getFields().get(HASH));
	}

	@Test
	public void put_urlEncoded() {

		stubFor(post(urlPathEqualTo("/testPutUrlEncoded")).willReturn(aResponse().withStatus(200).withBody("OK")));

		HashMap<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
			put(METHOD, "POST");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPutUrlEncoded");
			put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
		}};

		task.start(baseProps);

		LinkedHashMap<String, Object> data = new LinkedHashMap<>();
		data.put("message+id", "test/5");
		data.put("message+data", "Hello from Nexla");

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
			toJsonString(new NexlaMessage(data, metadata())), 1)));

		verify(postRequestedFor(
			urlPathEqualTo("/testPutUrlEncoded"))
			.withRequestBody(equalTo("message%2Bid=test%2F5&message%2Bdata=Hello%20from%20Nexla"))
			.withHeader("Content-Type", equalTo(MediaType.APPLICATION_FORM_URLENCODED_VALUE)));
	}

	@Test
	public void put_urlEncoded_body() {

		stubFor(post(urlPathEqualTo("/testPutUrlEncoded")).willReturn(aResponse().withStatus(200).withBody("OK")));

		HashMap<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
			put(METHOD, "POST");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPutUrlEncoded");
			put(BODY_TEMPLATE, "message+id={message+id}&message+data={message+data}");
			put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
		}};

		task.start(baseProps);

		LinkedHashMap<String, Object> data = new LinkedHashMap<>();
		data.put("message+id", "test/5");
		data.put("message+data", "Hello from Nexla");

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
			toJsonString(new NexlaMessage(data, metadata())), 1)));

		verify(postRequestedFor(
			urlPathEqualTo("/testPutUrlEncoded"))
			.withRequestBody(equalTo("message+id=test%2F5&message+data=Hello%20from%20Nexla"))
			.withHeader("Content-Type", equalTo(MediaType.APPLICATION_FORM_URLENCODED_VALUE)));
	}

	@SneakyThrows
	@Test
	public void put_batchBody_FB() {
		withConsumer((metricsTopic, metricsConsumer) -> {

			stubFor(post(urlPathEqualTo("/testPutUrlEncoded")).willReturn(aResponse().withStatus(200).withBody("OK")));

			stubFor(post(urlEqualTo("/bodyMaker64"))
				.willReturn(
					aResponse()
						.withBody("{\n" +
							"    \"data\": [\n" +
							"        [\n" +
							"            \"fn1\",\n" +
							"            \"ln1\",\n" +
							"            \"email1\"\n" +
							"        ],\n" +
							"        [\n" +
							"            \"fn2\",\n" +
							"            \"ln2\",\n" +
							"            \"email2\"\n" +
							"        ]\n" +
							"    ],\n" +
							"    \"schema\": [\n" +
							"        \"FN\",\n" +
							"        \"LN\",\n" +
							"        \"EMAIL\"\n" +
							"    ]\n" +
							"}")
						.withStatus(200)));

			HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
				put(METHOD, "POST");
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPutUrlEncoded");
				put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
				put(BATCH_MODE, "true");
				put(BODY_TRANSFORM_FUNCTION,
					"Map(\n" +
						"  \"schema\" -> Seq(\"FN\", \"LN\", \"EMAIL\"),\n" +
						"  \"data\" -> messages.map(m => " +
						"    Seq(\"firstName\", \"lastName\", \"email\").map(m.get(_)) " +
						"   )\n" +
						")");
				put(PROBE_APP_URL, "http://localhost:" + wireMockServer.port());
				put(METRICS_TOPIC, metricsTopic);
			}};

			task.start(baseProps);

			LinkedHashMap<String, Object> data1 = new LinkedHashMap<>();
			data1.put("lastName", "ln1");
			data1.put("firstName", "fn1");
			data1.put("email", "email1");

			LinkedHashMap<String, Object> data2 = new LinkedHashMap<>();
			data2.put("lastName", "ln2");
			data2.put("firstName", "fn2");
			data2.put("email", "email2");

			task.put(
				asList(
					new SinkRecord("test-topic", 1, null, null, null, toJsonString(new NexlaMessage(data1, metadata())), 1),
					new SinkRecord("test-topic", 1, null, null, null, toJsonString(new NexlaMessage(data2, metadata())), 1)
				)
			);

			verify(postRequestedFor(
				urlPathEqualTo("/testPutUrlEncoded"))
				.withRequestBody(equalToJson(
					"{\"schema\":[\"FN\",\"LN\",\"EMAIL\"]," +
						"\"data\":[[\"fn1\",\"ln1\",\"email1\"],[\"fn2\",\"ln2\",\"email2\"]]}")));

			assertMetrics(metricsConsumer, "/testPutUrlEncoded", 2, 1);
		}, "metrics");
	}

	@SneakyThrows
	@Test
	public void put_batchBody_Grouped() {
		withConsumer((metricsTopic, metricsConsumer) -> {

			stubFor(post(urlPathEqualTo("/testPutUrlEncoded/ln1")).willReturn(aResponse().withStatus(200).withBody("OK")));
			stubFor(post(urlPathEqualTo("/testPutUrlEncoded/ln2")).willReturn(aResponse().withStatus(200).withBody("OK")));

			HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
				put(METHOD, "POST");
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPutUrlEncoded/{lastName}");
				put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
				put(BATCH_MODE, "true");
				put(BATCH_GROUP_KEY, "lastName");
				put(BODY_TRANSFORM_FUNCTION, "messages");
				put(PROBE_APP_URL, "http://localhost:" + wireMockServer.port());
				put(METRICS_TOPIC, metricsTopic);
			}};

			stubFor(post(urlEqualTo("/bodyMaker64"))
				.withRequestBody(equalToJson("{\n" +
					"  \"bodyFn\" : \"CiAgICAgIGltcG9ydCBjb20ubmV4bGEucHJvYmUuYXBpLlJlZmxlY3RUb29sYm94CiAgICAgIGltcG9ydCBqYXZhLnV0aWwuQmFzZTY0CgkJCWltcG9ydCBzY2FsYS5jb2xsZWN0aW9uLm11dGFibGUuTWFwCiAgICAgIGltcG9ydCBjb20ubmV4bGEuY29tbW9uLmRhdGV0aW1lLkRhdGVUaW1lVXRpbHMKCQkJaW1wb3J0IGNvbS5uZXhsYS5jb25uZWN0b3IuY29uZmlnLnJlc3QuSHR0cENhbGxQYXJhbWV0ZXJzCgkJCWltcG9ydCBvcmcuYXBhY2hlLmNvbW1vbnMuY29kZWMuZGlnZXN0LkhtYWNBbGdvcml0aG1zCgkJCWltcG9ydCBvcmcuYXBhY2hlLmNvbW1vbnMuY29kZWMuZGlnZXN0LkhtYWNVdGlscwoJCQlpbXBvcnQgb3JnLmFwYWNoZS5jb21tb25zLmh0dHBjbGllbnQudXRpbC5VUklVdGlsCiAgICAgIGltcG9ydCBvcmcuc3ByaW5nZnJhbWV3b3JrLmh0dHAuSHR0cE1ldGhvZAoJCQlpbXBvcnQgb3JnLmFwYWNoZS5rYWZrYS5jb21tb24uY29uZmlnLkFic3RyYWN0Q29uZmlnCgogICAgICBkZWYgbWVzc2FnZXMgPSBSZWZsZWN0VG9vbGJveC5ib2R5TWVzc2FnZXMuZ2V0KCJ1dWlkIikKCiAgICAgIG1lc3NhZ2VzCiAgICA=\",\n" +
					"  \"messages\" : [ {\n" +
					"    \"firstName\" : \"fn1\",\n" +
					"    \"email\" : \"email1\"\n" +
					"  } ]\n" +
					"}"))
				.willReturn(
					aResponse()
						.withBody("[\n" +
							"    {\n" +
							"        \"firstName\": \"fn1\",\n" +
							"        \"email\": \"email1\"\n" +
							"    }\n" +
							"]")
						.withStatus(200)));

			stubFor(post(urlEqualTo("/bodyMaker64"))
				.withRequestBody(equalToJson("{\n" +
					"  \"bodyFn\" : \"CiAgICAgIGltcG9ydCBjb20ubmV4bGEucHJvYmUuYXBpLlJlZmxlY3RUb29sYm94CiAgICAgIGltcG9ydCBqYXZhLnV0aWwuQmFzZTY0CgkJCWltcG9ydCBzY2FsYS5jb2xsZWN0aW9uLm11dGFibGUuTWFwCiAgICAgIGltcG9ydCBjb20ubmV4bGEuY29tbW9uLmRhdGV0aW1lLkRhdGVUaW1lVXRpbHMKCQkJaW1wb3J0IGNvbS5uZXhsYS5jb25uZWN0b3IuY29uZmlnLnJlc3QuSHR0cENhbGxQYXJhbWV0ZXJzCgkJCWltcG9ydCBvcmcuYXBhY2hlLmNvbW1vbnMuY29kZWMuZGlnZXN0LkhtYWNBbGdvcml0aG1zCgkJCWltcG9ydCBvcmcuYXBhY2hlLmNvbW1vbnMuY29kZWMuZGlnZXN0LkhtYWNVdGlscwoJCQlpbXBvcnQgb3JnLmFwYWNoZS5jb21tb25zLmh0dHBjbGllbnQudXRpbC5VUklVdGlsCiAgICAgIGltcG9ydCBvcmcuc3ByaW5nZnJhbWV3b3JrLmh0dHAuSHR0cE1ldGhvZAoJCQlpbXBvcnQgb3JnLmFwYWNoZS5rYWZrYS5jb21tb24uY29uZmlnLkFic3RyYWN0Q29uZmlnCgogICAgICBkZWYgbWVzc2FnZXMgPSBSZWZsZWN0VG9vbGJveC5ib2R5TWVzc2FnZXMuZ2V0KCJ1dWlkIikKCiAgICAgIG1lc3NhZ2VzCiAgICA=\",\n" +
					"    \"messages\": [\n" +
					"        {\n" +
					"            \"firstName\": \"fn2\",\n" +
					"            \"email\": \"email2\"\n" +
					"        }\n" +
					"    ]\n" +
					"}"))
				.willReturn(
					aResponse()
						.withBody("[\n" +
							"    {\n" +
							"        \"firstName\": \"fn2\",\n" +
							"        \"email\": \"email2\"\n" +
							"    }\n" +
							"]")
						.withStatus(200)));

			task.start(baseProps);

			LinkedHashMap<String, Object> data1 = new LinkedHashMap<>();
			data1.put("lastName", "ln1");
			data1.put("firstName", "fn1");
			data1.put("email", "email1");

			LinkedHashMap<String, Object> data2 = new LinkedHashMap<>();
			data2.put("lastName", "ln2");
			data2.put("firstName", "fn2");
			data2.put("email", "email2");

			NexlaMetaData metaData = new NexlaMetaData();
			metaData.setRunId(123L);
			task.put(
				asList(
					new SinkRecord("test-topic", 1, null, null, null, toJsonString(new NexlaMessage(data1, metaData)), 1),
					new SinkRecord("test-topic", 1, null, null, null, toJsonString(new NexlaMessage(data2, metaData)), 1)));

			verify(postRequestedFor(
				urlPathEqualTo("/testPutUrlEncoded/ln1"))
				.withRequestBody(equalToJson(
					"[{\"firstName\":\"fn1\",\"email\":\"email1\"}]")));

			verify(postRequestedFor(
				urlPathEqualTo("/testPutUrlEncoded/ln2"))
				.withRequestBody(equalToJson(
					"[{\"firstName\":\"fn2\",\"email\":\"email2\"}]")));
		});
	}

	@SneakyThrows
	@Test
	public void put_batch_credential_substitution() {
		String token = UUID.randomUUID().toString();

		String xAuth = "x-auth: " + token;
		String cursor = "cursor: 5";

		stubFor(
				post(urlEqualTo("/test/ln1?auth=" + token))
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody("{\"status\": \"ok\"}")
						)
		);


		Map<String, String> base = baseConfigWithCreds(Map.of(
				"request.headers", xAuth + ", " + cursor
		));

		HashMap<String, String> baseProps = new HashMap<String, String>(base) {{
			put(METHOD, "POST");
			put(BATCH_MODE, "true");
			put(BATCH_GROUP_KEY, "lastName");
			put(BODY_TRANSFORM_FUNCTION, "messages");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test/{lastName}?auth={data_credential[\"request.headers\"][\"x-auth\"]}");
			put(BODY_TEMPLATE, "{\"id\":{id},\"city\":\"{city}\",\"country\":\"{country}\"}");
			put(POLL_MS, "1");
		}};

		task.start(baseProps);

		LinkedHashMap<String, Object> data = new LinkedHashMap<>();
		data.put("id", "1");
		data.put("lastName", "ln1");
		data.put("city", "SF");
		data.put("country", "USA");

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(new NexlaMessage(data, metadata())), 1)));

		verify(postRequestedFor(
				urlEqualTo("/test/ln1?auth=" + token))
				.withRequestBody(equalToJson("[{\"id\":\"1\",\"city\":\"SF\",\"country\":\"USA\"}]")));
	}

	@SneakyThrows
	@Test
	public void put_batch_header_substitution() {
		String token = UUID.randomUUID().toString();

		stubFor(
				post(urlEqualTo("/test/ln1"))
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody("{\"status\": \"ok\"}")
						)
		);


		Map<String, String> base = baseConfigWithCreds(Map.of(
			"token", token
		));

		HashMap<String, String> baseProps = new HashMap<String, String>(base) {{
			put(METHOD, "POST");
			put(BATCH_MODE, "true");
			put(BATCH_GROUP_KEY, "lastName");
			put(BODY_TRANSFORM_FUNCTION, "messages");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test/{lastName}");
			put(BODY_TEMPLATE, "{\"id\":{id},\"city\":\"{city}\",\"country\":\"{country}\"}");
			put(POLL_MS, "1");
			put(DATE_FORMAT, "yyyy-MM-dd");
			put(DATE_TIME_UNIT, "dd");
			put(REQUEST_HEADERS, "token: {data_credential[\"token\"]}, date: {now}");
		}};

		task.start(baseProps);

		LinkedHashMap<String, Object> data = new LinkedHashMap<>();
		data.put("id", "1");
		data.put("lastName", "ln1");
		data.put("city", "SF");
		data.put("country", "USA");

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
				toJsonString(new NexlaMessage(data, metadata())), 1)));

		var now = task.config.dateFormat.get().print(DateTime.now());

		verify(postRequestedFor(
						urlEqualTo("/test/ln1"))
						.withRequestBody(equalToJson("[{\"id\":\"1\",\"city\":\"SF\",\"country\":\"USA\"}]"))
						.withHeader("token", equalTo(token))
						.withHeader("date", equalTo(now)));
	}

	@Test
	public void put_body_messageJson() {

		stubFor(post(urlPathEqualTo("/testPut")).willReturn(aResponse().withStatus(200).withBody("OK")));

		HashMap<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
			put(METHOD, "POST");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut");
			put(BODY_TEMPLATE, "{message.json}");
			put(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
		}};

		task.start(baseProps);

		LinkedHashMap<String, Object> data = new LinkedHashMap<>(of("text", "test"));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
			toJsonString(new NexlaMessage(data, metadata())), 1)));

		verify(postRequestedFor(
			urlPathEqualTo("/testPut"))
			.withRequestBody(equalTo("{\"text\":\"test\"}"))
			.withHeader("Content-Type", equalTo(MediaType.APPLICATION_JSON_VALUE)));
	}

	@Test
	public void put_partialReplacement() {

		stubFor(post(urlPathEqualTo("/testPut")).willReturn(aResponse().withStatus(200).withBody("OK")));

		HashMap<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
			put(METHOD, "POST");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut");
			put(BODY_TEMPLATE, "{\"value1\":\"{value1}\",\"value2\":\"{value2}\"}");
			put(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
		}};

		task.start(baseProps);

		LinkedHashMap<String, Object> data = new LinkedHashMap<>(of("value1", "replaced",
			"value3", "notreplaced"));

		task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
			toJsonString(new NexlaMessage(data, metadata())), 1)));

		verify(postRequestedFor(
			urlPathEqualTo("/testPut"))
			.withRequestBody(equalTo("{\"value1\":\"replaced\",\"value2\":\"{value2}\"}"))
			.withHeader("Content-Type", equalTo(MediaType.APPLICATION_JSON_VALUE)));
	}

	@SneakyThrows
	@Test
	public void httpErrorResponseMetricTest() {
		int sinkId = new Random().nextInt(10000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {

				stubFor(post(urlEqualTo("/testPut?id=1&country=USA&city=SF"))
					.withRequestBody(equalToJson("{\"id\":1,\"city\":\"SF\",\"country\":\"USA\"}"))
					.willReturn(
						aResponse()
							.withBody("{\"status\": \"FAILURE\", \"messages\": [{\"code\":\"message.invalid.order\"}]}")
							.withStatus(422)));

				HashMap<String, String> baseProps = new HashMap<String, String>(getBaseParams()) {{
					put(METHOD, "POST");
					put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/testPut?id={id}&country={location.country}&city={location.city}");
					put(BODY_TEMPLATE, "{\"id\":{id},\"city\":\"{location.city}\",\"country\":\"{location.country}\"}");
					put(POLL_MS, "1");

					put(SINK_ID, sinkId + "");
					put(METRICS_TOPIC, metricsTopic);
				}};

				task.start(baseProps);

				LinkedHashMap<String, Object> data = new LinkedHashMap<>();
				data.put("id", "1");
				data.put("location.city", "SF");
				data.put("location", map("country", "USA"));

				task.put(singletonList(new SinkRecord("test-topic", 1, null, null, null,
					toJsonString(new NexlaMessage(data, metadata())), 1)));

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				List<NexlaQuarantineMessage> quarantine = readQuarantine(quarantineConsumer);

				assertEquals(1, metrics.size());
				Map<String, Object> fields = metrics.get(0).getFields();
				assertEquals(1, fields.get(ERROR_COUNT));

				assertEquals(1, quarantine.size());

				NexlaQuarantineMessage qMessage = quarantine.get(0);
				assertEquals("http://localhost:" + wireMockServer.port() + "/testPut?id=1&country=USA&city=SF", qMessage.getError().getUrl());
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	private static NexlaMetaData metadata() {
		NexlaMetaData meta = new NexlaMetaData();
		meta.setRunId(1L);
		return meta;
	}

	@Test
	public void put_single_success() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;

				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "false");
				}};

				stubFor(post(urlPathEqualTo(restPath)).willReturn(aResponse().withStatus(200).withBody("OK")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				verify(numberOfRecords, postRequestedFor(urlPathEqualTo(restPath)));

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				assertEquals(recordsCount, numberOfRecords);
				assertEquals(errorsCount, 0);
				assertEquals(quarantines.size(), 0);

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), numberOfRecords);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_single_error() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;

				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "false");
				}};

				stubFor(post(urlPathEqualTo(restPath)).willReturn(aResponse().withStatus(404).withBody("NOK")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				verify(2*numberOfRecords, postRequestedFor(urlPathEqualTo(restPath)));

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				assertEquals(recordsCount, 0);
				assertEquals(errorsCount, numberOfRecords);
				assertEquals(quarantines.size(), numberOfRecords);

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), 0);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_none_success() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;
				int numberOfBatch = 5;

				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.NONE.getKey());
				}};

				stubFor(post(urlPathEqualTo(restPath)).willReturn(aResponse().withStatus(200).withBody("OK")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				verify(numberOfRecords/numberOfBatch, postRequestedFor(urlPathEqualTo(restPath)));

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				assertEquals(recordsCount, numberOfRecords);
				assertEquals(errorsCount, 0);
				assertEquals(quarantines.size(), 0);

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), numberOfRecords/numberOfBatch);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_none_error() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;
				int numberOfBatch = 5;

				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.NONE.getKey());
				}};

				stubFor(post(urlPathEqualTo(restPath)).willReturn(aResponse().withStatus(404).withBody("NOK")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				verify(2*(numberOfRecords/numberOfBatch), postRequestedFor(urlPathEqualTo(restPath)));

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				assertEquals(recordsCount, 0);
				assertEquals(errorsCount, numberOfRecords);
				assertEquals(quarantines.size(), numberOfRecords);

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), 0);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_all_or_nothing_success() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;
				int numberOfBatch = 5;
				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.ALL_OR_NOTHING.getKey());
					put(BATCH_RESPONSE_STATUS_JSON_PATH, "$.Status");
					put(BATCH_RESPONSE_FAILURE_ENUM, "FAILED,ERROR");
				}};

				stubFor(post(urlPathEqualTo(restPath))
					.willReturn(aResponse().withStatus(200)
						.withBody("{\n" +
							"  \"Status\": \"SUCCESS\",\n" +
							"  \"Timestamp\": null,\n" +
							"  \"Field A\": null\n" +
							"}")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				verify(numberOfRecords/numberOfBatch, postRequestedFor(urlPathEqualTo(restPath)));
				assertEquals(recordsCount, numberOfRecords);
				assertEquals(errorsCount, 0);
				assertEquals(quarantines.size(), 0);

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), numberOfRecords);

				var firstWebhookValue = JsonUtils.jsonToMap(new String(allWebhookIngestedRequests.get(0).getBody(), StandardCharsets.UTF_8));
				var firstRawMessage = JsonUtils.jsonToMap(sinkRecordList.get(0).value().toString());
				assertEquals(firstWebhookValue.get("input"), List.of(firstRawMessage));
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_all_or_nothing_error_in_response() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;
				int numberOfBatch = 5;
				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.ALL_OR_NOTHING.getKey());
					put(BATCH_RESPONSE_STATUS_JSON_PATH, "$.Status");
					put(BATCH_RESPONSE_FAILURE_ENUM, "FAILED,ERROR");
				}};

				stubFor(post(urlPathEqualTo(restPath))
					.willReturn(aResponse().withStatus(200)
						.withBody("{\n" +
							"  \"Status\": \"error\",\n" +
							"  \"Timestamp\": null,\n" +
							"  \"Field A\": null\n" +
							"}")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				verify(numberOfRecords/numberOfBatch, postRequestedFor(urlPathEqualTo(restPath)));
				assertEquals(recordsCount, 0);
				assertEquals(errorsCount, numberOfRecords);
				assertEquals(quarantines.size(), numberOfRecords);

				var firstQuarantineRawMessage = quarantines.get(0).getRawMessage();
				var firstRawMessage = JsonUtils.jsonToMap(sinkRecordList.get(0).value().toString()).get("rawMessage");
				assertEquals(firstQuarantineRawMessage, firstRawMessage);

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), 0);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_by_ordered_items() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 10;
				int numberOfBatch = 5;
				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.BY_ORDERED_ITEMS.getKey());
					put(BATCH_RESPONSE_STATUS_JSON_PATH, "$.success");
					put(BATCH_RESPONSE_FAILURE_ENUM, "false");
				}};

				stubFor(post(urlPathEqualTo(restPath))
					.willReturn(aResponse().withStatus(200)
						.withBody("[\n" +
							"    {\n" +
							"        \"id\": \"0011U00002L4zbKQAR\",\n" +
							"        \"success\": true,\n" +
							"        \"errors\": []\n" +
							"    },\n" +
							"    {\n" +
							"        \"success\": false,\n" +
							"        \"errors\": [\n" +
							"            {\n" +
							"                \"statusCode\": \"INVALID_FIELD\",\n" +
							"                \"message\": \"No such column 'Name1' on sobject of type Account\",\n" +
							"                \"fields\": []\n" +
							"            }\n" +
							"        ]\n" +
							"    },\n" +
							"    {\n" +
							"        \"success\": false,\n" +
							"        \"errors\": [\n" +
							"            {\n" +
							"                \"statusCode\": \"INVALID_FIELD\",\n" +
							"                \"message\": \"No such column 'Name1' on sobject of type Account\",\n" +
							"                \"fields\": []\n" +
							"            }\n" +
							"        ]\n" +
							"    },\n" +
							"    {\n" +
							"        \"success\": false,\n" +
							"        \"errors\": [\n" +
							"            {\n" +
							"                \"statusCode\": \"INVALID_FIELD\",\n" +
							"                \"message\": \"No such column 'Name2' on sobject of type Account\",\n" +
							"                \"fields\": []\n" +
							"            }\n" +
							"        ]\n" +
							"    },\n" +
							"    {\n" +
							"        \"id\": \"0011U00002L4zbKQAR\",\n" +
							"        \"success\": true,\n" +
							"        \"errors\": []\n" +
							"    }\n" +
							"]")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				verify(numberOfRecords/numberOfBatch, postRequestedFor(urlPathEqualTo(restPath)));
				int numberOfError = 6;
				assertEquals(recordsCount, numberOfRecords-numberOfError);
				assertEquals(errorsCount, numberOfError);
				assertEquals(quarantines.size(), numberOfError);

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), numberOfRecords-numberOfError);

				var firstWebhookValue = JsonUtils.jsonToMap(new String(allWebhookIngestedRequests.get(0).getBody(), StandardCharsets.UTF_8));
				var firstRawMessage = JsonUtils.jsonToMap(sinkRecordList.get(0).value().toString());
				assertEquals(firstWebhookValue.get("input"), List.of(firstRawMessage));
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_by_ordered_items_error_response_is_not_an_array() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 10;
				int numberOfBatch = 5;
				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.BY_ORDERED_ITEMS.getKey());
					put(BATCH_RESPONSE_STATUS_JSON_PATH, "$.success");
					put(BATCH_RESPONSE_FAILURE_ENUM, "false");
				}};

				stubFor(post(urlPathEqualTo(restPath))
					.willReturn(aResponse().withStatus(200)
						.withBody(
							"    {\n" +
							"        \"id\": \"0011U00002L4zbKQAR\",\n" +
							"        \"success\": true,\n" +
							"        \"errors\": []\n" +
							"    }")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				verify(numberOfRecords/numberOfBatch, postRequestedFor(urlPathEqualTo(restPath)));
				assertEquals(recordsCount, 0);
				assertEquals(errorsCount, numberOfRecords);
				assertEquals(quarantines.size(), numberOfRecords);

				assertTrue(quarantines.get(0).getError().getMessage().contains("Response can not be converted to an array"));

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), 0);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_by_ordered_items_error_response_with_wrong_size() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 10;
				int numberOfBatch = 5;
				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.BY_ORDERED_ITEMS.getKey());
					put(BATCH_RESPONSE_STATUS_JSON_PATH, "$.success");
					put(BATCH_RESPONSE_FAILURE_ENUM, "false");
				}};

				stubFor(post(urlPathEqualTo(restPath))
					.willReturn(aResponse().withStatus(200)
						.withBody("[\n" +
							"    {\n" +
							"        \"id\": \"0011U00002L4zbKQAR\",\n" +
							"        \"success\": true,\n" +
							"        \"errors\": []\n" +
							"    },\n" +
							"    {\n" +
							"        \"success\": false,\n" +
							"        \"errors\": [\n" +
							"            {\n" +
							"                \"statusCode\": \"INVALID_FIELD\",\n" +
							"                \"message\": \"No such column 'Name1' on sobject of type Account\",\n" +
							"                \"fields\": []\n" +
							"            }\n" +
							"        ]\n" +
							"    },\n" +
							"    {\n" +
							"        \"success\": false,\n" +
							"        \"errors\": [\n" +
							"            {\n" +
							"                \"statusCode\": \"INVALID_FIELD\",\n" +
							"                \"message\": \"No such column 'Name1' on sobject of type Account\",\n" +
							"                \"fields\": []\n" +
							"            }\n" +
							"        ]\n" +
							"    },\n" +
							"    {\n" +
							"        \"success\": false,\n" +
							"        \"errors\": [\n" +
							"            {\n" +
							"                \"statusCode\": \"INVALID_FIELD\",\n" +
							"                \"message\": \"No such column 'Name2' on sobject of type Account\",\n" +
							"                \"fields\": []\n" +
							"            }\n" +
							"        ]\n" +
							"    }\n" +
							"]")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				verify(numberOfRecords/numberOfBatch, postRequestedFor(urlPathEqualTo(restPath)));
				assertEquals(recordsCount, 0);
				assertEquals(errorsCount, numberOfRecords);
				assertEquals(quarantines.size(), numberOfRecords);

				assertTrue(quarantines.get(0).getError().getMessage().contains("Size of response items is not equals to the size of request items"));

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), 0);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_by_item_with_id_success() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;
				int numberOfBatch = 5;
				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.BY_ITEM_WITH_ID.getKey());

					put(BATCH_RESPONSE_FAILED_ITEMS_JSON_PATH, "$[?(@.Status == 'Error' || @.Status == 'Failure')]");
					put(BATCH_RESPONSE_REQUEST_ID_FIELD, "email");
					put(BATCH_RESPONSE_RESPONSE_ID_FIELD, "Id");
				}};

				stubFor(post(urlPathEqualTo(restPath))
					.willReturn(aResponse().withStatus(200)
						.withBody("[\n" +
							"  {\n" +
							"    \"Id\": \"<EMAIL>\",\n" +
							"    \"Status\": \"Success\"\n" +
							"  }, {\n" +
							"    \"Id\": \"<EMAIL>\",\n" +
							"    \"Status\": \"Success\",\n" +
							"    \"ErrorMessage\": \"Order Y not found\"\n" +
							"  }, {\n" +
							"    \"Id\": \"<EMAIL>\",\n" +
							"    \"Status\": \"Success\",\n" +
							"    \"ErrorMessage\": \"Product X not found\"\n" +
							"  }, {\n" +
							"    \"Id\": \"<EMAIL>\",\n" +
							"    \"Status\": \"Warn\",\n" +
							"    \"ErrorMessage\": \"Product X not found\"\n" +
							"  }\n" +
							"]")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				verify(numberOfRecords/numberOfBatch, postRequestedFor(urlPathEqualTo(restPath)));
				assertEquals(recordsCount, numberOfRecords);
				assertEquals(errorsCount, 0);
				assertEquals(quarantines.size(), 0);

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), numberOfRecords);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_by_item_with_id_error_in_response() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;
				int numberOfBatch = 5;
				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.BY_ITEM_WITH_ID.getKey());

					put(BATCH_RESPONSE_FAILED_ITEMS_JSON_PATH, "$[?(@.Status == 'Error' || @.Status == 'Failure')]");
					put(BATCH_RESPONSE_REQUEST_ID_FIELD, "email");
					put(BATCH_RESPONSE_RESPONSE_ID_FIELD, "Id");
				}};

				stubFor(post(urlPathEqualTo(restPath))
					.willReturn(aResponse().withStatus(200)
						.withBody("[\n" +
							"  {\n" +
							"    \"Id\": \"<EMAIL>\",\n" +
							"    \"Status\": \"Success\"\n" +
							"  }, {\n" +
							"    \"Id\": \"<EMAIL>\",\n" +
							"    \"Status\": \"Error\",\n" +
							"    \"ErrorMessage\": \"Order Y not found\"\n" +
							"  }, {\n" +
							"    \"Id\": \"<EMAIL>\",\n" +
							"    \"Status\": \"Error\",\n" +
							"    \"ErrorMessage\": \"Product X not found\"\n" +
							"  }, {\n" +
							"    \"Id\": \"<EMAIL>\",\n" +
							"    \"Status\": \"Failure\",\n" +
							"    \"ErrorMessage\": \"Product X not found\"\n" +
							"  }\n" +
							"]")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				verify(numberOfRecords/numberOfBatch, postRequestedFor(urlPathEqualTo(restPath)));
				assertEquals(recordsCount, 22);
				assertEquals(errorsCount, 3);
				assertEquals(quarantines.size(), 3);

				var quarantineRawMessage = quarantines.stream()
					.filter(f -> f.getRawMessage().get("email").equals("<EMAIL>")).findFirst().get().getRawMessage();
				var firstRawMessage = JsonUtils.jsonToMap(sinkRecordList.get(0).value().toString()).get("rawMessage");
				assertEquals(quarantineRawMessage, firstRawMessage);

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), 22);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_by_item_with_no_id_success() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;
				int numberOfBatch = 5;
				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.BY_ITEM_WITH_NO_ID.getKey());

					put(BATCH_RESPONSE_ERROR_MESSAGES_JSON_PATH, "$.Errors[*].ErrorMessage");
					put(BATCH_RESPONSE_ERROR_MESSAGE_FILTER, "[{\n" +
						"  \"message.regex\": \"First name '(\\\\w+)' not found\",\n" +
						"  \"message.request.field\": \"firstName\"\n" +
						"},{\n" +
						"  \"message.regex\": \"First name '(\\\\w+)' is inactivated\",\n" +
						"  \"message.request.field\": \"firstName\"\n" +
						"}]");
				}};

				stubFor(post(urlPathEqualTo(restPath))
					.willReturn(aResponse().withStatus(200)
						.withBody("{\n" +
							"  \"Status\": \"Success\",\n" +
							"  \"Errors\": []\n" +
							"}")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				verify(numberOfRecords/numberOfBatch, postRequestedFor(urlPathEqualTo(restPath)));
				assertEquals(recordsCount, numberOfRecords);
				assertEquals(errorsCount, 0);
				assertEquals(quarantines.size(), 0);

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), numberOfRecords);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_by_item_with_no_id_error_in_response() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;
				int numberOfBatch = 5;
				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.BY_ITEM_WITH_NO_ID.getKey());

					put(BATCH_RESPONSE_ERROR_MESSAGES_JSON_PATH, "$.Errors[*].ErrorMessage");
					put(BATCH_RESPONSE_ERROR_MESSAGE_FILTER, "[{\n" +
						"  \"message.regex\": \"First name '(\\\\w+)' not found\",\n" +
						"  \"message.request.field\": \"firstName\"\n" +
						"},{\n" +
						"  \"message.regex\": \"First name '(\\\\w+)' is inactivated\",\n" +
						"  \"message.request.field\": \"firstName\"\n" +
						"}]");
				}};

				stubFor(post(urlPathEqualTo(restPath))
					.willReturn(aResponse().withStatus(200)
						.withBody("{\n" +
							"  \"Status\": \"Error\",\n" +
							"  \"Errors\": [\n" +
							"    {\n" +
							"      \"ErrorMessage\": \"First name 'fn2' not found\",\n" +
							"      \"Code\": \"A234\"\n" +
							"    },\n" +
							"    {\n" +
							"      \"ErrorMessage\": \"First name 'fn4' not found\",\n" +
							"      \"Code\": \"A235\"\n" +
							"    },\n" +
							"    {\n" +
							"      \"ErrorMessage\": \"First name 'fn3' is inactivated\",\n" +
							"      \"Code\": \"A235\"\n" +
							"    }\n" +
							"  ]\n" +
							"}")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				verify(numberOfRecords/numberOfBatch, postRequestedFor(urlPathEqualTo(restPath)));
				assertEquals(recordsCount, numberOfRecords-3);
				assertEquals(errorsCount, 3);
				assertEquals(quarantines.size(), 3);

				var quarantineRawMessage = quarantines.stream()
					.filter(f -> f.getRawMessage().get("firstName").equals("fn2")).findFirst().get().getRawMessage();
				var firstRawMessage = JsonUtils.jsonToMap(sinkRecordList.get(2).value().toString()).get("rawMessage");
				assertEquals(quarantineRawMessage, firstRawMessage);

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), numberOfRecords-3);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_by_item_with_no_id_config_error_with_empty_filter() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;
				int numberOfBatch = 5;
				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.BY_ITEM_WITH_NO_ID.getKey());

					put(BATCH_RESPONSE_ERROR_MESSAGES_JSON_PATH, "$.Errors[*].ErrorMessage");
					put(BATCH_RESPONSE_ERROR_MESSAGE_FILTER, "[]");
				}};

				stubFor(post(urlPathEqualTo(restPath))
					.willReturn(aResponse().withStatus(200)
						.withBody("{\n" +
							"  \"Status\": \"Error\",\n" +
							"  \"Errors\": [\n" +
							"    {\n" +
							"      \"ErrorMessage\": \"First name 'fn2' not found\",\n" +
							"      \"Code\": \"A234\"\n" +
							"    },\n" +
							"    {\n" +
							"      \"ErrorMessage\": \"First name 'fn4' not found\",\n" +
							"      \"Code\": \"A235\"\n" +
							"    },\n" +
							"    {\n" +
							"      \"ErrorMessage\": \"First name 'fn3' is inactivated\",\n" +
							"      \"Code\": \"A235\"\n" +
							"    }\n" +
							"  ]\n" +
							"}")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				verify(0, postRequestedFor(urlPathEqualTo(restPath)));
				assertEquals(recordsCount, 0);
				assertEquals(errorsCount, numberOfRecords);
				assertEquals(quarantines.size(), numberOfRecords);

				assertTrue(quarantines.get(0).getError().getMessage()
					.contains("Param 'batch.response.error.message.filter' can not be empty when batch response strategy is equals to 'by_item_with_no_id"));

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), 0);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}


	@Test
	public void put_batch_response_strategy_by_item_with_no_id_config_error_with_no_filter() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;
				int numberOfBatch = 5;
				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.BY_ITEM_WITH_NO_ID.getKey());

					put(BATCH_RESPONSE_ERROR_MESSAGES_JSON_PATH, "$.Errors[*].ErrorMessage");
				}};

				stubFor(post(urlPathEqualTo(restPath))
					.willReturn(aResponse().withStatus(200)
						.withBody("{\n" +
							"  \"Status\": \"Error\",\n" +
							"  \"Errors\": [\n" +
							"    {\n" +
							"      \"ErrorMessage\": \"First name 'fn2' not found\",\n" +
							"      \"Code\": \"A234\"\n" +
							"    },\n" +
							"    {\n" +
							"      \"ErrorMessage\": \"First name 'fn4' not found\",\n" +
							"      \"Code\": \"A235\"\n" +
							"    },\n" +
							"    {\n" +
							"      \"ErrorMessage\": \"First name 'fn3' is inactivated\",\n" +
							"      \"Code\": \"A235\"\n" +
							"    }\n" +
							"  ]\n" +
							"}")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				task.start(baseProps);

				List<SinkRecord> sinkRecordList = new ArrayList<>();
				for (int i = 0; i < numberOfRecords; i++) {
					sinkRecordList.add(new SinkRecord("test-topic", 1, null, null,
						null, toJsonString(new NexlaMessage(getRecordRawData(i), metadata())), i + 1));
				}

				task.put(sinkRecordList);

				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sinkId);
				int recordsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("records")).sum();
				int errorsCount = metrics.stream().mapToInt(m -> (Integer) m.getFields().get("error_count")).sum();
				List<NexlaQuarantineMessage> quarantines = readTopic(quarantineConsumer, NexlaQuarantineMessage.class);

				verify(0, postRequestedFor(urlPathEqualTo(restPath)));
				assertEquals(recordsCount, 0);
				assertEquals(errorsCount, numberOfRecords);
				assertEquals(quarantines.size(), numberOfRecords);

				assertTrue(quarantines.get(0).getError().getMessage()
					.contains("Param 'batch.response.error.message.filter' can not be empty when batch response strategy is equals to 'by_item_with_no_id"));

				var allWebhookIngestedRequests = WireMock.findAll(postRequestedFor(urlPathEqualTo(ingestPath)));
				assertEquals(allWebhookIngestedRequests.size(), 0);
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@Test
	public void put_batch_response_strategy_by_item_with_no_id_config_error_with_wrong_filter() {
		int sinkId = new Random().nextInt(1000);
		withConsumer((metricsTopic, metricsConsumer) -> {
			withConsumer((quarantineTopic, quarantineConsumer) -> {
				int numberOfRecords = 25;
				int numberOfBatch = 5;
				String wireMockUrl = "http://localhost:" + wireMockServer.port();
				String restPath = "/testPutUrlEncoded";
				String restUrl = wireMockUrl + restPath;
				String ingestMethod = "/ingestUrl";
				String ingestBaseUrl = wireMockUrl + ingestMethod;
				String ingestDataSource = String.valueOf(new Random().nextInt(1000));
				String ingestPath = ingestMethod + "/" + ingestDataSource;

				HashMap<String, String> baseProps = new HashMap<>(getBaseParams()) {{
					put(SINK_ID, String.valueOf(sinkId));
					put(PROBE_APP_URL, wireMockUrl);
					put(METHOD, "POST");
					put(URL_TEMPLATE, restUrl);
					put(CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
					put(BODY_TRANSFORM_FUNCTION, "messages");
					put(INGEST_URL_PARAM, ingestBaseUrl);
					put(INGEST_DATASOURCE_PARAM, ingestDataSource);
					put(BATCH_MODE, "true");
					put(BATCH_SIZE, String.valueOf(numberOfBatch));
					put(LOG_VERBOSE, "true");
					put(BATCH_RESPONSE_STRATEGY, BatchResponseStrategy.BY_ITEM_WITH_NO_ID.getKey());

					put(BATCH_RESPONSE_ERROR_MESSAGES_JSON_PATH, "$.Errors[*].ErrorMessage");
					put(BATCH_RESPONSE_ERROR_MESSAGE_FILTER, "[\"test\": \"1\"]");
				}};

				stubFor(post(urlPathEqualTo(restPath))
					.willReturn(aResponse().withStatus(200)
						.withBody("{\n" +
							"  \"Status\": \"Error\",\n" +
							"  \"Errors\": [\n" +
							"    {\n" +
							"      \"ErrorMessage\": \"First name 'fn2' not found\",\n" +
							"      \"Code\": \"A234\"\n" +
							"    },\n" +
							"    {\n" +
							"      \"ErrorMessage\": \"First name 'fn4' not found\",\n" +
							"      \"Code\": \"A235\"\n" +
							"    },\n" +
							"    {\n" +
							"      \"ErrorMessage\": \"First name 'fn3' is inactivated\",\n" +
							"      \"Code\": \"A235\"\n" +
							"    }\n" +
							"  ]\n" +
							"}")));
				stubFor(post(urlPathEqualTo(ingestPath)).willReturn(aResponse().withStatus(200)));

				ConfigException configException = assertThrows(ConfigException.class, () -> task.start(baseProps));
				assertTrue(configException.getMessage().contains("Rest sink batch response message filter can not be mapped."));
			}, "quarantine-sink-" + sinkId);
		}, "metrics");
	}

	@NotNull
	private static LinkedHashMap<String, Object> getRecordRawData(int sequence) {
		LinkedHashMap<String, Object> data = new LinkedHashMap<>();
		data.put("lastName", "ln" + sequence);
		data.put("firstName", "fn" + sequence);
		data.put("email", sequence + "@test.com");
		return data;
	}

	private static String generateBodyJsonStr(int numberOfRecords) {
		StringBuilder bodyJsonStr = new StringBuilder();
		bodyJsonStr.append("{\n" +
			"    \"data\": [\n");
		for (int i = 0; i < numberOfRecords; i++) {
			bodyJsonStr.append("        [\n" +
					"            \"fn" + i + "\",\n" +
					"            \"ln" + i + "\",\n" +
					"            \"email" + i + "\"\n" +
					"        ]");
			if (i != numberOfRecords - 1) {
				bodyJsonStr.append(",");
			}
		}
		bodyJsonStr.append("],\n" +
				"    \"schema\": [\n" +
				"        \"FN\",\n" +
				"        \"LN\",\n" +
				"        \"EMAIL\"\n" +
				"    ]\n" +
				"}");
		return bodyJsonStr.toString();
	}
}