package com.nexla.http

import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.nexla.admin.client.{AdminApiClient, AdminApiClientBuilder}
import com.nexla.common.NexlaConstants._
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.common.notify.transport.ControlMessageProducer
import com.nexla.common.{AppType, NexlaKafkaConfig, NexlaSslContext, RestTemplateBuilder}
import com.nexla.http.api.{ApiHandler, HttpSinkApiHandler}
import com.nexla.kafka.service.TopicMetaService
import com.nexla.kafka.{AdminControlUpdatesListener, CtrlTopics, KafkaMessageTransport}
import com.nexla.sc.util.{AppUtils, Async, StrictNexlaLogging}
import com.nexla.telemetry.jmx.JmxExporter
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsRoute
import org.slf4j.bridge.SLF4JBridgeHandler

import java.util.UUID
import scala.compat.java8.OptionConverters._
import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

object HttpSinkApp
  extends App
    with AppUtils
    with StrictNexlaLogging {

  // Needed to redirect java.util.logging to slf4j
  SLF4JBridgeHandler.removeHandlersForRootLogger
  SLF4JBridgeHandler.install

  implicit private val (props, nexlaAppConfig, envMap) = loadProps(AppType.HTTP_SINK, new AppProps(_))
  val (telemetry, httpMetricsRegistry) = initTelemetry(props.telemetryConf, AppType.HTTP_SINK.appName, Some(props.dataDog), Some(props.prometheus))
  val jmxExporter: JmxExporter = new JmxExporter(telemetry)

  implicit val system = defaultActorSystem(logger)
  implicit val materializer = Materializer(system)
  implicit val executionContext: ExecutionContext = Async.ioExecutorContext

  implicit val appSslContext: NexlaSslContext = nexlaSslContext(props)
  implicit val controlKafkaContext: NexlaKafkaConfig = controlKafkaContext(props)
  val restTemplate = new RestTemplateBuilder().withSSL(appSslContext)
    .build()

  implicit val adminApiClient = new AdminApiClientBuilder()
    .setAppName(AppType.HTTP_SINK.appName)
    .setDataPlaneUid(props.dataplaneUid)
    .setEnrichmentUrl(props.credEnrichmentUrl)
    .create(props.apiCredentialsServer, props.apiAccessKey, restTemplate)

  private implicit val topicMetadataService = new TopicMetaService(controlKafkaContext)

  private implicit val messageProducer = new ControlMessageProducer(new KafkaMessageTransport(controlKafkaContext))

  val ctrlTopics = new CtrlTopics(Some(UUID.randomUUID()), props, controlKafkaContext, system)
  private def initApp() = {

    val api = new ApiHandler(new HttpSinkApiHandler, httpMetricsRegistry, envMap)
    configureHttpClientSslContext(appSslContext)

    val httpSystem = Http(system)
    val context = appSslContext
      .getServerKeystoreStore()
      .asScala
      .map(sslContext => httpsContext(sslContext, appSslContext.getServerTruststoreStore.orElse(null)))
      .getOrElse(httpSystem.defaultServerHttpContext)

    httpMetricsRegistry.map {
      actualReg =>
        val metricsRoute = HttpMetricsRoute(api.metricsRoute).recordMetrics(actualReg)
        val metricsBinding = Http().bindAndHandle(metricsRoute, "0.0.0.0", props.nexlaMetricsPort)
        jmxExporter.enable()

        metricsBinding.andThen {
          case Success(Http.ServerBinding(_)) =>
            logger.info(s"Metrics server started on ${props.podIp}:${props.nexlaMetricsPort}")
          case Failure(e) =>
            logger.error(s"Could not start Metrics server on ${props.nexlaMetricsPort}.", e)
        }
    }

    val binding = httpSystem.bindAndHandle(api.route, "0.0.0.0", props.nexlaAppPort, context)
    appSslContext.getServerKeystoreStore().asScala.foreach(_.clean())
    binding.andThen {
      case Success(Http.ServerBinding(_)) =>
        val metricAggregator = HealthMetricAggregator.getInstance(messageProducer)

        new AdminControlUpdatesListener(AppType.HTTP_SINK, ctrlTopics, metricAggregator).startMonitoring()
        logger.info(s"API server started on ${props.podIp}:${props.nexlaAppPort}")

      case Failure(e) =>
        logger.error(s"Could not start API server on ${props.nexlaAppPort}. Exiting...", e)
        System.exit(1)
    }
  }

  initApp()

}
