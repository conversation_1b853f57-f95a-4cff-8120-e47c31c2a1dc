package com.nexla.connector.soap.source;

import com.bazaarvoice.jolt.JsonUtils;
import com.github.tomakehurst.wiremock.core.Options;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.github.tomakehurst.wiremock.matching.EqualToXmlPattern;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSource;
import com.nexla.common.NexlaConstants;
import com.nexla.common.NexlaMessage;
import com.nexla.common.StreamUtils;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connect.common.SchemaDetectionResult;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.connector.config.ConnectorAdminConfig;
import com.nexla.kafka.service.TopicMetaService;
import com.nexla.test.IntegrationTests;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.source.SourceTaskContext;
import org.apache.kafka.connect.storage.OffsetStorageReader;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.ClassRule;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.junit.*;
import org.testcontainers.containers.KafkaContainer;

import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.nexla.common.ConnectionType.SOAP;
import static com.nexla.common.NexlaConstants.CREDENTIALS_DECRYPT_KEY;
import static com.nexla.common.NexlaConstants.CREDS_ENC;
import static com.nexla.common.NexlaConstants.CREDS_ENC_IV;
import static com.nexla.common.NexlaConstants.LISTING_APP_SERVER_URL;
import static com.nexla.common.NexlaConstants.SOAP_PARAMS;
import static com.nexla.common.NexlaConstants.SOURCE_ID;
import static com.nexla.common.metrics.NexlaRawMetric.RUN_ID;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.soap.SoapWsdlConfig.SOAP_BINDING;
import static com.nexla.connector.config.soap.SoapWsdlConfig.SOAP_OPERATION;
import static com.nexla.connector.config.soap.SoapWsdlConfig.SOAP_PORT_TYPE;
import static com.nexla.connector.config.soap.SoapWsdlConfig.SOAP_SERVICE;
import static com.nexla.connector.config.soap.SoapWsdlConfig.SOAP_SERVICE_PORT;
import static com.nexla.connector.config.soap.SoapWsdlConfig.SOAP_WSDL_URL;
import static com.nexla.connector.properties.RestConfigAccessor.AUTH_TYPE;
import static com.nexla.connector.properties.RestConfigAccessor.AuthType;
import static com.nexla.connector.properties.RestConfigAccessor.POLL_MS;
import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

@Category(IntegrationTests.class)
public class SoapSourceTaskTest extends BaseKafkaTest {

	public static KafkaContainer kafka = new KafkaContainer("7.2.11");

	@BeforeClass
	public static void setUp() {
		kafka.withReuse(true);
		kafka.start();
		init(kafka);
	}

	@AfterClass
	public static void tearDown() {
		kafka.stop();
	}

	private static final String DATASET_TOPIC = "dataset-1-source-1";
	private static final String TEST_SOURCE_ID = "1";

	@ClassRule
	public static WireMockRule wireMockServer = new WireMockRule(Options.DYNAMIC_PORT);

	private SoapSourceTask task;
	private OffsetStorageReader offsetStorageReader;

	@Before
	public void onBefore() {

		DataSource ds = new DataSource();
		ds.setId(1);
		ds.setSourceConfig(new HashMap<>());
		ds.setDatasets(Lists.newLinkedList());
		ds.setConnectionType(SOAP);

		AdminApiClient adminApiClientMock = mock(AdminApiClient.class);
		when(adminApiClientMock.getDataSource(any())).thenReturn(Optional.ofNullable(ds));

		ControlMessageProducer controlMessageProducer = mock(ControlMessageProducer.class);
		TopicMetaService topicMetaService = mock(TopicMetaService.class);
		SchemaDetectionUtils schemaDetection = spy(new SchemaDetectionUtils(
			10, adminApiClientMock, controlMessageProducer,
			Optional.ofNullable(topicMetaService), 1, 1, 10, false));

		doReturn(new SchemaDetectionResult(1, DATASET_TOPIC, (c) -> {
		})).when(schemaDetection).updateOrCreateDataSet(anyList(), any());

		SourceTaskContext sourceTaskContext = mock(SourceTaskContext.class);
		offsetStorageReader = mock(OffsetStorageReader.class);
		when(sourceTaskContext.offsetStorageReader()).thenReturn(offsetStorageReader);
		when(offsetStorageReader.offset(anyMap())).thenReturn(null);

		this.task = new SoapSourceTask(schemaDetection) {
			@Override
			protected AdminApiClient getAdminApiClient(String adminApiPrefix, ConnectorAdminConfig adminConfig) {
				return adminApiClientMock;
			}
		};
		task.rethrowRetryExceptions = true;
		task.initialize(sourceTaskContext);

		wireMockServer.resetAll();
	}

	@After
	@SneakyThrows
	public void after() {
		if (task != null && task.getControlMessageProducer() != null) {
			task.getControlMessageProducer().close();
			task.stop();
		}
	}

	@Ignore
	@Test
	@SneakyThrows
	public void poll_soap() {

		String request = IOUtils.toString(
			getClass().getClassLoader().getResource("./soap-request.xml").toURI(),
			Charset.defaultCharset());

		String response = IOUtils.toString(
			getClass().getClassLoader().getResource("./soap-result.xml").toURI(),
			Charset.defaultCharset());

		stubFor(
			post(urlEqualTo("/soap-endpoint"))
				.withRequestBody(new EqualToXmlPattern(request))
				.willReturn(aResponse()
					.withStatus(200)
					.withHeader("Content-Type", "application/xml")
					.withBody(response)));

		String wsdl = IOUtils.toString(
			getClass().getClassLoader().getResource("./soap-wsdl.wsdl").toURI(),
			Charset.defaultCharset()
		).replace("###WS_ADDRESS###", "http://localhost:" + wireMockServer.port() + "/soap-endpoint");

		stubFor(
			get(urlEqualTo("/soap-wsdl"))
				.willReturn(aResponse()
					.withStatus(200)
					.withHeader("Content-Type", "application/xml")
					.withBody(wsdl)));

		Map<Object, Object> params = Maps.newHashMap();
		params.put("TrackRequest/WebAuthenticationDetail/UserCredential/Key", "4xxxxxxxxxxxxxxxy");
		params.put("TrackRequest/WebAuthenticationDetail/UserCredential/Password", "3xxxxxxxxxxxxxxxW");
		params.put("TrackRequest/ClientDetail/AccountNumber", "*********");
		params.put("TrackRequest/ClientDetail/MeterNumber", "*********");
		params.put("TrackRequest/TransactionDetail/CustomerTransactionId", "Track By Number_v16");
		params.put("TrackRequest/TransactionDetail/Localization/LanguageCode", "EN");
		params.put("TrackRequest/TransactionDetail/Localization/LocaleCode", "US");
		params.put("TrackRequest/SelectionDetails/PackageIdentifier/Type", "TRACKING_NUMBER_OR_DOORTAG");
		params.put("TrackRequest/SelectionDetails/PackageIdentifier/Value", "************");
		params.put("TrackRequest/SelectionDetails/Destination/GeographicCoordinates", "rates evertitque");

		String jsonParams = JsonUtils.toJsonString(params);

		Map<String, String> props = baseConfig();
		props.put(AUTH_TYPE, AuthType.NONE.name());
		props.put(SOAP_WSDL_URL, "http://localhost:" + wireMockServer.port() + "/soap-wsdl");
		props.put(SOAP_PORT_TYPE, "TrackPortType");
		props.put(SOAP_OPERATION, "track");
		props.put(SOAP_BINDING, "TrackServiceSoapBinding");
		props.put(SOAP_SERVICE_PORT, "TrackServicePort");
		props.put(SOAP_SERVICE, "TrackService");
		props.put(SOAP_PARAMS, jsonParams);

		task.start(props);

		String expected = IOUtils.toString(
			getClass().getClassLoader().getResource("./soap-expected.json").toURI(),
			Charset.defaultCharset()
		);

		assertEquals(JsonUtils.jsonToList(expected), pollData());
	}

	private List<Map<String, Object>> pollData() {
		return pollDataWithOffset().left;
	}

	private ImmutablePair<List<Map<String, Object>>, Optional<SourceRecord>> pollDataWithOffset() {
		List<SourceRecord> records = pollNoTrace(task);
		assertTopicName(records);
		List<NexlaMessage> data = toMessages(records);
		Optional<SourceRecord> offset = StreamEx.of(records).skip(Math.max(records.size() - 1, 0)).findFirst();
		return ImmutablePair.of(toRawMessages(data), offset);
	}

	private List<NexlaMessage> toMessages(List<SourceRecord> sourceRecords) {
		return sourceRecords.stream()
			.map(this::asNexlaMessage)
			.collect(toList());
	}

	private List<Map<String, Object>> toRawMessages(List<NexlaMessage> sourceRecords) {
		return sourceRecords.stream().map(NexlaMessage::getRawMessage).collect(toList());
	}

	private NexlaMessage asNexlaMessage(SourceRecord r) {
		return StreamUtils.jsonUtil().stringToType(r.value().toString(), NexlaMessage.class);
	}

	private void assertTopicName(List<SourceRecord> sourceRecords) {
		assertTrue(sourceRecords.stream().allMatch(r -> DATASET_TOPIC.equals(r.topic())));
	}

	private static Map<String, String> baseConfig() {
		Map<String, String> baseConfig = new HashMap<>();
		baseConfig.put(NexlaConstants.CONTROL_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		baseConfig.put(NexlaConstants.DATA_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		baseConfig.put(NexlaConstants.CONTROL_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);
		baseConfig.put(NexlaConstants.DATA_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);
		baseConfig.put(UNIT_TEST, "true");
		baseConfig.put(POLL_MS, "1");
		baseConfig.put(CREDS_ENC, "1");
		baseConfig.put(CREDS_ENC_IV, "1");
		baseConfig.put(CREDENTIALS_DECRYPT_KEY, "1");
		baseConfig.put(SOURCE_ID, TEST_SOURCE_ID);
		baseConfig.put(LISTING_APP_SERVER_URL, "123");
		baseConfig.put(RUN_ID, "1234567890");
		return baseConfig;
	}

}