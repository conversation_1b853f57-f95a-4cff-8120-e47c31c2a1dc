package com.nexla.inmemory_connector.pipeline.tx.ray

import akka.actor.ActorSystem
import com.nexla.admin.client.{DataCredentials, DataSource}
import com.nexla.common.NexlaMessage
import com.nexla.common.notify.transport.{ControlMessageProducer, NoopNexlaMessageTransport}
import com.nexla.connector.config.file.AWSAuthConfig
import com.nexla.inmemory_connector.compat._
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.{MezzanineAuthConfig, RayRuntimeConfig}
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryControlMessageProducer
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClient
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClient._
import org.scalatest.concurrent.Eventually.eventually
import org.scalatest.concurrent.PatienceConfiguration.Timeout
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import spray.json.{JsNull, JsonParser}

import java.util
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class RayDirectoryTransformerSpec extends AsyncWordSpec with Matchers with ScalaFutures {

  implicit val system: ActorSystem = ActorSystem("test-system")
  implicit val ec: ExecutionContext = system.dispatcher

  private val noopMessageProducer = new InMemoryControlMessageProducer(new ControlMessageProducer(new NoopNexlaMessageTransport))
  private val dataSource = new DataSource()
  private val rayConfig = RayRuntimeConfig(
    customCode = "customCode",
    driverFunction = "driverFunction",
    packages = List("package1"),
    privatePackages = Some(PrivatePackages("username", "token", List("url1"))),
    sourceS3Credentials = MezzanineAuthConfig("bucket", new AWSAuthConfig(new java.util.HashMap(), 123), new DataCredentials()),
    destS3Credentials = MezzanineAuthConfig("bucket", new AWSAuthConfig(new util.HashMap(), 123), new DataCredentials()),
    extraData = Some(JsonParser("""{"a": 1}""")),
    byoOptimized = false,
  )

  private def buildTransformer(rayApiClient: RayApiClient) = new RayTxTask(
    logPrefix = "",
    dataSource = Some(dataSource),
    runId = 123L,
    rayConfig = rayConfig,
    rayApi = rayApiClient,
    messageProducer = noopMessageProducer
  )

  private val directoryMessage = DirectoryTransformationMessage(
    headDatasetId = Some(234),
    offsets = List(FileOffset(123L, "filePath1", 111, 222, 333, Some(111), eof = true, Some("some message"))),
    fullDirectoryPath = "bucket/source/path"
  )

  private class NoopRayApiClient extends RayApiClient {
    def submitJobV2(submitJobRequest: SubmitDirectoryJobRequest): Future[JobId] = Future.successful(JobId("jobId"))

    def fetchJobDetails(jobId: JobId): Future[JobDetails] = Future.successful(JobDetails(JobStatus.Succeeded))

    def fetchErrorsDetails(jobId: JobId): Future[JobErrorDetails] = Future.successful(JobErrorDetails(List.empty))

    def fetchDirectoryJobDetails(jobId: JobId): Future[DirectoryJobDetails] = Future.successful(DirectoryJobDetails(JobStatus.Succeeded, Set(JobId("childJob1"))))

    def terminateJob(jobId: JobId): Future[JobTerminationDetails] = Future.successful(JobTerminationDetails(JsNull))
  }

  "RayDirectoryTransformer" should {

    "pass offsets, datasetId, sourceMetrics, bucket information in successful response" in {
      val rayApiClient = new RayApiClient {
        def submitJobV2(submitJobRequest: SubmitDirectoryJobRequest): Future[JobId] = Future.successful(JobId("jobId"))

        def fetchJobDetails(jobId: JobId): Future[JobDetails] = Future.successful(JobDetails(JobStatus.Succeeded))

        def fetchErrorsDetails(jobId: JobId): Future[JobErrorDetails] = Future.successful(JobErrorDetails(List.empty))

        def fetchDirectoryJobDetails(jobId: JobId): Future[DirectoryJobDetails] = Future.successful(DirectoryJobDetails(JobStatus.Succeeded, Set(JobId("childJob1"))))

        def terminateJob(jobId: JobId): Future[JobTerminationDetails] = Future.successful(JobTerminationDetails(JsNull))
      }

      val transformer = buildTransformer(rayApiClient)

      val responseList = transformer.transform(directoryMessage).futureValue(Timeout(1.second)).get.asInstanceOf[List[DirectoryTransformationMessage]]
      responseList should have size 1
      val response = responseList.head
      response.headDatasetId shouldEqual Some(234)
      response.offsets shouldEqual List(FileOffset(123L, "filePath1", 111, 222, 333, Some(111), eof = true, Some("some message")))
    }

    "submit proper data" in {
      var submittedRequest: SubmitDirectoryJobRequest = null
      val rayApiClient = new RayApiClient {
        def submitJobV2(submitJobRequest: SubmitDirectoryJobRequest): Future[JobId] = {
          submittedRequest = submitJobRequest
          Future.successful(JobId("jobId"))
        }

        def fetchJobDetails(jobId: JobId): Future[JobDetails] = Future.successful(JobDetails(JobStatus.Succeeded))

        def fetchErrorsDetails(jobId: JobId): Future[JobErrorDetails] = Future.successful(JobErrorDetails(List.empty))

        def fetchDirectoryJobDetails(jobId: JobId): Future[DirectoryJobDetails] = Future.successful(DirectoryJobDetails(JobStatus.Succeeded, Set(JobId("childJob1"))))

        def terminateJob(jobId: JobId): Future[JobTerminationDetails] = Future.successful(JobTerminationDetails(JsNull))
      }

      val transformer = buildTransformer(rayApiClient)

      val result = transformer.transform(directoryMessage).futureValue(Timeout(1.second)).get.asInstanceOf[List[DirectoryTransformationMessage]]
      result should have size 1
      submittedRequest.customCode shouldEqual "customCode"
      submittedRequest.driverFunction shouldEqual "driverFunction"
      submittedRequest.packages should contain theSameElementsAs List("package1", "boto3")
      submittedRequest.privatePackages shouldEqual Some(PrivatePackages("username", "token", List("url1")))
      submittedRequest.rayDestination.asInstanceOf[S3MezzanineRayDst].destS3Bucket shouldEqual MezzanineAuthConfig("bucket", new AWSAuthConfig(new util.HashMap(), 123), new DataCredentials())
      submittedRequest.raySource shouldEqual S3MezzanineRaySrc("bucket/source/path", MezzanineAuthConfig("bucket", new AWSAuthConfig(new util.HashMap(), 123), new DataCredentials()))
      submittedRequest.extraData shouldEqual Some(JsonParser("""{"a": 1}"""))
    }

    "pass through other type of messages" in {
      val transformer = buildTransformer(new NoopRayApiClient)
      val msg1 = JustOffset(Some(123), FileOffset(1, "a", 123, 123, 123, Some(444), eof = true, Some("msg")))
      val msg2 = RecordWithOffset(Some(123), new NexlaMessage(), FileOffset(1, "a", 123, 123, 123, Some(444), eof = true, Some("msg")))
      transformer.transform(msg1).futureValue.get shouldEqual List(msg1)
      transformer.transform(msg2).futureValue.get shouldEqual List(msg2)
    }

    "submit unique output paths" in {
      var submittedRequests = List.empty[SubmitDirectoryJobRequest]
      val rayApiClient = new NoopRayApiClient {
        override def submitJobV2(submitJobRequest: SubmitDirectoryJobRequest): Future[JobId] = {
          submittedRequests = submittedRequests ++ List(submitJobRequest)
          Future.successful(JobId("jobId"))
        }
      }

      val transformer = buildTransformer(rayApiClient)

      transformer.transform(directoryMessage).futureValue(Timeout(1.second)).get.asInstanceOf[List[DirectoryTransformationMessage]] should have size 1
      transformer.transform(directoryMessage).futureValue(Timeout(1.second)).get.asInstanceOf[List[DirectoryTransformationMessage]] should have size 1
      submittedRequests.head.rayDestination.asInstanceOf[S3MezzanineRayDst].fullDestinationDir should not equal submittedRequests(1).rayDestination.asInstanceOf[S3MezzanineRayDst].fullDestinationDir
    }

    "fail when job submission fails" in {
      val rayApiClient = new NoopRayApiClient {
        override def submitJobV2(submitJobRequest: SubmitDirectoryJobRequest): Future[JobId] = Future.failed(new Exception("some failure"))
      }

      val transformer = buildTransformer(rayApiClient)

      transformer.transform(directoryMessage).failed.futureValue(Timeout(1.second)) shouldBe an[Exception]
    }

    "return failure when directory job fails" in {
      val rayApiClient = new NoopRayApiClient {
        override def fetchDirectoryJobDetails(jobId: JobId): Future[DirectoryJobDetails] = Future.successful(DirectoryJobDetails(JobStatus.Failed, Set(JobId("childJob1"))))
      }

      val transformer = buildTransformer(rayApiClient)

      transformer.transform(directoryMessage).futureValue(Timeout(1.second)) shouldBe a[Failure[_]]
    }

    "return failure when any child job fails" in {
      val rayApiClient = new NoopRayApiClient {
        override def fetchJobDetails(jobId: JobId): Future[JobDetails] = Future.successful(JobDetails(JobStatus.Failed))
      }

      val transformer = buildTransformer(rayApiClient)

      transformer.transform(directoryMessage).futureValue(Timeout(1.second)) shouldBe a[Failure[_]]
    }

    "terminate pending jobs on stop method" in {
      var jobDispatched: Boolean = false
      var jobTerminated: Boolean = false
      val rayApiClient = new NoopRayApiClient {
        override def fetchJobDetails(jobId: JobId): Future[JobDetails] = Future {
          jobDispatched = true
          Thread.sleep(10000)
          JobDetails(JobStatus.Pending)
        }

        override def terminateJob(jobId: JobId): Future[JobTerminationDetails] = {
          jobTerminated = true
          Future.successful(JobTerminationDetails(JsNull))
        }
      }

      val transformer = buildTransformer(rayApiClient)
      transformer.transform(directoryMessage)

      eventually {
        jobDispatched shouldEqual true
      }

      transformer.stop().futureValue

      eventually {
       jobTerminated shouldEqual true
      }
    }

  }
}
