package com.nexla.inmemory_connector.pipeline.source

import akka.actor.ActorSystem
import akka.stream.KillSwitches
import akka.stream.scaladsl.{Keep, Sink}
import com.nexla.common.{NexlaMessage, Resource, ResourceType}
import com.nexla.connect.common.postponedFlush.InMemoryProgressTracker
import com.nexla.connector.config.FlowType
import com.nexla.inmemory_connector.compat.{MapOffset, RecordWithOffset}
import com.nexla.inmemory_connector.monitoring.telemetry.InMemoryTelemetry
import com.nexla.inmemory_connector.pipeline.source.SourceFlow.SourceDone
import com.nexla.inmemory_connector.pipeline.source.stopping_condition.SourceStoppingCondition
import com.nexla.inmemory_connector.pipeline.source.task.BasicSourceTask
import com.nexla.inmemory_connector.state.{FilesState, ResourceState}
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum
import com.nexla.telemetry.NoopTelemetry
import org.scalatest.concurrent.Eventually.eventually
import org.scalatest.concurrent.PatienceConfiguration.Timeout
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatest.time.{Millisecond, Span}

import scala.concurrent.duration._
import java.util
import java.util.concurrent.atomic.AtomicBoolean
import scala.concurrent.duration.{DurationInt, MILLISECONDS}
import scala.concurrent.{ExecutionContextExecutor, Future}
import scala.jdk.CollectionConverters.mapAsScalaMapConverter

class SourceFlowSpec extends AnyFlatSpec with Matchers {
  implicit val system: ActorSystem = ActorSystem()
  implicit val ec: ExecutionContextExecutor = system.dispatcher

  private def resourceState(): ResourceState = new ResourceState(new Resource(1, ResourceType.SOURCE))

  private def recordWithOffset(i: Int) = {
    val m = new util.LinkedHashMap[String, AnyRef] {
      {
        put("msgId", s"msg$i")
      }
    }
    RecordWithOffset(None, new NexlaMessage(m), MapOffset(1, 1, i, Map.empty[String, String]))
  }

  "InMemorySourceFlow" should "call start method before data processing" in {
    val startCalled = new AtomicBoolean(false)
    class DummySourceTask extends BasicSourceTask {
      val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = 1)
      var shouldStop = false
      def start(): Future[Unit] = {
        startCalled.set(true)
        Future.unit
      }

      def poll(): Future[List[RecordWithOffset]] = Future.successful(Nil).transform { r =>
        val isEmpty = r.map(_.isEmpty).getOrElse(true)
        shouldStop = stopCondition.shouldStop(isEmpty)
        r
      }

      def stop(): Future[Unit] = Future.unit

      def isStoppingConditionReached(): Boolean = shouldStop
    }

    val inMemorySourceTask = new InMemorySourceTask("", new DummySourceTask)
    val flow = new SourceFlow("", inMemorySourceTask, new FilesState, new InMemoryProgressTracker, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    flow.applySource(resourceState())
    eventually {
      startCalled.get() shouldEqual true
    }
  }

  it should "call stop method after pipeline finishes" in {
    val stopCalled = new AtomicBoolean(false)
    class DummySourceTask extends BasicSourceTask {
      val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = 1)
      var shouldStop = false
      def start(): Future[Unit] = Future.unit

      def poll(): Future[List[RecordWithOffset]] = Future.successful(Nil).transform { r =>
        val isEmpty = r.map(_.isEmpty).getOrElse(true)
        shouldStop = stopCondition.shouldStop(isEmpty)
        r
      }

      def stop(): Future[Unit] = {
        stopCalled.set(true)
        Future.unit
      }

      def isStoppingConditionReached(): Boolean = shouldStop
    }

    val inMemorySourceTask = new InMemorySourceTask("", new DummySourceTask)
    val flow = new SourceFlow("", inMemorySourceTask, new FilesState, new InMemoryProgressTracker, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    val _: SourceDone = flow.applySource(resourceState()).to(Sink.ignore).run().futureValue
    stopCalled.get() shouldEqual true
  }

  it should "call stop method after task start fails" in {
    val stopCalled = new AtomicBoolean(false)
    class DummySourceTask extends BasicSourceTask {
      val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = 1)
      var shouldStop = false
      def start(): Future[Unit] = Future.failed(new Exception("something went wrong with starting the task"))

      def poll(): Future[List[RecordWithOffset]] = Future.successful(Nil).transform { r =>
        val isEmpty = r.map(_.isEmpty).getOrElse(true)
        shouldStop = stopCondition.shouldStop(isEmpty)
        r
      }

      def stop(): Future[Unit] = {
        stopCalled.set(true)
        Future.unit
      }

      def isStoppingConditionReached(): Boolean = shouldStop
    }

    val inMemorySourceTask = new InMemorySourceTask("", new DummySourceTask)
    val flow = new SourceFlow("", inMemorySourceTask, new FilesState, new InMemoryProgressTracker, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    flow.applySource(resourceState()).to(Sink.ignore).run().failed.futureValue
    stopCalled.get() shouldEqual true
  }

  it should "fail stream if starting task fails" in {
    class DummySourceTask extends BasicSourceTask {
      val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = 1)
      var shouldStop = false
      def start(): Future[Unit] = Future.failed(new Exception("something went wrong with starting the task"))

      def poll(): Future[List[RecordWithOffset]] = Future.successful(Nil).transform { r =>
        val isEmpty = r.map(_.isEmpty).getOrElse(true)
        shouldStop = stopCondition.shouldStop(isEmpty)
        r
      }

      def stop(): Future[Unit] = Future.unit

      def isStoppingConditionReached(): Boolean = shouldStop
    }

    val inMemorySourceTask = new InMemorySourceTask("", new DummySourceTask)
    val flow = new SourceFlow("", inMemorySourceTask, new FilesState, new InMemoryProgressTracker, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    val exception = flow.applySource(resourceState()).runWith(Sink.ignore).failed.futureValue
    exception.getMessage shouldEqual "something went wrong with starting the task"
  }

  it should "not fail if polling fails" in {
    class DummySourceTask extends BasicSourceTask {
      val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = 1)
      var shouldStop = false
      def start(): Future[Unit] = Future.unit

      def poll(): Future[List[RecordWithOffset]] = Future.failed[List[RecordWithOffset]](new Exception("something wrong with polling")).transform { r =>
        val isEmpty = r.map(_.isEmpty).getOrElse(true)
        shouldStop = stopCondition.shouldStop(isEmpty)
        r
      }

      def stop(): Future[Unit] = Future.unit

      def isStoppingConditionReached(): Boolean = shouldStop
    }
    val inMemorySourceTask = new InMemorySourceTask("", new DummySourceTask)
    val flow = new SourceFlow("", inMemorySourceTask, new FilesState, new InMemoryProgressTracker, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    val _: SourceDone = flow.applySource(resourceState()).toMat(Sink.seq)(Keep.left).run().futureValue
    succeed
  }

  it should "not fail if stopping fails" in {
    val stopCalled = new AtomicBoolean(false)
    class DummySourceTask extends BasicSourceTask {
      val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = 1)
      var shouldStop = false
      def start(): Future[Unit] = Future.unit

      def poll(): Future[List[RecordWithOffset]] = Future.successful(Nil).transform { r =>
        val isEmpty = r.map(_.isEmpty).getOrElse(true)
        shouldStop = stopCondition.shouldStop(isEmpty)
        r
      }

      def stop(): Future[Unit] = {
        stopCalled.set(true)
        Future.failed(new Exception("sth wrong"))
      }

      def isStoppingConditionReached(): Boolean = shouldStop
    }
    val inMemorySourceTask = new InMemorySourceTask("", new DummySourceTask)
    val flow = new SourceFlow("", inMemorySourceTask, new FilesState, new InMemoryProgressTracker, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    val _: SourceDone = flow.applySource(resourceState()).to(Sink.ignore).run().futureValue
    stopCalled.get() shouldEqual true
    succeed
  }

  it should "treat polling failures as empty data" in {
    class DummySourceTask extends BasicSourceTask {
      var cnt = 0
      val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = 2)
      var shouldStop = false

      def start(): Future[Unit] = Future.unit

      def poll(): Future[List[RecordWithOffset]] = {
        cnt += 1
        if (cnt == 2) Future.failed(new Exception("sth wrong"))
        else if (cnt <= 3) Future.successful(List(recordWithOffset(cnt)))
        else Future.successful(Nil)
      }.transform { r =>
        val isEmpty = r.map(_.isEmpty).getOrElse(true)
        shouldStop = stopCondition.shouldStop(isEmpty)
        r
      }

      def stop(): Future[Unit] = Future.unit

      def isStoppingConditionReached(): Boolean = shouldStop
    }
    val inMemorySourceTask = new InMemorySourceTask("", new DummySourceTask)
    val flow = new SourceFlow("", inMemorySourceTask, new FilesState, new InMemoryProgressTracker, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    val (matF: Future[SourceDone], recordsF) = flow.applySource(resourceState()).toMat(Sink.seq)(Keep.both).run()
    matF.futureValue
    val records = recordsF.futureValue
    val extractedMessages = records.collect { case r: RecordWithOffset => r }.map(_.message).map(_.getRawMessage.asScala)
    extractedMessages shouldEqual Seq(Map("msgId" -> "msg1"), Map("msgId" -> "msg3"))
  }

  it should "read all records from batch provided by task" in {
    class DummySourceTask extends BasicSourceTask {
      var cnt = 0
      val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = 2)
      var shouldStop = false

      def start(): Future[Unit] = Future.unit

      def poll(): Future[List[RecordWithOffset]] = {
        cnt += 1
        if (cnt <= 2) Future.successful(List(recordWithOffset(cnt), recordWithOffset(100 + cnt)))
        else Future.successful(Nil)
      }.transform { r =>
        val isEmpty = r.map(_.isEmpty).getOrElse(true)
        shouldStop = stopCondition.shouldStop(isEmpty)
        r
      }

      def stop(): Future[Unit] = Future.unit

      def isStoppingConditionReached(): Boolean = shouldStop
    }
    val inMemorySourceTask = new InMemorySourceTask("", new DummySourceTask)
    val flow = new SourceFlow("", inMemorySourceTask, new FilesState, new InMemoryProgressTracker, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    val (matF: Future[SourceDone], recordsF) = flow.applySource(resourceState()).toMat(Sink.seq)(Keep.both).run()
    matF.futureValue
    val records = recordsF.futureValue
    val extractedMessages = records.collect { case r: RecordWithOffset => r }.map(_.message).map(_.getRawMessage.asScala)
    extractedMessages shouldEqual Seq(Map("msgId" -> "msg1"), Map("msgId" -> "msg101"), Map("msgId" -> "msg2"), Map("msgId" -> "msg102"))
  }

  it should "stop according to source stopping condition logic" in {
    class DummySourceTask(stopOnEmptyPollCount: Int) extends BasicSourceTask {
      var cnt = 0
      val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = stopOnEmptyPollCount)
      var shouldStop = false

      def start(): Future[Unit] = Future.unit

      def poll(): Future[List[RecordWithOffset]] = {
        cnt += 1
        if (cnt == 1) Future.successful(Nil)
        else if (cnt <= 2) Future.successful(List(recordWithOffset(cnt)))
        else Future.successful(Nil)
      }.transform { r =>
        val isEmpty = r.map(_.isEmpty).getOrElse(true)
        shouldStop = stopCondition.shouldStop(isEmpty)
        r
      }

      def stop(): Future[Unit] = Future.unit

      def isStoppingConditionReached(): Boolean = shouldStop
    }
    val inMemorySourceTask1 = new InMemorySourceTask("", new DummySourceTask(1))
    val flow1 = new SourceFlow("", inMemorySourceTask1, new FilesState, new InMemoryProgressTracker, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    val (_: Future[SourceDone], records1F) = flow1.applySource(resourceState()).toMat(Sink.seq)(Keep.both).run()
    val records1 = records1F.futureValue
    val extractedMessages1 = records1.collect { case r: RecordWithOffset => r }.map(_.message).map(_.getRawMessage.asScala)
    extractedMessages1 shouldEqual Seq.empty

    val inMemorySourceTask2 = new InMemorySourceTask("", new DummySourceTask(2))
    val flow2 = new SourceFlow("", inMemorySourceTask2, new FilesState, new InMemoryProgressTracker, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    val (_: Future[SourceDone], records2F) = flow2.applySource(resourceState()).toMat(Sink.seq)(Keep.both).run()
    val records2 = records2F.futureValue
    val extractedMessages2 = records2.collect { case r: RecordWithOffset => r }.map(_.message).map(_.getRawMessage.asScala)
    extractedMessages2 shouldEqual Seq(Map("msgId" -> "msg2"))
  }

  it should "mark source as started after first data is given by task" in {
    class DummySourceTask extends BasicSourceTask {
      var cnt = 0
      val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = 1)
      var shouldStop = false

      def start(): Future[Unit] = Future.unit

      def poll(): Future[List[RecordWithOffset]] = {
        cnt += 1
        Future.successful(List(recordWithOffset(cnt)))
      }.transform { r =>
        val isEmpty = r.map(_.isEmpty).getOrElse(true)
        shouldStop = stopCondition.shouldStop(isEmpty)
        r
      }

      def stop(): Future[Unit] = Future.unit

      def isStoppingConditionReached(): Boolean = shouldStop
    }
    val inMemorySourceTask = new InMemorySourceTask("", new DummySourceTask)
    val flow = new SourceFlow("", inMemorySourceTask, new FilesState, new InMemoryProgressTracker, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    val state = resourceState()
    state.getStatus() shouldEqual PipelineTaskStateEnum.NotRunning
    val ks = flow.applySource(state).viaMat(KillSwitches.single)(Keep.right).to(Sink.ignore).run()
    eventually {
      state.getStatus() shouldEqual PipelineTaskStateEnum.Running
    }
    ks.shutdown()
  }

  it should "mark source as finished when pipeline finishes" in {
    class DummySourceTask extends BasicSourceTask {
      val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = 1)
      var shouldStop = false
      
      def start(): Future[Unit] = Future.unit

      def poll(): Future[List[RecordWithOffset]] = Future.successful(Nil).transform { r =>
        val isEmpty = r.map(_.isEmpty).getOrElse(true)
        shouldStop = stopCondition.shouldStop(isEmpty)
        r
      }

      def stop(): Future[Unit] = Future.unit

      def isStoppingConditionReached(): Boolean = shouldStop
    }
    val inMemorySourceTask = new InMemorySourceTask("", new DummySourceTask)
    val flow = new SourceFlow("", inMemorySourceTask, new FilesState, new InMemoryProgressTracker, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    val state = resourceState()
    state.getStatus() shouldEqual PipelineTaskStateEnum.NotRunning
    val _: SourceDone = flow.applySource(state).to(Sink.ignore).run().futureValue
    state.getStatus() shouldEqual PipelineTaskStateEnum.Finished
  }

  it should "mark source as finished in progress tracker when pipeline finishes" in {
    class DummySourceTask extends BasicSourceTask {
      val stopCondition = new SourceStoppingCondition(warmupTime = 0.seconds, stopOnEmptyPollCount = 1)
      var shouldStop = false
      def start(): Future[Unit] = Future.unit

      def poll(): Future[List[RecordWithOffset]] = Future.successful(Nil).transform { r =>
        val isEmpty = r.map(_.isEmpty).getOrElse(true)
        shouldStop = stopCondition.shouldStop(isEmpty)
        r
      }

      def stop(): Future[Unit] = Future.unit

      def isStoppingConditionReached(): Boolean = shouldStop
    }
    val pt = new InMemoryProgressTracker
    val inMemorySourceTask = new InMemorySourceTask("", new DummySourceTask)
    val flow = new SourceFlow("", inMemorySourceTask, new FilesState, pt, new InMemoryTelemetry(new NoopTelemetry, 1, FlowType.IN_MEMORY, 1))
    pt.isSourceFinished(null) shouldEqual false
    val _: SourceDone = flow.applySource(resourceState()).to(Sink.ignore).run().futureValue(Timeout(300.milliseconds))
    pt.isSourceFinished(null) shouldEqual true
  }

}
