package com.nexla.inmemory_connector.monitoring

import com.bazaarvoice.jolt.JsonUtils
import com.nexla.common.ResourceType
import com.nexla.common.metrics.NexlaRawMetric
import com.nexla.common.notify.transport.{ControlMessageProducer, NexlaMessageProducer, NexlaMessageTransport}
import com.nexla.connector.config.FlowType
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryControlMessageProducer
import com.nexla.listing.client.AdaptiveFlowTask
import org.mockito.ArgumentCaptor
import org.mockito.Mockito.{mock, spy, verify, when}
import org.scalatest.TagAnnotation
import org.scalatest.funsuite.AnyFunSuite

import scala.concurrent.ExecutionContext

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class MetricsSenderImplTest extends AnyFunSuite {

  test("should append display path, name and id to the metric event") {
    val mockMsgProducer = mock(classOf[ControlMessageProducer])
    val mockTransport = mock(classOf[NexlaMessageTransport])
    when(mockMsgProducer.getTransport).thenReturn(mockTransport)
    val spyProducer = spy(new InMemoryControlMessageProducer(mockMsgProducer))

    val metricsSender = new MetricsSenderImpl(spyProducer, FlowType.IN_MEMORY, 123L) {
      override protected val ioEx: ExecutionContext = DirectExecutionContext
    }
    val adaptiveFlowTask: AdaptiveFlowTask = JsonUtils.streamToType(getClass.getClassLoader.getResourceAsStream("./adaptiveTaskDef.json"), classOf[AdaptiveFlowTask])

    // Capture the metric argument
    val metricCaptor = ArgumentCaptor.forClass(classOf[NexlaRawMetric])

    metricsSender.publishMetric(
      123,
      ResourceType.SOURCE,
      None,
      100L, // 100 records
      200L, // 200 bytes
      "filePath",
      Some(true), // eof
      Option(adaptiveFlowTask)
    )

    // Verify the metric was published and capture it
    verify(spyProducer).publishMetrics(metricCaptor.capture())
    
    // Get the captured metric and verify its contents
    val capturedMetric: NexlaRawMetric = metricCaptor.getValue
    assert(capturedMetric.getTags.get("name") == "99")
    assert(capturedMetric.getTags.get("display_path") == "#99 myTask")
  }
}

// hack: here we are creating a custom ExecutionContext that runs tasks directly in the current thread. otherwise futures get a bit crazy
object DirectExecutionContext extends ExecutionContext {
  override def execute(runnable: Runnable): Unit = runnable.run()
  override def reportFailure(cause: Throwable): Unit = throw cause
}