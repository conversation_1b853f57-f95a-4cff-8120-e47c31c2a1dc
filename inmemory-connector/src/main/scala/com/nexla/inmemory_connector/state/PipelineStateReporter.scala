package com.nexla.inmemory_connector.state

import akka.actor.ActorSystem
import cats.implicits.toTraverseOps
import com.nexla.admin.client.PipelineRunState
import com.nexla.admin.utils.SourceUtils
import com.nexla.common.datetime.DateTimeUtils
import com.nexla.common.{AppType, NexlaConstants, Resource, ResourceType}
import com.nexla.control.ListingFileStatus
import com.nexla.control.coordination.{HeartbeatConnectorCoordination, HeartbeatType}
import com.nexla.control.message.{ControlEventType, SourceControlMessage}
import com.nexla.inmemory_connector.context.Context.PipelineContext
import com.nexla.inmemory_connector.monitoring.MetricsSender
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryControlMessageProducer
import com.nexla.inmemory_connector.pipeline.sink.offsets.FileOffsetCommitter
import com.nexla.inmemory_connector.state.FilesState.FileInfo
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum.PipelineTaskState
import com.nexla.sc.client.listing.CoordinationAppClient
import com.nexla.sc.util.StrictNexlaLogging

import java.util.concurrent.Executors
import java.util.UUID
import scala.collection.concurrent.TrieMap
import scala.compat.java8.OptionConverters.RichOptionForJava8
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}

class PipelineStateReporter(pipelineContext: PipelineContext, inFlightFilesState: FilesState)
                           (coordinationClient: CoordinationAppClient, fileOffsetCommitter: FileOffsetCommitter, metricsSender: MetricsSender, controlMessageProducer: InMemoryControlMessageProducer, initialDelay: FiniteDuration, heartbeatPeriod: FiniteDuration)
                           (implicit val system: ActorSystem, ec: ExecutionContext) extends StateReporter with StrictNexlaLogging {
  private val blockingKafkaExecutionContext: ExecutionContext = {
    val threadPool = Executors.newCachedThreadPool()
    ExecutionContext.fromExecutor(threadPool)
  }
  private var resourceStates: List[ResourceState] = List.empty
  private var stopScheduling: () => Unit = () => ()
  private val lastSentStatuses: TrieMap[Resource, PipelineTaskState] = TrieMap.empty

  private def heartbeatMessage(resource: Resource, pipelineTaskState: PipelineTaskState): HeartbeatConnectorCoordination = {
    val msg = new HeartbeatConnectorCoordination(
      UUID.randomUUID().toString,
      resource.`type`,
      resource.id,
      pipelineContext.runId,
      HeartbeatType.DATA,
      DateTimeUtils.toMillis(DateTimeUtils.nowUtc()),
    )
    msg.getContext.put("flowType", pipelineContext.flowType.name())
    msg.getContext.put("flowId", String.valueOf(pipelineContext.flow.getId))
    msg.getContext.put("sourceId", String.valueOf(pipelineContext.dataSource.getId))
    msg.getContext.put("state", taskStateToRunState(pipelineTaskState).name())
    msg
  }

  private def taskStateToRunState(pipelineTaskState: PipelineTaskState): PipelineRunState =
    pipelineTaskState match {
      case PipelineTaskStateEnum.NotRunning => PipelineRunState.NOT_STARTED
      case PipelineTaskStateEnum.Running => PipelineRunState.IN_PROGRESS
      case PipelineTaskStateEnum.Finished => PipelineRunState.DONE
      case PipelineTaskStateEnum.Failed => PipelineRunState.ERROR
      case PipelineTaskStateEnum.Decomissioning => PipelineRunState.IN_PROGRESS
    }

  private def heartbeatFiles(): Future[Unit] = {
    val inFlightFiles = inFlightFilesState.getFiles().filter { f =>
      f.status match {
        case FilesState.DuringProcessing => true
        case _ => false
      }
    }

    if (inFlightFiles.nonEmpty) {
      inFlightFiles.groupBy(_.sourceId).toList.flatTraverse { case (sourceId, files) =>
        val fileIds = files.map(_.fileId)
        fileIds.grouped(pipelineContext.pipelineConf.fileHeartbeatBatchSize).toList.traverse { fileIdsChunk =>
          logger.info(s"Heartbeating files: [$sourceId; $fileIdsChunk]")
          coordinationClient.batchHeartBeat(sourceId, fileIdsChunk)
        }
      }.map(_ => ())
    } else Future.unit
  }

  private def markFailedFilesAsDone(): Future[Unit] = {
    val failedFiles = inFlightFilesState.getFiles().collect {
      case f @ FileInfo(_, _, _, FilesState.Failed(errMsg)) => f -> errMsg
    }
    if (failedFiles.nonEmpty) {
      logger.info(s"Marking failed files as done: [$failedFiles]")
      failedFiles.toList.traverse { case (FileInfo(fileId, filePath, sourceId, _), errMsg) =>
        for {
          _ <- fileOffsetCommitter.commit(sourceId, fileId, ListingFileStatus.DONE, None, Some(errMsg))
          _ <- metricsSender.publishMetric(sourceId, ResourceType.SOURCE, None, 0, 0, filePath, Some(true), pipelineContext.maybeAdaptiveFlowTask)
          _ = inFlightFilesState.removeFile(fileId)
        } yield ()
      }.map(_ => ())
    } else Future.unit
  }

  private def markRemainingFiles(): Future[Unit] = {
    val files = inFlightFilesState.getFiles()
    if (files.nonEmpty) {
      logger.info(s"Marking remaining files: [$files]")
      files.toList.traverse { case FileInfo(fileId, _, sourceId, status) =>
        val (listingStatus, msg) = status match {
          case FilesState.DuringProcessing => ListingFileStatus.STOPPED -> "File processing incomplete"
          case FilesState.Failed(errMsg) => ListingFileStatus.DONE -> errMsg
        }
        fileOffsetCommitter.commit(sourceId, fileId, listingStatus, None, Some(msg)).map(_ => inFlightFilesState.removeFile(fileId))
      }.map(_ => ())
    } else Future.unit
  }

  private def heartbeatResource(resourceState: ResourceState): Future[Unit] = {
    logger.info(s"Heartbeating resource state: [${resourceState.summary()}]")
    val currentStatus = resourceState.getStatus()
    val sourceMsg = heartbeatMessage(resourceState.resource, currentStatus)
    Future(controlMessageProducer.sendHeartbeat(sourceMsg))(blockingKafkaExecutionContext).map { _ =>
      lastSentStatuses.put(resourceState.resource, currentStatus)
    }
  }

  private def emitEventForCtrlToFreeUpResources(): Future[Unit] = {
    if (pipelineContext.pipelineConf.dockerInstances == 1 && pipelineContext.maybeAdaptiveFlowTask.isEmpty) {
      val context = java.util.Map.of(NexlaConstants.HARD_STOP, String.valueOf(true))
      val dataSource = pipelineContext.dataSource

      val stopMsg = new SourceControlMessage(
        UUID.randomUUID(),
        dataSource.getId,
        ControlEventType.PAUSE,
        dataSource.getConnectionType,
        AppType.IN_MEMORY_CONNECTOR.appName,
        context,
        Option(SourceUtils.toResourceDto(dataSource)).asJava,
        None.asJava,
      )

      Future {
        controlMessageProducer.sendControlMessage(stopMsg)
        logger.info("Final state report and STOP message sent")
      }(blockingKafkaExecutionContext)
    } else {
      logger.info(s"Final state report sent. Skipping Pause message because docker.instances = ${pipelineContext.pipelineConf.dockerInstances} or is adaptive flow (${pipelineContext.maybeAdaptiveFlowTask.isDefined})")
      Future.unit
    }
  }

  private def resourceStateChanged(resourceState: ResourceState): Boolean = {
    val currentStatus = resourceState.getStatus()
    val stateChanged = lastSentStatuses.get(resourceState.resource) match {
      case Some(oldStatus) => oldStatus != currentStatus
      case None => true
    }
    stateChanged
  }

  def start(states: List[ResourceState]): Unit = {
    resourceStates = states
    logger.info(s"Scheduling state reporter runs for: [${resourceStates.map(_.summary()).mkString("|")}]")
    val stateChangeDetectorSchedule = system.scheduler.scheduleWithFixedDelay(initialDelay, 5.seconds)(() =>
      resourceStates.filter(resourceStateChanged).traverse(heartbeatResource)
    )
    val heartbeatingSchedule = system.scheduler.scheduleWithFixedDelay(initialDelay, heartbeatPeriod)(() =>
      resourceStates.filter(_.getStatus() == PipelineTaskStateEnum.Running).traverse(heartbeatResource)
    )
    val fileHeartbeatSchedule = system.scheduler.scheduleWithFixedDelay(initialDelay, heartbeatPeriod)(() => {
      heartbeatFiles()
      markFailedFilesAsDone()
    })
    this.stopScheduling = () => {
      stateChangeDetectorSchedule.cancel()
      heartbeatingSchedule.cancel()
      fileHeartbeatSchedule.cancel()
    }
  }

  def finalReportAndFinish(): Future[Unit] = {
    logger.info(s"Reporting state for the last time...")
    this.stopScheduling()
    for {
      _ <- resourceStates.traverse { state =>
        if (pipelineContext.pipelineConf.dockerInstances > 1 && state.resource.isSink) {
          logger.info(s"Skipping reporting terminal state for Sink because dockerInstances = ${pipelineContext.pipelineConf.dockerInstances} to allow timeout-based WriteDone event")
          Future.unit
        } else {
          heartbeatResource(state)
        }
      }
      _ <- heartbeatFiles()
      _ <- markFailedFilesAsDone()
      _ <- markRemainingFiles()
      _ <- emitEventForCtrlToFreeUpResources()
    } yield {
      controlMessageProducer.flush()
    }
  }

}
