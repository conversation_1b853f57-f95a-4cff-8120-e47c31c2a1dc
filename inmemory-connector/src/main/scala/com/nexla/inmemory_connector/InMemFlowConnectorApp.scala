package com.nexla.inmemory_connector

import akka.actor.{ActorSystem, Scheduler}
import akka.http.scaladsl.Http
import akka.http.scaladsl.server.Route
import akka.stream.Materializer
import com.nexla.admin.client.{AdminApiClient, AdminApiClientBuilder}
import com.nexla.common.connectiontype.LoadConnectorServiceClient
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.common.notify.transport.{ControlMessageProducer, DataMessageProducer}
import com.nexla.common.{AppType, NexlaKafkaConfig, RestTemplateBuilder}
import com.nexla.inmemory_connector.context.builder.ContextBuilder
import com.nexla.inmemory_connector.pipeline.flow.newimpl.ImcListingClient
import com.nexla.inmemory_connector.pipeline.metrics.{InMemoryControlMessageProducer, InMemoryDataMessageProducer}
import com.nexla.inmemory_connector.state.FilesState
import com.nexla.inmemory_connector_common.PipelineKiller
import com.nexla.inmemory_connector_common.api.ApiHandler
import com.nexla.inmemory_connector_common.listing.AdaptiveFlowTasksStreamSource
import com.nexla.inmemory_connector_common.probe.ProbeFactoryImpl
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.kafka.{AdminControlUpdatesListener, CtrlTopics, KafkaMessageTransport}
import com.nexla.listing.client.{AdaptiveFlowTask, FileVaultClient, ListingClient}
import com.nexla.sc.client.OffsetSaver
import com.nexla.sc.client.listing._
import com.nexla.sc.util.{AppUtils, StrictNexlaLogging}
import com.nexla.telemetry.jmx.JmxExporter
import com.nexla.transform.TransformServiceImpl
import com.nexla.transform.cache.{CustomScriptService, DataMapService}
import com.nexla.transform.schema.FormatDetector
import fr.davit.akka.http.metrics.core.HttpMetricsRegistry
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsRoute
import org.apache.commons.lang3.StringUtils
import org.slf4j.bridge.SLF4JBridgeHandler

import java.util.{Optional, UUID}
import scala.compat.java8.OptionConverters._
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

object InMemFlowConnectorApp
  extends App
    with AppUtils
    with StrictNexlaLogging {

  // Needed to redirect java.util.logging to slf4j
  SLF4JBridgeHandler.removeHandlersForRootLogger
  SLF4JBridgeHandler.install

  private implicit val (props, _, envMap) = loadProps(AppType.IN_MEMORY_CONNECTOR, new AppProps(_))
  private val appSslContext = nexlaSslContext(props)
  LoadConnectorServiceClient.init(props.probeApp, props.nexlaCreds.username, props.nexlaCreds.password, appSslContext)
  private val (telemetry, httpMetricsRegistry) = initTelemetry(props.telemetryConf, AppType.IN_MEMORY_CONNECTOR.name, Some(props.dataDog), Some(props.prometheus))
  private val jmxExporter: JmxExporter = new JmxExporter(telemetry)

  implicit val system: ActorSystem = ActorSystem(defaultActorSystemName, loadAkkaConfig(None))
  implicit val materializer: Materializer = Materializer(system)
  implicit val scheduler: Scheduler = system.scheduler
  implicit val executionContext: ExecutionContext = system.dispatcher

  private val nodeId = props.nodeId
    .getOrElse(UUID.randomUUID().toString)

  private val restTemplate = new RestTemplateBuilder().withSSL(appSslContext).build()

  val controlKafkaConfig = controlKafkaContext(props)

  val dataKafkaConfig = new NexlaKafkaConfig(
    props.dataBootstrapServer,
    props.dataKafkaSecurityProtocol,
    props.dataKafkaTruststoreLocation.asJava,
    props.dataKafkaTruststorePassword.asJava,
    props.dataKafkaKeystoreLocation.asJava,
    props.dataKafkaKeystorePassword.asJava,
    props.dataKafkaSaslMechanism.asJava,
    props.dataKafkaJaasTemplate.asJava,
    props.dataKafkaSaslUsername.asJava,
    props.dataKafkaSaslPassword.asJava
  )

  private val controlMessageProducer = new InMemoryControlMessageProducer(new ControlMessageProducer(new KafkaMessageTransport(controlKafkaConfig)))
  private val dataMessageProducer = new InMemoryDataMessageProducer(new DataMessageProducer(new KafkaMessageTransport(dataKafkaConfig)))

  private val adminApi: AdminApiClient = new AdminApiClientBuilder()
    .setAppName(s"${AppType.IN_MEMORY_CONNECTOR.appName}-$nodeId")
    .setDataPlaneUid(props.dataplaneUid)
    .setEnrichmentUrl(props.credEnrichmentUrl)
    .setNoCache(false)
    .create(props.apiCredentialsServer, props.apiAccessKey, restTemplate)

  private val ctrlTopics = new CtrlTopics(Some(UUID.randomUUID()), props, controlKafkaConfig, system)
  private val metricAggregator = HealthMetricAggregator.getInstance(controlMessageProducer)
  new AdminControlUpdatesListener(AppType.IN_MEMORY_CONNECTOR, ctrlTopics, metricAggregator).startMonitoring()

  FormatDetector.initDefault()

  // create static service
  new DataMapService(
    props.redisCreds.hosts, 100, 2,
    props.redisCreds.clusterEnabled, props.redisCreds.password.orNull, adminApi,
    props.redisCreds.tlsContext.asJava,
    props.lookupDataModelVersioningEnabled
  )

  private val transformServiceImpl: TransformServiceImpl = new TransformServiceImpl()
  val listingClient = new ListingAppClient(props.listingAppServer, props.nexlaCreds)
  val coordinationClient = new CoordinationAppClient(props.coordinationAppServer, props.nexlaCreds)
  val javaListingClient = new ImcListingClient(props.listingAppServer, props.nexlaCreds.username, props.nexlaCreds.password, restTemplate)

  new CustomScriptService(
    props.redisCreds.hosts, props.redisLruCacheCapacity, props.redisLruExpirationMin,
    props.redisCreds.clusterEnabled, props.redisCreds.password.orNull, adminApi,
    props.redisCreds.tlsContext.asJava)

  private val fileVault = new FileVaultClient(props.fileVaultUrl, props.nexlaCreds.username, props.nexlaCreds.password, restTemplate)

  val offsetSaver = new OffsetSaver(coordinationClient, controlMessageProducer)
  val probeFactory = new ProbeFactoryImpl()

  configureHttpClientSslContext(appSslContext)
  logger.info(s"using adaptive flow tasks service URL (listing app): ${props.listingAppServer}")
  private val adaptiveFlowTasksStreamSource = new AdaptiveFlowTasksStreamSource(javaListingClient, 1.minute)

  private val pipelineRunner = new PipelineRunner(adminApi, listingClient, javaListingClient, coordinationClient, fileVault,
    probeFactory, props, transformServiceImpl, offsetSaver, controlMessageProducer, dataMessageProducer, telemetry)

  private val pipelineContextBuilder = new ContextBuilder(adminApi, controlMessageProducer, props)
  private val pipelineKiller = new PipelineKiller()
  private val api = new ApiHandler(httpMetricsRegistry, envMap, pipelineKiller)

  private val Interface = "0.0.0.0"

  private def startMetricsServer(port: Int, route: Route, registry: HttpMetricsRegistry, shouldStartMetricsServer: Boolean): Future[Unit] = {
    if (shouldStartMetricsServer) {
      val routeWithMetrics = HttpMetricsRoute(route).recordMetrics(registry)
      val bindingFuture = Http(system).newServerAt(Interface, port).bindFlow(routeWithMetrics)

      jmxExporter.enable()

      bindingFuture.onComplete {
        case Success(_) => logger.info(s"Metrics server started on $Interface:$port")
        case Failure(ex) => logger.error(s"Could not start Metrics server on port $Interface:$port", ex)
      }
      bindingFuture.map(_ => ())
    } else {
      logger.info("shouldStartMetricsServer is false or there's a pending adaptive flow task, skipping start of the metrics server")
      Future.successful()
    }
  }

  private def startInternalApiServer(port: Int, route: Route, shouldStartInternalApiServer: Boolean): Future[Unit] = {
    if (shouldStartInternalApiServer) {
      val bindingFuture = Http(system).newServerAt(Interface, port).bindFlow(route)
      bindingFuture.onComplete {
        case Success(_) => logger.info(s"API server started on $Interface:$port")
        case Failure(ex) => logger.error(s"Could not start API server on port $Interface:$port", ex)
      }
      bindingFuture.map(_ => ())
    } else {
      logger.info("shouldStartInternalApiServer is false or there's a pending adaptive flow task, skipping start of the internal API server")
      Future.successful()
    }
  }

  val flowId = props.imcFlowId.getOrElse(throw new Exception("Flow id needs to be defined"))
  val runId = props.imcRunId.getOrElse(throw new Exception("Run id needs to be defined"))

  private def pipelineFuture(maybeAdaptiveFlowTask: Option[AdaptiveFlowTask]) = for {
    _ <- Future(logger.info(s"App is starting [nodeId: $nodeId, podIp: ${props.podIp}, flowId: ${props.imcFlowId}, runId: ${props.imcRunId}]"))
    _ <- httpMetricsRegistry.fold(Future.unit)(r => startMetricsServer(props.nexlaMetricsPort, api.metricsRoute, r, maybeAdaptiveFlowTask.isEmpty))
    _ <- startInternalApiServer(8083, api.internalRoutes, maybeAdaptiveFlowTask.isEmpty)
    ctx <- pipelineContextBuilder.createPipelineContext(maybeAdaptiveFlowTask, flowId, runId)
    _ <- pipelineRunner.run(ctx, pipelineKiller, new FilesState())
  } yield ()

  private def startWaitUntilKilledFuture(): Future[Unit] = {
    Future {
      while (true) {
        logger.info("App finished. Waiting to be killed externally")
        Thread.sleep(20000)
      }
    }(system.dispatcher)
  }

  sys.addShutdownHook {
    logger.info("Shutting down app")
    val f = Future.unit
      .transformWith { result =>
        logger.info("Shutting down pipeline")
        pipelineKiller.stop()
        pipelineFuture(Option.empty).tap(_ => logger.info("Pipeline shutdown normally")).tapError(logger.error("Pipeline shutdown failed", _))
          .transformWith(_ => Future.fromTry(result))
      }
      .transformWith { result: Try[_] =>
        logger.info("Closing kafka producer")
        Future(controlMessageProducer.close()).tap(_ => logger.info("Kafka producer closed normally")).tapError(logger.error("Failed to close kafka producer", _))
          .transformWith(_ => Future.fromTry(result))
      }
      .transformWith { result: Try[_] =>
        logger.info("Shutting down http pool")
        Http().shutdownAllConnectionPools().tap(_ => logger.info("Http pool shutdown normally")).tapError(logger.error("Http pool shutdown failed", _))
          .transformWith(_ => Future.fromTry(result))
      }
      .transformWith { result: Try[_] =>
        logger.info("Terminating actor system")
        system.terminate().tap(_ => logger.info("Actor system terminated normally")).tapError(logger.error("Actor system termination failed", _))
          .transformWith(_ => Future.fromTry(result))
      }
    Await.ready(f, 5.minutes)
  }

  // here we create a source, which emits "pipeline futures" based on the cfg
  // violating the isolation principles - but allowing us to control it on the basic level
  val source = adminApi.getNexlaFlow(flowId).get().getDataSources.get(0)
  private val adaptiveEnabled = Optional.ofNullable(source.getSourceConfig.get("adaptive.flow")).asScala.map(_.toString).exists(_.toBoolean)

  private def markAsSmth(tb: Try[Unit], task: AdaptiveFlowTask): Unit = Try {
    if (tb.isSuccess) {
      logger.info(s"try was a success, marking task id ${task.getId} as DONE")
      javaListingClient.updateAdaptiveTaskStatus(source.getId, task.getId, AdaptiveFlowTask.TaskStatus.DONE, Optional.of("Success"))
    } else {
      logger.info(s"try was a failure, marking task id ${task.getId} as STOPPED")
      javaListingClient.updateAdaptiveTaskStatus(source.getId, task.getId, AdaptiveFlowTask.TaskStatus.STOPPED, Optional.of("Failure: " + StringUtils.substring(tb.failed.get.getMessage, 0, 250)))
    }
  }.failed.foreach(e => logger.error("Failed to update status for task", e))

  if (adaptiveEnabled) {
    logger.info("detected adaptive enabled, using adaptive tasks loop")
    adaptiveFlowTasksStreamSource
      .listTasks(source.getId, () => ()).runForeach(
      task => pipelineFuture(Option(task)).onComplete(tb => markAsSmth(tb, task))
    )
  } else {
    pipelineFuture(Option.empty)
      .transformWith {
        case Success(_) =>
          logger.info(s"Pipeline terminated normally")
          startWaitUntilKilledFuture()
        case Failure(ex) =>
          logger.error(s"Pipeline terminated with error", ex)
          startWaitUntilKilledFuture()
      }
  }

}
