package com.nexla.inmemory_connector.context.builder

import cats.implicits.toTraverseOps
import com.nexla.admin.client.flownode.{AdminApiFlow, NexlaFlow}
import com.nexla.admin.client.{AdminApiClient, DataSet, DataSink, DataSource}
import com.nexla.common.ResourceType
import com.nexla.common.notify.monitoring.{NexlaMonitoringLogEvent, NexlaMonitoringLogSeverity, NexlaMonitoringLogType}
import com.nexla.common.notify.transport.{ControlMessageProducer, NexlaMessageProducer}
import com.nexla.connector.config.BaseConnectorConfig.FAST_MODE
import com.nexla.connector.config.FlowType
import com.nexla.inmemory_connector.AppProps
import com.nexla.inmemory_connector.compat.PipelineConf
import com.nexla.inmemory_connector.context.Context.PipelineContext
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfigBuilder
import com.nexla.inmemory_connector_common.utils.ConfigurationOverrider
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.listing.client.AdaptiveFlowTask
import com.nexla.sc.util.StrictNexlaLogging

import java.util.Optional
import scala.compat.java8.OptionConverters.RichOptionalGeneric
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters.collectionAsScalaIterableConverter

class PipelineContextBuilder(flowId: Int, runId: Long, adminApi: AdminApiClient, props: AppProps, maybeAdaptiveFlowTask: Option[AdaptiveFlowTask], controlMessageProducer: ControlMessageProducer)(implicit ec: ExecutionContext) extends StrictNexlaLogging {
  private val customRuntimeConfigBuilder = new CustomRuntimeConfigBuilder(adminApi, props)
  private val configOverrider = new ConfigurationOverrider(adminApi, debugLogging = true, controlMessageProducer, runId)

  private def inspectFlow(flow: NexlaFlow): Unit = {
    if (flow.getFlows.size() != 1) {
      logger.warn(s"Unexpected nr of flows: ${flow.getFlows.size()}")
    }
    val flowType = flow.getFlows.get(0).getFlowType
    if (flowType != FlowType.IN_MEMORY && flowType != FlowType.CUSTOM) {
      logger.warn(s"Invalid flow type: $flowType")
    }
    if (flow.getDataSources.size() != 1) {
      logger.warn(s"Unexpected number of sources: ${flow.getDataSources.size()}")
    }
    if (flow.getDataSinks.size() != 1) {
      logger.warn(s"Unexpected number of sinks: ${flow.getDataSinks.size()}")
    }
  }

  private def validateFlowAndFetchSourceSink(): Future[(AdminApiFlow, DataSource, List[DataSet], DataSink)] = {

    val getFlow = () => Future(adminApi.getNexlaFlow(flowId).asScala.getOrElse(throw new Exception(s"Flow with id $flowId was not found")))
    val getDataSource = (sourceId: Int) => Future(adminApi.getDataSource(sourceId).asScala.getOrElse(throw new Exception(s"Data source with id: $sourceId was not found")))
    val getDataSink = (sinkId: Int) => Future(adminApi.getDataSink(sinkId).asScala.getOrElse(throw new Exception(s"Data sink with id: $sinkId was not found")))
    val getDataSet = (dataSetId: Int) => Future(adminApi.getDataSet(dataSetId).asScala.getOrElse(throw new Exception(s"DataSet is null")))

    for {
      flow <- getFlow()
      _ = inspectFlow(flow)
      singleFlow = flow.getFlows.asScala.filter(_.getId == flowId).head
      sourceId = flow.getDataSources.asScala.filter(_.getOriginNodeId == flowId).map(_.getId).head
      sinkId = flow.getDataSinks.asScala.filter(_.getOriginNodeId == flowId).map(_.getId).head
      dataSetIds = flow.getDataSets.asScala.filter(_.getOriginNodeId == flowId).map(_.getId).toList
      dataSource <- getDataSource(sourceId)
      dataSink <- getDataSink(sinkId)
      dataSets <- dataSetIds.traverse(getDataSet)
    } yield (singleFlow, dataSource, dataSets, dataSink)
  }

  private def enrichConfigsWithFastMode(dataSource: DataSource, dataSink: DataSink): Unit = {
    dataSource.getSourceConfig.put(FAST_MODE, "true")
    dataSink.getSinkConfig.put(FAST_MODE, "true")
  }

  private def readAdaptiveFlowProp(dataSource: DataSource): Boolean =
    Optional.ofNullable(dataSource.getSourceConfig.get("adaptive.flow")).asScala.map(_.toString).map(_.toBoolean).getOrElse(false)

  private def enrichConfigsWithAdaptiveFlowTasksConfig(dataSource: DataSource, dataSets: List[DataSet], dataSink: DataSink): Unit = {
    Optional.ofNullable(dataSource.getSourceConfig.get("adaptive.flow")).asScala.map(_.toString).map(_.toBoolean).foreach {
      adaptiveFlowTaskEnabled =>
        if (adaptiveFlowTaskEnabled && maybeAdaptiveFlowTask.isDefined) {
          maybeAdaptiveFlowTask.map {
            actualTask => {
              val monitoringLogEvent = NexlaMonitoringLogEvent.of(
                dataSource.getOrgId, runId, dataSource.getId, ResourceType.SOURCE, s"Using adaptive flow task for the source, parameters: ${actualTask.getParameters}", NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.INFO, System.currentTimeMillis()
              )
              controlMessageProducer.publishMonitoringLog(monitoringLogEvent)
              configOverrider.overrideConfigs(List(dataSource), dataSets, List(dataSink), actualTask)
              configOverrider.overrideMetricName(List(dataSource), List(dataSink), actualTask)
            }
          }
        } else {
          logger.info("skipping additional config enrichment because adaptive flow task is not enabled or it's empty")
        }
    }
  }

  def createPipelineContext(): Future[PipelineContext] =
    validateFlowAndFetchSourceSink().flatMap { case (flow, dataSource, dataSets, dataSink) =>
        enrichConfigsWithFastMode(dataSource, dataSink)
        val isAdaptiveFlow = readAdaptiveFlowProp(dataSource)
        if (isAdaptiveFlow) {
          enrichConfigsWithAdaptiveFlowTasksConfig(dataSource, dataSets, dataSink)
        }
        customRuntimeConfigBuilder.buildCustomRuntimeConfig(flow.flowType, dataSource, dataSink, runId).map { runtimeConfig =>
          val pipelineConf = PipelineConf.getPipelineConf(props, dataSource, dataSets)
          PipelineContext(runId, FlowType.IN_MEMORY, flow, dataSource, dataSets, dataSink, pipelineConf, runtimeConfig, maybeAdaptiveFlowTask)
        }
      }
      .tap(ctx => logger.info(s"Successfully created context for pipeline: $ctx"))
      .tapError(err => logger.error("Failed to create a context for pipeline", err))
}
