package com.nexla.inmemory_connector.pipeline.metrics

import com.nexla.common.metrics.NexlaRawMetric
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent
import com.nexla.inmemory_connector.pipeline.PipelineUtils.putNameForAdaptiveFlowTask
import com.nexla.inmemory_connector_common.utils.TappingOps.tryOps
import com.nexla.listing.client.AdaptiveFlowTask
import com.typesafe.scalalogging.StrictLogging

import java.nio.file.Paths
import scala.util.Try

class FlatMapperControlMessageProducer(messageProducer: InMemoryControlMessageProducer, maybeAdaptiveFlowTask: Option[AdaptiveFlowTask]) extends InMemoryControlMessageProducer(messageProducer) with StrictLogging {
  private def truncatePath(fullPath: String): String = {
    Try(Paths.get(fullPath).getFileName.toString).tapError(logger.error(s"Failed to truncate S3 path: $fullPath", _)).getOrElse(fullPath)
  }

  override def publishMonitoringLog(monitoringLogEvent: NexlaMonitoringLogEvent): Unit = {
    val newLog = s"[flat-mapper] ${monitoringLogEvent.getLog}"
    monitoringLogEvent.setLog(newLog)
    super.publishMonitoringLog(monitoringLogEvent)
  }

  override def publishMetrics(nexlaRawMetric: NexlaRawMetric): Unit = {
    if (maybeAdaptiveFlowTask.isEmpty) {
      nexlaRawMetric.getTags.computeIfPresent(NexlaRawMetric.NAME, (_, fullPath) => truncatePath(fullPath))
    } else {
      putNameForAdaptiveFlowTask(nexlaRawMetric, maybeAdaptiveFlowTask.get)
    }
    super.publishMetrics(nexlaRawMetric)
  }
}