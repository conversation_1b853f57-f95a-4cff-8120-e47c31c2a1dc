package com.nexla.inmemory_connector.pipeline.flow.newimpl

import akka.stream.Materializer
import akka.stream.scaladsl.{Flow, Keep}
import akka.{Done, NotUsed}
import com.nexla.common.notify.transport.ControlMessageProducer
import com.nexla.common.{EncryptionUtils, NexlaMessage}
import com.nexla.connector.MessageMapper
import com.nexla.connector.config.SinkConnectorConfig
import com.nexla.inmemory_connector.compat.{BasicMessage, RecordWithOffset, ReplicationSourceFile, ReplicationSourceLocal}
import com.nexla.inmemory_connector.pipeline.sink.SinkFlow.SinkDone
import com.nexla.inmemory_connector.state.ResourceState
import com.nexla.sc.client.OffsetSaver
import com.nexla.sc.client.listing.{CoordinationAppClient, ListingAppClient}
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import connect.jdbc.sink.dialect.copy.filewriter.DataFileWriter
import one.util.streamex.StreamEx
import org.apache.commons.io.FileUtils

import java.io.File
import java.nio.file.{Files, Paths}
import java.util
import java.util.UUID
import scala.collection.JavaConverters._
import scala.compat.java8.OptionConverters._
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}

trait DirectSink
  extends WithLogging
    with StrictNexlaLogging {

  implicit val ec: ExecutionContext
  implicit val mat: Materializer
  implicit val listingClient: ListingAppClient
  implicit val coordinationClient: CoordinationAppClient
  implicit val controlMessageProducer: ControlMessageProducer

  val config: SinkConnectorConfig
  val offsetSaver: OffsetSaver

  def dataFileWriter(): DataFileWriter

  lazy val messageMapper: MessageMapper = {
    val encryptionUtils = if (config.trackerEncryptionEnabled) {
      Some(new EncryptionUtils(config.trackerEncryptionKey))
    } else {
      None
    }

    new MessageMapper(config.mappingConfig, config, false, encryptionUtils.asJava)
  }

  def filesSinkFlow(resourceState: ResourceState): Flow[ReplicationSourceLocal, ReplicationSourceLocal, Future[Unit]]

  def recordsSinkFlow(resourceState: ResourceState): Flow[BasicMessage, ReplicationSourceLocal, Future[SinkDone]] = {

    val tempDir = Files.createTempDirectory(s"direct-sink").toFile

    val lowerCasedRecords = Flow[BasicMessage]
      .collect { case r: RecordWithOffset => r }
      .map{ rec =>
        val original =  rec.message
        val m = messageMapper.extractMessage(original).getMapped
        val message = new util.LinkedHashMap[String, AnyRef]()
        m.getRawMessage.asScala.foreach { case (key, value) =>
          message.put(key.replaceAll("[^a-zA-Z0-9_]", "_"), value)
        }
        val newMessage = new NexlaMessage(message, m.getNexlaMetaData)
        rec.copy(message = newMessage)
      }

    val filesSource = messagesToFiles(dataFileWriter(), lowerCasedRecords, tempDir)

    val sinkFilesFlow = filesSinkFlow(resourceState)
      .watchTermination()((_, eventualDone: Future[Done]) => {
        eventualDone.andThen { case _ =>
          logger.info(s"removing temporary directory [$tempDir]")
          FileUtils.deleteDirectory(tempDir)
        }.map(_ => SinkDone())
      })

    filesSource.viaMat(sinkFilesFlow)(Keep.right)

  }

  private def messagesToFiles(dataFileWriter: DataFileWriter, tx: Flow[BasicMessage, RecordWithOffset, NotUsed], tempDir: File) = {
    tx
      .groupedWithin(Int.MaxValue, 60.seconds)
      .mapAsync(1) { recs =>
        Future {
          val path = Paths.get(tempDir.getAbsolutePath, UUID.randomUUID().toString).toFile
          dataFileWriter.writeDataFile(path, StreamEx.of(recs.map(_.message).asJava), (_, _) => ())

          val file = ReplicationSourceFile(None, path.getAbsolutePath, path.getAbsolutePath, Some(dataFileWriter.getFileFormat), Option(path), Some(recs.length))
          ReplicationSourceLocal(Seq(file), tempDir)
        }
      }
  }

}
