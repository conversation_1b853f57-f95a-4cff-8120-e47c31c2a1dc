package com.nexla.inmemory_connector.pipeline.sink

import akka.actor.{ActorSystem, Scheduler}
import com.nexla.admin.client.{AdminApiClient, DataSink}
import com.nexla.connect.common.PostponedFlush
import com.nexla.connect.common.postponedFlush.InMemoryProgressTracker
import com.nexla.connector.NexlaMessageContext
import com.nexla.connector.config.file.FileSinkConnectorConfig
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig
import com.nexla.connector.config.redis.RedisConnectorConfig
import com.nexla.connector.config.rest.RestSinkConnectorConfig
import com.nexla.connector.config.soap.SoapSinkConnectorConfig
import com.nexla.connector.config.vectordb.VectorSinkConnectorConfig
import com.nexla.connector.file.sink.names.FileNamingGenerator
import com.nexla.connector.push.sink.FileSinkTask
import com.nexla.connector.redis.sink.RedisSinkTask
import com.nexla.connector.rest.sink.RestSinkTask
import com.nexla.connector.soap.sink.SoapSinkTask
import com.nexla.connector.sql.sink.JdbcSinkTask
import com.nexla.connector.vectordb.sink.VectorDbSinkTask
import com.nexla.inmemory_connector.compat.BasicMessage
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.RayRuntimeConfig
import com.nexla.inmemory_connector.monitoring.MetricsOffsetReporter
import com.nexla.inmemory_connector.monitoring.telemetry.InMemoryTelemetry
import com.nexla.inmemory_connector.pipeline.NexlaConfigPreparer
import com.nexla.inmemory_connector.pipeline.flow.newimpl.GlobalFileNaming
import com.nexla.inmemory_connector.pipeline.metrics.{InMemoryControlMessageProducer, InMemoryDataMessageProducer}
import com.nexla.inmemory_connector.pipeline.sink.flusher.{SinkFlusher, SinkFlusherFactory}
import com.nexla.inmemory_connector.pipeline.sink.flusher.sink_specials.JdbcSinkSpecials
import com.nexla.inmemory_connector.pipeline.sink.flusher.sink_specials.JdbcSinkSpecials.jdbcSinkTaskJdbcWithInsertMode
import com.nexla.inmemory_connector.pipeline.sink.shutdown_monitor.AdminApiBasedSinkShutdownMonitor
import com.nexla.inmemory_connector.pipeline.sink.task.{BasicSinkTask, FileUploaderSinkTask, KafkaConnectSinkTask}
import com.nexla.inmemory_connector.state.ResourceState
import com.nexla.inmemory_connector.{AppProps, LogPrefix}
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.listing.client.{AdaptiveFlowTask, FileVaultClient, ListingClient => JavaListingClient}
import com.nexla.sc.util.StrictNexlaLogging

import java.{lang, util}
import java.util.concurrent.TimeUnit
import java.util.function.Function
import scala.concurrent.{ExecutionContext, Future}
import scala.concurrent.duration.FiniteDuration

class SinkFlowFactory(props: AppProps,
                      runId: Long,
                      adminApiClient: AdminApiClient,
                      javaListingClient: JavaListingClient,
                      fileVaultClient: FileVaultClient,
                      metricsOffsetReporter: MetricsOffsetReporter,
                      progressTracker: InMemoryProgressTracker,
                      controlMessageProducer: InMemoryControlMessageProducer,
                      dataMessageProducer: InMemoryDataMessageProducer,
                      probeFactory: ProbeFactory,
                      telemetry: InMemoryTelemetry,
                      intermediateFlushDelay: FiniteDuration,
                      maxIntermediateFlushAwaitInterval: FiniteDuration,
                      maybeAdaptiveFlowTask: Option[AdaptiveFlowTask] = Option.empty
                     )(implicit sys: ActorSystem, ec: ExecutionContext, sch: Scheduler) extends StrictNexlaLogging {
  private val configPreparer = new NexlaConfigPreparer(props, adminApiClient, runId)

  private def create[T <: BasicSinkTask](dataSink: DataSink, task: T, resourceState: ResourceState, sinkFlusherFactory: SinkFlusherFactory[T]): SinkFlow = {
    val loggingPrefix = LogPrefix.forSink(dataSink)
    val shutdownMonitor = new AdminApiBasedSinkShutdownMonitor(adminApiClient, dataSink.getId)
    val inMemorySinkTask = new InMemorySinkTask[T](loggingPrefix, task, sinkFlusherFactory)
    new SinkFlow(loggingPrefix, props, inMemorySinkTask, resourceState, metricsOffsetReporter, shutdownMonitor, telemetry, intermediateFlushDelay, maxIntermediateFlushAwaitInterval)
  }

  def forFile(dataSink: DataSink, state: ResourceState): SinkFlow = {
    val task = new FileSinkTask() {

      override protected def createFileNamingGenerator(config: FileSinkConnectorConfig, localDir: String): FileNamingGenerator = {
        new FileNamingGenerator(config, localDir, new Function[NexlaMessageContext, String]() {
          override def apply(messageContext: NexlaMessageContext): String = GlobalFileNaming.getNextSuffix()
        })
      }

      override protected def postponedFlush(): PostponedFlush = {
        new PostponedFlush(config, adminApiClient, progressTracker, logger) {
          def dataMatureMs(delayMs: Long): Boolean = fileSinkWriter.writersReady(TimeUnit.MILLISECONDS.toMinutes(delayMs))
          override protected def noDataMatureMs(delayMs: Long): Boolean = !dataMatureMs(delayMs)
          override protected def hasAccumulatedRecords: Boolean = true
        }
      }
    }
    val configDef = FileSinkConnectorConfig.configDef()
    val flusherFactory = SinkFlusherFactory.forFile[KafkaConnectSinkTask[FileSinkTask]]
    val basicSinkTask = new KafkaConnectSinkTask(configPreparer, runId)(task, dataSink, configDef)
    create(dataSink, basicSinkTask, state, flusherFactory)
  }

  def forBYOFile(dataSink: DataSink, raySourceConfig: RayRuntimeConfig, state: ResourceState): SinkFlow = {
    if (raySourceConfig.byoOptimized) {
      logger.info("Running optimized BYO flow")
      val basicSinkTask = new BasicSinkTask {
        def start(): Future[Unit] = Future.unit
        def putData(messages: Seq[BasicMessage]): Future[Unit] = Future.unit
        def flushData(): Future[SinkFlusher.FlushingResults] = Future.successful(SinkFlusher.FlushingResults(List.empty, List.empty))
        def forceFlushData(): Future[SinkFlusher.FlushingResults] = Future.successful(SinkFlusher.FlushingResults(List.empty, List.empty))
        def stop(): Future[Unit] = Future.unit
      }
      val flusherFactory = SinkFlusherFactory.forFile[BasicSinkTask]
      create(dataSink, basicSinkTask, state, flusherFactory)
    } else {
      logger.info("Running non-optimized BYO flow")
      val configDef = FileSinkConnectorConfig.configDef()
      val basicSinkTask = new FileUploaderSinkTask(dataSink, runId, raySourceConfig, adminApiClient, configPreparer, javaListingClient, controlMessageProducer, props, probeFactory, configDef, maybeAdaptiveFlowTask, fileVaultClient)
      val flusherFactory = SinkFlusherFactory.forFile[FileUploaderSinkTask]
      create(dataSink, basicSinkTask, state, flusherFactory)
    }
  }

  def forJdbc(dataSink: DataSink, state: ResourceState): SinkFlow = {
    val task = new JdbcSinkTask()
    task.overridePipelineProgressTracker(progressTracker)
    val configDef = JdbcSinkConnectorConfig.configDef()
    implicit val jdbcSpecials: JdbcSinkSpecials[KafkaConnectSinkTask[JdbcSinkTask]] = jdbcSinkTaskJdbcWithInsertMode(progressTracker)
    val flusherFactory = SinkFlusherFactory.forJdbc
    val basicSinkTask = new KafkaConnectSinkTask(configPreparer, runId)(task, dataSink, configDef)
    create(dataSink, basicSinkTask, state, flusherFactory)
  }

  def forRest(dataSink: DataSink, state: ResourceState): SinkFlow = {
    val task = new RestSinkTask()
    val configDef = RestSinkConnectorConfig.configDef()
    val flusherFactory = SinkFlusherFactory.forRest[KafkaConnectSinkTask[RestSinkTask]]
    val basicSinkTask = new KafkaConnectSinkTask(configPreparer, runId)(task, dataSink, configDef)
    create(dataSink, basicSinkTask, state, flusherFactory)
  }

  def forRedis(dataSink: DataSink, state: ResourceState): SinkFlow = {
    val task = new RedisSinkTask()
    val configDef = RedisConnectorConfig.configDef()
    val flusherFactory = SinkFlusherFactory.forRedis[KafkaConnectSinkTask[RedisSinkTask]]
    val basicSinkTask = new KafkaConnectSinkTask(configPreparer, runId)(task, dataSink, configDef)
    create(dataSink, basicSinkTask, state, flusherFactory)
  }

  def forVectorDb(dataSink: DataSink, state: ResourceState): SinkFlow = {
    val task = new VectorDbSinkTask()
    val configDef = VectorSinkConnectorConfig.configDef()
    val flusherFactory = SinkFlusherFactory.forVectorDb[KafkaConnectSinkTask[VectorDbSinkTask]]
    val basicSinkTask = new KafkaConnectSinkTask(configPreparer, runId)(task, dataSink, configDef)
    create(dataSink, basicSinkTask, state, flusherFactory)
  }

  def forSoap(dataSink: DataSink, state: ResourceState): SinkFlow = {
    val task = new SoapSinkTask()
    val configDef = SoapSinkConnectorConfig.configDef()
    val flusherFactory = SinkFlusherFactory.forSoap[KafkaConnectSinkTask[SoapSinkTask]]
    val basicSinkTask = new KafkaConnectSinkTask(configPreparer, runId)(task, dataSink, configDef)
    create(dataSink, basicSinkTask, state, flusherFactory)
  }
}
