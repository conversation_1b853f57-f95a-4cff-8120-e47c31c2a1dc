package com.nexla.inmemory_connector.pipeline.source

import akka.actor.ActorSystem
import com.nexla.admin.client.{AdminApiClient, DataSource}
import com.nexla.common.NexlaKafkaConfig
import com.nexla.connect.common.postponedFlush.InMemoryProgressTracker
import com.nexla.connector.config.BaseConnectorConfig
import com.nexla.connector.config.file.FileSourceConnectorConfig
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig
import com.nexla.connector.config.rest.RestSourceConnectorConfig
import com.nexla.connector.config.soap.SoapSourceConnectorConfig
import com.nexla.connector.config.vectordb.VectorSourceConnectorConfig
import com.nexla.connector.file.source.{FileSourceConnector, FileSourceNotificationSender, FileSourceOffsetWriter, FileSourceTask, NoopOffsetWriter}
import com.nexla.connector.rest.source.{RestSourceConnector, RestSourceTask}
import com.nexla.connector.soap.source.{SoapSourceConnector, SoapSourceTask}
import com.nexla.connector.sql.poll.{JdbcSourceConnector, JdbcSourceTask}
import com.nexla.connector.vectordb.source.{VectorDbSourceConnector, VectorDbSourceTask}
import com.nexla.inmemory_connector.compat.BYOOptimizedFlowMessage
import com.nexla.inmemory_connector.context.Context.PipelineContext
import com.nexla.inmemory_connector.monitoring.telemetry.InMemoryTelemetry
import com.nexla.inmemory_connector.pipeline.NexlaConfigPreparer
import com.nexla.inmemory_connector.pipeline.flow.newimpl.ImcListingClient
import com.nexla.inmemory_connector.pipeline.source.parsers.{DirectoryTransformationMessageParser, FileOffsetSourceRecordParser, MapOffsetSourceRecordParser}
import com.nexla.inmemory_connector.pipeline.metrics.{InMemoryControlMessageProducer, InMemoryDataMessageProducer}
import com.nexla.inmemory_connector.pipeline.source.task.{BasicSourceTask, FlatMapperForSource, KafkaConnectSourceTask, SingleMessageSource}
import com.nexla.inmemory_connector.pipeline.storage.InMemoryOffsetStorageReader
import com.nexla.inmemory_connector.state.FilesState
import com.nexla.inmemory_connector.{AppProps, LogPrefix}
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.listing.client.{FileVaultClient, ListingClient => JavaListingClient}
import com.nexla.sc.client.listing.CoordinationAppClient
import com.nexla.sc.util.StrictNexlaLogging
import org.springframework.web.client.RestTemplate

import scala.concurrent.ExecutionContext

class SourceFlowFactory(props: AppProps, ctx: PipelineContext, filesState: FilesState, adminApiClient: AdminApiClient,
                        coordinationAppClient: CoordinationAppClient, controlProducer: InMemoryControlMessageProducer,
                        dataProducer: InMemoryDataMessageProducer, progressTracker: InMemoryProgressTracker,
                        telemetry: InMemoryTelemetry, javaListingClient: ImcListingClient)(implicit system: ActorSystem, ec: ExecutionContext) extends StrictNexlaLogging {
  private val configPreparer = new NexlaConfigPreparer(props, adminApiClient, ctx.runId)

  private def create[T <: BasicSourceTask](dataSource: DataSource, task: T): SourceFlow = {
    val loggingPrefix = LogPrefix.forSource(dataSource)
    val inmemoryTask = new InMemorySourceTask(loggingPrefix, task)
    new SourceFlow(loggingPrefix, inmemoryTask, filesState, progressTracker, telemetry)
  }

  def forFile(dataSource: DataSource): SourceFlow = {
    logger.info("Using common file source connector")
    val offsetStorageReader = InMemoryOffsetStorageReader.nopOffsetStorageReader
    val task = createFileSourceTask()
    val connector = new FileSourceConnector()
    val configDef = FileSourceConnectorConfig.configDef()
    val parser = new FileOffsetSourceRecordParser()
    val basicSourceTask = new KafkaConnectSourceTask[FileSourceConnectorConfig, FileSourceTask](configPreparer, offsetStorageReader, task, connector, dataSource, configDef, parser)
    create(dataSource, basicSourceTask)
  }

  def sourceFlatMapper(fileVaultClient: FileVaultClient, probeFactory: ProbeFactory): FlatMapperForSource = {
    new FlatMapperForSource(ctx, configPreparer, adminApiClient, fileVaultClient, controlProducer, dataProducer, props, probeFactory, javaListingClient, telemetry)
  }

  def forRayFile(dataSource: DataSource): SourceFlow = {
    val offsetStorageReader = InMemoryOffsetStorageReader.nopOffsetStorageReader
    val task = createFileSourceTask()
    val connector = new FileSourceConnector()
    val configDef = FileSourceConnectorConfig.configDef()
    val parser = new DirectoryTransformationMessageParser(dataSource.getId, filesState)
    val basicSourceTask = new KafkaConnectSourceTask[FileSourceConnectorConfig, FileSourceTask](configPreparer, offsetStorageReader, task, connector, dataSource, configDef, parser)
    create(dataSource, basicSourceTask)
  }

  def forBYOFile(dataSource: DataSource, datasetId: Int, byoOptimizedFlow: Boolean): SourceFlow = {
    if (byoOptimizedFlow) {
      logger.info("Running optimized BYO flow")
      val basicSourceTask = new SingleMessageSource(BYOOptimizedFlowMessage(Some(datasetId)))
      create(dataSource, basicSourceTask)
    } else {
      logger.info("Running non-optimized BYO flow")
      forRayFile(dataSource)
    }
  }

  def forJdbc(dataSource: DataSource): SourceFlow = {
    val offsetStorageReader = InMemoryOffsetStorageReader.offsetStorageReader(dataSource.getId, 0, coordinationAppClient)
    val task = new JdbcSourceTask() {
      override protected def createControlMessageProducer(sslContext: NexlaKafkaConfig) = controlProducer
      override protected def createDataMessageProducer(sslContext: NexlaKafkaConfig) = dataProducer
    }
    val connector = new JdbcSourceConnector()
    val configDef = JdbcSourceConnectorConfig.configDef()
    val parser = new MapOffsetSourceRecordParser()
    val basicSourceTask = new KafkaConnectSourceTask[JdbcSourceConnectorConfig, JdbcSourceTask](configPreparer, offsetStorageReader, task, connector, dataSource, configDef, parser)
    create(dataSource, basicSourceTask)
  }

  def forVectorDb(dataSource: DataSource): SourceFlow = {
    val offsetStorageReader = InMemoryOffsetStorageReader.offsetStorageReader(dataSource.getId, 0, coordinationAppClient)
    val task = new VectorDbSourceTask() {
      override protected def createControlMessageProducer(sslContext: NexlaKafkaConfig) = controlProducer
      override protected def createDataMessageProducer(sslContext: NexlaKafkaConfig) = dataProducer
    }
    val connector = new VectorDbSourceConnector()
    val configDef = VectorSourceConnectorConfig.configDef()
    val parser = new MapOffsetSourceRecordParser()
    val basicSourceTask = new KafkaConnectSourceTask[VectorSourceConnectorConfig, VectorDbSourceTask](configPreparer, offsetStorageReader, task, connector, dataSource, configDef, parser)
    create(dataSource, basicSourceTask)
  }

  def forRest(dataSource: DataSource): SourceFlow = {
    val offsetStorageReader = InMemoryOffsetStorageReader.offsetStorageReader(dataSource.getId, 0, coordinationAppClient)
    val task = new RestSourceTask() {
      override protected def createControlMessageProducer(sslContext: NexlaKafkaConfig) = controlProducer
      override protected def createDataMessageProducer(sslContext: NexlaKafkaConfig) = dataProducer
    }
    val connector = new RestSourceConnector()
    val configDef = RestSourceConnectorConfig.configDef()
    val parser = new MapOffsetSourceRecordParser()
    val basicSourceTask = new KafkaConnectSourceTask[RestSourceConnectorConfig, RestSourceTask](configPreparer, offsetStorageReader, task, connector, dataSource, configDef, parser)
    create(dataSource, basicSourceTask)
  }

  def forSoap(dataSource: DataSource): SourceFlow = {
    val offsetStorageReader = InMemoryOffsetStorageReader.offsetStorageReader(dataSource.getId, 0, coordinationAppClient)
    val task = new SoapSourceTask() {
      override protected def createControlMessageProducer(sslContext: NexlaKafkaConfig) = controlProducer
      override protected def createDataMessageProducer(sslContext: NexlaKafkaConfig) = dataProducer
    }
    val connector = new SoapSourceConnector()
    val configDef = SoapSourceConnectorConfig.configDef()
    val parser = new MapOffsetSourceRecordParser()
    val basicSourceTask = new KafkaConnectSourceTask[SoapSourceConnectorConfig, SoapSourceTask](configPreparer, offsetStorageReader, task, connector, dataSource, configDef, parser)
    create(dataSource, basicSourceTask)
  }

  private def createFileSourceTask() = new FileSourceTask() {
    override protected def createControlMessageProducer(sslContext: NexlaKafkaConfig): InMemoryControlMessageProducer = controlProducer
    override protected def createDataMessageProducer(sslContext: NexlaKafkaConfig): InMemoryDataMessageProducer = dataProducer

    override protected def createListingClient(config: BaseConnectorConfig, restTemplate: RestTemplate): JavaListingClient = javaListingClient

    override protected def createOffsetWriter(kafkaMessageSender: FileSourceNotificationSender): FileSourceOffsetWriter = new NoopOffsetWriter
  }

}
