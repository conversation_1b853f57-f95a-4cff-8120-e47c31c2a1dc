package com.nexla.inmemory_connector.pipeline.sink.task

import akka.NotUsed
import akka.actor.Scheduler
import akka.stream.Materializer
import akka.stream.scaladsl.{Sink, Source}
import cats.implicits.toTraverseOps
import software.amazon.awssdk.services.s3.model.{GetObjectRequest, S3Object}
import com.nexla.admin.client.{AdminApiClient, DataSink}
import com.nexla.common.ResourceType
import com.nexla.common.datetime.DateTimeUtils
import com.nexla.common.time.VarUtils
import com.nexla.connector.config.NexlaConfigDef
import com.nexla.connector.config.file.{AWSAuthConfig, FileSinkConnectorConfig}
import com.nexla.connector.config.rest.BaseAuthConfig
import com.nexla.file.service.FileConnectorService
import com.nexla.inmemory_connector.compat.{BasicMessage, DirectoryTransformationMessage, SinkMetric}
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.RayRuntimeConfig
import com.nexla.inmemory_connector.monitoring.FileConnectorFlowLogs
import com.nexla.inmemory_connector.pipeline.NexlaConfigPreparer
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryControlMessageProducer
import com.nexla.inmemory_connector.pipeline.sink.flusher.SinkFlusher.FlushingResults
import com.nexla.inmemory_connector.pipeline.sink.offsets.OffsetAggregator
import com.nexla.inmemory_connector.pipeline.sink.task.FileUploaderSinkTask.{FileStats, FileUploaderSinkConfig, RetryConfig}
import com.nexla.inmemory_connector.{AppProps, LogPrefix}
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.inmemory_connector_common.utils.{Retry, S3PathUtil}
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.listing.client.{AdaptiveFlowTask, FileVaultClient, ListingClient}
import com.nexla.probe.s3.S3ConnectorService
import com.nexla.sc.util.{Async, StrictNexlaLogging}
import one.util.streamex.StreamEx
import org.joda.time.DateTimeZone
import software.amazon.awssdk.services.s3.S3Client

import java.io.File
import java.nio.file.Paths
import java.util
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.{Optional, UUID}
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters.{asScalaIteratorConverter, mapAsJavaMapConverter, mapAsScalaMapConverter, seqAsJavaListConverter}
import scala.util.{Success, Try}


class FileUploaderSinkTask(dataSink: DataSink,
                           runId: Long,
                           rayRuntimeConfig: RayRuntimeConfig,
                           adminApiClient: AdminApiClient,
                           configPreparer: NexlaConfigPreparer,
                           javaListingClient: ListingClient,
                           pipelineMessageProducer: InMemoryControlMessageProducer,
                           props: AppProps,
                           probeFactory: ProbeFactory,
                           configDef: NexlaConfigDef,
                           maybeAdaptiveFlowTask: Option[AdaptiveFlowTask] = Option.empty,
                           fileVault: FileVaultClient
                          )
                          (implicit ec: ExecutionContext, mat: Materializer, sch: Scheduler) extends BasicSinkTask with StrictNexlaLogging {
  override def logPrefix: Option[String] = Some(LogPrefix.forSink(dataSink))

  private val taskEc = Async.ioExecutorContext

  private var fileSinkContext: FileSinkContext = _
  private val flowLogs = new FileConnectorFlowLogs(pipelineMessageProducer)
  private val offsetAggregator = new OffsetAggregator()
  private val s3ConnectorService = new S3ConnectorService()
  private val fileUploaderSinkConfig: FileUploaderSinkConfig = readFileUploaderSinkConfig()

  logger.info(s"Using file uploader config: $fileUploaderSinkConfig")

  private def readFileUploaderSinkConfig(): FileUploaderSinkConfig = {
    val replicationParallelismEnabled = dataSink.getSinkConfig.asScala
      .get("imc.byo.replication.parallelism.enabled")
      .flatMap(value => Try(value.toString.toBoolean).toOption)
      .getOrElse(false)
    val retries = dataSink.getSinkConfig.asScala
      .get("imc.byo.sink.write.retries")
      .flatMap(value => Try(value.toString.toInt).toOption)
      .getOrElse(3)
    val delay = dataSink.getSinkConfig.asScala
      .get("imc.byo.sink.write.retry.delay.sec")
      .flatMap(value => Try(value.toString.toInt).toOption)
      .getOrElse(1)

    FileUploaderSinkConfig(replicationParallelismEnabled = replicationParallelismEnabled, retryConfig = RetryConfig(retries, delay))
  }

  private def startTask(taskConfig: util.Map[String, String]): Future[Unit] = Future {
    val config = new FileSinkConnectorConfig(taskConfig)
    fileSinkContext = new FileSinkContext(config, dataSink)
    flowLogs.publishSinkStartingFlowLogs(dataSink, runId)
  }(taskEc)

  private def listFiles(directoryPath: String): Source[String, NotUsed] = {
    val bucketPrefix = AWSAuthConfig.toBucketPrefix(directoryPath, false)
    val srcS3Client: S3Client = S3ConnectorService.createS3ClientFromCreds(rayRuntimeConfig.sourceS3Credentials.authConfig, null)
    val stream: StreamEx[S3Object] = s3ConnectorService.listS3Objects(srcS3Client, bucketPrefix.bucket, bucketPrefix.prefix)
    Source.fromIterator(() => stream.iterator().asScala).map(_.key()).map(p => s"${bucketPrefix.bucket}/$p")
  }

  private def downloadFile(filePath: String): Future[File] = Future {
    val srcS3Client: S3Client = S3ConnectorService.createS3ClientFromCreds(rayRuntimeConfig.sourceS3Credentials.authConfig, null) // todo: use s3ConnectorService instead
    val bucketPrefix = AWSAuthConfig.toBucketPrefix(filePath, false)
    val tempFile = File.createTempFile("file-uploader", null, fileSinkContext.localDir)
    val getObjectRequest = GetObjectRequest.builder()
      .bucket(bucketPrefix.bucket)
      .key(bucketPrefix.prefix)
      .build()

    srcS3Client.getObject(getObjectRequest, tempFile.toPath)
    tempFile
  }(taskEc)

  private def uploadFile(relativeDstPath: String, localFile: File): Future[FileStats] = Future {
    val normDirPath = AWSAuthConfig.getDirectoryPath(fileSinkContext.config.sinkType, fileSinkContext.config.getPath)
    val nowInUserTimeZone = DateTimeUtils.nowUTC().withZone(DateTimeZone.forID(fileSinkContext.config.timezone))
    val folderStructure = VarUtils.getFolderStructure(nowInUserTimeZone, Map.empty[String, String].asJava, fileSinkContext.config.outputDirNamePattern.map(_.getTemplate), fileSinkContext.config.datetimePadding)
    val resultPath = Paths.get(normDirPath, folderStructure, relativeDstPath).toString
    val destinationFilePath = AWSAuthConfig.normalizeFilePath(fileSinkContext.config.sinkType, resultPath)

    if (fileSinkContext.config.overwriteExistingFile) {
      fileSinkContext.fileConnectorService.write(fileSinkContext.config, destinationFilePath, localFile)
    } else {
      fileSinkContext.fileConnectorService.writeWithSuffixIfExists(fileSinkContext.config, destinationFilePath, localFile)
    }

    FileStats(filePath = destinationFilePath, fileSizeBytes = localFile.length())
  }(taskEc)

  private def replicateSingleFile(fullFilePath: String, mezzanineDirPath: String): Future[FileStats] = {
    val retryUtil = new Retry(fileUploaderSinkConfig.retryConfig.retries, fileUploaderSinkConfig.retryConfig.delaySeconds.seconds)
    for {
      localFile <- retryUtil.retry { () =>
        downloadFile(fullFilePath)
          .tap(_ => logger.info(s"File $fullFilePath downloaded successfully"))
          .tapError(e => logger.error(s"Failed to download file: $fullFilePath", runId, e))
      }
      relativeFilePath <- Future.fromTry(S3PathUtil.getRelativeS3Path(fullFilePath, mezzanineDirPath))
      fileStats <- retryUtil.retry { () =>
        uploadFile(relativeFilePath, localFile).andThen { case _ => localFile.delete() }
          .tap(_ => logger.info(s"File $fullFilePath uploaded successfully", runId))
          .tapError(e => logger.error(s"Failed to upload file: $fullFilePath", runId, e))
      }
    } yield fileStats
  }

  private def replicateDirectory(fileTransformationMessage: DirectoryTransformationMessage): Future[List[SinkMetric]] = {
    val mezzanineDirPath = fileTransformationMessage.fullDirectoryPath
    for {
      fileStats <- listFiles(mezzanineDirPath).mapAsyncUnordered(fileSinkContext.parallelism) { f =>
        logger.info(s"File $f replication starts...")
        val r = replicateSingleFile(f, mezzanineDirPath)
          .tap(_ => flowLogs.logAndPublishSinkMonitoringLog(logger, dataSink, s"File $f replicated successfully", runId))
          .tapError(e => flowLogs.logAndPublishSinkMonitoringError(logger, dataSink, s"Failed to replicate file $f", runId, e))
        r.transformWith(Future.successful)
      }.runWith(Sink.seq)
      sinkMetrics = fileStats.toList.collect { case Success(fs) => SinkMetric(dataSink.getId, fs.filePath, fs.fileSizeBytes, maybeAdaptiveFlowTask) }
    } yield sinkMetrics
  }

  private val msgBuffer: ConcurrentLinkedQueue[BasicMessage] = new ConcurrentLinkedQueue()

  def start(): Future[Unit] = {
    logger.info("Starting task...")
    val eff = for {
      taskConfig <- configPreparer.prepareSinkConnectorConfig(dataSink, configDef).tapError(logger.error("Failed to prepare task config", _))
      _ <- startTask(taskConfig).tapError(logger.error("Failed to start task", _))
    } yield ()
    eff.tap(_ => logger.info("Task started successfully")).tapError(logger.error("Task failed to start", _))
  }

  def putData(messages: Seq[BasicMessage]): Future[Unit] = Future {
    msgBuffer.addAll(messages.asJava)
    fileSinkContext.nrOfProcessedMessages += messages.length
    offsetAggregator.rememberOffsets(messages)
  }

  def flushData(): Future[FlushingResults] = {
    val drainedBuffer = Iterator.continually(msgBuffer.poll()).takeWhile(_ != null).toList
    drainedBuffer.flatTraverse {
      case r: DirectoryTransformationMessage => replicateDirectory(r)
      case e =>
        logger.warn(s"Unexpected message: $e")
        Future.successful(Nil)
    }.map(sinkMetrics => FlushingResults(offsetAggregator.handAndCleanOffsets(), sinkMetrics))
  }

  def forceFlushData(): Future[FlushingResults] = flushData()

  def stop(): Future[Unit] = Future {
    fileSinkContext.fileConnectorService.close()
    flowLogs.publishSinkEndingFlowLogs(dataSink, runId, fileSinkContext.nrOfProcessedMessages)
  }(taskEc)

  private class FileSinkContext(val config: FileSinkConnectorConfig, currentSink: DataSink) {
    private val localDirPath = config.localBufferDir + "/" + UUID.randomUUID().toString
    val localDir = new File(localDirPath)
    localDir.mkdirs()

    val fileConnectorService: FileConnectorService[_ <: BaseAuthConfig] = {
      val ps = probeFactory.getProbeService(adminApiClient, javaListingClient, config.decryptKey, config.sinkType, fileVault)
      ps.initLogger(ResourceType.SINK, currentSink.getId, Optional.empty())
      ps.asInstanceOf[FileConnectorService[_ <: BaseAuthConfig]]
    }

    var nrOfProcessedMessages = 0L

    val parallelism: Int = if (fileUploaderSinkConfig.replicationParallelismEnabled) {
      Try {
        val calculated = fileConnectorService.calculateSinkParallelism(config)
        logger.info(s"Replication parallelism enabled: calculated sink parallelism: $calculated")
        calculated
      }.getOrElse {
        logger.warn("Replication parallelism enabled: failed to calculate sink parallelism, defaulting to 1")
        1
      }
    } else {
      logger.info("Replication parallelism not enabled, using default value 1")
      1
    }
  }
}

object FileUploaderSinkTask {
  private final case class FileStats(filePath: String, fileSizeBytes: Long)

  private case class RetryConfig(retries: Int, delaySeconds: Int)
  private final case class FileUploaderSinkConfig(replicationParallelismEnabled: Boolean, retryConfig: RetryConfig)

}


