package com.nexla.inmemory_connector.pipeline.tx.ray

import akka.actor.ActorSystem
import cats.implicits.toTraverseOps
import com.nexla.admin.client.DataSource
import com.nexla.inmemory_connector.compat.{BYOOptimizedFlowMessage, BasicMessage, DirectoryTransformationMessage}
import com.nexla.inmemory_connector.context.custom_runtime.CustomRuntimeConfig.RayRuntimeConfig
import com.nexla.inmemory_connector.monitoring.FileConnectorFlowLogs
import com.nexla.inmemory_connector.pipeline.metrics.InMemoryControlMessageProducer
import com.nexla.inmemory_connector.pipeline.tx.TxTask
import com.nexla.inmemory_connector.pipeline.tx.ray.RayTxTask.{HardcodedValues, JobTracker, prettyPrintSinkType, prettyPrintSourceType}
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClient
import com.nexla.inmemory_connector.pipeline.tx.ray.api_client.RayApiClient._
import com.nexla.inmemory_connector_common.utils.TappingOps.futureOps
import com.nexla.sc.util.StrictNexlaLogging

import java.util.UUID
import java.util.concurrent.TimeUnit
import scala.collection.concurrent.TrieMap
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Random, Success, Try}


class RayTxTask(logPrefix: String, dataSource: Option[DataSource], runId: Long, rayConfig: RayRuntimeConfig, rayApi: RayApiClient, messageProducer: InMemoryControlMessageProducer)(implicit ec: ExecutionContext, system: ActorSystem) extends TxTask with StrictNexlaLogging {
  override def logPrefix: Option[String] = Some(logPrefix)

  private val flowLogs = new FileConnectorFlowLogs(messageProducer)
  private val dataSourceId = dataSource.map(_.getId).getOrElse(0)
  private val jobTracker = new JobTracker

  def transform(msg: BasicMessage): Future[Try[List[BasicMessage]]] = msg match {
    case t: DirectoryTransformationMessage => transformDirectory(t).map(_.map(List(_)))
    case t: BYOOptimizedFlowMessage => transformBYOOptimized(t).map(_.map(List(_)))
    case other => Future.successful(Success(List(other)))
  }

  private def transformDirectory(fileTransformationMessage: DirectoryTransformationMessage): Future[Try[DirectoryTransformationMessage]] = {
    val destinationFilePath = s"${rayConfig.destS3Credentials.bucketWithPrefix}/ray-output/$dataSourceId/$runId/${UUID.randomUUID()}"
    val srcType =  S3MezzanineRaySrc(fileTransformationMessage.fullDirectoryPath, rayConfig.sourceS3Credentials)
    val dstType =  S3MezzanineRayDst(destinationFilePath, rayConfig.destS3Credentials)

    submitAndWaitForJob(srcType, dstType, HardcodedValues.entrypointScriptNameRegular).map(_.map { _ =>
      DirectoryTransformationMessage(
        headDatasetId = fileTransformationMessage.headDatasetId,
        offsets = fileTransformationMessage.offsets,
        fullDirectoryPath = destinationFilePath,
      )
    })
  }

  private def transformBYOOptimized(t: BYOOptimizedFlowMessage): Future[Try[BYOOptimizedFlowMessage]] = {
    submitAndWaitForJob(BYOOptimizedFlowRaySrc, BYOOptimizedFlowRayDst, HardcodedValues.entrypointScriptNameOptimized).map(_.map(_ => t))
  }

  private def submitAndWaitForJob(srcType: RaySrc, dstType: RayDst, entrypoint: String): Future[Try[Unit]] = {
    val req = SubmitDirectoryJobRequest(
      customCode = rayConfig.customCode,
      driverFunction = rayConfig.driverFunction,
      packages = rayConfig.packages ++ List("boto3"),
      privatePackages = rayConfig.privatePackages,
      raySource = srcType,
      rayDestination = dstType,
      extraData = rayConfig.extraData,
      entrypoint: String,
    )
    val eff = for {
      _ <- Future.successful(flowLogs.logAndPublishSourceMonitoringLog(logger, dataSource, "Custom code execution has started", runId))
      parentJobId <- rayApi.submitJobV2(req)
        .tap { jobId =>
          logger.info(s"Job was submitted (jobId: ${jobId.id}, srcDir: ${prettyPrintSourceType(req.raySource)}, dstDir: ${prettyPrintSinkType(req.rayDestination)})")
          jobTracker.trackJob(jobId)
        }
        .tapError(e => logger.error(s"Job submitting failed (srcDir: ${prettyPrintSourceType(req.raySource)}, dstDir: ${prettyPrintSinkType(req.rayDestination)})", e))
      directoryJobStatus <- waitUntilChildJobsAreSpawned(parentJobId)
        .tap { status =>
          logger.info(s"Directory job finished (jobId: ${parentJobId.id}, status: $status")
          jobTracker.removeJob(parentJobId)
          status.childJobs.foreach(jobTracker.trackJob)
        }
      childStatuses <- directoryJobStatus.childJobs.toList.traverse(childJobId => waitUntilJobFinishes(childJobId).map(r => childJobId -> r.status)
        .tap { case (childJobId, status) =>
          logger.info(s"Child job finished (jobId: ${parentJobId.id}, status: $status")
          jobTracker.removeJob(childJobId)
        })
      jobErrorDetails <- rayApi.fetchErrorsDetails(parentJobId).recover { case _ => JobErrorDetails(List.empty) }
    } yield {
      if (directoryJobStatus.status == JobStatus.Succeeded && childStatuses.toMap.values.forall(_ == JobStatus.Succeeded)) {
        Success(())
      } else {
        Failure(new Exception(s"Job execution failed: main job status: ${directoryJobStatus.status}, child statuses: ${childStatuses.toMap}, errorDetails: ${jobErrorDetails.reasons}"))
      }
    }
    eff
      .tap {
        case Success(_) => flowLogs.logAndPublishSourceMonitoringLog(logger, dataSource, "Custom code execution has finished", runId)
        case Failure(e) => flowLogs.logAndPublishSourceMonitoringError(logger, dataSource, "Custom code execution has failed", runId, e)
      }
      .tapError(e => flowLogs.logAndPublishSourceMonitoringError(logger, dataSource, "Custom code execution has failed", runId, e))
  }

  private def waitUntilChildJobsAreSpawned(jobId: JobId): Future[DirectoryJobDetails] = {
    waitUntilJobCompletes[DirectoryJobDetails](jobId, rayApi.fetchDirectoryJobDetails, _.status, 5)
  }

  private def waitUntilJobFinishes(jobId: JobId): Future[JobDetails] = {
    waitUntilJobCompletes[JobDetails](jobId, rayApi.fetchJobDetails, _.status, 5)
  }

  private def waitUntilJobCompletes[T](jobId: JobId, fn: JobId => Future[T], extractStatus: T => JobStatus, retries: Int): Future[T] = {
    def randomDelay(): FiniteDuration = {
      val jitter = FiniteDuration(Random.nextInt(60) - 30, TimeUnit.SECONDS) // -30 to 30 seconds
      1.minute + jitter
    }

    def attempt(retriesLeft: Int): Future[T] = fn(jobId)
      .transformWith {
        case Failure(err) =>
          if (retriesLeft > 0) {
            val delay = randomDelay()
            logger.error(s"Checking job status $jobId failed, retries left: $retriesLeft. Rescheduling after: $delay", err)
            akka.pattern.after(delay)(attempt(retriesLeft - 1))
          } else {
            logger.error(s"Checking job status $jobId failed, no retries left", err)
            Future.failed(err)
          }
        case Success(jobDetails) =>
          val status = extractStatus(jobDetails)
          if (status == JobStatus.Pending || status == JobStatus.Running) {
            val delay = randomDelay()
            logger.info(s"Job $jobId is not complete yet (current status: $status). Rescheduling after: $delay")
            akka.pattern.after(delay)(attempt(retriesLeft))
          }
          else {
            logger.info(s"Job $jobId is complete with status: $status")
            Future.successful(jobDetails)
          }
      }

    attempt(retries)
  }

  override def start(): Future[Unit] = Future.unit

  override def stop(): Future[Unit] = {
    val pendingJobs = jobTracker.getAllTrackedJobs()
    logger.info(s"Terminating pending jobs: $pendingJobs")
    pendingJobs.traverse { jobId =>
      rayApi.terminateJob(jobId).transformWith {
        case Failure(exception) =>
          logger.warn(s"Failed to stop job $jobId", exception)
          Future.unit
        case Success(value) =>
          logger.info(s"Successfully stopped job $jobId: ${value.body.compactPrint}")
          Future.unit
      }
    }.map(_ => ())
  }
}

object RayTxTask {
  private class JobTracker {
    private val startedJobs: TrieMap[JobId, Unit] = TrieMap.empty

    def trackJob(jobId: JobId): Unit = {
      startedJobs.put(jobId, ())
      ()
    }

    def removeJob(jobId: JobId): Unit = {
      startedJobs.remove(jobId)
      ()
    }

    def getAllTrackedJobs(): List[JobId] = startedJobs.keySet.toList

  }

  private def prettyPrintSourceType(srcType: RaySrc): String = srcType match {
    case S3MezzanineRaySrc(fullSourceDir, _) => fullSourceDir
    case BYOOptimizedFlowRaySrc => "byo-optimized"
  }

  private def prettyPrintSinkType(sinkType: RayDst): String = sinkType match {
    case S3MezzanineRayDst(fullDestinationDir, _) => fullDestinationDir
    case BYOOptimizedFlowRayDst => "byo-optimized"
  }

  private object HardcodedValues {
    val entrypointScriptNameRegular = "batch_jobs"
    val entrypointScriptNameOptimized = "byo_opt_create_batches"
  }
}
