package com.nexla.connector.rest.source;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.MappingBuilder;
import com.github.tomakehurst.wiremock.core.Options;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.github.tomakehurst.wiremock.matching.AnythingPattern;
import com.github.tomakehurst.wiremock.matching.EqualToJsonPattern;
import com.github.tomakehurst.wiremock.matching.EqualToPattern;
import com.github.tomakehurst.wiremock.matching.StringValuePattern;
import com.github.tomakehurst.wiremock.stubbing.StubMapping;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.nexla.admin.client.*;
import com.nexla.common.NexlaConstants;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NotificationEventType;
import com.nexla.common.StreamUtils;
import com.nexla.common.io.RedisConnect;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.common.notify.NexlaNotificationEvent;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.tracker.SourceItem;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connect.common.SchemaDetectionResult;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.connector.config.ConnectorAdminConfig;
import com.nexla.connector.config.rest.IterationType;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.connector.config.rest.RestSourceConnectorConfig;
import com.nexla.connector.properties.RestConfigAccessor;
import com.nexla.kafka.service.TopicMetaService;
import com.nexla.redis.RedisCreds;
import com.nexla.rest.RestIterationOffset;
import com.nexla.rest.iterations.offsets.PagingIncrementingOffset;
import com.nexla.test.IntegrationTests;
import com.nexla.transform.schema.FormatDetector;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.net.util.Base64;
import org.apache.kafka.common.config.ConfigException;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.source.SourceTaskContext;
import org.apache.kafka.connect.storage.OffsetStorageReader;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.junit.*;
import org.junit.experimental.categories.Category;
import org.junit.rules.TestName;
import org.slf4j.MDC;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.utility.DockerImageName;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.util.*;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.github.tomakehurst.wiremock.stubbing.Scenario.STARTED;
import static com.google.common.collect.ImmutableMap.of;
import static com.nexla.admin.client.FindOrCreateDataSetResult.Result.CREATED;
import static com.nexla.admin.client.FindOrCreateDataSetResult.Result.FOUND;
import static com.nexla.common.ConnectionType.REST;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.StreamUtils.lhm;
import static com.nexla.common.metrics.NexlaRawMetric.HASH;
import static com.nexla.common.metrics.NexlaRawMetric.NAME;
import static com.nexla.common.metrics.NexlaRawMetric.RECORDS;
import static com.nexla.common.metrics.NexlaRawMetric.RUN_ID;
import static com.nexla.common.parse.ParserConfigs.SchemaDetection.DATASET_REPLICATION;
import static com.nexla.common.parse.ParserConfigs.SchemaDetection.SCHEMA_DETECTION_ONCE;
import static com.nexla.connect.common.DbTestUtils.RedisData;
import static com.nexla.connect.common.DbTestUtils.cleanRedisDb;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.BaseConnectorConfig.*;
import static com.nexla.connector.config.SourceConnectorConfig.MAX_POLL_TIME_MS;
import static com.nexla.connector.config.rest.IterationType.ASYNC_ITERATION;
import static com.nexla.connector.config.rest.IterationType.BODY_AS_FILE_ITERATION;
import static com.nexla.connector.config.rest.IterationType.EMIT_ONCE;
import static com.nexla.connector.config.rest.IterationType.HORIZONTAL_ITERATION;
import static com.nexla.connector.config.rest.IterationType.STATIC_URL;
import static com.nexla.connector.config.rest.RestAuthConfig.*;
import static com.nexla.connector.config.rest.RestIterationConfig.*;
import static com.nexla.connector.properties.RestConfigAccessor.REST_ITERATIONS_JSON;
import static com.nexla.connector.properties.RestConfigAccessor.*;
import static com.nexla.connector.rest.source.RestSourceTaskTest.JSON.j;
import static com.nexla.redis.LookupUtils.withLookup;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toList;
import static org.assertj.core.api.Java6Assertions.assertThatThrownBy;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.lessThanOrEqualTo;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;
import static org.springframework.util.MimeTypeUtils.APPLICATION_JSON;

@Slf4j
@Category(IntegrationTests.class)
public class RestSourceTaskTest extends BaseKafkaTest {

	private static String datasetTopic = "";
	private static Integer testSourceId = 0;
	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
	private static final Integer CODE_CONTAINER_ID = 1;

	@ClassRule
	public static WireMockRule wireMockServer = new WireMockRule(Options.DYNAMIC_PORT);

	private static final int REDIS_PORT = 6379;
	private static final int REDIS_INDEX = 0;
	public static GenericContainer redis = new GenericContainer(DockerImageName.parse("redis:7.0.12"))
			.withExposedPorts(REDIS_PORT);

	public static KafkaContainer kafka = new KafkaContainer("7.2.11");

	private RestSourceTask task;
	private OffsetStorageReader offsetStorageReader;
	private AdminApiClient adminApiClientMock;
	private TopicMetaService topicMetaService;

	@BeforeClass
	public static void setUp() {
		redis.withReuse(true);
		kafka.withReuse(true);
		FormatDetector.initDefault();
	}

	@AfterClass
	public static void tearDown() {
		redis.stop();
		kafka.stop();
	}

	protected RedisData redisData(GenericContainer redis) {
		return new RedisData(redis.getHost(), redis.getMappedPort(REDIS_PORT), REDIS_INDEX);
	}

	protected RedisConnect toHostPort(GenericContainer redis) {
		return new RedisConnect(redis.getHost(), redis.getMappedPort(REDIS_PORT), REDIS_INDEX);
	}

	@After
	@SneakyThrows
	public void after() {
		cleanRedisDb(redisData(redis));
		if (task != null &&
				task.getControlMessageProducer() != null &&
				task.getControlMessageProducer().getTransport() != null) {
			task.getControlMessageProducer().getTransport().flush();
			task.getControlMessageProducer().getTransport().close();
		}
		closeProducer();
		closeConsumers();
		MDC.clear();
	}

	@Rule
	public TestName testName = new TestName();

	@Before
	public void onBefore() {
		testSourceId = testSourceId + 1;
		datasetTopic = "dataset-" + testSourceId;

		kafka.start();
		redis.start();
		init(kafka);
		org.eclipse.jetty.util.log.Log.setLog(new NoLogging());
		DataSource ds = new DataSource();
		ds.setId(testSourceId);
		ds.setSourceConfig(new HashMap<>());
		ds.setDatasets(Lists.newLinkedList());
		ds.setConnectionType(REST);
		ds.setRunIds(List.of());

		adminApiClientMock = mock(AdminApiClient.class);
		when(adminApiClientMock.getDataSource(any())).thenReturn(Optional.ofNullable(ds));
		when(adminApiClientMock.findOrCreateDataSet(any())).thenReturn(new FindOrCreateDataSetResult(FOUND, ds.getId()));

		var transformCode = "def transform(input, metadata, args):\n" +
							"    input['baz'] = input['foo'] + input['bar']\n" +
							"    return input";
		String encodedTransformCode = new String(java.util.Base64.getEncoder().encode(transformCode.getBytes()));
		when(adminApiClientMock.getTransform(CODE_CONTAINER_ID)).thenReturn(new Transform(
				CODE_CONTAINER_ID,
				"test transform",
				true,
				true,
				null,
				null,
				"test",
				"jolt_custom",
				new ArrayList<>() {{
					add(new TransformCode("nexla.custom", new TransformCodeSpec(null, "python", "base64", encodedTransformCode)));
				}},
				"record"
		));

		ControlMessageProducer controlMessageProducer = mock(ControlMessageProducer.class);
		topicMetaService = mock(TopicMetaService.class);
		SchemaDetectionUtils schemaDetection = spy(new SchemaDetectionUtils(
				10, adminApiClientMock, controlMessageProducer,
				Optional.ofNullable(topicMetaService), 1, 1, 10, false));

		doReturn(new SchemaDetectionResult(testSourceId, datasetTopic, c -> {
		})).when(schemaDetection).updateOrCreateDataSet(anyList(), any());

		SourceTaskContext sourceTaskContext = mock(SourceTaskContext.class);
		offsetStorageReader = mock(OffsetStorageReader.class);
		when(sourceTaskContext.offsetStorageReader()).thenReturn(offsetStorageReader);
		when(offsetStorageReader.offset(anyMap())).thenReturn(null);

		LinkedList<DateTime> times = Lists.newLinkedList();
		times.add(new DateTime("2018-04-15T00:08:18Z").withZone(DateTimeZone.UTC));
		times.add(new DateTime("2018-04-16T00:08:18Z").withZone(DateTimeZone.UTC));

		this.task = new RestSourceTask(schemaDetection) {

			@Override
			protected AdminApiClient getAdminApiClient(String adminApiPrefix, ConnectorAdminConfig adminConfig) {
				return adminApiClientMock;
			}

			@Override
			public Integer getDataSetId() {
				return 1;
			}

			@Override
			public DateTime now() {
				return times.remove();
			}
		};
		task.controlMessageProducer = controlMessageProducer;
		task.rethrowRetryExceptions = true;
		task.initialize(sourceTaskContext);
		task.config = mock(RestSourceConnectorConfig.class);
		task.logger = mock(NexlaLogger.class);
		this.runBeforeTopicReading = Optional.of(
				new LoggedRunnable(testName.getMethodName(), () -> task.getControlMessageProducer().getTransport().flush())
		);

		wireMockServer.resetAll();
		MDC.put("test.name", this.testName.getMethodName());
		System.setProperty("org.eclipse.jetty.util.log.class", "org.eclipse.jetty.util.log.StdErrLog");
		System.setProperty("org.eclipse.jetty.LEVEL", "OFF");
	}

	@Test
	public void async_wait() {
		String partialUrl = "http://localhost:" + wireMockServer.port();

		String reportNotReady = "{\n" +
				"\"kind\": \"doubleclicksearch#report\",\n" +
				"\"id\": \"MTMyNDM1NDYK\",\n" +
				"\"isReportReady\": false\n" +
				"}";

		String reportReady = "{\n" +
				"  \"kind\": \"doubleclicksearch#report\",\n" +
				"  \"id\": \"MTMyNDM1NDYK\",\n" +
				"  \"isReportReady\": true,\n" +
				"  \"rowCount\": 1329,\n" +
				"  \"files\": [\n" +
				"    {\n" +
				"      \"url\": \"" + partialUrl + "/reports/MTMyNDM1NDYK/files/0\",\n" +
				"      \"byteCount\": \"10242323\"\n" +
				"    },\n" +
				"    {\n" +
				"      \"url\": \"" + partialUrl + "/reports/MTMyNDM1NDYK/files/1\",\n" +
				"      \"byteCount\": \"10242323\"\n" +
				"    }\n" +
				"  ]\n" +
				"}";

		createMapping("/report", reportNotReady);

		Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, partialUrl + "/report");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.files[*].url");
			put(POLL_MS, "1");
			put(ASYNC_ITERATION_PATH, "$.isReportReady");
			put(ASYNC_ITERATION_VALUE, "true");
			put(ASYNC_ITERATION_WAIT_RETRY_MS, "0");
			put(ITERATION_TYPE, ASYNC_ITERATION.property);
		}});

		createMapping("/report", reportNotReady);

		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);
		List<NexlaMessage> data = toMessages(sourceRecords);
		List<Map<String, Object>> rawMessages = toRawMessages(data);
		assertTrue(rawMessages.isEmpty());

		sourceRecords = pollNoTrace(task);
		data = toMessages(sourceRecords);
		rawMessages = toRawMessages(data);
		assertTrue(rawMessages.isEmpty());

		createMapping("/report", reportReady);

		sourceRecords = pollNoTrace(task);
		data = toMessages(sourceRecords);
		rawMessages = toRawMessages(data);
		assertEquals("[{\"url\":\"http://localhost:" + wireMockServer.port() + "/reports/MTMyNDM1NDYK/files/0\"}," +
				"{\"url\":\"http://localhost:" + wireMockServer.port() + "/reports/MTMyNDM1NDYK/files/1\"}]", JsonUtils.toJsonString(rawMessages));
		task.stop();
	}

	@Test
	public void execStepWithSuccessfulPrevStep() {
		String baseURL = "http://localhost:" + wireMockServer.port();

		task.start(
				new StepConfigBuilder(baseURL)
						.addStep(STATIC_URL.property, "POST", "/submit", Map.of(
								"response.data.path", "$",
								BODY_TEMPLATE, j("{ 'operation': 'query', 'query': 'SELECT Id FROM Account' }")
						), () -> {
							createPostMapping("/submit", j("{ 'operation': 'query', 'query': 'SELECT Id FROM Account' }"), j("{'id': '1'}"));
						})
						.addStep(STATIC_URL.property, "GET", "/report/{step1.id}", Map.of(
								RESPONSE_DATA_PATH, "$.files[*].url"
						), () -> {
							createMapping("/report/1", j("{'isReportReady': true, 'files': [{'url': '" + baseURL + "/reports/1/files/0'}, {'url': '" + baseURL + "/reports/1/files/1'}]}"));
						})
						.build()
		);

		assertMessagesEqual(
				List.of(
						Map.of("url", baseURL + "/reports/1/files/0"),
						Map.of("url", baseURL + "/reports/1/files/1")
				),
				toRawMessages(toMessages(pollNoTrace(task)))
		);

		assertTrue(pollNoTrace(task).isEmpty());
		assertTrue(pollNoTrace(task).isEmpty());
		assertTrue(pollNoTrace(task).isEmpty());

		task.stop();
	}

	@Test
	public void execStepWithPrevStepFailedWithBody() {
		String baseURL = "http://localhost:" + wireMockServer.port();

		int sourceId = testSourceId;
		withConsumer((nexlaLogsTopic, nexlaLogsConsumer) -> {
			Map<String, String> props = new StepConfigBuilder(baseURL)
					.addStep(STATIC_URL.property, "POST", "/submit", Map.of(
							"response.data.path", "$",
							BODY_TEMPLATE, j("{ 'operation': 'query', 'query': 'SELECT Id FROM Account' }")
					), () -> {
						createPostMapping("/submit", j("{ 'operation': 'query', 'query': 'SELECT Id FROM Account' }"), j("[ { 'errorCode': 'JOB_FAILED' } ]"));
					})
					.addStep(STATIC_URL.property, "GET", "/report/{step1.id}", Map.of(
							RESPONSE_DATA_PATH, "$.files[*].url"
					), () -> {
						createMapping("/report/1", j("{ 'isReportReady': true, 'files': [{'url': '" + baseURL + "/reports/1/files/0'}, {'url': '" + baseURL + "/reports/1/files/1'}] }"));
					})
					.build();

			props.put(SOURCE_ID, sourceId + "");
			props.put(TOPIC_MONITORING_LOGS, nexlaLogsTopic);
			task.start(props);

			assertTrue(pollNoTrace(task).isEmpty());
			assertTrue(pollNoTrace(task).isEmpty());
			assertTrue(pollNoTrace(task).isEmpty());

			List<NexlaMonitoringLogEvent> logs = readMonitoringLogs(nexlaLogsConsumer, sourceId);
			List<NexlaMonitoringLogEvent> errors = logs
					.stream()
					.filter(log -> log.getSeverity() == NexlaMonitoringLogSeverity.ERROR)
					.collect(toList());

			List<NexlaMonitoringLogEvent> warnings = logs
					.stream()
					.filter(log -> log.getSeverity() == NexlaMonitoringLogSeverity.WARNING)
					.collect(toList());

			assertEquals(0, errors.size());

			assertEquals(1, warnings.size());
			assertEquals("Skipping call '" + baseURL + "/report/{step1.id}' because those variables are not filled: [step1.id]", warnings.get(0).getLog());

			task.stop();
		}, "nexla_logs");
	}

	@Test
	public void execStepWithPrevStepFailedErrorCode() {
		String baseURL = "http://localhost:" + wireMockServer.port();

		int sourceId = testSourceId;
		withConsumer((nexlaLogsTopic, nexlaLogsConsumer) -> {
			Map<String, String> props = new StepConfigBuilder(baseURL)
					.addStep(STATIC_URL.property, "POST", "/submit", Map.of(
							"response.data.path", "$",
							BODY_TEMPLATE, j("{ 'operation': 'query', 'query': 'SELECT Id FROM Account' }")
					), () -> {
						createPostMapping("/submit", j("{ 'operation': 'query', 'query': 'SELECT Id FROM Account' }"), j("[ { 'errorCode': 'JOB_FAILED' } ]"), 403);
					})
					.addStep(STATIC_URL.property, "GET", "/report/{step1.id}", Map.of(
							RESPONSE_DATA_PATH, "$.files[*].url"
					), () -> {
						createMapping("/report/1", j("{ 'isReportReady': true, 'files': [{'url': '" + baseURL + "/reports/1/files/0'}, {'url': '" + baseURL + "/reports/1/files/1'}] }"));
					})
					.build();

			props.put(SOURCE_ID, sourceId + "");
			props.put(TOPIC_MONITORING_LOGS, nexlaLogsTopic);
			task.start(props);

			assertTrue(pollNoTrace(task).isEmpty());
			assertTrue(pollNoTrace(task).isEmpty());
			assertTrue(pollNoTrace(task).isEmpty());

			List<NexlaMonitoringLogEvent> logs = readMonitoringLogs(nexlaLogsConsumer, sourceId);
			List<NexlaMonitoringLogEvent> errors = logs
					.stream()
					.filter(log -> log.getSeverity() == NexlaMonitoringLogSeverity.ERROR)
					.collect(toList());

			List<NexlaMonitoringLogEvent> warnings = logs
					.stream()
					.filter(log -> log.getSeverity() == NexlaMonitoringLogSeverity.WARNING)
					.collect(toList());

			assertEquals(2, errors.size());
			assertEquals("Error while executing request to " + baseURL + "/submit, cause: 403 Forbidden", errors.get(0).getLog());

			assertEquals(1, warnings.size());
			assertEquals("Skipping call '" + baseURL + "/report/{step1.id}' because those variables are not filled: [step1.id]", warnings.get(0).getLog());

			task.stop();
		}, "nexla_logs");
	}


	@Test
	public void report_360() {
		String partialUrl = "http://localhost:" + wireMockServer.port();

		String reportReady = "{\n" +
				"  \"kind\": \"doubleclicksearch#report\",\n" +
				"  \"id\": \"MTMyNDM1NDYK\",\n" +
				"  \"isReportReady\": true,\n" +
				"  \"rowCount\": 1329,\n" +
				"  \"files\": [\n" +
				"    {\n" +
				"      \"url\": \"" + partialUrl + "/reports/MTMyNDM1NDYK/files/0\",\n" +
				"      \"byteCount\": \"10242323\"\n" +
				"    },\n" +
				"    {\n" +
				"      \"url\": \"" + partialUrl + "/reports/MTMyNDM1NDYK/files/1\",\n" +
				"      \"byteCount\": \"10242323\"\n" +
				"    }\n" +
				"  ]\n" +
				"}";

		MappingBuilder builder = get(urlEqualTo("/report"))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody(reportReady)
						.withHeader("Content-Type", "application/json;charset=UTF-8")
						.withHeader("access_token", "123"));


		stubFor(builder);

		MappingBuilder builder2 = get(urlEqualTo("/reports/MTMyNDM1NDYK/files/0"))
				.withHeader("Authorization", new EqualToPattern("Bearer 123"))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody(
								"a,b,c\n" +
										"2,2,2\n" +
										"3,3,3\n" +
										"4,4,4")
						.withHeader("Content-Type", "application/json;charset=UTF-8"));

		stubFor(builder2);

		MappingBuilder builder3 = get(urlEqualTo("/reports/MTMyNDM1NDYK/files/1"))
				.withHeader("Authorization", new EqualToPattern("Bearer 123"))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody(
								"a,b,c\n" +
										"5,5,5\n" +
										"6,6,6\n" +
										"7,7,7\n" +
										"8,8,8\n" +
										"9,9,9\n" +
										"10,10,10\n" +
										"11,11,11\n")
						.withHeader("Content-Type", "application/json;charset=UTF-8"));
		stubFor(builder3);

		Map<String, String> restCaller1 = new HashMap<>() {{
			put(KEY, "step1");
			put(METHOD, "GET");
			put(URL_TEMPLATE, partialUrl + "/report");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.files[*].url");
			put(POLL_MS, "1");
			put(ASYNC_ITERATION_PATH, "$.isReportReady");
			put(ASYNC_ITERATION_VALUE, "true");
			put(ASYNC_ITERATION_WAIT_RETRY_MS, "0");
			put(ITERATION_TYPE, ASYNC_ITERATION.property);
		}};

		Map<String, String> restCaller2 = new HashMap<>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, "{step1.url}");
			put(FILE_RESPONSE_FORMAT, "csv");
			put(POLL_MS, "1");
			put(ITERATION_TYPE, BODY_AS_FILE_ITERATION.property);
			put(REQUEST_HEADERS, "Authorization:Bearer {step1.nx.header.access_token}");
			put(BATCH_SIZE, "3");
		}};
		Map<String, String> props = buildByCallersConfig(restCaller1, restCaller2);

		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);
		List<NexlaMessage> data = toMessages(sourceRecords);
		List<Map<String, Object>> rawMessages = toRawMessages(data);
		assertEquals(3, rawMessages.size());
		assertEquals(lhm("a", "2", "b", "2", "c", "2"), rawMessages.get(0));
		assertEquals(lhm("a", "3", "b", "3", "c", "3"), rawMessages.get(1));
		assertEquals(lhm("a", "4", "b", "4", "c", "4"), rawMessages.get(2));

		sourceRecords = pollNoTrace(task);
		data = toMessages(sourceRecords);
		rawMessages = toRawMessages(data);
		assertEquals(3, rawMessages.size());
		assertEquals(lhm("a", "5", "b", "5", "c", "5"), rawMessages.get(0));
		assertEquals(lhm("a", "6", "b", "6", "c", "6"), rawMessages.get(1));
		assertEquals(lhm("a", "7", "b", "7", "c", "7"), rawMessages.get(2));

		sourceRecords = pollNoTrace(task);
		data = toMessages(sourceRecords);
		rawMessages = toRawMessages(data);
		assertEquals(3, rawMessages.size());
		assertEquals(lhm("a", "8", "b", "8", "c", "8"), rawMessages.get(0));
		assertEquals(lhm("a", "9", "b", "9", "c", "9"), rawMessages.get(1));
		assertEquals(lhm("a", "10", "b", "10", "c", "10"), rawMessages.get(2));

		sourceRecords = pollNoTrace(task);
		data = toMessages(sourceRecords);
		rawMessages = toRawMessages(data);
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "11", "b", "11", "c", "11"), rawMessages.get(0));

		sourceRecords = pollNoTrace(task);
		data = toMessages(sourceRecords);
		rawMessages = toRawMessages(data);
		assertEquals(0, rawMessages.size());
		task.stop();
	}

	@Test
	public void report_salesforce() {
		String partialUrl = "http://localhost:" + wireMockServer.port();

		String reportReady = "{\n" +
				"  \"kind\": \"doubleclicksearch#report\",\n" +
				"  \"id\": \"MTMyNDM1NDYK\",\n" +
				"  \"isReportReady\": true,\n" +
				"  \"rowCount\": 1329,\n" +
				"  \"files\": [\n" +
				"    {\n" +
				"      \"url\": \"" + partialUrl + "/reports/MTMyNDM1NDYK/files/1\",\n" +
				"      \"byteCount\": \"10242323\"\n" +
				"    }\n" +
				"  ]\n" +
				"}";

		createMapping("/report", reportReady);

		stubFor(get(urlEqualTo("/reports/MTMyNDM1NDYK/files/1"))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody("a,b,c\n1,2,3")
						.withHeader("Content-Type", "text/csv")
						.withHeader("next", "123")));

		createMapping("/reports/MTMyNDM1NDYK/files/1?next=123", "a,b,c\n2,3,4");

		Map<String, String> restCaller1 = new HashMap<>() {{
			put(KEY, "step1");
			put(METHOD, "GET");
			put(URL_TEMPLATE, partialUrl + "/report");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.files[*].url");
			put(POLL_MS, "1");
			put(ASYNC_ITERATION_PATH, "$.isReportReady");
			put(ASYNC_ITERATION_VALUE, "true");
			put(ASYNC_ITERATION_WAIT_RETRY_MS, "0");
			put(ITERATION_TYPE, ASYNC_ITERATION.property);
		}};

		Map<String, String> restCaller2 = new HashMap<>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, "{step1.url}");
			put(FILE_RESPONSE_FORMAT, "csv");
			put(POLL_MS, "1");
			put(RESPONSE_NEXT_TOKEN_HEADER, "next");
			put(PARAM_ID, "next");
			put(ITERATION_TYPE, BODY_AS_FILE_ITERATION.property);
		}};
		Map<String, String> props = buildByCallersConfig(restCaller1, restCaller2);

		createMapping("/report", reportReady);

		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);
		List<NexlaMessage> data = toMessages(sourceRecords);
		List<Map<String, Object>> rawMessages = toRawMessages(data);
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "1", "b", "2", "c", "3"), rawMessages.get(0));

		sourceRecords = pollNoTrace(task);
		data = toMessages(sourceRecords);
		rawMessages = toRawMessages(data);
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "2", "b", "3", "c", "4"), rawMessages.get(0));
		task.stop();
	}

	@Test
	public void location_xml_header() {
		String partialUrl = "http://localhost:" + wireMockServer.port();

		String xmlReportStarted = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
				"<BillingReportCreationResponse>" +
				"<ReportStatus>RECEIVED</ReportStatus>" +
				"<Description>The report archive is currently being constructed.</Description>" +
				"</BillingReportCreationResponse>";

		MappingBuilder builder = post(urlEqualTo("/report"))
				.withRequestBody(new AnythingPattern())
				.willReturn(aResponse()
						.withStatus(200)
						.withBody(xmlReportStarted)
						.withHeader("Content-Type", "application/xml")
						.withHeader("Location", partialUrl + "/reportLocation"));


		String xmlReportReady = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
				"<BillingReportRetrievalResponse>" +
				"<ReportStatus>COMPLETED</ReportStatus>" +
				"<Description>The report archive is constructed.</Description>" +
				"</BillingReportRetrievalResponse>";

		MappingBuilder builder2 = get(urlEqualTo("/reportLocation"))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody(xmlReportReady)
						.withHeader("Content-Type", "application/xml")
						.withHeader("Location", partialUrl + "/reportLocationFile"));

		stubFor(builder);
		stubFor(builder2);
		createMapping("/reportLocationFile", "a,b,c\n1,2,3");


		Map<String, String> restCaller1 = new HashMap<>() {{
			put(KEY, "step1");
			put(METHOD, "POST");
			put(URL_TEMPLATE, partialUrl + "/report");
			put(RESPONSE_FORMAT, "xml");
			put(RESPONSE_DATA_PATH, "BillingReportCreationResponse/ReportStatus");
			put(POLL_MS, "1");
			put(ASYNC_ITERATION_PATH, "BillingReportCreationResponse/ReportStatus");
			put(ASYNC_ITERATION_VALUE, "RECEIVED");
			put(ASYNC_ITERATION_WAIT_RETRY_MS, "0");
			put(ITERATION_TYPE, ASYNC_ITERATION.property);
		}};

		Map<String, String> restCaller2 = new HashMap<>() {{
			put(KEY, "step2");
			put(METHOD, "GET");
			put(URL_TEMPLATE, "{step1.nx.header.Location}");
			put(RESPONSE_FORMAT, "xml");
			put(RESPONSE_DATA_PATH, "BillingReportRetrievalResponse/ReportStatus");
			put(POLL_MS, "1");
			put(ASYNC_ITERATION_PATH, "BillingReportRetrievalResponse/ReportStatus");
			put(ASYNC_ITERATION_VALUE, "COMPLETED");
			put(ASYNC_ITERATION_WAIT_RETRY_MS, "0");
			put(ITERATION_TYPE, ASYNC_ITERATION.property);
		}};

		Map<String, String> restCaller3 = new HashMap<>() {{
			put(KEY, "step3");
			put(METHOD, "GET");
			put(URL_TEMPLATE, "{step2.nx.header.Location}");
			put(FILE_RESPONSE_FORMAT, "csv");
			put(POLL_MS, "1");
			put(ITERATION_TYPE, BODY_AS_FILE_ITERATION.property);
		}};
		Map<String, String> props = buildByCallersConfig(restCaller1, restCaller2, restCaller3);

		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);
		List<NexlaMessage> data = toMessages(sourceRecords);
		List<Map<String, Object>> rawMessages = toRawMessages(data);
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "1", "b", "2", "c", "3"), rawMessages.get(0));
		task.stop();
	}

	@Test
	public void pagingHorizontal() {
		String partialUrl = "http://localhost:" + wireMockServer.port();

		List<LinkedHashMap<Object, Object>> data1 = List.of(lhm("a", "1"));
		List<LinkedHashMap<Object, Object>> data2 = List.of(lhm("b", "2"));
		List<LinkedHashMap<Object, Object>> data3 = List.of(lhm("c", "3"));

		stubFor(
				get(urlEqualTo("/json"))
						.withRequestBody(new AnythingPattern())
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody(JsonUtils.toJsonString(data1))
										.withHeader("Content-Type", "application/json")
										.withHeader("scroll_token", "TOKEN")));

		stubFor(
				get(urlEqualTo("/json?token=TOKEN"))
						.withRequestBody(new AnythingPattern())
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody(JsonUtils.toJsonString(data2))));

		Map<String, String> config1 = new HashMap<>() {{
			put(KEY, "step1");
			put(METHOD, "GET");
			put(URL_TEMPLATE, partialUrl + "/json");
			put(PAGING_HORIZONTAL_URL, partialUrl + "/json?token={nx.header.scroll_token}");
			put(ITERATION_TYPE, HORIZONTAL_ITERATION.property);
		}};

		Map<String, String> props = buildByCallersConfig(config1);

		task.start(props);

		List<Map<String, Object>> rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(lhm("value", data1), rawMessages.get(0));
		assertEquals(1, rawMessages.size());

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(lhm("value", data2), rawMessages.get(0));
		assertEquals(1, rawMessages.size());

		stubFor(
				get(urlEqualTo("/json?token=TOKEN"))
						.withRequestBody(new AnythingPattern())
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody(JsonUtils.toJsonString(data3))
										.withHeader("Content-Type", "application/json")));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(lhm("value", data3), rawMessages.get(0));
		assertEquals(1, rawMessages.size());

		stubFor(
				get(urlEqualTo("/json?token=TOKEN"))
						.withRequestBody(new AnythingPattern())
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody(JsonUtils.toJsonString(Maps.newHashMap()))
										.withHeader("Content-Type", "application/json")));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(0, rawMessages.size());
		task.stop();
	}

	@Test
	public void bodyAsFileWithPaging() {
		String partialUrl = "http://localhost:" + wireMockServer.port();
		createMapping("/csv?batch=1", "a,b,c\n1,2,3\n4,5,6");
		createMapping("/csv?batch=2", "a,b,c\n7,8,9\n10,11,12");
		createMapping("/csv?batch=3", "a,b,c\n13,14,15\n16,17,18");
		createMapping("/csv?batch=4", "a,b,c\n19,20,21\n22,23,24");

		Map<String, String> config = new HashMap<>() {{
			put(KEY, "step1");
			put(METHOD, "GET");
			put(BATCH_SIZE, "1");
			put(PAGE_PARAM_NAME, "batch");
			put(START_PAGE_FROM, "1");
			put(END_PAGE_TO, "3");
			put(URL_TEMPLATE, partialUrl + "/csv");
			put(FILE_RESPONSE_FORMAT, "csv");
			put(POLL_MS, "1");
			put(MODE, PAGING_MODE);
			put(ITERATION_TYPE, BODY_AS_FILE_ITERATION.property);
		}};

		Map<String, String> props = buildByCallersConfig(config);

		task.start(props);

		List<Map<String, Object>> rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "1", "b", "2", "c", "3"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "4", "b", "5", "c", "6"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "7", "b", "8", "c", "9"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "10", "b", "11", "c", "12"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "13", "b", "14", "c", "15"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "16", "b", "17", "c", "18"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(0, rawMessages.size());
		task.stop();
	}

	@Test
	@SneakyThrows
	public void bodyAsFile_archiveOfFilesWithDifferentSchemasMultipleSchemas() {
		String partialUrl = "http://localhost:" + wireMockServer.port();
		var zipFile = IOUtils.toByteArray(this.getClass().getClassLoader().getResourceAsStream("4files-20records-each-different-schemas.zip"));

		MappingBuilder builder = get(urlEqualTo("/file"))
				.withRequestBody(new AnythingPattern())
				.willReturn(aResponse()
						.withStatus(200)
						.withBody(zipFile)
						.withHeader("Content-Type", "application/octet-stream"));

		stubFor(builder);

		Map<String, String> restCaller3 = new HashMap<>() {{
			put(KEY, "step3");
			put(METHOD, "GET");
			put(URL_TEMPLATE, partialUrl + "/file");
			put(FILE_RESPONSE_FORMAT, "zip");
			put(POLL_MS, "1");
			put(ITERATION_TYPE, BODY_AS_FILE_ITERATION.property);
		}};
		Map<String, String> props = buildByCallersConfig(restCaller3);


		when(adminApiClientMock.findOrCreateDataSet(any()))
				.thenReturn(
						new FindOrCreateDataSetResult(CREATED, 1),
						new FindOrCreateDataSetResult(CREATED, 2),
						new FindOrCreateDataSetResult(CREATED, 3),
						new FindOrCreateDataSetResult(CREATED, 4)
				);


		SchemaDetectionUtils schemaDetection = new SchemaDetectionUtils(
				10, adminApiClientMock, mock(ControlMessageProducer.class),
				Optional.ofNullable(this.topicMetaService), 1, 1, 10, false
		);

		SourceTaskContext sourceTaskContext = mock(SourceTaskContext.class);
		when(sourceTaskContext.offsetStorageReader()).thenReturn(offsetStorageReader);
		RestSourceTask task = new RestSourceTask(schemaDetection) {

			@Override
			protected AdminApiClient getAdminApiClient(String adminApiPrefix, ConnectorAdminConfig adminConfig) {
				return adminApiClientMock;
			}

			@Override
			public Integer getDataSetId() {
				return 1;
			}
		};
		task.controlMessageProducer = mock(ControlMessageProducer.class);
		task.rethrowRetryExceptions = true;
		task.initialize(sourceTaskContext);
		task.config = mock(RestSourceConnectorConfig.class);
		task.logger = mock(NexlaLogger.class);
		this.runBeforeTopicReading = Optional.of(
				new LoggedRunnable(testName.getMethodName(), () -> task.getControlMessageProducer().getTransport().flush())
		);

		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);
		assertEquals(20, sourceRecords.size());
		assertEquals("dataset-1", sourceRecords.get(0).topic());
		sourceRecords = pollNoTrace(task);
		assertEquals(20, sourceRecords.size());
		assertEquals("dataset-2", sourceRecords.get(0).topic());
		sourceRecords = pollNoTrace(task);
		assertEquals(20, sourceRecords.size());
		assertEquals("dataset-3", sourceRecords.get(0).topic());
		sourceRecords = pollNoTrace(task);
		assertEquals(20, sourceRecords.size());
		assertEquals("dataset-4", sourceRecords.get(0).topic());
		sourceRecords = pollNoTrace(task);
		assertEquals(0, sourceRecords.size());

		assertFalse(schemaDetection.getDataSetService().singleSchemaMode());
		task.stop();
	}

	@Test
	@SneakyThrows
	public void bodyAsFile_archiveOfFilesWithDifferentSchemasSingleSchemas() {
		String partialUrl = "http://localhost:" + wireMockServer.port();
		var zipFile = IOUtils.toByteArray(this.getClass().getClassLoader().getResourceAsStream("4files-20records-each-different-schemas.zip"));

		MappingBuilder builder = get(urlEqualTo("/file"))
				.withRequestBody(new AnythingPattern())
				.willReturn(aResponse()
						.withStatus(200)
						.withBody(zipFile)
						.withHeader("Content-Type", "application/octet-stream"));

		stubFor(builder);

		Map<String, String> restCaller3 = new HashMap<>() {{
			put(KEY, "step3");
			put(METHOD, "GET");
			put(URL_TEMPLATE, partialUrl + "/file");
			put(FILE_RESPONSE_FORMAT, "zip");
			put(POLL_MS, "1");
			put(SCHEMA_DETECTION_ONCE, "true");
			put(ITERATION_TYPE, BODY_AS_FILE_ITERATION.property);
		}};
		Map<String, String> props = buildByCallersConfig(restCaller3);


		when(adminApiClientMock.findOrCreateDataSet(any()))
				.thenReturn(
						new FindOrCreateDataSetResult(CREATED, 1),
						new FindOrCreateDataSetResult(CREATED, 2),
						new FindOrCreateDataSetResult(CREATED, 3),
						new FindOrCreateDataSetResult(CREATED, 4)
				);


		SchemaDetectionUtils schemaDetection = new SchemaDetectionUtils(
				10, adminApiClientMock, mock(ControlMessageProducer.class),
				Optional.ofNullable(this.topicMetaService), 1, 1, 10, false
		);

		SourceTaskContext sourceTaskContext = mock(SourceTaskContext.class);
		when(sourceTaskContext.offsetStorageReader()).thenReturn(offsetStorageReader);
		RestSourceTask task = new RestSourceTask(schemaDetection) {

			@Override
			protected AdminApiClient getAdminApiClient(String adminApiPrefix, ConnectorAdminConfig adminConfig) {
				return adminApiClientMock;
			}

			@Override
			public Integer getDataSetId() {
				return 1;
			}
		};
		task.controlMessageProducer = mock(ControlMessageProducer.class);
		task.rethrowRetryExceptions = true;
		task.initialize(sourceTaskContext);
		task.config = mock(RestSourceConnectorConfig.class);
		task.logger = mock(NexlaLogger.class);
		this.runBeforeTopicReading = Optional.of(
				new LoggedRunnable(testName.getMethodName(), () -> task.getControlMessageProducer().getTransport().flush())
		);
		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);
		assertEquals(20, sourceRecords.size());
		assertEquals("dataset-1", sourceRecords.get(0).topic());
		sourceRecords = pollNoTrace(task);
		assertEquals(20, sourceRecords.size());
		assertEquals("dataset-1", sourceRecords.get(0).topic());
		sourceRecords = pollNoTrace(task);
		assertEquals(20, sourceRecords.size());
		assertEquals("dataset-1", sourceRecords.get(0).topic());
		sourceRecords = pollNoTrace(task);
		assertEquals(20, sourceRecords.size());
		assertEquals("dataset-1", sourceRecords.get(0).topic());
		sourceRecords = pollNoTrace(task);
		assertEquals(0, sourceRecords.size());

		assertTrue(schemaDetection.getDataSetService().singleSchemaMode());
		task.stop();
	}

	@Test
	public void emitOnce0() {
		String partialUrl = "http://localhost:" + wireMockServer.port();

		List<LinkedHashMap<Object, Object>> data = List.of(
				lhm("a", "1", "b", "2", "c", "3"),
				lhm("a", "4", "b", "5", "c", "6")
		);

		createMapping("/json?batch=1", JsonUtils.toJsonString(data));

		Map<String, String> config = new HashMap<>() {{
			put(KEY, "step1");
			put(METHOD, "GET");
			put(BATCH_SIZE, "1");
			put(URL_TEMPLATE, partialUrl + "/json?batch=1");
			put(POLL_MS, "1");
			put(ITERATION_TYPE, EMIT_ONCE.property);
		}};

		Map<String, String> props = buildByCallersConfig(config);

		task.start(props);

		List<Map<String, Object>> rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("value", data), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(0, rawMessages.size());
		task.stop();
	}

	@Test
	public void emitOnce1() {
		String partialUrl = "http://localhost:" + wireMockServer.port();

		List<LinkedHashMap<Object, Object>> data = List.of(
				lhm("a", "1", "b", "2", "c", "3"),
				lhm("a", "4", "b", "5", "c", "6")
		);

		createMapping("/json?batch=1", JsonUtils.toJsonString(data));

		Map<String, String> config = new HashMap<>() {{
			put(KEY, "step1");
			put(METHOD, "GET");
			put(BATCH_SIZE, "1");
			put(URL_TEMPLATE, partialUrl + "/json?batch=1");
			put(RESPONSE_DATA, "{}");
			put(POLL_MS, "1");
			put(ITERATION_TYPE, EMIT_ONCE.property);
		}};

		Map<String, String> props = buildByCallersConfig(config);

		task.start(props);

		List<Map<String, Object>> rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(Map.of(), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(0, rawMessages.size());
		task.stop();
	}

	@Test
	public void emitOnce2() {
		String partialUrl = "http://localhost:" + wireMockServer.port();

		List<LinkedHashMap<Object, Object>> data = List.of(
				lhm("a", "1", "b", "2", "c", "3"),
				lhm("a", "4", "b", "5", "c", "6")
		);

		createMapping("/json?batch=1", JsonUtils.toJsonString(data));

		Map<String, String> config = new HashMap<>() {{
			put(KEY, "step1");
			put(METHOD, "GET");
			put(BATCH_SIZE, "1");
			put(URL_TEMPLATE, partialUrl + "/json?batch=1");
			put(RESPONSE_DATA, "{\"page\": [1, 2, 3, 4]}");
			put(RESPONSE_DATA_PATH, "$.page[*]");
			put(POLL_MS, "1");
			put(ITERATION_TYPE, EMIT_ONCE.property);
		}};

		Map<String, String> props = buildByCallersConfig(config);

		task.start(props);

		List<Map<String, Object>> rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(4, rawMessages.size());
		assertEquals(List.of(
				lhm("page", 1),
				lhm("page", 2),
				lhm("page", 3),
				lhm("page", 4)
		), rawMessages);

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(0, rawMessages.size());
		task.stop();
	}

	@Test
	public void twoStepWithBodyAsFileWithPaging() {
		String partialUrl = "http://localhost:" + wireMockServer.port();

		stubFor(
				get(urlEqualTo("/csv?batch=1"))
						.withRequestBody(new AnythingPattern())
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody("a,b,c\n1,2,3\n4,5,6")
										.withHeader("cont_token", "CONT+TOKEN+VALUE")
						)
		);

		stubFor(
				get(urlEqualTo("/csv?batch=2"))
						.withRequestBody(new AnythingPattern())
						.withHeader("token", new EqualToPattern("CONT+TOKEN+VALUE"))
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody("a,b,c\n7,8,9\n10,11,12")
										.withHeader("cont_token", "CONT+TOKEN+VALUE")
						)
		);
		stubFor(
				get(urlEqualTo("/csv?batch=3"))
						.withRequestBody(new AnythingPattern())
						.withHeader("token", new EqualToPattern("CONT+TOKEN+VALUE"))
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody((String) null)
										.withHeader("cont_token", "CONT+TOKEN+VALUE")
						)
		);

		Map<String, String> it1 = new HashMap<>() {{
			put(KEY, "step1");
			put(METHOD, "GET");
			put(BATCH_SIZE, "1");
			put(RESPONSE_DATA, "{}");
			put(URL_TEMPLATE, partialUrl + "/csv?batch=1");
			put(POLL_MS, "1");
			put(ITERATION_TYPE, EMIT_ONCE.property);
		}};

		Map<String, String> it2 = new HashMap<>() {{
			put(KEY, "step2");
			put(METHOD, "GET");
			put(BATCH_SIZE, "1");
			put(PAGE_PARAM_NAME, "batch");
			put(START_PAGE_FROM, "1");
			put(URL_TEMPLATE, partialUrl + "/csv");
			put(FILE_RESPONSE_FORMAT, "csv");
			put(REQUEST_HEADERS, "token: {step1.nx.header.cont_token}");
			put(POLL_MS, "1");
			put(MODE, PAGING_MODE);
			put(ITERATION_TYPE, BODY_AS_FILE_ITERATION.property);
		}};

		Map<String, String> props = buildByCallersConfig(it1, it2);

		task.start(props);

		List<Map<String, Object>> rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "1", "b", "2", "c", "3"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "4", "b", "5", "c", "6"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "7", "b", "8", "c", "9"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "10", "b", "11", "c", "12"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(0, rawMessages.size());
		task.stop();
	}

	@Test
	public void headerCapture() {
		String partialUrl = "http://localhost:" + wireMockServer.port();

		stubFor(
				get(urlEqualTo("/csv?batch=1"))
						.withRequestBody(new AnythingPattern())
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody("a,b,c\n1,2,3\n4,5,6")
										.withHeader("cont_token", "CONT+TOKEN+VALUE-batch-1")
						)
		);

		stubFor(
				get(urlEqualTo("/csv?batch=2"))
						.withRequestBody(new AnythingPattern())
						.withHeader("token", new EqualToPattern("CONT+TOKEN+VALUE-batch-1"))
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody("a,b,c\n7,8,9\n10,11,12")
										.withHeader("cont_token", "CONT+TOKEN+VALUE-batch-2")
						)
		);
		stubFor(
				get(urlEqualTo("/csv?batch=3"))
						.withRequestBody(new AnythingPattern())
						.withHeader("token", new EqualToPattern("CONT+TOKEN+VALUE-batch-2"))
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody((String) null)
										.withHeader("cont_token", "CONT+TOKEN+VALUE-batch-3")
						)
		);

		Map<String, String> it1 = new HashMap<>() {{
			put(KEY, "step1");
			put(METHOD, "GET");
			put(BATCH_SIZE, "1");
			put(PAGE_PARAM_NAME, "batch");
			put(START_PAGE_FROM, "1");
			put(URL_TEMPLATE, partialUrl + "/csv");
			put(FILE_RESPONSE_FORMAT, "csv");
			put(SKIP_EMPTY_HEADERS, "true");
			put(REQUEST_HEADERS, "token: {previous.nx.header.cont_token}");
			put(POLL_MS, "1");
			put(MODE, PAGING_MODE);
			put(ITERATION_TYPE, BODY_AS_FILE_ITERATION.property);
		}};

		Map<String, String> props = buildByCallersConfig(it1);

		task.start(props);

		List<Map<String, Object>> rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "1", "b", "2", "c", "3"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "4", "b", "5", "c", "6"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "7", "b", "8", "c", "9"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(1, rawMessages.size());
		assertEquals(lhm("a", "10", "b", "11", "c", "12"), rawMessages.get(0));

		rawMessages = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(0, rawMessages.size());
		task.stop();
	}

	@Test
	@SneakyThrows
	public void zip_file() {
		String partialUrl = "http://localhost:" + wireMockServer.port();

		String xmlReportStarted = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
				"<BillingReportCreationResponse>" +
				"<ReportStatus>RECEIVED</ReportStatus>" +
				"<Description>The report archive is currently being constructed.</Description>" +
				"</BillingReportCreationResponse>";

		var zipFile = IOUtils.toByteArray(this.getClass().getClassLoader().getResourceAsStream("zip.zip"));

		MappingBuilder builder = get(urlEqualTo("/file"))
				.withRequestBody(new AnythingPattern())
				.willReturn(aResponse()
						.withStatus(200)
						.withBody(zipFile)
						.withHeader("Content-Type", "application/octet-stream"));

		stubFor(builder);

		Map<String, String> restCaller3 = new HashMap<>() {{
			put(KEY, "step3");
			put(METHOD, "GET");
			put(URL_TEMPLATE, partialUrl + "/file");
			put(FILE_RESPONSE_FORMAT, "zip");
			put(POLL_MS, "1");
			put(ITERATION_TYPE, BODY_AS_FILE_ITERATION.property);
		}};
		Map<String, String> props = buildByCallersConfig(restCaller3);

		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);
		List<NexlaMessage> data = toMessages(sourceRecords);
		List<Map<String, Object>> rawMessages = toRawMessages(data);
		assertEquals(1, rawMessages.size());
		var results = Sets.newHashSet(rawMessages.get(0));

		sourceRecords = pollNoTrace(task);
		data = toMessages(sourceRecords);
		rawMessages = toRawMessages(data);
		assertEquals(1, rawMessages.size());
		results.add(rawMessages.get(0));

		sourceRecords = pollNoTrace(task);
		data = toMessages(sourceRecords);
		rawMessages = toRawMessages(data);
		assertEquals(1, rawMessages.size());
		results.add(rawMessages.get(0));

		assertEquals(results, Sets.newHashSet(lhm("a", "a"), lhm("b", "b"), lhm("c", "c")));
		task.stop();
	}

	@Test
	public void poll_ModeEntireDataset_jsonPath() {

		createMapping("/te%20st", "{\"values\" : [{ \"id\": \"1\"}, {\"id\": \"2\"} ] }");
		String partialUrl = "http://localhost:" + wireMockServer.port();

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, partialUrl + "/te st");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.values[*]");
			put(POLL_MS, "1");
			put(ITERATION_TYPE, IterationType.STATIC_URL.property);
		}});
		props.put(REQUEST_HEADERS, "header:value1:value2");
		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);
		List<NexlaMessage> data = toMessages(sourceRecords);
		List<Map<String, Object>> rawMessages = toRawMessages(data);

		assertTopicName(sourceRecords);

		assertEquals(
				asList(
						of("id", "1"),
						of("id", "2")),
				rawMessages);

		assertTrackerId(data.get(0), 1L, partialUrl + "/te%20st");

		StringValuePattern valuePattern = new EqualToPattern("value1:value2");
		verify(getRequestedFor(urlEqualTo("/te%20st")).withHeader("header", valuePattern));
		task.stop();
	}

	@Test
	public void poll_ModeEntireDataset_jsonPath_incorrectJson() {
		int sourceId = testSourceId;
		withConsumer((notifyTopic, notifyConsumer) -> withConsumer((metricsTopic, metricsConsumer) -> {
			createMapping("/test", "{\"error");
			Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
				put(KEY, "KEY");
				put(AUTH_TYPE, AuthType.NONE.name());
				put(METHOD, "GET");
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
				put(RESPONSE_FORMAT, "json");
				put(RESPONSE_DATA_PATH, "$.values[*]");
				put(POLL_MS, "1");
				put(ITERATION_TYPE, IterationType.STATIC_URL.property);
			}});
			props.put(SOURCE_ID, sourceId + "");
			props.put(METRICS_TOPIC, metricsTopic);
			props.put(NOTIFY_TOPIC, notifyTopic);

			try {
				task.start(props);

				task.collectRecords();
				List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sourceId);
				Assert.assertEquals(0, metrics.get(0).getFields().get("records"));
				Assert.assertEquals(1, metrics.get(0).getFields().get("error_count"));

				List<NexlaNotificationEvent> notifications = readNotifications(notifyConsumer);
				assertFalse(notifications.isEmpty());
				Assert.assertTrue(notifications.get(0).getContext().getErrorMessage().contains("Parser is stopped"));
			} finally {
				task.stop();
			}
		}, "metrics"), "notify");
	}

	@Test
	public void poll_Mode_LinkHeader() {

		String partialUrl = "http://localhost:" + wireMockServer.port();

		MappingBuilder builder = get(urlEqualTo("/test1"))
				.willReturn(aResponse()
						.withStatus(200)
						.withHeader("Link",
								"<" + partialUrl + "/test1&page=2>; rel=\"next\", " +
										"<" + partialUrl + "/test1&page=3>; rel=\"last\"")
						.withBody("{\"values\" : [{ \"id\": \"1\"}, {\"id\": \"2\"} ] }"));
		stubFor(builder);

		builder = get(urlEqualTo("/test1&page=2"))
				.willReturn(aResponse()
						.withStatus(200)
						.withHeader("Link",
								"<" + partialUrl + "/test1&page=3>; rel=\"next\", " +
										"<" + partialUrl + "/test1&page=3>; rel=\"last\"")
						.withBody("{\"values\" : [{ \"id\": \"3\"}, {\"id\": \"4\"} ] }"));
		stubFor(builder);

		builder = get(urlEqualTo("/test1&page=3"))
				.willReturn(aResponse()
						.withStatus(200)
						.withHeader("Link",
								"<" + partialUrl + "/test1&page=2>; rel=\"prev\"")
						.withBody("{\"values\" : [{ \"id\": \"5\"}, {\"id\": \"6\"} ] }"));
		stubFor(builder);

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, partialUrl + "/test1");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.values[*]");
			put(POLL_MS, "1");
			put(ITERATION_TYPE, IterationType.LINK_HEADER.property);
		}});
		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);
		List<NexlaMessage> data = toMessages(sourceRecords);
		List<Map<String, Object>> rawMessages = toRawMessages(data);

		assertTopicName(sourceRecords);
		assertEquals(
				asList(
						of("id", "1"),
						of("id", "2")),
				rawMessages);

		List<Map<String, Object>> rawMessages2 = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(
				asList(
						of("id", "3"),
						of("id", "4")),
				rawMessages2);

		List<Map<String, Object>> rawMessages3 = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(
				asList(
						of("id", "5"),
						of("id", "6")),
				rawMessages3);

		List<Map<String, Object>> rawMessages4 = toRawMessages(toMessages(pollNoTrace(task)));
		assertTrue(rawMessages4.isEmpty());
		task.stop();
	}

	@SneakyThrows
	@Test
	public void poll_xml_post_intuit() {
		String response = IOUtils.toString(getClass().getResourceAsStream("/intuitResponse.xml"), "UTF-8");
		stubFor(get(urlEqualTo("/test"))
				.willReturn(aResponse()
						.withStatus(200)
						.withHeader("Content-Type", "application/xml")
						.withBody(response)));

		stubFor(get(urlEqualTo("/test2?id=4"))
				.willReturn(aResponse()
						.withStatus(200)
						.withHeader("Content-Type", "application/xml")
						.withBody(
								"<values>" +
										"<item><id>1</id></item>" +
										"<item><id>2</id></item>" +
										"</values>")));

		Map<String, String> step1 = new HashMap<String, String>() {{
			put(KEY, "step1");
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(RESPONSE_FORMAT, "xml");
			put(RESPONSE_DATA_PATH, "IntuitResponse/CDCResponse/QueryResponse/Customer");
			put(ITERATION_TYPE, IterationType.STATIC_URL.property);
		}};

		Map<String, String> step2 = new HashMap<String, String>() {{
			put(KEY, "step2");
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test2?id={step1.Customer.Id}");
			put(RESPONSE_FORMAT, "xml");
			put(RESPONSE_DATA_PATH, "/values/item/*");
			put(ITERATION_TYPE, IterationType.STATIC_URL.property);
		}};

		Map<String, String> props = buildByCallersConfig(step1, step2);
		props.put(AUTH_TYPE, AuthType.NONE.name());

		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);
		List<NexlaMessage> data = toMessages(sourceRecords);
		List<Map<String, Object>> rawMessages = toRawMessages(data);

		assertEquals(
				asList(
						of("id", "1"),
						of("id", "2")),
				rawMessages);
		task.stop();
	}

	@Test
	public void poll_ModeEntireDataset_xmlPath_basicAuthentication() {

		stubFor(get(urlEqualTo("/test"))
				.withBasicAuth("test_user", "qwerty")
				.willReturn(aResponse()
						.withStatus(200)
						.withHeader("Content-Type", "application/xml")
						.withBody(
								"<values>" +
										"<item><id>1</id></item>" +
										"<item><id>2</id></item>" +
										"</values>")));

		stubFor(get(urlEqualTo("/test2?id=1"))
				.withBasicAuth("test_user", "qwerty")
				.willReturn(aResponse()
						.withStatus(200)
						.withHeader("Content-Type", "application/xml")
						.withBody(
								"<values>" +
										"<item><id>1_1</id></item>" +
										"<item><id>2_1</id></item>" +
										"</values>")));

		stubFor(get(urlEqualTo("/test2?id=2"))
				.withBasicAuth("test_user", "qwerty")
				.willReturn(aResponse()
						.withStatus(200)
						.withHeader("Content-Type", "application/xml")
						.withBody(
								"<values>" +
										"<item><id>1_2</id></item>" +
										"<item><id>2_2</id></item>" +
										"</values>")));

		Map<String, String> step1 = new HashMap<String, String>() {{
			put(KEY, "step1");
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(RESPONSE_FORMAT, "xml");
			put(RESPONSE_DATA_PATH, "/values/item/*");
			put(ITERATION_TYPE, IterationType.STATIC_URL.property);
		}};
		String partialUrl = "http://localhost:" + wireMockServer.port() + "/test2";

		Map<String, String> step2 = new HashMap<String, String>() {{
			put(KEY, "step2");
			put(METHOD, "GET");
			put(URL_TEMPLATE, partialUrl + "?id={step1.id}");
			put(RESPONSE_FORMAT, "xml");
			put(RESPONSE_DATA_PATH, "/values/item/*");
			put(ITERATION_TYPE, IterationType.STATIC_URL.property);
		}};

		Map<String, String> props = buildByCallersConfig(step1, step2);
		props.put(AUTH_TYPE, AuthType.BASIC.name());
		props.put(BASIC_USERNAME, "test_user");
		props.put(BASIC_PASSWORD, "qwerty");

		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);
		List<NexlaMessage> data = toMessages(sourceRecords);
		List<Map<String, Object>> rawMessages = toRawMessages(data);

		assertTopicName(sourceRecords);
		assertEquals(
				asList(
						of("id", "1_1"),
						of("id", "2_1")),
				rawMessages);
		assertTrackerId(data.get(0), 1L, partialUrl + "?id=1");
		assertTrackerId(data.get(1), 2L, partialUrl + "?id=1");

		List<Map<String, Object>> rawMessages2 = toRawMessages(toMessages(pollNoTrace(task)));
		assertEquals(
				asList(
						of("id", "1_2"),
						of("id", "2_2")),
				rawMessages2);
		task.stop();
	}

	@Test
	public void poll_jwt_auth_basic() {

		StubMapping stubAuth = stubFor(post(urlEqualTo("/access_token"))
				.withBasicAuth("test_user", "qwerty")
				.withRequestBody(new AnythingPattern())
				.willReturn(aResponse()
						.withStatus(200)
						.withHeader("Content-Type", "application/json")
						.withBody(
								"{\n" +
										"\"access_token\": 11111, " +
										"\"expires_in\": 100, " +
										"\"token_type\": \"Bearer\"\n" +
										"}\n")));

		StubMapping stubData = stubFor(
				get(urlEqualTo("/test?pageNumber=1&pageSize=1"))
						.withHeader("Authorization", equalTo("Bearer 11111"))
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody("{ \"success1\": true }")));

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(PAGE_EXPECTED_ROWS, "1");

			put(PARAM_ID, "pageNumber");
			put(PARAM_PAGE_SIZE, "pageSize");
			put(START_PAGE_FROM, "1");

			put(RESPONSE_FORMAT, "json");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
		}});

		props.put(JWT_ENABLED, "true");
		props.put(JWT_HTTP_URL, "http://localhost:" + wireMockServer.port() + "/access_token");
		props.put(JWT_RESPONSE_TOKEN_PATH, "$.access_token");
		props.put(JWT_RESPONSE_TOKEN_TYPE_PATH, "$.token_type");
		props.put(JWT_TOKEN_SECRET, "secret");
		props.put(JWT_CLAIM_AUDIENCE, "audience");
		props.put(JWT_CLAIM_EXPIRATION_SEC, "100");
		props.put(JWT_CLAIM_ISSUER, "issuer");
		props.put(JWT_CLAIM_SCOPE, "scope");
		props.put(JWT_CONTENT_TYPE, APPLICATION_JSON.toString());

		props.put(AUTH_TYPE, AuthType.BASIC.name());
		props.put(BASIC_USERNAME, "test_user");
		props.put(BASIC_PASSWORD, "qwerty");

		task.start(props);

		assertEquals(asList(of("success1", true)), toRawMessages(toMessages(pollNoTrace(task))));
		wireMockServer.removeStub(stubAuth);
		wireMockServer.removeStub(stubData);

		stubFor(post(urlEqualTo("/access_token"))
				.withBasicAuth("test_user", "qwerty")
				.withRequestBody(new AnythingPattern())
				.willReturn(aResponse()
						.withStatus(200)
						.withHeader("Content-Type", "application/json")
						.withBody(
								"{\n" +
										"\"access_token\": 22222, " +
										"\"expires_in\": 100, " +
										"\"token_type\": \"Bearer\"\n" +
										"}\n")));

		stubFor(
				get(urlEqualTo("/test?pageNumber=2&pageSize=1"))
						.withHeader("Authorization", equalTo("Bearer 11111"))
						.willReturn(
								aResponse()
										.withStatus(401)));

		stubFor(
				get(urlEqualTo("/test?pageNumber=2&pageSize=1"))
						.withHeader("Authorization", equalTo("Bearer 22222"))
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody("{ \"success2\": true }")));

		assertEquals(asList(of("success2", true)), toRawMessages(toMessages(pollNoTrace(task))));
		task.stop();
	}

	@Test
	public void poll_token_auth_plain_url() {
		stubFor(post(urlEqualTo("/access_token"))
				.withHeader("Aaa", new EqualToPattern("BbbBbb"))
				.withRequestBody(new EqualToPattern("grant_type=password&username=<EMAIL>&password=passwordtoken"))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody("{ \"token\": \"accesstoken\" }")));

		stubFor(post(urlEqualTo("/test"))
				.withHeader("Blabla", new EqualToPattern("Blah accesstoken"))
				.withRequestBody(new EqualToPattern("message"))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody("{ \"success\": true }")));

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(METHOD, "POST");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(BODY_TEMPLATE, "message");
			put(RESPONSE_FORMAT, "json");
			put(ITERATION_TYPE, IterationType.STATIC_URL.property);
		}});

		props.put(AUTH_TYPE, AuthType.TOKEN.name());
		props.put(TOKEN_AUTH_HTTP_URL, "http://localhost:" + wireMockServer.port() + "/access_token");
		props.put(TOKEN_AUTH_HTTP_BODY, "grant_type=password&username=<EMAIL>&password=passwordtoken");
		props.put(TOKEN_AUTH_RESPONSE_TOKEN_PATH, "$.token");
		props.put(BASIC_USERNAME, "test_user");
		props.put(BASIC_PASSWORD, "qwerty");

		props.put(TOKEN_AUTH_TOKEN_HEADER_NAME, "Aaa");
		props.put(TOKEN_AUTH_TOKEN_HEADER_VALUE, "BbbBbb");
		props.put(TOKEN_AUTH_REQUEST_HEADER_NAME, "Blabla");
		props.put(TOKEN_AUTH_REQUEST_HEADER_PREFIX, "Blah");

		task.start(props);

		assertEquals(asList(of("success", true)), toRawMessages(toMessages(pollNoTrace(task))));

		verify(postRequestedFor(
				urlEqualTo("/access_token"))
				.withHeader("Aaa", new EqualToPattern("BbbBbb"))
				.withRequestBody(equalTo("grant_type=password&username=<EMAIL>&password=passwordtoken")));
		task.stop();
	}

	@Test
	public void poll_token_folded_post() {

		stubFor(post(urlEqualTo("/access_token"))
				.withHeader("Aaa", new EqualToPattern("Basic dGVzdF91c2VyOnF3ZXJ0eQ=="))
				.withRequestBody(new EqualToPattern("grant_type=password&username=<EMAIL>&password=passwordtoken"))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody("{ \"token\": \"accesstoken\" }")));

		stubFor(post(urlEqualTo("/test1"))
				.withHeader("Blabla", new EqualToPattern("Blah accesstoken"))
				.withRequestBody(new EqualToPattern("{\"countryCode\":\"US\", \"now\": \"2018-04-16\"}"))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody("{ \"campaignId\": \"1\", \"startDate\": \"01-02-03\" }")));

		stubFor(post(urlEqualTo("/test2"))
				.withHeader("Blabla", new EqualToPattern("Blah accesstoken"))
				.withRequestBody(new EqualToPattern("{\"countryCode\":\"US\",\"campaignId\":\"{step1.campaignId}\", \"date\":\"{step1.startDate}\"}"))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody("{ \"success\": true }")));

		Map<String, String> restCaller1 = new HashMap<String, String>() {{
			put(KEY, "step1");
			put(METHOD, "POST");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test1");
			put(RESPONSE_DATA_PATH, "$.*");
			put(ITERATION_TYPE, IterationType.STATIC_URL.property);
			put(BODY_TEMPLATE, "{\"countryCode\":\"US\", \"now\": \"{now+1}\"}");
		}};

		Map<String, String> restCaller2 = new HashMap<String, String>() {{
			put(KEY, "step2");
			put(METHOD, "POST");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test2");
			put(ITERATION_TYPE, IterationType.STATIC_URL.property);
			put(BODY_TEMPLATE, "{\"countryCode\":\"US\",\"campaignId\":\"{step1.campaignId}\", \"date\":\"{step1.startDate}\"}");
		}};
		Map<String, String> props = buildByCallersConfig(restCaller1, restCaller2);

		props.put(AUTH_TYPE, AuthType.TOKEN.name());
		props.put(TOKEN_AUTH_HTTP_URL, "http://localhost:" + wireMockServer.port() + "/access_token");
		props.put(TOKEN_AUTH_HTTP_BODY, "grant_type=password&username=<EMAIL>&password=passwordtoken");
		props.put(TOKEN_AUTH_RESPONSE_TOKEN_PATH, "$.token");
		props.put(BASIC_USERNAME, "test_user");
		props.put(BASIC_PASSWORD, "qwerty");

		props.put(TOKEN_AUTH_TOKEN_HEADER_NAME, "Aaa");
		props.put(TOKEN_AUTH_REQUEST_HEADER_NAME, "Blabla");
		props.put(TOKEN_AUTH_REQUEST_HEADER_PREFIX, "Blah");

		task.start(props);

		assertEquals(asList(of("success", true)), toRawMessages(toMessages(pollNoTrace(task))));
		task.stop();
	}

	@Test
	public void poll_jwt_plain_url() {

		stubFor(post(urlEqualTo("/access_token"))
				.withBasicAuth("test_user", "qwerty")
				.withRequestBody(new AnythingPattern())
				.willReturn(aResponse()
						.withStatus(200)
						.withHeader("Content-Type", "application/json")
						.withBody("11111")));

		stubFor(
				get(urlEqualTo("/test?pageNumber=1&pageSize=1&jwt=11111"))
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody("{ \"success1\": true }")));

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(PAGE_EXPECTED_ROWS, "1");

			put(PARAM_ID, "pageNumber");
			put(PARAM_PAGE_SIZE, "pageSize");
			put(START_PAGE_FROM, "1");

			put(RESPONSE_FORMAT, "json");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
		}});

		props.put(JWT_ENABLED, "true");
		props.put(JWT_HTTP_URL, "http://localhost:" + wireMockServer.port() + "/access_token");
		props.put(JWT_RESPONSE_TOKEN_PATH, "$.access_token");
		props.put(JWT_RESPONSE_TOKEN_TYPE_PATH, "$.token_type");
		props.put(JWT_TOKEN_SECRET, "secret");
		props.put(JWT_CLAIM_AUDIENCE, "audience");
		props.put(JWT_CLAIM_EXPIRATION_SEC, "100");
		props.put(JWT_CLAIM_ISSUER, "issuer");
		props.put(JWT_CLAIM_SCOPE, "scope");
		props.put(JWT_TOKEN_INCLUDE_MODE, "url_parameter");
		props.put(JWT_TOKEN_URL_PARAMETER, "jwt");
		props.put(JWT_RESPONSE_FORMAT, "plain");
		props.put(JWT_CONTENT_TYPE, APPLICATION_JSON.toString());

		props.put(AUTH_TYPE, AuthType.BASIC.name());
		props.put(BASIC_USERNAME, "test_user");
		props.put(BASIC_PASSWORD, "qwerty");

		task.start(props);

		assertEquals(asList(of("success1", true)), toRawMessages(toMessages(pollNoTrace(task))));
		task.stop();
	}

	@Test
	public void poll_oauth2WithBasic() {

		stubFor(
				get(urlEqualTo("/access_token?grant_type=client_credentials&scope=read%20write"))
						.withBasicAuth("user", "pass")
						.willReturn(aResponse()
								.withStatus(200)
								.withHeader("Content-Type", "application/json")
								.withBody(
										"{\n" +
												"    \"issued_at\": \"1516037183910\",\n" +
												"    \"scope\": \"\",\n" +
												"    \"expires_in\": \"3599\",\n" +
												"    \"token_type\": \"BearerToken\",\n" +
												"    \"access_token\": \"vDuYBZsvOGrAMh2WAWmvWYma1fZA\",\n" +
												"    \"refresh_token_expires_in\": \"0\"\n" +
												"}")));

		stubFor(
				post(urlEqualTo("/files"))
						.withHeader("Authorization", equalTo("Bearer vDuYBZsvOGrAMh2WAWmvWYma1fZA"))
						.willReturn(
								aResponse()
										.withStatus(200)
										.withBody("{ \"success\": true }")));

		String url = "http://localhost:" + wireMockServer.port() + "/files";

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(METHOD, "POST");
			put(URL_TEMPLATE, url);
			put(ITERATION_TYPE, IterationType.STATIC_URL.property);
		}});
		props.put(AUTH_TYPE, AuthType.OAUTH2.name());
		props.put(OAUTH2_ACCESS_TOKEN_URL, "http://localhost:" + wireMockServer.port() + "/access_token");
		props.put(OAUTH2_CLIENT_ID, "user");
		props.put(OAUTH2_CLIENT_SECRET, "pass");
		props.put(OAUTH2_TOKEN_TYPE_OVERRIDE, "Bearer");
		props.put(OAUTH2_ACCESS_TOKEN_METHOD, "GET");

		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);
		List<NexlaMessage> data = toMessages(sourceRecords);
		List<Map<String, Object>> rawMessages = toRawMessages(data);

		assertTopicName(sourceRecords);
		assertEquals(asList(of("success", true)), rawMessages);

		assertTrackerId(data.get(0), 1L, url);
		task.stop();
	}

	@SneakyThrows
	@Test
	public void poll_oauth2AuthAndSkipUrlEncode() {
		stubFor(
				get(urlEqualTo("/access_token?grant_type=client_credentials&scope=read%20write"))
						.withBasicAuth("user", "pass")
						.willReturn(aResponse()
								.withStatus(200)
								.withHeader("Content-Type", "application/json")
								.withBody(
										"{\n" +
												"    \"issued_at\": \"1516037183910\",\n" +
												"    \"scope\": \"\",\n" +
												"    \"expires_in\": \"3599\",\n" +
												"    \"token_type\": \"BearerToken\",\n" +
												"    \"access_token\": \"vDuYBZsvOGrAMh2WAWmvWYma1fZA\",\n" +
												"    \"refresh_token_expires_in\": \"0\"\n" +
												"}")));

		stubFor(get("/foo?q=1,2,3")
				.withHeader("Authorization", equalTo("Bearer vDuYBZsvOGrAMh2WAWmvWYma1fZA"))
				.willReturn(aResponse().withStatus(200).withBody("{\"success\": true}")));

		// test with skip url encoding
		Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
            put(KEY, "KEY");
            put(METHOD, "GET");
            put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/foo?q=1,2,3");
            put(ITERATION_TYPE, IterationType.STATIC_URL.property);
			put(RestConfigAccessor.SKIP_URL_ENCODING, "true");
        }});
		props.put(AUTH_TYPE, AuthType.OAUTH2.name());
		props.put(OAUTH2_ACCESS_TOKEN_URL, "http://localhost:" + wireMockServer.port() + "/access_token");
		props.put(OAUTH2_CLIENT_ID, "user");
		props.put(OAUTH2_CLIENT_SECRET, "pass");
		props.put(OAUTH2_TOKEN_TYPE_OVERRIDE, "Bearer");
		props.put(OAUTH2_ACCESS_TOKEN_METHOD, "GET");
		// check that verbose logging still allows the source to return data
		// NEX-16454 RestTemplateLoggerInterceptor was consuming the InputStream returned by response.getBody()
		props.put(LOG_VERBOSE, "true");

		task.start(props);

		List<SourceRecord> sourceRecords = pollNoTrace(task);

		Assert.assertEquals(1, sourceRecords.size());
		NexlaMessage message = OBJECT_MAPPER.readValue((String) sourceRecords.get(0).value(), NexlaMessage.class);
		Assert.assertTrue(message.getNexlaMetaData().getSourceKey().endsWith("q=1,2,3"));

		task.stop();

		// test without skip url encoding
		props = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/foo?q=1,2,3");
			put(ITERATION_TYPE, IterationType.STATIC_URL.property);
			// SKIP_URL_ENCODING has a default value of false
		}});
		props.put(AUTH_TYPE, AuthType.OAUTH2.name());
		props.put(OAUTH2_ACCESS_TOKEN_URL, "http://localhost:" + wireMockServer.port() + "/access_token");
		props.put(OAUTH2_CLIENT_ID, "user");
		props.put(OAUTH2_CLIENT_SECRET, "pass");
		props.put(OAUTH2_TOKEN_TYPE_OVERRIDE, "Bearer");
		props.put(OAUTH2_ACCESS_TOKEN_METHOD, "GET");

		task.start(props);

		sourceRecords = pollNoTrace(task);

		Assert.assertEquals(1, sourceRecords.size());
		message = OBJECT_MAPPER.readValue((String) sourceRecords.get(0).value(), NexlaMessage.class);
		Assert.assertTrue(message.getNexlaMetaData().getSourceKey().endsWith("q=1%2C2%2C3"));

		task.stop();
	}

	@Test
	public void poll_responseIdIncrementing() {

		createMapping("/test", "{ \"response\": [ {\"id\": \"1\", \"data\": \"right data\"}] } ");
		createMapping("/test?idFrom=2", "{ \"response\": [ {\"id\": \"2\", \"data\": \"wrong data\"}] } ");
		String url = "http://localhost:" + wireMockServer.port() + "/test";

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, url);
			put(PARAM_ID, "idFrom");
			put(RESPONSE_ID_FIELD_NAME, "id");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(ITERATION_TYPE, IterationType.RESPONSE_ID_NUMBER.property);
		}});

		task.start(props);

		List<SourceRecord> firstRunRecords = pollNoTrace(task);
		assertTopicName(firstRunRecords);
		List<NexlaMessage> data = toMessages(firstRunRecords);
		assertEquals(singletonList((of("id", "1", "data", "right data"))), toRawMessages(data));
		assertTrackerId(data.get(0), 1L, url);

		List<SourceRecord> secondRunRecords = pollNoTrace(task);
		assertTopicName(secondRunRecords);
		data = toMessages(secondRunRecords);
		assertEquals(singletonList((of("id", "2", "data", "wrong data"))), toRawMessages(data));
		assertTrackerId(data.get(0), 1L, url + "?idFrom=2");
		task.stop();
	}

	@Test
	public void poll_responseIdIncrementing_macro() {
		final String urlTemplate = "http://localhost:" + wireMockServer.port() + "/test?idFrom+{page_number_value}";
		Map<String, String> propsWithoutStartIncrementingIdFrom = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, urlTemplate);
			put(PARAM_ID_MACRO, "page_number_value");
//			put(START_ID_FROM, "0"); // required field not set intentionally
			put(RESPONSE_ID_FIELD_NAME, "id");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(ITERATION_TYPE, IterationType.RESPONSE_ID_NUMBER.property);
		}});

		assertThatThrownBy(() ->  task.start(propsWithoutStartIncrementingIdFrom))
				.isInstanceOf(ConfigException.class)
				.hasMessageContaining("start.id.from should be set");

		createMapping("/test?idFrom%201", "{ \"response\": [ {\"id\": \"1\", \"data\": \"right data\"}] } ");
		createMapping("/test?idFrom%202", "{ \"response\": [ {\"id\": \"2\", \"data\": \"wrong data\"}] } ");

		Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, urlTemplate);
			put(PARAM_ID_MACRO, "page_number_value");
			put(START_ID_FROM, "0");
			put(RESPONSE_ID_FIELD_NAME, "id");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(ITERATION_TYPE, IterationType.RESPONSE_ID_NUMBER.property);
		}});

		task.start(props);

		List<SourceRecord> firstRunRecords = pollNoTrace(task);
		assertTopicName(firstRunRecords);
		List<NexlaMessage> data = toMessages(firstRunRecords);
		assertEquals(singletonList((of("id", "1", "data", "right data"))), toRawMessages(data));
		Map<String, String> replacementsForMacro = new HashMap<>();
		replacementsForMacro.put("{page_number_value}", "1");
		assertTrackerIdMacro(data.get(0), 1L, urlTemplate, replacementsForMacro);

		List<SourceRecord> secondRunRecords = pollNoTrace(task);
		assertTopicName(secondRunRecords);
		data = toMessages(secondRunRecords);
		assertEquals(singletonList((of("id", "2", "data", "wrong data"))), toRawMessages(data));
		replacementsForMacro.put("{page_number_value}", "2");
		assertTrackerIdMacro(data.get(0), 1L, urlTemplate, replacementsForMacro);
	}

	@Test
	public void poll_responseIdIncrementing_limit() {

		createMapping("/test", "{ \"response\": [ {\"id\": \"1\", \"data\": \"right data\"}] } ");
		createMapping("/test?idFrom=2", "{ \"response\": [ {\"id\": \"2\", \"data\": \"wrong data\"}] } ");
		createMapping("/test?idFrom=3", "{ \"response\": [ {\"id\": \"3\", \"data\": \"wrong data\"}] } ");
		String url = "http://localhost:" + wireMockServer.port() + "/test";

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, url);
			put(PARAM_ID, "idFrom");
			put(RESPONSE_ID_FIELD_NAME, "id");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(END_ID_TO, "2");
			put(ITERATION_TYPE, IterationType.RESPONSE_ID_NUMBER.property);
		}});

		task.start(props);

		List<SourceRecord> firstRunRecords = pollNoTrace(task);
		assertTopicName(firstRunRecords);
		List<NexlaMessage> data = toMessages(firstRunRecords);
		assertEquals(singletonList((of("id", "1", "data", "right data"))), toRawMessages(data));
		assertTrackerId(data.get(0), 1L, url);

		List<SourceRecord> secondRunRecords = pollNoTrace(task);
		assertTopicName(secondRunRecords);
		data = toMessages(secondRunRecords);
		assertEquals(singletonList((of("id", "2", "data", "wrong data"))), toRawMessages(data));
		assertTrackerId(data.get(0), 1L, url + "?idFrom=2");

		assertEquals(emptyList(), pollNoTrace(task));
		task.stop();
	}

	@Test
	public void poll_nextToken() {

		createMapping("/t%20%20%20est%60?token=initToken", "{ \"response\": [ {\"id\": \"1\", \"data\": \"first data\"}], \"nextToken\": \"testToken\" } ");
		createMapping("/t%20%20%20est%60?token=testToken", "{ \"response\": [ {\"id\": \"2\", \"data\": \"second data\"}] } ");

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/t %20 est`?token=initToken");
			put(PARAM_ID, "token");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(RESPONSE_NEXT_TOKEN_DATA_PATH, "$.nextToken");
			put(ITERATION_TYPE, IterationType.PAGING_NEXT_TOKEN.property);
		}});

		task.start(props);

		assertEquals(singletonList((of("id", "1", "data", "first data"))), pollData());

		ImmutablePair<List<Map<String, Object>>, Optional<SourceRecord>> pollData2 = pollDataWithOffset();
		assertEquals(singletonList((of("id", "2", "data", "second data"))), pollData2.left);

		Map<String, Object> stringMap = (Map) pollData2.right.get().sourceOffset();
		when(offsetStorageReader.offset(anyMap())).thenReturn(stringMap);

		task.start(props);

		assertEquals(singletonList((of("id", "1", "data", "first data"))), pollData());
		task.stop();
		task.stop();
	}

	@Test
	public void poll_nextToken_macro() {
		createMapping("/t%20%20%20est%60", "{ \"response\": [ {\"id\": \"0\", \"data\": \"init data\"}], \"nextToken\": \"initToken\" } ");
		createMapping("/t%20%20%20est%60?token=initToken", "{ \"response\": [ {\"id\": \"1\", \"data\": \"first data\"}], \"nextToken\": \"testToken\" } ");
		createMapping("/t%20%20%20est%60?token=testToken", "{ \"response\": [ {\"id\": \"2\", \"data\": \"second data\"}] } ");

		final String urlTemplate = "http://localhost:" + wireMockServer.port() + "/t %20 est`?token={next_token_value}";
		final String initUrlTemplate = "http://localhost:" + wireMockServer.port() + "/t %20 est`";
		Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, urlTemplate);
			put(URL_TEMPLATE_INIT_REQUEST, initUrlTemplate);
			put(PARAM_ID_MACRO, "next_token_value");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(RESPONSE_NEXT_TOKEN_DATA_PATH, "$.nextToken");
			put(ITERATION_TYPE, IterationType.PAGING_NEXT_TOKEN.property);
		}});

		task.start(props);

		assertEquals(singletonList((of("id", "0", "data", "init data"))), pollData());
		assertEquals(singletonList((of("id", "1", "data", "first data"))), pollData());

		ImmutablePair<List<Map<String, Object>>, Optional<SourceRecord>> pollData2 = pollDataWithOffset();
		assertEquals(singletonList((of("id", "2", "data", "second data"))), pollData2.left);

		Map<String, Object> stringMap = (Map) pollData2.right.get().sourceOffset();
		when(offsetStorageReader.offset(anyMap())).thenReturn(stringMap);

		task.start(props);

		assertEquals(singletonList((of("id", "0", "data", "init data"))), pollData());
	}

	@Test
	public void graphql_cursor_graphql_query_body() {
		String firstPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjA=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjA=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjA=\",\"node\":{\"entityId\":80,\"name\":\"Orbit Terrarium - Large\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.willReturn(aResponse().withStatus(200)
						.withBody(firstPollResponseBody))
		);

		String secondPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjE=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjE=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjE=\",\"node\":{\"entityId\":81,\"name\":\"Shower Curtain\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(containing("YXJyYXljb25uZWN0aW9uOjA="))
				.willReturn(aResponse().withStatus(200)
						.withBody(secondPollResponseBody))
		);

		String thirdPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjI=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjI=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjI=\",\"node\":{\"entityId\":82,\"name\":\"Chambray Towel\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(containing("YXJyYXljb25uZWN0aW9uOjE="))
				.willReturn(aResponse().withStatus(200)
						.withBody(thirdPollResponseBody))
		);

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "CODE1");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/graphql");
			put(GRAPHQL_QUERY,
					"query paginateProducts(                    \n" +
							"  $cursor: String                          \n" +
							") {                                        \n" +
							"  site {                                   \n" +
							"    products (first: 1, after:$cursor) {   \n" +
							"      pageInfo {                           \n" +
							"        startCursor                        \n" +
							"        endCursor                          \n" +
							"      }                                    \n" +
							"      edges {                              \n" +
							"        cursor                             \n" +
							"        node {                             \n" +
							"          entityId                         \n" +
							"          name                             \n" +
							"        }                                  \n" +
							"      }                                    \n" +
							"    }                                      \n" +
							"  }                                        \n" +
							"}                                          \n"
			);

			put(GRAPHQL_CURSOR_VARIABLE_NAME, "cursor");
			put(METHOD, "POST");
			put(ITERATION_TYPE, IterationType.GRAPHQL_CURSOR_ITERATION.property);
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$");
			put(GRAPHQL_CURSOR_PATH, "$.data.site.products.pageInfo.endCursor");
		}});

		task.start(props);

		List<Map<String, Object>> firstPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(firstPollResponseBody)), firstPoll);
		List<Map<String, Object>> secondPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(secondPollResponseBody)), secondPoll);
		List<Map<String, Object>> thirdPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(thirdPollResponseBody)), thirdPoll);
		task.stop();
	}

	@Test
	public void graphql_cursor_json_body_with_vars() {
		String firstPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjA=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjA=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjA=\",\"node\":{\"entityId\":80,\"name\":\"Orbit Terrarium - Large\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.willReturn(aResponse().withStatus(200)
						.withBody(firstPollResponseBody))
		);

		String secondPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjE=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjE=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjE=\",\"node\":{\"entityId\":81,\"name\":\"Shower Curtain\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(containing("YXJyYXljb25uZWN0aW9uOjA="))
				.willReturn(aResponse().withStatus(200)
						.withBody(secondPollResponseBody))
		);

		String thirdPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjI=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjI=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjI=\",\"node\":{\"entityId\":82,\"name\":\"Chambray Towel\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(containing("YXJyYXljb25uZWN0aW9uOjE="))
				.willReturn(aResponse().withStatus(200)
						.withBody(thirdPollResponseBody))
		);

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "CODE1");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/graphql");
			put(GRAPHQL_QUERY, JsonUtils.toJsonString(Map.of(
							"query", "query paginateProducts($cursor: String, $pageSize: Int) {site { products (first: $pageSize, after:$cursor) { pageInfo { startCursor endCursor} edges {cursor node { entityId name }}}}}",
							"variables", Map.of(
									"cursor", "",
									"pageSize", 1
							)
					))
			);

			put(GRAPHQL_CURSOR_VARIABLE_NAME, "cursor");
			put(METHOD, "POST");
			put(ITERATION_TYPE, IterationType.GRAPHQL_CURSOR_ITERATION.property);
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$");
			put(GRAPHQL_CURSOR_PATH, "$.data.site.products.pageInfo.endCursor");
		}});

		task.start(props);

		List<Map<String, Object>> firstPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(firstPollResponseBody)), firstPoll);
		List<Map<String, Object>> secondPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(secondPollResponseBody)), secondPoll);
		List<Map<String, Object>> thirdPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(thirdPollResponseBody)), thirdPoll);
		task.stop();
	}

	@Test
	public void graphql_cursor_json_body_with_variable_replacement() {
		String firstPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjA=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjA=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjA=\",\"node\":{\"entityId\":80,\"name\":\"Orbit Terrarium - Large\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(equalToJson("{\"variables\":{\"cursor\":null,\"pageSize\": 1},\"query\":\"query paginateProducts($cursor: String, $pageSize: Int) {site { products (first: $pageSize, after:$cursor) { pageInfo { startCursor endCursor} edges {cursor node { entityId name }}}}}\"}"))
				.willReturn(aResponse().withStatus(200)
						.withBody(firstPollResponseBody))
		);

		String secondPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjE=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjE=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjE=\",\"node\":{\"entityId\":81,\"name\":\"Shower Curtain\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(equalToJson("{\"variables\":{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjA=\",\"pageSize\": 1},\"query\":\"query paginateProducts($cursor: String, $pageSize: Int) {site { products (first: $pageSize, after:$cursor) { pageInfo { startCursor endCursor} edges {cursor node { entityId name }}}}}\"}"))
				.willReturn(aResponse().withStatus(200)
						.withBody(secondPollResponseBody))
		);

		String thirdPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjI=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjI=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjI=\",\"node\":{\"entityId\":82,\"name\":\"Chambray Towel\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(equalToJson("{\"variables\":{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjE=\",\"pageSize\": 1},\"query\":\"query paginateProducts($cursor: String, $pageSize: Int) {site { products (first: $pageSize, after:$cursor) { pageInfo { startCursor endCursor} edges {cursor node { entityId name }}}}}\"}"))
				.willReturn(aResponse().withStatus(200)
						.withBody(thirdPollResponseBody))
		);

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "CODE1");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/graphql");
			put(GRAPHQL_QUERY, "{\"variables\":{\"cursor\":null,\"pageSize\": {pageSize=1}},\"query\":\"query paginateProducts($cursor: String, $pageSize: Int) {site { products (first: $pageSize, after:$cursor) { pageInfo { startCursor endCursor} edges {cursor node { entityId name }}}}}\"}");

			put(GRAPHQL_CURSOR_VARIABLE_NAME, "cursor");
			put(METHOD, "POST");
			put(ITERATION_TYPE, IterationType.GRAPHQL_CURSOR_ITERATION.property);
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$");
			put(GRAPHQL_CURSOR_PATH, "$.data.site.products.pageInfo.endCursor");
		}});

		task.start(props);

		List<Map<String, Object>> firstPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(firstPollResponseBody)), firstPoll);
		List<Map<String, Object>> secondPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(secondPollResponseBody)), secondPoll);
		List<Map<String, Object>> thirdPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(thirdPollResponseBody)), thirdPoll);
		task.stop();
	}

	@Test
	public void graphql_cursor_json_body_with_variable_replacement_in_query_body() {
		String firstPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjA=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjA=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjA=\",\"node\":{\"entityId\":80,\"name\":\"Orbit Terrarium - Large\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(equalToJson("{\"variables\":{\"cursor\":null},\"query\":\"query paginateProducts($cursor: String, $pageSize: Int) {site { products (first: 1, after:$cursor) { pageInfo { startCursor endCursor} edges {cursor node { entityId name }}}}}\"}"))
				.willReturn(aResponse().withStatus(200)
						.withBody(firstPollResponseBody))
		);

		String secondPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjE=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjE=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjE=\",\"node\":{\"entityId\":81,\"name\":\"Shower Curtain\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(equalToJson("{\"variables\":{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjA=\"},\"query\":\"query paginateProducts($cursor: String, $pageSize: Int) {site { products (first: 1, after:$cursor) { pageInfo { startCursor endCursor} edges {cursor node { entityId name }}}}}\"}"))
				.willReturn(aResponse().withStatus(200)
						.withBody(secondPollResponseBody))
		);

		String thirdPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjI=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjI=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjI=\",\"node\":{\"entityId\":82,\"name\":\"Chambray Towel\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(equalToJson("{\"variables\":{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjE=\"},\"query\":\"query paginateProducts($cursor: String, $pageSize: Int) {site { products (first: 1, after:$cursor) { pageInfo { startCursor endCursor} edges {cursor node { entityId name }}}}}\"}"))
				.willReturn(aResponse().withStatus(200)
						.withBody(thirdPollResponseBody))
		);

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "CODE1");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/graphql");
			put(GRAPHQL_QUERY, "{\"variables\":{\"cursor\":\"\"},\"query\":\"query paginateProducts($cursor: String, $pageSize: Int) {site { products (first: {pageSize=1}, after:$cursor) { pageInfo { startCursor endCursor} edges {cursor node { entityId name }}}}}\"}");

			put(GRAPHQL_CURSOR_VARIABLE_NAME, "cursor");
			put(METHOD, "POST");
			put(ITERATION_TYPE, IterationType.GRAPHQL_CURSOR_ITERATION.property);
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$");
			put(GRAPHQL_CURSOR_PATH, "$.data.site.products.pageInfo.endCursor");
		}});

		task.start(props);

		List<Map<String, Object>> firstPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(firstPollResponseBody)), firstPoll);
		List<Map<String, Object>> secondPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(secondPollResponseBody)), secondPoll);
		List<Map<String, Object>> thirdPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(thirdPollResponseBody)), thirdPoll);
		task.stop();
	}

	@Test
	public void graphql_cursor_json_body_without_vars() {
		String firstPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjA=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjA=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjA=\",\"node\":{\"entityId\":80,\"name\":\"Orbit Terrarium - Large\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.willReturn(aResponse().withStatus(200)
						.withBody(firstPollResponseBody))
		);

		String secondPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjE=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjE=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjE=\",\"node\":{\"entityId\":81,\"name\":\"Shower Curtain\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(containing("YXJyYXljb25uZWN0aW9uOjA="))
				.willReturn(aResponse().withStatus(200)
						.withBody(secondPollResponseBody))
		);

		String thirdPollResponseBody = "{\"data\":{\"site\":{\"products\":{\"pageInfo\":{\"startCursor\":\"YXJyYXljb25uZWN0aW9uOjI=\",\"endCursor\":\"YXJyYXljb25uZWN0aW9uOjI=\"},\"edges\":[{\"cursor\":\"YXJyYXljb25uZWN0aW9uOjI=\",\"node\":{\"entityId\":82,\"name\":\"Chambray Towel\"}}]}}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(containing("YXJyYXljb25uZWN0aW9uOjE="))
				.willReturn(aResponse().withStatus(200)
						.withBody(thirdPollResponseBody))
		);

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "CODE1");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/graphql");
			put(GRAPHQL_QUERY, JsonUtils.toJsonString(Map.of(
							"query", "query paginateProducts($cursor: String) {site { products (first: 1, after:$cursor) { pageInfo { startCursor endCursor} edges {cursor node { entityId name }}}}}"
					))
			);

			put(GRAPHQL_CURSOR_VARIABLE_NAME, "cursor");
			put(METHOD, "POST");
			put(ITERATION_TYPE, IterationType.GRAPHQL_CURSOR_ITERATION.property);
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$");
			put(GRAPHQL_CURSOR_PATH, "$.data.site.products.pageInfo.endCursor");
		}});

		task.start(props);

		List<Map<String, Object>> firstPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(firstPollResponseBody)), firstPoll);
		List<Map<String, Object>> secondPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(secondPollResponseBody)), secondPoll);
		List<Map<String, Object>> thirdPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(thirdPollResponseBody)), thirdPoll);
		task.stop();
	}

    @Test
	public void graphql_cursor_next_items_page() {
		String firstStepResponseBody = "{\"data\":{\"boards\":[{\"items_page\":{\"cursor\":\"page2\",\"items\":[{\"id\":\"1\",\"name\":\"item1\"}]}}]}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(containing("query {boards"))
				.willReturn(aResponse().withStatus(200)
						.withBody(firstStepResponseBody))
		);

		String firstPollResponseBody = "{\"data\":{\"next_items_page\":{\"cursor\":\"page3\",\"items\":[{\"id\":\"2\",\"name\":\"item2\"}]}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(containing("next_items_page"))
				.withRequestBody(containing("\"cursor\":\"page2\""))
				.willReturn(aResponse().withStatus(200)
						.withBody(firstPollResponseBody))
		);

		String secondPollResponseBody = "{\"data\":{\"next_items_page\":{\"cursor\":null,\"items\":[{\"id\":\"3\",\"name\":\"item3\"}]}}}";
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(containing("next_items_page"))
				.withRequestBody(containing("\"cursor\":\"page3\""))
				.willReturn(aResponse().withStatus(200)
						.withBody(secondPollResponseBody))
		);

		Map<String, String> props = buildByCallersConfig(
			new HashMap<>() {{
				put(KEY, "STEP1");
				put(AUTH_TYPE, AuthType.NONE.name());
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/graphql");
				put(GRAPHQL_QUERY, JsonUtils.toJsonString(Map.of(
						"query", "query {boards (ids: 1234567890) {items_page(limit:1) {cursor items {id name}}}}"
					))
				);
				put(METHOD, "POST");
				put(ITERATION_TYPE, STATIC_URL.property);
				put(RESPONSE_FORMAT, "json");
				put(RESPONSE_DATA_PATH, "$.data.boards[0].items_page");
			}},
			new HashMap<>() {{
				put(KEY, "STEP2");
				put(AUTH_TYPE, AuthType.NONE.name());
				put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/graphql");
				put(GRAPHQL_QUERY, JsonUtils.toJsonString(Map.of(
						"query", "query {next_items_page(limit: 1 cursor: $cursor) {cursor items {id name}}}}"
					))
				);

				put(GRAPHQL_CURSOR_VARIABLE_NAME, "cursor");
				put(GRAPHQL_CURSOR_TEMPLATE, "STEP1.cursor");
				put(RESULTS_PASS_THROUGH, "true");
				put(METHOD, "POST");
				put(ITERATION_TYPE, IterationType.GRAPHQL_CURSOR_ITERATION.property);
				put(RESPONSE_FORMAT, "json");
				put(RESPONSE_DATA_PATH, "$.data.next_items_page");
				put(GRAPHQL_CURSOR_PATH, "$.data.next_items_page.cursor");
        }});

		task.start(props);

		String firstExpected = "{\"cursor\":\"page2\",\"items\":[{\"id\":\"1\",\"name\":\"item1\"}]}";
		String secondExpected = "{\"cursor\":\"page3\",\"items\":[{\"id\":\"2\",\"name\":\"item2\"}]}";
		List<Map<String, Object>> firstPoll = pollData();
		assertEquals(Arrays.asList(JsonUtils.jsonToMap(firstExpected), JsonUtils.jsonToMap(secondExpected)), firstPoll);

		String expected = "{\"cursor\":null,\"items\":[{\"id\":\"3\",\"name\":\"item3\"}]}";
		List<Map<String, Object>> secondPoll = pollData();
		assertEquals(Collections.singletonList(JsonUtils.jsonToMap(expected)), secondPoll);
		task.stop();
	}

	@Test
	public void graphql_page_graphql_body_with_vars() {
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(matching(".*\"currentPage\":1.*"))
				.willReturn(aResponse().withStatus(200).withBody("{\"data\": {\"products\": {\"items\": [{\"id\": 2046, \"name\": \"Erika Running Short\", \"sku\": \"WSH12\", \"description\": {\"html\": \"\"}, \"special_price\": null, \"image\": {\"url\": null}, \"thumbnail\": {\"url\": null}, \"country_of_manufacture\": null, \"type_id\": \"configurable\", \"price_range\": {\"minimum_price\": {\"regular_price\": {\"value\": 45}}, \"maximum_price\": {\"regular_price\": {\"value\": 45}}}, \"categories\": [{\"id\": 22, \"name\": \"Bottoms\", \"path\": \"1/2/20/22\"}, {\"id\": 28, \"name\": \"Shorts\", \"path\": \"1/2/20/22/28\"}, {\"id\": 34, \"name\": \"Erin Recommends\", \"path\": \"1/2/7/34\"}], \"canonical_url\": null, \"url_key\": \"erika-running-short\", \"attribute_set_id\": 10, \"__typename\": \"ConfigurableProduct\"}], \"total_count\": 187, \"page_info\": {\"page_size\": 1, \"current_page\": 1, \"total_pages\": 187}}}}"))
		);

		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(matching(".*\"currentPage\":2.*"))
				.willReturn(aResponse().withStatus(200).withBody("{\"data\": {\"products\": {\"items\": [{\"id\": 2030, \"name\": \"Ina Compression Short\", \"sku\": \"WSH11\", \"description\": {\"html\": null}, \"special_price\": null, \"image\": {\"url\": null}, \"thumbnail\": {\"url\": null}, \"country_of_manufacture\": null, \"type_id\": \"configurable\", \"price_range\": {\"minimum_price\": {\"regular_price\": {\"value\": 49}}, \"maximum_price\": {\"regular_price\": {\"value\": 49}}}, \"categories\": [{\"id\": 22, \"name\": \"Bottoms\", \"path\": \"1/2/20/22\"}, {\"id\": 28, \"name\": \"Shorts\", \"path\": \"1/2/20/22/28\"}, {\"id\": 34, \"name\": \"Erin Recommends\", \"path\": \"1/2/7/34\"}], \"canonical_url\": null, \"url_key\": \"ina-compression-short\", \"attribute_set_id\": 10, \"__typename\": \"ConfigurableProduct\", \"configurable_options\": [{\"id\": 293, \"attribute_code\": \"color\", \"values\": [{\"value_index\": 50, \"label\": \"Blue\", \"swatch_data\": {\"value\": \"#1857f7\"}}, {\"value_index\": 56, \"label\": \"Orange\", \"swatch_data\": {\"value\": \"#eb6703\"}}, {\"value_index\": 58, \"label\": \"Red\", \"swatch_data\": {\"value\": \"#ff0000\"}}]}, {\"id\": 292, \"attribute_code\": \"size\", \"values\": [{\"value_index\": 171, \"label\": \"28\", \"swatch_data\": {\"value\": \"28\"}}, {\"value_index\": 172, \"label\": \"29\", \"swatch_data\": {\"value\": \"29\"}}]}]}], \"total_count\": 187, \"page_info\": {\"page_size\": 1, \"current_page\": 2, \"total_pages\": 187}}}}"))
		);

		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(matching(".*\"currentPage\":3.*"))
				.willReturn(aResponse().withStatus(200).withBody("{\"data\": {\"products\": {\"items\": [], \"total_count\": 187, \"page_info\": {\"page_size\": 1, \"current_page\": 2, \"total_pages\": 187}}}}"))
		);

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "CODE1");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/graphql");
			put(GRAPHQL_QUERY, "query Products ($currentPage: Int $pageSize: Int){ products(filter:{} pageSize: $pageSize, currentPage: $currentPage){ items{ id name sku description{ html } special_price image{ url } thumbnail{ url } country_of_manufacture type_id price_range{ minimum_price{ regular_price{ value } } maximum_price{ regular_price{ value } } } categories{ id name path } canonical_url url_key attribute_set_id __typename ... on ConfigurableProduct{ configurable_options{ id attribute_code values{ value_index label swatch_data{ value } } } variants{ product{ id name sku image{ url } thumbnail{ url } attribute_set_id ... on PhysicalProductInterface{ weight } price_range{ minimum_price{ regular_price{ value currency } } } } attributes{ label code value_index } } } } total_count page_info {page_size current_page total_pages}}}");
			put(METHOD, "POST");
			put(ITERATION_TYPE, IterationType.GRAPHQL_PAGE_ITERATION.property);
			put(GRAPHQL_PAGE_VARIABLE_NAME, "currentPage");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.data.products.items[*]");
		}});

		task.start(props);

		assertEquals(1, pollData().size());
		assertEquals(1, pollData().size());
		assertEquals(emptyList(), pollData());
		task.stop();
	}

	@Test
	public void graphql_page_graphql_body_with_vars_with_total_pages() {
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(matching(".*\"currentPage\":1.*"))
				.willReturn(aResponse().withStatus(200).withBody("{\"data\": {\"products\": {\"items\": [{\"id\": 2046, \"name\": \"Erika Running Short\", \"sku\": \"WSH12\", \"description\": {\"html\": \"\"}, \"special_price\": null, \"image\": {\"url\": null}, \"thumbnail\": {\"url\": null}, \"country_of_manufacture\": null, \"type_id\": \"configurable\", \"price_range\": {\"minimum_price\": {\"regular_price\": {\"value\": 45}}, \"maximum_price\": {\"regular_price\": {\"value\": 45}}}, \"categories\": [{\"id\": 22, \"name\": \"Bottoms\", \"path\": \"1/2/20/22\"}, {\"id\": 28, \"name\": \"Shorts\", \"path\": \"1/2/20/22/28\"}, {\"id\": 34, \"name\": \"Erin Recommends\", \"path\": \"1/2/7/34\"}], \"canonical_url\": null, \"url_key\": \"erika-running-short\", \"attribute_set_id\": 10, \"__typename\": \"ConfigurableProduct\"}], \"total_count\": 187, \"page_info\": {\"page_size\": 1, \"current_page\": 1, \"total_pages\": 2}}}}"))
		);

		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(matching(".*\"currentPage\":2.*"))
				.willReturn(aResponse().withStatus(200).withBody("{\"data\": {\"products\": {\"items\": [{\"id\": 2030, \"name\": \"Ina Compression Short\", \"sku\": \"WSH11\", \"description\": {\"html\": null}, \"special_price\": null, \"image\": {\"url\": null}, \"thumbnail\": {\"url\": null}, \"country_of_manufacture\": null, \"type_id\": \"configurable\", \"price_range\": {\"minimum_price\": {\"regular_price\": {\"value\": 49}}, \"maximum_price\": {\"regular_price\": {\"value\": 49}}}, \"categories\": [{\"id\": 22, \"name\": \"Bottoms\", \"path\": \"1/2/20/22\"}, {\"id\": 28, \"name\": \"Shorts\", \"path\": \"1/2/20/22/28\"}, {\"id\": 34, \"name\": \"Erin Recommends\", \"path\": \"1/2/7/34\"}], \"canonical_url\": null, \"url_key\": \"ina-compression-short\", \"attribute_set_id\": 10, \"__typename\": \"ConfigurableProduct\", \"configurable_options\": [{\"id\": 293, \"attribute_code\": \"color\", \"values\": [{\"value_index\": 50, \"label\": \"Blue\", \"swatch_data\": {\"value\": \"#1857f7\"}}, {\"value_index\": 56, \"label\": \"Orange\", \"swatch_data\": {\"value\": \"#eb6703\"}}, {\"value_index\": 58, \"label\": \"Red\", \"swatch_data\": {\"value\": \"#ff0000\"}}]}, {\"id\": 292, \"attribute_code\": \"size\", \"values\": [{\"value_index\": 171, \"label\": \"28\", \"swatch_data\": {\"value\": \"28\"}}, {\"value_index\": 172, \"label\": \"29\", \"swatch_data\": {\"value\": \"29\"}}]}]}], \"total_count\": 187, \"page_info\": {\"page_size\": 1, \"current_page\": 2, \"total_pages\": 2}}}}"))
		);

		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(matching(".*\"currentPage\":3.*"))
				.willReturn(aResponse().withStatus(200).withBody("{\"data\": {\"products\": {\"items\": [{\"id\": 2030, \"name\": \"Ina Compression Short\", \"sku\": \"WSH11\", \"description\": {\"html\": null}, \"special_price\": null, \"image\": {\"url\": null}, \"thumbnail\": {\"url\": null}, \"country_of_manufacture\": null, \"type_id\": \"configurable\", \"price_range\": {\"minimum_price\": {\"regular_price\": {\"value\": 49}}, \"maximum_price\": {\"regular_price\": {\"value\": 49}}}, \"categories\": [{\"id\": 22, \"name\": \"Bottoms\", \"path\": \"1/2/20/22\"}, {\"id\": 28, \"name\": \"Shorts\", \"path\": \"1/2/20/22/28\"}, {\"id\": 34, \"name\": \"Erin Recommends\", \"path\": \"1/2/7/34\"}], \"canonical_url\": null, \"url_key\": \"ina-compression-short\", \"attribute_set_id\": 10, \"__typename\": \"ConfigurableProduct\", \"configurable_options\": [{\"id\": 293, \"attribute_code\": \"color\", \"values\": [{\"value_index\": 50, \"label\": \"Blue\", \"swatch_data\": {\"value\": \"#1857f7\"}}, {\"value_index\": 56, \"label\": \"Orange\", \"swatch_data\": {\"value\": \"#eb6703\"}}, {\"value_index\": 58, \"label\": \"Red\", \"swatch_data\": {\"value\": \"#ff0000\"}}]}, {\"id\": 292, \"attribute_code\": \"size\", \"values\": [{\"value_index\": 171, \"label\": \"28\", \"swatch_data\": {\"value\": \"28\"}}, {\"value_index\": 172, \"label\": \"29\", \"swatch_data\": {\"value\": \"29\"}}]}]}], \"total_count\": 187, \"page_info\": {\"page_size\": 1, \"current_page\": 2, \"total_pages\": 2}}}}"))
		);

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "CODE1");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/graphql");
			put(GRAPHQL_QUERY, "query Products ($currentPage: Int $pageSize: Int){ products(filter:{} pageSize: $pageSize, currentPage: $currentPage){ items{ id name sku description{ html } special_price image{ url } thumbnail{ url } country_of_manufacture type_id price_range{ minimum_price{ regular_price{ value } } maximum_price{ regular_price{ value } } } categories{ id name path } canonical_url url_key attribute_set_id __typename ... on ConfigurableProduct{ configurable_options{ id attribute_code values{ value_index label swatch_data{ value } } } variants{ product{ id name sku image{ url } thumbnail{ url } attribute_set_id ... on PhysicalProductInterface{ weight } price_range{ minimum_price{ regular_price{ value currency } } } } attributes{ label code value_index } } } } total_count page_info {page_size current_page total_pages}}}");
			put(METHOD, "POST");
			put(ITERATION_TYPE, IterationType.GRAPHQL_PAGE_ITERATION.property);
			put(GRAPHQL_PAGE_VARIABLE_NAME, "currentPage");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.data.products.items[*]");
			put(RESPONSE_TOTAL_PAGES_PATH, "$.data.products.page_info.total_pages");
		}});

		task.start(props);

		assertEquals(1, pollData().size());
		assertEquals(1, pollData().size());
		assertEquals(emptyList(), pollData());
		assertEquals(emptyList(), pollData());
		task.stop();
	}

	@Test
	public void graphql_page_json_body_with_vars() {
		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(matching(".*\"currentPage\":1.*"))
				.willReturn(aResponse().withStatus(200).withBody("{\"data\": {\"products\": {\"items\": [{\"id\": 2046, \"name\": \"Erika Running Short\", \"sku\": \"WSH12\", \"description\": {\"html\": \"\"}, \"special_price\": null, \"image\": {\"url\": null}, \"thumbnail\": {\"url\": null}, \"country_of_manufacture\": null, \"type_id\": \"configurable\", \"price_range\": {\"minimum_price\": {\"regular_price\": {\"value\": 45}}, \"maximum_price\": {\"regular_price\": {\"value\": 45}}}, \"categories\": [{\"id\": 22, \"name\": \"Bottoms\", \"path\": \"1/2/20/22\"}, {\"id\": 28, \"name\": \"Shorts\", \"path\": \"1/2/20/22/28\"}, {\"id\": 34, \"name\": \"Erin Recommends\", \"path\": \"1/2/7/34\"}], \"canonical_url\": null, \"url_key\": \"erika-running-short\", \"attribute_set_id\": 10, \"__typename\": \"ConfigurableProduct\"}], \"total_count\": 187, \"page_info\": {\"page_size\": 1, \"current_page\": 1, \"total_pages\": 187}}}}"))
		);

		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(matching(".*\"currentPage\":2.*"))
				.willReturn(aResponse().withStatus(200).withBody("{\"data\": {\"products\": {\"items\": [{\"id\": 2030, \"name\": \"Ina Compression Short\", \"sku\": \"WSH11\", \"description\": {\"html\": null}, \"special_price\": null, \"image\": {\"url\": null}, \"thumbnail\": {\"url\": null}, \"country_of_manufacture\": null, \"type_id\": \"configurable\", \"price_range\": {\"minimum_price\": {\"regular_price\": {\"value\": 49}}, \"maximum_price\": {\"regular_price\": {\"value\": 49}}}, \"categories\": [{\"id\": 22, \"name\": \"Bottoms\", \"path\": \"1/2/20/22\"}, {\"id\": 28, \"name\": \"Shorts\", \"path\": \"1/2/20/22/28\"}, {\"id\": 34, \"name\": \"Erin Recommends\", \"path\": \"1/2/7/34\"}], \"canonical_url\": null, \"url_key\": \"ina-compression-short\", \"attribute_set_id\": 10, \"__typename\": \"ConfigurableProduct\", \"configurable_options\": [{\"id\": 293, \"attribute_code\": \"color\", \"values\": [{\"value_index\": 50, \"label\": \"Blue\", \"swatch_data\": {\"value\": \"#1857f7\"}}, {\"value_index\": 56, \"label\": \"Orange\", \"swatch_data\": {\"value\": \"#eb6703\"}}, {\"value_index\": 58, \"label\": \"Red\", \"swatch_data\": {\"value\": \"#ff0000\"}}]}, {\"id\": 292, \"attribute_code\": \"size\", \"values\": [{\"value_index\": 171, \"label\": \"28\", \"swatch_data\": {\"value\": \"28\"}}, {\"value_index\": 172, \"label\": \"29\", \"swatch_data\": {\"value\": \"29\"}}]}]}], \"total_count\": 187, \"page_info\": {\"page_size\": 1, \"current_page\": 2, \"total_pages\": 187}}}}"))
		);

		stubFor(post(urlEqualTo("/graphql")).inScenario("GraphQL cursor scenario")
				.whenScenarioStateIs(STARTED)
				.withRequestBody(matching(".*\"currentPage\":3.*"))
				.willReturn(aResponse().withStatus(200).withBody("{\"data\": {\"products\": {\"items\": [], \"total_count\": 187, \"page_info\": {\"page_size\": 1, \"current_page\": 2, \"total_pages\": 187}}}}"))
		);

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "CODE1");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/graphql");
			put(GRAPHQL_QUERY, JsonUtils.toJsonString(Map.of(
					"query", "query Products ($currentPage: Int $pageSize: Int){ products(filter:{} pageSize: $pageSize, currentPage: $currentPage){ items{ id name sku description{ html } special_price image{ url } thumbnail{ url } country_of_manufacture type_id price_range{ minimum_price{ regular_price{ value } } maximum_price{ regular_price{ value } } } categories{ id name path } canonical_url url_key attribute_set_id __typename ... on ConfigurableProduct{ configurable_options{ id attribute_code values{ value_index label swatch_data{ value } } } variants{ product{ id name sku image{ url } thumbnail{ url } attribute_set_id ... on PhysicalProductInterface{ weight } price_range{ minimum_price{ regular_price{ value currency } } } } attributes{ label code value_index } } } } total_count page_info {page_size current_page total_pages}}}",
					"variables", Map.of(
							"pageSize", 1
					)
			)));
			put(METHOD, "POST");
			put(ITERATION_TYPE, IterationType.GRAPHQL_PAGE_ITERATION.property);
			put(GRAPHQL_PAGE_VARIABLE_NAME, "currentPage");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.data.products.items[*]");
		}});

		task.start(props);

		assertEquals(1, pollData().size());
		assertEquals(1, pollData().size());
		assertEquals(emptyList(), pollData());
		task.stop();
	}

	@Test
	public void poll_nextToken_limit() {

		createMapping("/t%20%20%20est%60", "{ \"response\": [ {\"id\": \"1\", \"data\": \"first data\"}], \"nextToken\": \"testToken\" } ");
		createMapping("/t%20%20%20est%60?token=testToken", "{ \"response\": [ {\"id\": \"2\", \"data\": \"second data\"}], \"nextToken\": \"testToken2\"} ");

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/t %20 est`");
			put(PARAM_ID, "token");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(RESPONSE_NEXT_TOKEN_DATA_PATH, "$.nextToken");
			put(END_TOKEN_TO, "testToken");
			put(ITERATION_TYPE, IterationType.PAGING_NEXT_TOKEN.property);
		}});

		task.start(props);

		assertEquals(singletonList((of("id", "1", "data", "first data"))), pollData());
		assertEquals(singletonList((of("id", "2", "data", "second data"))), pollData());
		assertEquals(emptyList(), pollData());
		task.stop();
	}

	@Test
	public void poll_nextUrl() {

		createMapping("/test", "{" +
				"\"header\": {\"info\": \"header\"}," +
				"\"response\": [{\"id\": \"1\", \"data\": \"first data\"}]," +
				" \"nextUrl\": \"/test?nextPage=1\"" +
				"} ");
		createMapping("/test?nextPage=1", "{ \"response\": [ {\"id\": \"2\", \"data\": \"second data\"}], \"nextUrl\": null   } ");

		Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(PARAM_ID, "token");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(RESPONSE_DATA_PATH_ADDITIONAL, "[\"$.header[*]\"]");
			put(RESPONSE_NEXT_URL_DATA_PATH, "$.nextUrl");
			put(ITERATION_TYPE, IterationType.PAGING_NEXT_URL.property);
		}});

		task.start(props);

		assertEquals(singletonList((of("header", "header", "id", "1", "data", "first data"))), pollData());
		assertEquals(singletonList((of("id", "2", "data", "second data"))), pollData());
		task.stop();
	}

	@Test
	public void poll_nextUrl_limit() {

		createMapping("/test", "{" +
				"\"header\": {\"info\": \"header\"}," +
				"\"response\": [{\"id\": \"1\", \"data\": \"first data\"}]," +
				" \"nextUrl\": \"/test?nextPage=1\"" +
				"} ");

		createMapping("/test?nextPage=1", "{ \"response\": [ {\"id\": \"2\", \"data\": \"second data\"}], \"nextUrl\": \"/test?nextPage=2\"   } ");

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(PARAM_ID, "token");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(RESPONSE_DATA_PATH_ADDITIONAL, "[\"$.header[*]\"]");
			put(RESPONSE_NEXT_URL_DATA_PATH, "$.nextUrl");
			put(END_URL_TO, "/test?nextPage=1");
			put(ITERATION_TYPE, IterationType.PAGING_NEXT_URL.property);
		}});

		task.start(props);

		assertEquals(singletonList((of("header", "header", "id", "1", "data", "first data"))), pollData());
		assertEquals(singletonList((of("id", "2", "data", "second data"))), pollData());
		assertEquals(emptyList(), pollData());
		task.stop();
	}

	@Test(expected = ConfigException.class)
	public void poll_duplicate_key() {
		Map<String, String> props = buildByCallersConfig(
				new HashMap<String, String>() {{
					put(KEY, "KEY");
					put(AUTH_TYPE, AuthType.NONE.name());
					put(METHOD, "GET");
					put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
					put(PARAM_ID, "token");
					put(RESPONSE_FORMAT, "json");
					put(RESPONSE_DATA_PATH, "$.response[*]");
					put(RESPONSE_NEXT_URL_DATA_PATH, "$.nextUrl");
					put(ITERATION_TYPE, IterationType.PAGING_NEXT_URL.property);
				}},
				new HashMap<String, String>() {{
					put(KEY, "KEY");
					put(AUTH_TYPE, AuthType.NONE.name());
					put(METHOD, "GET");
					put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
					put(PARAM_ID, "token");
					put(RESPONSE_FORMAT, "json");
					put(RESPONSE_DATA_PATH, "$.response[*]");
					put(RESPONSE_NEXT_URL_DATA_PATH, "$.nextUrl");
					put(ITERATION_TYPE, IterationType.PAGING_NEXT_URL.property);
				}}
		);
		try {
			task.start(props);
		} finally {
			try {
				task.stop();
			} catch (NullPointerException npe) {
				System.err.println("npe in task stop, don't care much");
			}
		}
	}

	@SneakyThrows
	@Test
	public void poll_ModeIdResponseField() {
		int sourceId = testSourceId;
		withConsumer((metricsTopic, metricsConsumer) -> {

			createMapping("/test", "[ {\"id\": \"1\"}]");
			String url = "http://localhost:" + wireMockServer.port() + "/test";

			Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
				put(KEY, "KEY");
				put(AUTH_TYPE, AuthType.NONE.name());
				put(METHOD, "GET");
				put(URL_TEMPLATE, url);
				put(RESPONSE_ID_FIELD_NAME, "id");
				put(PARAM_ID, "idFrom");
				put(RESPONSE_FORMAT, "json");
				put(RESPONSE_DATA_PATH, "$[*]");
				put(ITERATION_TYPE, IterationType.RESPONSE_ID_NUMBER.property);
			}});

			props.put(SOURCE_ID, sourceId + "");
			props.put(METRICS_TOPIC, metricsTopic);

			task.start(props);

			List<SourceRecord> firstRunRecords = pollNoTrace(task);
			assertTopicName(firstRunRecords);
			assertEquals(singletonList(of("id", "1")), toRawMessages(toMessages(firstRunRecords)));

			wireMockServer.resetMappings();

			createMapping("/test?idFrom=2", "[{\"id\": \"2\"}]");

			List<SourceRecord> secondRunRecords = pollNoTrace(task);
			assertTopicName(secondRunRecords);

			List<NexlaMessage> data = toMessages(secondRunRecords);
			assertEquals(singletonList(of("id", "2")), toRawMessages(data));
			assertTrackerId(data.get(0), 1L, url + "?idFrom=2");

			List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sourceId);

			assertEquals(2, metrics.size());

			metrics.forEach(metric ->
					assertEquals("Invalid Dataset Id", 1, metric.getFields().get(DATASET_ID)));

			NexlaRawMetric metric = metrics.get(0);

			assertTrue(metric.getTags().get(NAME).endsWith("/test"));
			assertEquals("Invalid records", 1, metric.getFields().get(RECORDS));
			assertEquals("Invalid resource Id", sourceId, metric.getResourceId().intValue());
			assertEquals("Invalid resource Type", SOURCE, metric.getResourceType());
			assertNull("Invalid Error Message", metric.getFields().get(EXCEPTION_TRACE));
			assertNull("Invalid hash", metric.getFields().get(HASH));
			task.stop();
		}, "metrics");
	}

	@Test
	public void poll_ModeId_String_ResponseField() {

		List<String> ids = Stream.generate(() -> UUID.randomUUID().toString()).limit(10).collect(toList());
		String startFromId = ids.remove(0);

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(RESPONSE_ID_FIELD_NAME, "id");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(START_ID_FROM, startFromId);
			put(PARAM_ID, "starting_after");
			put(ITERATION_TYPE, IterationType.RESPONSE_ID_STRING.property);
		}});

		task.start(props);

		String currIdFrom = startFromId;
		for (String id : ids) {
			wireMockServer.resetMappings();
			createMapping("/test?starting_after=" + currIdFrom, "[ {\"id\": \"" + id + "\"}]");
			List<SourceRecord> polledRecords = pollNoTrace(task);
			assertEquals(singletonList(of("id", id)), toRawMessages(toMessages(polledRecords)));
			currIdFrom = id;
		}
		createMapping("/test?starting_after=" + currIdFrom, "[]");
		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		task.stop();
	}

	@Test
	public void poll_ModeId_String_ResponseField_macro() {
		List<String> ids = Stream.generate(() -> UUID.randomUUID().toString()).limit(10).collect(toList());
		String startFromId = ids.remove(0);

		Map<String, String> propsWithoutStartIdFrom = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test?page+{page_number_value}");
			put(RESPONSE_ID_FIELD_NAME, "id");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
//			put(START_ID_FROM, startFromId); required field not set intentionally
			put(PARAM_ID_MACRO, "page_number_value");
			put(ITERATION_TYPE, IterationType.RESPONSE_ID_STRING.property);
		}});

		assertThatThrownBy(() ->  task.start(propsWithoutStartIdFrom))
				.isInstanceOf(ConfigException.class)
				.hasMessageContaining("start.id.from should be set");


		Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test?page+{page_number_value}");
			put(RESPONSE_ID_FIELD_NAME, "id");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(START_ID_FROM, startFromId);
			put(PARAM_ID_MACRO, "page_number_value");
			put(ITERATION_TYPE, IterationType.RESPONSE_ID_STRING.property);
		}});

		task.start(props);

		String currIdFrom = startFromId;
		for (String id : ids) {
			wireMockServer.resetMappings();
			createMapping("/test?page%20" + currIdFrom, "[ {\"id\": \"" + id + "\"}]");
			List<SourceRecord> polledRecords = pollNoTrace(task);
			assertEquals(singletonList(of("id", id)), toRawMessages(toMessages(polledRecords)));
			currIdFrom = id;
		}
		createMapping("/test?page%20" + currIdFrom, "[]");
		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
	}

	@Test
	public void poll_paging() {

		createMapping("/test?pageNumber=1&pageSize=1", "[ {\"id\": \"1\"} ]");
		String url = "http://localhost:" + wireMockServer.port() + "/test";

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, url);
			put(AUTH_TYPE, AuthType.NONE.name());
			put(PAGE_EXPECTED_ROWS, "1");

			put(PARAM_ID, "pageNumber");
			put(PARAM_PAGE_SIZE, "pageSize");
			put(START_PAGE_FROM, "1");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
		}});

		task.start(props);
		List<SourceRecord> firstPageFull = pollNoTrace(task);
		assertTopicName(firstPageFull);
		assertEquals(singletonList(of("id", "1")), toRawMessages(toMessages(firstPageFull)));

		createMapping("/test?pageNumber=2&pageSize=1", "[ {\"id\": \"2\"} ]");
		createMapping("/test?pageNumber=3&pageSize=1", "[ ]");

		List<SourceRecord> secondPageNowFull = pollNoTrace(task);
		assertTopicName(secondPageNowFull);
		List<NexlaMessage> data = toMessages(secondPageNowFull);
		assertEquals(singletonList(of("id", "2")), toRawMessages(data));
		assertTrackerId(data.get(0), 1L, url + "?pageNumber=2&pageSize=1");

		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		task.stop();
	}

	@Test
	public void poll_paging_last_page() {

		createMapping("/test?pageNumber=2&pageSize=1", "[ {\"id\": \"1\"} ]");
		String url = "http://localhost:" + wireMockServer.port() + "/test";

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, url);
			put(AUTH_TYPE, AuthType.NONE.name());
			put(PAGE_EXPECTED_ROWS, "1");

			put(PARAM_ID, "pageNumber");
			put(PARAM_PAGE_SIZE, "pageSize");
			put(START_PAGE_FROM, "2");
			put(END_PAGE_TO, "4");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
		}});

		task.start(props);
		List<SourceRecord> firstPageFull = pollNoTrace(task);
		assertTopicName(firstPageFull);
		assertEquals(singletonList(of("id", "1")), toRawMessages(toMessages(firstPageFull)));

		createMapping("/test?pageNumber=3&pageSize=1", "[ {\"id\": \"2\"} ]");
		createMapping("/test?pageNumber=4&pageSize=1", "[ ]");

		List<SourceRecord> secondPageNowFull = pollNoTrace(task);
		assertTopicName(secondPageNowFull);
		List<NexlaMessage> data = toMessages(secondPageNowFull);
		assertEquals(singletonList(of("id", "2")), toRawMessages(data));
		assertTrackerId(data.get(0), 1L, url + "?pageNumber=3&pageSize=1");

		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		task.stop();
	}

	@Test
	public void poll_pagingIncrementing_withMacros_onlyPageNumberMacro() {
		createMapping("/test?page%201%20per_page%201", "[ {\"request1\": \"pg1&pgS1\"} ]");
		final String urlTemplate = "http://localhost:" + wireMockServer.port() + "/test?page+{page_number_value}+per_page+1";

		Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, urlTemplate);
			put(AUTH_TYPE, AuthType.NONE.name());

			put(PARAM_ID_MACRO, "page_number_value");
			put(START_PAGE_FROM, "1");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
		}});

		task.start(props);
		List<SourceRecord> firstPageFull = pollNoTrace(task);
		assertTopicName(firstPageFull);
		assertEquals(singletonList(of("request1", "pg1&pgS1")), toRawMessages(toMessages(firstPageFull)));

		createMapping("/test?page%202%20per_page%201", "[ {\"request2\": \"pg2&pgS1\"} ]");
		createMapping("/test?page%203%20per_page%201", "[ ]");

		List<SourceRecord> secondPageNowFull = pollNoTrace(task);
		assertTopicName(secondPageNowFull);
		List<NexlaMessage> data = toMessages(secondPageNowFull);
		assertEquals(singletonList(of("request2", "pg2&pgS1")), toRawMessages(data));
		Map<String, String> replacementsForMacro = new HashMap<>();
		replacementsForMacro.put("{page_number_value}", "2");
		assertTrackerIdMacro(data.get(0), 1L, urlTemplate, replacementsForMacro);

		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
	}

	@Test
	public void poll_pagingIncrementing_withMacros_failsWithParamId() {
		createMapping("/test?page%201%20per_page%201", "[ {\"request1\": \"pg1&pgS1\"} ]");
		String urlTemplate = "http://localhost:" + wireMockServer.port() + "/test?page+{page_number_value}+per_page+1";

		// PARAM_ID and PARAM_ID_MACRO both not set
		Map<String, String> propsEmtpy = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, urlTemplate);
			put(AUTH_TYPE, AuthType.NONE.name());
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
		}});

		org.assertj.core.api.Assertions.assertThatThrownBy(() -> task.start(propsEmtpy))
				.isInstanceOf(ConfigException.class)
				.hasMessage("Only one of parameters should be set: [param.id, param.id.macro]");

		// PARAM_ID and PARAM_ID_MACRO both set
		Map<String, String> propsBoth = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, urlTemplate);
			put(AUTH_TYPE, AuthType.NONE.name());
			put(PARAM_ID, "page");
			put(PARAM_ID_MACRO, "page_number_value");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
		}});

		org.assertj.core.api.Assertions.assertThatThrownBy(() -> task.start(propsBoth))
				.isInstanceOf(ConfigException.class)
				.hasMessage("Only one of parameters should be set: [param.id, param.id.macro]");
	}

	@Test
	public void poll_pagingIncrementing_withMacros_pageNumberMacro_withDefaultOffsetMacro() {
		createMapping("/test?page%201%20per_page%201", "[ {\"request1\": \"pg1&pgS1\"} ]");
		String urlTemplate = "http://localhost:" + wireMockServer.port() + "/test?page+{page_number_value}+per_page+{per_page_value=1}";

		Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, urlTemplate);
			put(AUTH_TYPE, AuthType.NONE.name());

			put(PARAM_ID_MACRO, "page_number_value");
			put(PARAM_OFFSET_MACRO, "per_page_value");
			put(START_PAGE_FROM, "1");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
		}});

		task.start(props);
		List<SourceRecord> firstPageFull = pollNoTrace(task);
		assertTopicName(firstPageFull);
		assertEquals(singletonList(of("request1", "pg1&pgS1")), toRawMessages(toMessages(firstPageFull)));

		createMapping("/test?page%202%20per_page%201", "[ {\"request2\": \"pg2&pgS1\"} ]");
		createMapping("/test?page%203%20per_page%201", "[ ]");

		List<SourceRecord> secondPageNowFull = pollNoTrace(task);
		assertTopicName(secondPageNowFull);
		List<NexlaMessage> data = toMessages(secondPageNowFull);
		assertEquals(singletonList(of("request2", "pg2&pgS1")), toRawMessages(data));

		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
	}

	@Test
	public void poll_PagingPartial() {

		createMapping("/test?pageNumber=1&pageSize=5", "[ {\"id\": \"1\"} ]");

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(AUTH_TYPE, AuthType.NONE.name());

			put(PARAM_ID, "pageNumber");
			put(PARAM_PAGE_SIZE, "pageSize");
			put(PAGE_EXPECTED_ROWS, "5");
			put(START_PAGE_FROM, "1");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
		}});

		task.start(props);
		List<SourceRecord> firstPagePartial = pollNoTrace(task);
		SourceRecord sourceRecord = firstPagePartial.get(0);
		assertEquals(
				"{\"page\":1,\"skipMessages\":1,\"messageNumber\":1,\"parentMessageNumber\":0,\"dateTime\":1523750898000}",
				sourceRecord.sourceOffset().get("KEY")
		);
		assertEquals(singletonList(of("id", "1")), toRawMessages(toMessages(firstPagePartial)));

		wireMockServer.resetAll();
		createMapping(
				"/test?pageNumber=1&pageSize=5",
				"[ {\"id\": \"1\"}, {\"id\": \"2\"}, {\"id\": \"3\"}, {\"id\": \"4\"}, {\"id\": \"5\"} ]");

		assertEquals(asList(
				of("id", "2"),
				of("id", "3"),
				of("id", "4"),
				of("id", "5")), toRawMessages(toMessages(pollNoTrace(task))));

		wireMockServer.resetAll();
		createMapping(
				"/test?pageNumber=2&pageSize=5",
				"[ {\"id\": \"6\"}, {\"id\": \"7\"}, {\"id\": \"8\"}, {\"id\": \"9\"}, {\"id\": \"10\"} ]");

		List<SourceRecord> secondPage = pollNoTrace(task);
		assertEquals(asList(
						of("id", "6"),
						of("id", "7"),
						of("id", "8"),
						of("id", "9"),
						of("id", "10")),
				toRawMessages(toMessages(secondPage)));
		task.stop();
	}

	@Test
	public void poll_PagingParallel() {

		IntStream.range(1, 5 + 1).boxed().forEach(page ->
				createMapping("/test?pageNumber=" + page + "&pageSize=1", "[ {\"id\": \"" + page + "\"} ]"));

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");

			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(PAGE_EXPECTED_ROWS, "1");

			put(PARAM_ID, "pageNumber");
			put(PARAM_PAGE_SIZE, "pageSize");
			put(START_PAGE_FROM, "1");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(REQUEST_PARALLELISM_COUNT, "5");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
		}});

		task.start(props);
		List<SourceRecord> firstPollRecords = pollNoTrace(task);

		IntStream.range(1, 5 + 1).boxed().forEach(page ->
				verify(getRequestedFor(urlEqualTo("/test?pageNumber=" + page + "&pageSize=1"))));

		assertEquals(
				asList(
						of("id", "1"),
						of("id", "2"),
						of("id", "3"),
						of("id", "4"),
						of("id", "5")
				),
				toRawMessages(toMessages(firstPollRecords)));

		IntStream.range(6, 7 + 1).boxed().forEach(page ->
				createMapping("/test?pageNumber=" + page + "&pageSize=1", "[ {\"id\": \"" + page + "\"} ]"));

		IntStream.range(8, 10 + 1).boxed().forEach(page ->
				create404Mapping("/test?pageNumber=" + page + "&pageSize=1"));

		List<SourceRecord> secondPollRecords = pollNoTrace(task);

		IntStream.range(6, 10 + 1).boxed().forEach(page ->
				verify(getRequestedFor(urlEqualTo("/test?pageNumber=" + page + "&pageSize=1"))));

		assertEquals(
				asList(
						of("id", "6"),
						of("id", "7")
				),
				toRawMessages(toMessages(secondPollRecords)));
		task.stop();
	}

	@Test
	public void poll_dataMapKeyQueue() {

		int mapId1 = 100;
		int mapId2 = 101;
		int notExistingMap = 102;

		fillDataMap(mapId1);
		fillDataMap(mapId2);

		createMapping("/test?key1=value1&key2=value1", "{\"values\" : [{ \"id\": \"1\"}, {\"id\": \"2\"} ] }");
		createMapping("/test?key1=value1&key2=value2", "{\"values\" : [{ \"id\": \"3\"}, {\"id\": \"4\"} ] }");
		createMapping("/test?key1=value2&key2=value1", "{\"values\" : [{ \"id\": \"5\"}, {\"id\": \"6\"} ] }");
		createMapping("/test?key1=value2&key2=value2", "{\"values\" : [{ \"id\": \"7\"}, {\"id\": \"8\"} ] }");

		Map<String, String> dataMap0 = new HashMap<>() {{
			put(KEY, "KEY0");
			put(ITERATION_TYPE, IterationType.DATA_MAP_KEY_QUEUE.property);
			put(MAP_ID, notExistingMap + "");
			put(REDIS_HOSTS, toHostPort(redis).toUrl());
			put(REDIS_CLUSTER_ENABLED, "false");
			put(REDIS_TLS_ENABLED, "false");
		}};

		Map<String, String> dataMap1 = new HashMap<>() {{
			put(KEY, "KEY1");

			put(ITERATION_TYPE, IterationType.DATA_MAP_KEY_QUEUE.property);
			put(MAP_ID, mapId1 + "");
			put(REDIS_HOSTS, toHostPort(redis).toUrl());
			put(REDIS_CLUSTER_ENABLED, "false");
			put(REDIS_TLS_ENABLED, "false");
		}};

		Map<String, String> dataMap2 = new HashMap<>() {{
			put(KEY, "KEY2");

			put(ITERATION_TYPE, IterationType.DATA_MAP_KEY_QUEUE.property);
			put(MAP_ID, mapId1 + "");
			put(REDIS_HOSTS, toHostPort(redis).toUrl());
			put(REDIS_CLUSTER_ENABLED, "false");
			put(REDIS_TLS_ENABLED, "false");
		}};

		Map<String, String> staticCall = new HashMap<>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test?key1={KEY1.key}&key2={KEY2.key}");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.values[*]");
			put(POLL_MS, "1");
			put(ITERATION_TYPE, IterationType.STATIC_URL.property);
		}};

		Map<String, String> props = buildByCallersConfig(dataMap0, dataMap1, dataMap2, staticCall);

		task.start(props);

		HashSet<List<Map<String, Object>>> resultData = Sets.newHashSet(
				pollData(),
				pollData(),
				pollData(),
				pollData());

		HashSet<List<Map<String, Object>>> expectedData = Sets.newHashSet(
				Lists.newArrayList(of("id", "1"), of("id", "2")),
				Lists.newArrayList(of("id", "3"), of("id", "4")),
				Lists.newArrayList(of("id", "5"), of("id", "6")),
				Lists.newArrayList(of("id", "7"), of("id", "8")));

		assertEquals(expectedData, resultData);
		assertEquals(emptyList(), pollData());
		task.stop();
	}

	@Test
	public void poll_dataMapKeyQueue_oldConfig() {

		int mapId1 = 100;
		fillDataMap(mapId1);

		createMapping("/test?value1", "{\"values\" : [{ \"id\": \"1\"}, {\"id\": \"2\"} ] }");
		createMapping("/test?value2", "{\"values\" : [{ \"id\": \"3\"}, {\"id\": \"4\"} ] }");

		Map<String, String> staticCall = new HashMap<String, String>(baseConfig()) {{
			put(METHOD, "GET");
			put(MAP_ID, mapId1 + "");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test?{map.key}");
			put(ITERATION_TYPE, IterationType.DATA_MAP_KEY_QUEUE.property);
			put(RESPONSE_DATA_PATH, "$.values[*]");
			put(REDIS_HOSTS, toHostPort(redis).toUrl());
			put(REDIS_CLUSTER_ENABLED, "false");
			put(REDIS_TLS_ENABLED, "false");
		}};

		task.start(staticCall);

		HashSet<List<Map<String, Object>>> resultData = Sets.newHashSet(
				pollData(),
				pollData()
		);

		HashSet<List<Map<String, Object>>> expectedData = Sets.newHashSet(
				Lists.newArrayList(of("id", "1"), of("id", "2")),
				Lists.newArrayList(of("id", "3"), of("id", "4"))
		);

		assertEquals(expectedData, resultData);
		assertEquals(emptyList(), pollData());
		task.stop();
	}

	@Test
	@SneakyThrows
	public void poll_folded_fail() {

		createMapping("/test1", "{\"items\": [{\"a\": \"1\"}, {\"a\": \"2\"}, {\"a\": \"3\"}, {\"a\": \"4\"}]}");
		createMapping("/test=1", "{\"b\": \"00\"}");
		createMapping("/test=2", "{\"b\": \"11\"}");
		createMapping("/test=3", "{\"b\": \"22\"}");
		createMapping("/test=4", "{\"b\": \"33\"}");

		Map<String, String> restCaller1 = new HashMap<>() {{
			put("iteration.type", "static.url");
			put("method", "GET");
			put("date.format", "yyyy-MM-dd");
			put("date.time.unit", "dd");
			put("key", "step1");
			put("url.template", "http://localhost:" + wireMockServer.port() + "/test1");
			put("response.data.path", "$.items[*]");
		}};

		Map<String, String> restCaller2 = new HashMap<>() {{
			put("iteration.type", "static.url");
			put("method", "GET");
			put("date.format", "yyyy-MM-dd");
			put("date.time.unit", "dd");
			put("key", "step2");
			put("url.template", "http://localhost:" + wireMockServer.port() + "/test={step1.a}");
			put("response.data.path", "$");
		}};
		Map<String, String> props = buildByCallersConfig(restCaller1, restCaller2);

		task.start(props);

		assertEquals(asList(of("b", "00")), toRawMessages(toMessages(pollNoTrace(task))));
		assertEquals(asList(of("b", "11")), toRawMessages(toMessages(pollNoTrace(task))));
		assertEquals(asList(of("b", "22")), toRawMessages(toMessages(pollNoTrace(task))));
		assertEquals(asList(of("b", "33")), toRawMessages(toMessages(pollNoTrace(task))));
		task.stop();
	}

	@Test
	public void poll_nextUrl_folded() {

		createMapping("/test", "{ \"response\": [ {\"id\": \"1\", \"data\": \"data1\", \"val\": null}]," +
				" \"nextUrl\": \"/test?nextPage=1\" } ");
		createMapping("/test?nextPage=1", "{ \"response\": [ {\"id\": \"2\", \"data\": \"data2\"}], \"nextUrl\": null   } ");

		createMapping("/test2?forID=1&forDATA=data1",
				"{ \"response\": [ {\"id\": \"11\", \"data\": \"1 folded data\"}]," +
						" \"nextUrl\": \"/test2?forID=1&forDATA=data1&nextPage=1\" } ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));

		createMapping("/test2?forID=1&forDATA=data1&nextPage=1",
				"{ \"response\": [ {\"id\": \"22\", \"data\": \"2 folded data\"}]," +
						" \"nextUrl\": null } ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));

		createMapping("/test2?forID=2&forDATA=data2",
				"{ \"response\": [ {\"id\": \"33\", \"data\": \"3 folded data\"}]," +
						" \"nextUrl\": \"/test2?forID=2&forDATA=data2&nextPage=1\" } ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));

		createMapping("/test2?forID=2&forDATA=data2&nextPage=1",
				"{ \"response\": [ {\"id\": \"44\", \"data\": \"4 folded data\"}]," +
						" \"nextUrl\": null } ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));

		Map<String, String> restCaller1 = new HashMap<String, String>() {{
			put(KEY, "CODE1");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(PARAM_ID, "token");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(RESPONSE_NEXT_URL_DATA_PATH, "$.nextUrl");
			put(ITERATION_TYPE, IterationType.PAGING_NEXT_URL.property);
		}};

		Map<String, String> restCaller2 = new HashMap<String, String>() {{
			put(KEY, "CODE2");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test2?forID={CODE1.id}&forDATA={CODE1.data}");
			put(PARAM_ID, "token");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(CONTENT_TYPE, "application/json;charset=UTF-8");
			put(ACCEPT_HEADER, "application/json;charset=UTF-8");
			put(RESPONSE_NEXT_URL_DATA_PATH, "$.nextUrl");
			put(ITERATION_TYPE, IterationType.PAGING_NEXT_URL.property);
		}};
		Map<String, String> props = buildByCallersConfig(restCaller1, restCaller2);

		task.start(props);

		List<SourceRecord> records;
		List<NexlaMessage> data;

		records = pollNoTrace(task);
		assertTopicName(records);
		data = toMessages(records);
		assertEquals(singletonList((of("id", "11", "data", "1 folded data"))), toRawMessages(data));

		records = pollNoTrace(task);
		assertTopicName(records);
		data = toMessages(records);
		assertEquals(singletonList((of("id", "22", "data", "2 folded data"))), toRawMessages(data));

		records = pollNoTrace(task);
		assertTopicName(records);
		data = toMessages(records);
		assertEquals(singletonList((of("id", "33", "data", "3 folded data"))), toRawMessages(data));

		records = pollNoTrace(task);
		assertTopicName(records);
		data = toMessages(records);
		assertEquals(singletonList((of("id", "44", "data", "4 folded data"))), toRawMessages(data));
		task.stop();
	}

	@Test
	public void poll_nextUrl_folded_empty_response() {

		createMapping("/test", "{ \"response\": " +
				"[ {\"id\": \"1\", \"data\": \"data1\", \"val\": null}, " +
				" {\"id\": \"2\", \"data\": \"data2\", \"val\": null}, " +
				" {\"id\": \"3\", \"data\": \"data3\", \"val\": null}, " +
				" {\"id\": \"4\", \"data\": \"data4\", \"val\": null}, " +
				" {\"id\": \"5\", \"data\": \"data5\", \"val\": null}, " +
				" {\"id\": \"6\", \"data\": \"data6\", \"val\": null}, " +
				" {\"id\": \"7\", \"data\": \"data7\", \"val\": null}, " +
				" {\"id\": \"8\", \"data\": \"data8\", \"val\": null}, " +
				" {\"id\": \"9\", \"data\": \"data9\", \"val\": null}, " +
				" {\"id\": \"10\", \"data\": \"data10\", \"val\": null}], " +
				" \"nextUrl\": null } ");

		createMapping("/test2?forID=1&forDATA=data1", "{ } ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));
		createMapping("/test2?forID=2&forDATA=data2", "{ } ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));
		createMapping("/test2?forID=3&forDATA=data3", "{ } ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));
		createMapping("/test2?forID=4&forDATA=data4", "{} ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));
		createMapping("/test2?forID=5&forDATA=data5", "{} ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));

		createMapping("/test2?forID=6&forDATA=data6",
				"{ \"response\": [ {\"id\": \"66\", \"data\": \"6 folded data\"}]," +
						" \"nextUrl\": null} ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));
		createMapping("/test2?forID=7&forDATA=data7",
				"{ \"response\": [ {\"id\": \"77\", \"data\": \"7 folded data\"}]," +
						" \"nextUrl\": null} ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));
		createMapping("/test2?forID=8&forDATA=data8",
				"{ \"response\": [ {\"id\": \"88\", \"data\": \"8 folded data\"}]," +
						" \"nextUrl\": null} ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));
		createMapping("/test2?forID=9&forDATA=data9",
				"{ \"response\": [ {\"id\": \"99\", \"data\": \"9 folded data\"}]," +
						" \"nextUrl\": null} ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));
		createMapping("/test2?forID=10&forDATA=data10",
				"{ \"response\": [ {\"id\": \"1010\", \"data\": \"10 folded data\"}]," +
						" \"nextUrl\": null} ", Optional.empty(), Optional.of("application/json;charset=UTF-8"));

		Map<String, String> restCaller1 = new HashMap<String, String>() {{
			put(KEY, "CODE1");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(PARAM_ID, "token");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(RESPONSE_NEXT_URL_DATA_PATH, "$.nextUrl");
			put(ITERATION_TYPE, IterationType.PAGING_NEXT_URL.property);
			put(MAX_POLL_TIME_MS, "10");
		}};

		Map<String, String> restCaller2 = new HashMap<String, String>() {{
			put(KEY, "CODE2");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test2?forID={CODE1.id}&forDATA={CODE1.data}");
			put(PARAM_ID, "token");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(CONTENT_TYPE, "application/json;charset=UTF-8");
			put(ACCEPT_HEADER, "application/json;charset=UTF-8");
			put(RESPONSE_NEXT_URL_DATA_PATH, "$.nextUrl");
			put(ITERATION_TYPE, IterationType.PAGING_NEXT_URL.property);
			put(MAX_POLL_TIME_MS, "10");
		}};
		Map<String, String> props = buildByCallersConfig(restCaller1, restCaller2);

		task.start(props);

		List<SourceRecord> records;
		List<NexlaMessage> data;

		records = pollNoTrace(task);
		assertTrue(records.isEmpty());
		records = pollNoTrace(task);
		assertTrue(records.isEmpty());
		records = pollNoTrace(task);
		assertTrue(records.isEmpty());
		records = pollNoTrace(task);
		assertTrue(records.isEmpty());
		records = pollNoTrace(task);
		assertTrue(records.isEmpty());
		records = pollNoTrace(task);
		assertEquals(1, records.size());
		records = pollNoTrace(task);
		assertEquals(1, records.size());
		records = pollNoTrace(task);
		assertEquals(1, records.size());
		records = pollNoTrace(task);
		assertEquals(1, records.size());
		records = pollNoTrace(task);
		assertTopicName(records);
		data = toMessages(records);
		assertEquals(singletonList((of("id", "1010", "data", "10 folded data"))), toRawMessages(data));
		task.stop();
	}

	@Test
	public void poll_PagingParallel_oldConfig() {

		IntStream.range(1, 5 + 1).boxed().forEach(page ->
				createMapping("/test?pageNumber=" + page + "&pageSize=1", "[ {\"id\": \"" + page + "\"} ]"));

		Map<String, String> baseProps = new HashMap<String, String>(baseConfig()) {{
			put(AUTH_TYPE, AuthType.NONE.name());

			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(PAGE_EXPECTED_ROWS, "1");

			put(PARAM_ID, "pageNumber");
			put(PARAM_PAGE_SIZE, "pageSize");
			put(START_PAGE_FROM, "1");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(REQUEST_PARALLELISM_COUNT, "5");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
		}};

		task.start(baseProps);
		List<SourceRecord> firstPollRecords = pollNoTrace(task);

		IntStream.range(1, 5 + 1).boxed().forEach(page ->
				verify(getRequestedFor(urlEqualTo("/test?pageNumber=" + page + "&pageSize=1"))));

		assertEquals(
				asList(
						of("id", "1"),
						of("id", "2"),
						of("id", "3"),
						of("id", "4"),
						of("id", "5")
				),
				toRawMessages(toMessages(firstPollRecords)));

		IntStream.range(6, 10 + 1).boxed().forEach(page ->
				createMapping("/test?pageNumber=" + page + "&pageSize=1", "[ {\"id\": \"" + page + "\"} ]"));

		List<SourceRecord> secondPollRecords = pollNoTrace(task);

		IntStream.range(6, 10 + 1).boxed().forEach(page ->
				verify(getRequestedFor(urlEqualTo("/test?pageNumber=" + page + "&pageSize=1"))));

		assertEquals(
				asList(
						of("id", "6"),
						of("id", "7"),
						of("id", "8"),
						of("id", "9"),
						of("id", "10")
				),
				toRawMessages(toMessages(secondPollRecords)));
		task.stop();
	}

	@Test
	public void poll_with_dates() {
		createMapping("/test?date0=2018-04-14T00:08:18.000Z&date1=2018-04-15T00:08:18.000Z&date2=2018-04-16T00:08:18.000Z",
				"{ \"response\": [ {\"id\": \"1\", \"data\": \"data1\"}], \"nextUrl\": \"/test?date0=2018-04-14T00:08:18.000Z&date1=2018-04-15T00:08:18.000Z&date2=2018-04-16T00:08:18.000Z&nextPage=1\" } "
		);
		createMapping("/test?date0=2018-04-14T00:08:18.000Z&date1=2018-04-15T00:08:18.000Z&date2=2018-04-16T00:08:18.000Z&nextPage=1",
				"{ \"response\": [ {\"id\": \"2\", \"data\": \"data2\"}], \"nextUrl\": null }"
		);
		createMapping("/test2/date0=2018-04-14&date1=2018-04-15&date2=2018-04-16/id1=1?pageNumber=1&pageSize=1", "[ {\"result\": \"1\"} ]");
		createMapping("/test2/date0=2018-04-14&date1=2018-04-15&date2=2018-04-16/id1=1?pageNumber=2&pageSize=1", "[ {\"result\": \"2\"} ]");
		create404Mapping("/test2/date0=2018-04-14&date1=2018-04-15&date2=2018-04-16/id1=1?pageNumber=3&pageSize=1");
		createMapping("/test2/date0=2018-04-14&date1=2018-04-15&date2=2018-04-16/id1=2?pageNumber=1&pageSize=1", "[ {\"result\": \"3\"} ]");
		create404Mapping("/test2/date0=2018-04-14&date1=2018-04-15&date2=2018-04-16/id1=2?pageNumber=2&pageSize=1");

		createMapping("/test?date0=2018-04-15T00:08:18.000Z&date1=2018-04-16T00:08:18.000Z&date2=2018-04-17T00:08:18.000Z",
				"{ \"response\": [ {\"id\": \"11\", \"data\": \"data1\"}], \"nextUrl\": \"/test?date0=2018-04-15T00:08:18.000Z&date1=2018-04-16T00:08:18.000Z&date2=2018-04-17T00:08:18.000Z&nextPage=1\" } "
		);
		createMapping("/test?date0=2018-04-15T00:08:18.000Z&date1=2018-04-16T00:08:18.000Z&date2=2018-04-17T00:08:18.000Z&nextPage=1",
				"{ \"response\": [ {\"id\": \"22\", \"data\": \"data2\"}], \"nextUrl\": null }"
		);
		createMapping("/test2/date0=2018-04-15&date1=2018-04-16&date2=2018-04-17/id1=11?pageNumber=1&pageSize=1", "[ {\"result\": \"4\"} ]");
		createMapping("/test2/date0=2018-04-15&date1=2018-04-16&date2=2018-04-17/id1=11?pageNumber=2&pageSize=1", "[ {\"result\": \"5\"} ]");
		create404Mapping("/test2/date0=2018-04-15&date1=2018-04-16&date2=2018-04-17/id1=11?pageNumber=3&pageSize=1");
		createMapping("/test2/date0=2018-04-15&date1=2018-04-16&date2=2018-04-17/id1=22?pageNumber=1&pageSize=1", "[ {\"result\": \"6\"} ]");
		create404Mapping("/test2/date0=2018-04-15&date1=2018-04-16&date2=2018-04-17/id1=22?pageNumber=2&pageSize=1");

		Map<String, String> restCaller1 = new HashMap<String, String>() {{
			put(KEY, "CODE1");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test?date0={now-1}&date1={now}&date2={now+1}");
			put(PARAM_ID, "token");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(RESPONSE_NEXT_URL_DATA_PATH, "$.nextUrl");
			put(ITERATION_TYPE, IterationType.PAGING_NEXT_URL.property);
			put(DATE_FORMAT, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z");
			put(DATE_TIME_UNIT, "dd");
		}};

		Map<String, String> restCaller3 = new HashMap<String, String>() {{
			put(KEY, "CODE2");
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test2/date0={now-1}&date1={now}&date2={now+1}/id1={CODE1.id}");
			put(AUTH_TYPE, AuthType.NONE.name());

			put(PARAM_ID, "pageNumber");
			put(PARAM_PAGE_SIZE, "pageSize");
			put(PAGE_EXPECTED_ROWS, "1");
			put(START_PAGE_FROM, "1");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
			put(DATE_FORMAT, "yyyy-MM-dd");
			put(DATE_TIME_UNIT, "dd");
		}};

		Map<String, String> props = buildByCallersConfig(restCaller1, restCaller3);

		task.start(props);

		assertEquals(singletonList((of("result", "1"))), pollData());
		assertEquals(singletonList((of("result", "2"))), pollData());
		assertEquals(singletonList((of("result", "3"))), pollData());
		assertEquals(emptyList(), pollData());
		task.stop();
	}

	@Test
	public void poll_nextUrl_folded_3_times() {

		createMapping("/test", "{ \"response\": [ {\"id\": \"1\", \"data\": \"data1\"}],             \"nextUrl\": \"/test?nextPage=1\" } ");
		createMapping("/test?nextPage=1", "{ \"response\": [ {\"id\": \"2\", \"data\": \"data2\"}], \"nextUrl\": null   } ");

		createMapping("/test2?id=1&data=data1",
				"{ \"response\": [ {\"id\": \"11\", \"data\": \"1_data\"}]," +
						" \"nextUrl\": \"/test2?id=1&data=data1&nextPage=1\" } ");

		createMapping("/test2?id=1&data=data1&nextPage=1",
				"{ \"response\": [ {\"id\": \"22\", \"data\": \"2_data\"}]," +
						" \"nextUrl\": null } ");

		createMapping("/test2?id=2&data=data2",
				"{ \"response\": [ {\"id\": \"33\", \"data\": \"3_data\"}]," +
						" \"nextUrl\": \"/test2?id=2&data=data2&nextPage=1\" } ");

		createMapping("/test2?id=2&data=data2&nextPage=1",
				"{ \"response\": [ {\"id\": \"44\", \"data\": \"4_data\"}]," +
						" \"nextUrl\": null } ");

		createMapping("/test3/id1=1/id2=11&date=2018-04-15?pageNumber=1&pageSize=1", "[ {\"result\": \"1\"} ]");
		createMapping("/test3/id1=1/id2=11&date=2018-04-15?pageNumber=2&pageSize=1", "[ {\"result\": \"2\"} ]");
		createMapping("/test3/id1=1/id2=22&date=2018-04-15?pageNumber=1&pageSize=1", "[ {\"result\": \"3\"} ]");
		createMapping("/test3/id1=1/id2=22&date=2018-04-15?pageNumber=2&pageSize=1", "[ {\"result\": \"4\"} ]");
		createMapping("/test3/id1=2/id2=33&date=2018-04-15?pageNumber=1&pageSize=1", "[ {\"result\": \"5\"} ]");
		createMapping("/test3/id1=2/id2=33&date=2018-04-15?pageNumber=2&pageSize=1", "[ {\"result\": \"6\"} ]");
		createMapping("/test3/id1=2/id2=44&date=2018-04-15?pageNumber=1&pageSize=1", "[ {\"result\": \"7\"} ]");
		createMapping("/test3/id1=2/id2=44&date=2018-04-15?pageNumber=2&pageSize=1", "[ {\"result\": \"8\"} ]");

		createMapping("/test3/id1=1/id2=11&date=2018-04-15?pageNumber=3&pageSize=1", "[ ]");
		createMapping("/test3/id1=2/id2=33&date=2018-04-15?pageNumber=3&pageSize=1", "[ ]");
		create404Mapping("/test3/id1=1/id2=22&date=2018-04-15?pageNumber=3&pageSize=1");

		Map<String, String> restCaller1 = new HashMap<String, String>() {{
			put(KEY, "CODE1");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test");
			put(PARAM_ID, "token");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(RESPONSE_NEXT_URL_DATA_PATH, "$.nextUrl");
			put(ITERATION_TYPE, IterationType.PAGING_NEXT_URL.property);
		}};

		Map<String, String> restCaller2 = new HashMap<String, String>() {{
			put(KEY, "CODE2");
			put(AUTH_TYPE, AuthType.NONE.name());
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test2?id={CODE1.id}&data={CODE1.data}");
			put(PARAM_ID, "token");
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$.response[*]");
			put(RESPONSE_NEXT_URL_DATA_PATH, "$.nextUrl");
			put(ITERATION_TYPE, IterationType.PAGING_NEXT_URL.property);
		}};

		Map<String, String> restCaller3 = new HashMap<String, String>() {{
			put(KEY, "CODE3");
			put(METHOD, "GET");
			put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/test3/id1={CODE1.id}/id2={CODE2.id}&date={now}");
			put(AUTH_TYPE, AuthType.NONE.name());

			put(PARAM_ID, "pageNumber");
			put(PARAM_PAGE_SIZE, "pageSize");
			put(PAGE_EXPECTED_ROWS, "1");
			put(START_PAGE_FROM, "1");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING.property);
			put(DATE_FORMAT, "yyyy-MM-dd");
			put(DATE_TIME_UNIT, "dd");
		}};

		Map<String, String> props = buildByCallersConfig(restCaller1, restCaller2, restCaller3);

		task.start(props);

		assertEquals(singletonList((of("result", "1"))), pollData());
		assertEquals(singletonList((of("result", "2"))), pollData());
		assertEquals(singletonList((of("result", "3"))), pollData());
		assertEquals(singletonList((of("result", "4"))), pollData());
		assertEquals(singletonList((of("result", "5"))), pollData());
		assertEquals(singletonList((of("result", "6"))), pollData());
		assertEquals(singletonList((of("result", "7"))), pollData());
		assertEquals(singletonList((of("result", "8"))), pollData());
		task.stop();
	}

	@SneakyThrows
	@Test
	public void testAddNewOffsetParameter() {
		String json1 = "{\"page\":0,\"messageNumber\":0,\"pageSize\":0,\"parentMessageNumber\":0,\"dateTime\":1549911021242}";
		String json2 = "{\"page\":0,\"skipMessages\":0,\"messageNumber\":0,\"pageSize\":0,\"parentMessageNumber\":0,\"dateTime\":1549911021242}";
		PagingIncrementingOffset offset1 = RestIterationOffset.fromJson(json1, PagingIncrementingOffset.class);
		PagingIncrementingOffset offset2 = RestIterationOffset.fromJson(json2, PagingIncrementingOffset.class);

		assertEquals(offset1.toJson(), offset2.toJson());
	}

	public static byte[] iv() {
		byte[] iv = new byte[12];
		new SecureRandom().nextBytes(iv);
		return iv;
	}

	public static byte[] encrypt(Map<String, Object> credsMap, SecretKey secret, byte[] iv) throws Exception {
		Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");

		GCMParameterSpec gcmSpecWithIV = new GCMParameterSpec(128, iv);
		cipher.init(Cipher.ENCRYPT_MODE, secret, gcmSpecWithIV);

		return cipher.doFinal(JsonUtils.toJsonString(credsMap).getBytes(UTF_8));
	}

	private static Map<String, String> baseConfigWithCreds(Map<String, Object> credentials) throws Exception {
		byte[] iv = iv();

		String decryptKey = "e2bdf6f807bgf66316c38acb0cc01919";
		SecretKey key = new SecretKeySpec(decryptKey.getBytes(UTF_8), "AES");

		String credsEnc = new String(java.util.Base64.getEncoder().encode(encrypt(credentials, key, iv)), UTF_8);
		String credsEncIv = new String(java.util.Base64.getEncoder().encode(iv), UTF_8);

		Map<String, String> baseConfig = baseConfig();
		baseConfig.put(CREDS_ENC, credsEnc);
		baseConfig.put(CREDS_ENC_IV, credsEncIv);
		baseConfig.put(CREDENTIALS_DECRYPT_KEY, decryptKey);
		baseConfig.put(NEXLA_CREDS_ENC, credsEnc);
		baseConfig.put(NEXLA_CREDS_ENC_IV, credsEncIv);
		baseConfig.put(UNIT_TEST, "false");
		return baseConfig;
	}

	@SneakyThrows
	@Test
	public void credentialSubstitutionTest1() {
		String token = UUID.randomUUID().toString();

		String xAuth = "x-auth: " + token;
		String cursor = "cursor: 5";

		stubFor(post(urlEqualTo("/test?token=" + token))
				.withRequestBody(new EqualToPattern(
						"request.headers: " + xAuth + ", " + cursor + "\n" +
								"request.headers.x-auth: " + token + "\n" +
								"request.headers.cursor: 5"
				))
				.willReturn(aResponse().withStatus(200).withBody("[{\"result\": \"1\"}]"))
		);
		String url = "http://localhost:" + wireMockServer.port() + "/test?token={data_credential[\"request.headers\"][\"x-auth\"]}";

		Map<String, String> restCaller1 = new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(METHOD, "POST");
			put(URL_TEMPLATE, url);
			put(AUTH_TYPE, AuthType.NONE.name());
			put(BODY_TEMPLATE,
					"request.headers: {data_credential[\"request.headers\"]}\n" +
							"request.headers.x-auth: {data_credential[\"request.headers\"][\"x-auth\"]}\n" +
							"request.headers.cursor: {data_credential[\"request.headers\"][\"cursor\"]}"
			);
			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, STATIC_URL.property);
		}};

		Map<String, String> props = buildByCallersConfigWithBase(baseConfigWithCreds(Map.of(
				"request.headers", xAuth + ", " + cursor
		)), restCaller1);

		task.start(props);

		assertEquals(singletonList((of("result", "1"))), pollData());
		assertEquals(emptyList(), pollData());
	}

	@Test
	public void poll_paging_offset() {

		createMapping("/test?pageSize=5",
				"[{\"id\": \"1\"}, " +
						"{\"id\": \"2\"}, " +
						"{\"id\": \"3\"}, " +
						"{\"id\": \"4\"}, " +
						"{\"id\": \"5\"}]");

		createMapping("/test?offset=5&pageSize=5",
				"[{\"id\": \"6\"}, " +
						"{\"id\": \"7\"}, " +
						"{\"id\": \"8\"}, " +
						"{\"id\": \"9\"}, " +
						"{\"id\": \"10\"}]");

		createMapping("/test?offset=10&pageSize=5", "[{\"id\": \"11\"}]");

		String url = "http://localhost:" + wireMockServer.port() + "/test";

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, url);
			put(AUTH_TYPE, AuthType.NONE.name());

			put(PAGE_EXPECTED_ROWS, "5");
			put(PARAM_OFFSET, "offset");
			put(PARAM_PAGE_SIZE, "pageSize");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING_OFFSET.property);
		}});

		task.start(props);

		assertEquals(asList(
				of("id", "1"),
				of("id", "2"),
				of("id", "3"),
				of("id", "4"),
				of("id", "5")), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(asList(
				of("id", "6"),
				of("id", "7"),
				of("id", "8"),
				of("id", "9"),
				of("id", "10")), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(asList(of("id", "11")), toRawMessages(toMessages(pollNoTrace(task))));
		assertEquals(emptyList(), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		task.stop();
	}

	@Test
	public void poll_paging_offset_withMacro_onlyOffsetMacro() {
		createMapping("/test?offset%200%20pageSize%205",
				"[{\"id\": \"1\"}, " +
						"{\"id\": \"2\"}, " +
						"{\"id\": \"3\"}, " +
						"{\"id\": \"4\"}, " +
						"{\"id\": \"5\"}]");

		createMapping("/test?offset%205%20pageSize%205",
				"[{\"id\": \"6\"}, " +
						"{\"id\": \"7\"}, " +
						"{\"id\": \"8\"}, " +
						"{\"id\": \"9\"}, " +
						"{\"id\": \"10\"}]");

		createMapping("/test?offset%2010%20pageSize%205", "[{\"id\": \"11\"}]");
		final String urlTemplate = "http://localhost:" + wireMockServer.port() + "/test?offset+{offset_macro}+pageSize+5";

		Map<String, String> propsWithoutStartOffsetFrom = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, urlTemplate);
			put(AUTH_TYPE, AuthType.NONE.name());

			put(PARAM_OFFSET_MACRO, "offset_macro");
			put(PAGE_EXPECTED_ROWS_MACRO, "per_page_value");
			put(START_OFFSET_FROM, "0");
			put(PARAM_PAGE_SIZE_MACRO, "5");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING_OFFSET.property);
		}});

		assertThatThrownBy(() -> task.start(propsWithoutStartOffsetFrom))
				.isInstanceOf(ConfigException.class)
				.hasMessage("page.expected.rows should be set");

		Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, urlTemplate);
			put(AUTH_TYPE, AuthType.NONE.name());

			put(PARAM_OFFSET_MACRO, "offset_macro");
			put(PAGE_EXPECTED_ROWS_MACRO, "per_page_value");
			put(START_OFFSET_FROM, "0");
			put(PARAM_PAGE_SIZE_MACRO, "5");
			put(PAGE_EXPECTED_ROWS, "5");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING_OFFSET.property);
		}});

		task.start(props);

		assertEquals(asList(
				of("id", "1"),
				of("id", "2"),
				of("id", "3"),
				of("id", "4"),
				of("id", "5")), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(asList(
				of("id", "6"),
				of("id", "7"),
				of("id", "8"),
				of("id", "9"),
				of("id", "10")), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(asList(of("id", "11")), toRawMessages(toMessages(pollNoTrace(task))));
		assertEquals(emptyList(), toRawMessages(toMessages(pollNoTrace(task))));
		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
	}

	@Test
	public void poll_paging_offset_withMacro_onlyOffsetAndPageSizeMacroDefaultValue() {
		createMapping("/test?offset%200%20pageSize%205",
				"[{\"id\": \"1\"}, " +
						"{\"id\": \"2\"}, " +
						"{\"id\": \"3\"}, " +
						"{\"id\": \"4\"}, " +
						"{\"id\": \"5\"}]");

		createMapping("/test?offset%205%20pageSize%205",
				"[{\"id\": \"6\"}, " +
						"{\"id\": \"7\"}, " +
						"{\"id\": \"8\"}, " +
						"{\"id\": \"9\"}, " +
						"{\"id\": \"10\"}]");

		createMapping("/test?offset%2010%20pageSize%205", "[{\"id\": \"11\"}]");

		String urlTemplate = "http://localhost:" + wireMockServer.port() + "/test?offset+{offset_macro}+pageSize+{per_page_value=5}";

		Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, urlTemplate);
			put(AUTH_TYPE, AuthType.NONE.name());

			put(PARAM_OFFSET_MACRO, "offset_macro");
			put(PAGE_EXPECTED_ROWS_MACRO, "per_page_value");
			put(START_OFFSET_FROM, "0");
			put(PARAM_PAGE_SIZE_MACRO, "5");
			put(PAGE_EXPECTED_ROWS, "5");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING_OFFSET.property);
		}});

		task.start(props);

		assertEquals(asList(
				of("id", "1"),
				of("id", "2"),
				of("id", "3"),
				of("id", "4"),
				of("id", "5")), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(asList(
				of("id", "6"),
				of("id", "7"),
				of("id", "8"),
				of("id", "9"),
				of("id", "10")), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(asList(of("id", "11")), toRawMessages(toMessages(pollNoTrace(task))));
		assertEquals(emptyList(), toRawMessages(toMessages(pollNoTrace(task))));
		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
	}

	@Test
	public void poll_paging_offset_body_params() {

		MappingBuilder builder = post(urlEqualTo("/test")) //
				.withRequestBody(new EqualToJsonPattern("{\"pageSize\": \"5\"}", true, false))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody("[{\"id\": \"1\"}, " +
								"{\"id\": \"2\"}, " +
								"{\"id\": \"3\"}, " +
								"{\"id\": \"4\"}, " +
								"{\"id\": \"5\"}]"));

		stubFor(builder);

		MappingBuilder builder1 = post(urlEqualTo("/test")) // offset=5&pageSize=5
				.withRequestBody(new EqualToJsonPattern("{\"pageSize\": \"5\", \"offset\": \"5\"}", true, false))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody("[{\"id\": \"6\"}, " +
								"{\"id\": \"7\"}, " +
								"{\"id\": \"8\"}, " +
								"{\"id\": \"9\"}, " +
								"{\"id\": \"10\"}]"));

		stubFor(builder1);

		MappingBuilder builder4 = post(urlEqualTo("/test")) // offset=10&pageSize=5
				.withRequestBody(new EqualToJsonPattern("{\"pageSize\": \"5\", \"offset\": \"10\"}", true, false))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody("[{\"id\": \"11\"}]"));

		stubFor(builder4);

		String url = "http://localhost:" + wireMockServer.port() + "/test";

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(METHOD, "POST");
			put(URL_TEMPLATE, url);
			put(AUTH_TYPE, AuthType.NONE.name());
			put(PARAMS_IN_BODY, "true");
			put(PAGE_EXPECTED_ROWS, "5");
			put(PARAM_OFFSET, "offset");
			put(PARAM_PAGE_SIZE, "pageSize");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING_OFFSET.property);
		}});

		task.start(props);

		assertEquals(asList(
				of("id", "1"),
				of("id", "2"),
				of("id", "3"),
				of("id", "4"),
				of("id", "5")), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(asList(
				of("id", "6"),
				of("id", "7"),
				of("id", "8"),
				of("id", "9"),
				of("id", "10")), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(asList(of("id", "11")), toRawMessages(toMessages(pollNoTrace(task))));
		assertEquals(emptyList(), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		task.stop();
	}

	@Test
	public void poll_paging_offset_limit() {

		createMapping("/test?pageSize=5",
				"[{\"id\": \"1\"}, " +
						"{\"id\": \"2\"}, " +
						"{\"id\": \"3\"}, " +
						"{\"id\": \"4\"}, " +
						"{\"id\": \"5\"}]");

		createMapping("/test?offset=5&pageSize=5",
				"[{\"id\": \"6\"}, " +
						"{\"id\": \"7\"}, " +
						"{\"id\": \"8\"}, " +
						"{\"id\": \"9\"}, " +
						"{\"id\": \"10\"}]");

		createMapping("/test?offset=10&pageSize=5", "[{\"id\": \"11\"}]");

		String url = "http://localhost:" + wireMockServer.port() + "/test";

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, url);
			put(AUTH_TYPE, AuthType.NONE.name());

			put(PAGE_EXPECTED_ROWS, "5");
			put(PARAM_OFFSET, "offset");
			put(PARAM_PAGE_SIZE, "pageSize");
			put(END_OFFSET_TO, "5");

			put(RESPONSE_FORMAT, "json");
			put(RESPONSE_DATA_PATH, "$[*]");
			put(ITERATION_TYPE, IterationType.PAGING_INCREMENTING_OFFSET.property);
		}});

		task.start(props);

		assertEquals(asList(
				of("id", "1"),
				of("id", "2"),
				of("id", "3"),
				of("id", "4"),
				of("id", "5")), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(asList(
				of("id", "6"),
				of("id", "7"),
				of("id", "8"),
				of("id", "9"),
				of("id", "10")), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(emptyList(), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		task.stop();
	}

	@Test
	public void staticUrl_DetectContentFormat() {
		createMapping("/detectContentFormat", "<ValidXml>\n" +
						"    <Body>\n" +
						"        <Item>\n" +
						"            <Title>Title0</Title>\n" +
						"            <Description>Description0</Description>\n" +
						"        </Item>\n" +
						"        <Item>\n" +
						"            <Title>Title1</Title>\n" +
						"            <Description>Description1</Description>\n" +
						"        </Item>" +
						"    </Body>\n" +
						"</ValidXml>",
				Optional.empty(),
				Optional.of("text/plain"),
				Optional.empty()
		);

		String url = "http://localhost:" + wireMockServer.port() + "/detectContentFormat";

		Map<String, String> props = buildByCallersConfig(new HashMap<String, String>() {{
			put(KEY, "KEY");
			put(METHOD, "GET");
			put(URL_TEMPLATE, url);
			put(AUTH_TYPE, AuthType.NONE.name());

			put(RESPONSE_DATA_PATH, "*/Body/Item");
			put(ITERATION_TYPE, IterationType.STATIC_URL.property);
		}});

		task.start(props);


		assertEquals(
				asList(
						of("Item", of("Title", "Title0", "Description", "Description0")),
						of("Item", of("Title", "Title1", "Description", "Description1"))
				),
				toRawMessages(toMessages(pollNoTrace(task)))
		);

		assertEquals(emptyList(), toRawMessages(toMessages(pollNoTrace(task))));

		assertEquals(emptyList(), toMessages(pollNoTrace(task)));
		task.stop();
	}

	@Test
	public void codeContainer_withUrl () {
		String url = "http://localhost:" + wireMockServer.port() + "/source";

		stubFor(get(urlEqualTo("/source")).inScenario("")
				.willReturn(aResponse().withStatus(200)
						.withBody("{\"foo\":1,\"bar\":2}"))
		);

		Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
            put(KEY, "KEY");
            put(METHOD, "GET");
            put(URL_TEMPLATE, url);
            put(AUTH_TYPE, AuthType.NONE.name());

            put(ITERATION_TYPE, IterationType.CODE_CONTAINER_ITERATION.property);
			put(RestIterationConfig.CODE_CONTAINER_ID, "1");
        }});

		task.start(props);

		List<Map<String,Object>> expected = Collections.singletonList(new HashMap<>() {{
			put("foo", 1);
			put("bar", 2);
			put("baz", 3);
		}});
		assertEquals(expected, pollData());

		task.stop();
	}

	@Test
	public void codeContainer_secondStep () {
		String url = "http://localhost:" + wireMockServer.port() + "/source";

		stubFor(get(urlEqualTo("/source")).inScenario("")
				.willReturn(aResponse().withStatus(200)
						.withBody("{\"foo\":1,\"bar\":2}"))
		);

		Map<String, String> props = buildByCallersConfig(
				new HashMap<>() {{
					put(KEY, "STEP1");
					put(METHOD, "GET");
					put(URL_TEMPLATE, url);
					put(AUTH_TYPE, AuthType.NONE.name());

					put(ITERATION_TYPE, STATIC_URL.property);
				}},
				new HashMap<>() {{
					put(KEY, "STEP2");
					put(METHOD, "GET");
					put(AUTH_TYPE, AuthType.NONE.name());

					put(ITERATION_TYPE, IterationType.CODE_CONTAINER_ITERATION.property);
					put(RestIterationConfig.CODE_CONTAINER_ID, "1");
				}}
		);

		task.start(props);

		List<Map<String,Object>> expected = Collections.singletonList(new HashMap<>() {{
			put("foo", 1);
			put("bar", 2);
			put("baz", 3);
		}});
		assertEquals(expected, pollData());

		task.stop();
	}

	@Test
	public void codeContainer_returnList() {
		// assume input has shape {"items": [{any object}]}
		String transformCode = "def transform(input, metadata, args):\n" +
				"    return input['items']";
		String encodedTransformCode = new String(java.util.Base64.getEncoder().encode(transformCode.getBytes()));
		when(adminApiClientMock.getTransform(2)).thenReturn(new Transform(
				2,
				"test transform",
				true,
				true,
				null,
				null,
				"test",
				"jolt_custom",
				new ArrayList<>() {{
					add(new TransformCode("nexla.custom", new TransformCodeSpec(null, "python", "base64", encodedTransformCode)));
				}},
				"record"
		));

		stubFor(get(urlEqualTo("/items"))
				.willReturn(aResponse().withStatus(200)
						.withBody("{\"items\": [{\"foo\": \"bar\"}, {\"foo\": \"baz\"}]}")));

		Map<String, String> props = buildByCallersConfig(
				new HashMap<>() {{
					put(KEY, "STEP1");
					put(METHOD, "GET");
					put(URL_TEMPLATE, "http://localhost:" + wireMockServer.port() + "/items");
					put(AUTH_TYPE, AuthType.NONE.name());
					put(RESPONSE_DATA_PATH, "$");

					put(ITERATION_TYPE, STATIC_URL.property);
				}},
				new HashMap<>() {{
					put(KEY, "STEP2");
					put(METHOD, "GET");
					put(AUTH_TYPE, AuthType.NONE.name());

					put(ITERATION_TYPE, IterationType.CODE_CONTAINER_ITERATION.property);
					put(RestIterationConfig.CODE_CONTAINER_ID, "2");
				}}
		);
		task.start(props);

		List<Map<String,Object>> res = pollData();
		Assert.assertEquals(2, res.size());
		Assert.assertEquals("bar", res.get(0).get("foo"));
		Assert.assertEquals("baz", res.get(1).get("foo"));

		task.stop();
	}

	@Test
	public void metrics_happyPath() {
		String url = "http://localhost:" + wireMockServer.port() + "/test";

		stubFor(get(urlEqualTo("/test"))
				.willReturn(aResponse().withStatus(200).withBody("{\"foo\":1,\"bar\":2}"))
		);

		int sourceId = testSourceId;
		withConsumer((notifyTopic, notifyConsumer) -> withConsumer((metricsTopic, metricsConsumer) -> {
			Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
				put(KEY, "KEY");
				put(METHOD, "GET");
				put(URL_TEMPLATE, url);
				put(AUTH_TYPE, AuthType.NONE.name());

				put(ITERATION_TYPE, IterationType.STATIC_URL.property);
			}});
			props.put(SOURCE_ID, sourceId + "");
			props.put(METRICS_TOPIC, metricsTopic);
			props.put(NOTIFY_TOPIC, notifyTopic);

			task.start(props);

			List<Map<String,Object>> result = pollData();

			assertEquals(Collections.singletonList(new HashMap<>() {{
				put("foo", 1);
				put("bar", 2);
			}}), result);

			List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sourceId);
			Assert.assertEquals(1, metrics.size());
			Assert.assertEquals(1, metrics.get(0).getFields().get("records"));
			Assert.assertEquals(0, metrics.get(0).getFields().get("error_count"));
			Assert.assertNotEquals(Long.valueOf(0), metrics.get(0).getMillis());

			List<NexlaNotificationEvent> notifications = readNotifications(notifyConsumer, sourceId);
			Assert.assertTrue(notifications.isEmpty());
		}, "metrics"), "notify");

		task.stop();
	}

	@Test
	public void metrics_error_noException() {
		String url = "http://localhost:" + wireMockServer.port() + "/test";

		stubFor(get(urlEqualTo("/test"))
				.willReturn(aResponse().withStatus(404)) // static url with a 404 returns the error message, rather than throwing an exception
		);

		int sourceId = testSourceId;
		withConsumer((notifyTopic, notifyConsumer) -> withConsumer((metricsTopic, metricsConsumer) -> {
			Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
				put(KEY, "KEY");
				put(METHOD, "GET");
				put(URL_TEMPLATE, url);
				put(AUTH_TYPE, AuthType.NONE.name());

				put(ITERATION_TYPE, IterationType.STATIC_URL.property);
			}});
			props.put(SOURCE_ID, sourceId + "");
			props.put(METRICS_TOPIC, metricsTopic);
			props.put(NOTIFY_TOPIC, notifyTopic);

			task.start(props);

			List<Map<String,Object>> result = pollData();

			Assert.assertTrue(result.isEmpty());

			List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sourceId);
			Assert.assertEquals(1, metrics.size());
			Assert.assertEquals(1, metrics.get(0).getFields().get("error_count"));
			Assert.assertNotEquals(Long.valueOf(0), metrics.get(0).getMillis());

			List<NexlaNotificationEvent> errorNotifications = readNotifications(notifyConsumer, sourceId).stream().filter(n -> n.getEventType() == NotificationEventType.ERROR).collect(toList());
			Assert.assertEquals(1, errorNotifications.size());
		}, "metrics"), "notify");

		task.stop();
	}

	@Test
	public void metrics_error_withException() {
		String url = "http://localhost:" + wireMockServer.port() + "/test";

		stubFor(get(urlEqualTo("/test"))
				.willReturn(aResponse().withStatus(500)) // static url with a 500 throws an exception
		);

		int sourceId = testSourceId;
		withConsumer((notifyTopic, notifyConsumer) -> withConsumer((metricsTopic, metricsConsumer) -> {
			Map<String, String> props = buildByCallersConfig(new HashMap<>() {{
				put(KEY, "KEY");
				put(METHOD, "GET");
				put(URL_TEMPLATE, url);
				put(AUTH_TYPE, AuthType.NONE.name());

				put(ITERATION_TYPE, IterationType.STATIC_URL.property);
			}});
			props.put(SOURCE_ID, sourceId + "");
			props.put(METRICS_TOPIC, metricsTopic);
			props.put(NOTIFY_TOPIC, notifyTopic);

			task.start(props);

			List<Map<String,Object>> result = pollData();

			Assert.assertTrue(result.isEmpty());

			List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sourceId);
			Assert.assertEquals(1, metrics.size());
			Assert.assertEquals(1, metrics.get(0).getFields().get("error_count"));
			Assert.assertNotEquals(Long.valueOf(0), metrics.get(0).getMillis());

			List<NexlaNotificationEvent> errorNotifications = readNotifications(notifyConsumer, sourceId).stream().filter(n -> n.getEventType() == NotificationEventType.ERROR).collect(toList());
			Assert.assertEquals(1, errorNotifications.size());
		}, "metrics"), "notify");

		task.stop();
	}

	@Test
	public void metrics_partial_error() {
		String url = "http://localhost:" + wireMockServer.port();

		int mapId = 1;
		DataMap dm = new DataMap();
		dm.setEmitDataDefault(true);
		dm.setUseVersioning(true);
		dm.setMapPrimaryKey("ID");
		dm.setId(mapId);
		List<Map<String, String>> entries = new ArrayList<>();
		entries.add(Map.of("ID", "1", "key", "200"));
		entries.add(Map.of("ID", "2", "key", "404"));
		dm.setDataMap(entries);

		RedisCreds redisCreds = new RedisCreds(Sets.newHashSet(toHostPort(redis)), false, Optional.empty(), Optional.empty());
		withLookup(redisCreds, dm.getId(), (lookup) -> {
			lookup.save(dm);
		});

		stubFor(get(urlEqualTo("/200"))
				.willReturn(aResponse().withStatus(200).withBody("{\"id\": 100}")));
		stubFor(get(urlEqualTo("/404"))
				.willReturn(aResponse().withStatus(404)));

		int sourceId = testSourceId;
		withConsumer((notifyTopic, notifyConsumer) -> withConsumer((metricsTopic, metricsConsumer) -> {
			Map<String, String> props = buildByCallersConfig(
					new HashMap<>() {{
						put(KEY, "step1");
						put(ITERATION_TYPE, IterationType.DATA_MAP_KEY_QUEUE.property);

						put(MAP_ID, String.valueOf(mapId));
						put(REDIS_HOSTS, toHostPort(redis).toUrl());
						put(REDIS_CLUSTER_ENABLED, "false");
						put(REDIS_TLS_ENABLED, "false");
					}},
					new HashMap<>() {{
						put(KEY, "step2");
						put(METHOD, "GET");
						put(URL_TEMPLATE, url + "/{step1.key}");
						put(AUTH_TYPE, AuthType.NONE.name());

						put(ITERATION_TYPE, IterationType.STATIC_URL.property);
					}}
			);
			props.put(SOURCE_ID, sourceId + "");
			props.put(METRICS_TOPIC, metricsTopic);
			props.put(NOTIFY_TOPIC, notifyTopic);

			task.start(props);

			List<Map<String,Object>> result = pollData();
			assertEquals(Collections.singletonList(Map.of("id", 100)), result);

			result = pollData();
			Assert.assertTrue(result.isEmpty());

			List<NexlaRawMetric> metrics = readMetrics(metricsConsumer, sourceId);
			Assert.assertEquals(1, metrics.stream().map(m -> m.getFields().get("records")).mapToInt(Integer.class::cast).sum());
			Assert.assertEquals(1, metrics.stream().map(m -> m.getFields().get("error_count")).mapToInt(Integer.class::cast).sum());

			List<NexlaNotificationEvent> notifications = readNotifications(notifyConsumer, sourceId);
			Assert.assertEquals(1, notifications.stream().filter(n -> n.getEventType().equals(NotificationEventType.ERROR)).count());
		}, "metrics"), "notify");

		task.stop();
	}

	private List<Map<String, Object>> pollData() {
		return pollDataWithOffset().left;
	}

	private ImmutablePair<List<Map<String, Object>>, Optional<SourceRecord>> pollDataWithOffset() {
		List<SourceRecord> records = pollNoTrace(task);
		assertTopicName(records);
		List<NexlaMessage> data = toMessages(records);
		Optional<SourceRecord> offset = StreamEx.of(records).skip(Math.max(records.size() - 1, 0)).findFirst();
		return ImmutablePair.of(toRawMessages(data), offset);
	}

	private List<NexlaMessage> toMessages(List<SourceRecord> sourceRecords) {
		return sourceRecords.stream()
				.map(this::asNexlaMessage)
				.collect(toList());
	}

	private List<Map<String, Object>> toRawMessages(List<NexlaMessage> sourceRecords) {
		return sourceRecords.stream().map(NexlaMessage::getRawMessage).collect(toList());
	}

	private NexlaMessage asNexlaMessage(SourceRecord r) {
		return StreamUtils.jsonUtil().stringToType(r.value().toString(), NexlaMessage.class);
	}

	private StubMapping create404Mapping(String url) {
		return stubFor(get(urlEqualTo(url))
				.willReturn(aResponse()
						.withStatus(404)));
	}

	private StubMapping createPostMapping(String url, String expectedPayload, String response) {
		return createPostMapping(url, expectedPayload, response, 200);
	}

	private StubMapping createPostMapping(String url, String expectedPayload, String response, int status) {
		return stubFor(
				post(urlEqualTo(url))
						.withRequestBody(new EqualToPattern(expectedPayload))
						.willReturn(
								aResponse()
										.withStatus(status)
										.withBody(response)
						)
		);
	}

	private StubMapping createMapping(String url, String body) {
		return createMapping(url, body, Optional.empty(), Optional.empty());
	}

	private StubMapping createMapping(String url, String body, Optional<String> content, Optional<String> responseContent, Optional<String> acceptHeader) {
		MappingBuilder builder = get(urlEqualTo(url))
				.willReturn(aResponse()
						.withStatus(200)
						.withBody(body)
						.withHeader("Content-Type", responseContent.orElse("application/json;charset=UTF-8")));

		MappingBuilder builder2 = acceptHeader
				.map(ah -> builder.withHeader("Accept", new EqualToPattern(ah)))
				.orElse(builder);

		MappingBuilder builder3 = content
				.map(ah -> builder.withHeader("Content-Type", new EqualToPattern(ah)))
				.orElse(builder2);

		return stubFor(builder3);
	}

	private StubMapping createMapping(String url, String body, Optional<String> content, Optional<String> acceptHeader) {
		return createMapping(url, body, content, Optional.empty(), acceptHeader);
	}

	private void assertTopicName(List<SourceRecord> sourceRecords) {
		assertTrue(sourceRecords.stream().allMatch(r -> datasetTopic.equals(r.topic())));
	}

	private void assertTrackerIdMacro(NexlaMessage nexlaMessage, long offset, String url,
	                                  Map<String, String> replacementsForMacro) {
		String replacedUrl = replacementsForMacro.computeIfPresent("{page_number_value}", url::replace);
		Tracker trackerId = nexlaMessage.getNexlaMetaData().getTrackerId();
		SourceItem trackerIdSource = trackerId.getSource();

		assertThat(trackerIdSource.getDataSetId(), is(testSourceId));
		org.assertj.core.api.Assertions.assertThat(replacedUrl)
				.isEqualTo(new String(Base64.decodeBase64(trackerIdSource.getSource())));
		assertThat(trackerIdSource.getRecordNumber(), is(offset));
		assertThat(trackerIdSource.getVersion(), is(1));
		assertThat(trackerIdSource.getInitialIngestTimestamp(), lessThanOrEqualTo(System.currentTimeMillis()));

		assertThat(trackerId.getSets(), is(empty()));
		assertThat(trackerId.getSink(), is(nullValue()));
	}

	private void assertTrackerId(NexlaMessage nexlaMessage, long offset, String url) {
		Tracker trackerId = nexlaMessage.getNexlaMetaData().getTrackerId();
		SourceItem trackerIdSource = trackerId.getSource();

		assertThat(trackerIdSource.getDataSetId(), is(testSourceId));
		assertThat(trackerIdSource.getSource(), is(new String(Base64.encodeBase64(url.getBytes()))));
		assertThat(trackerIdSource.getRecordNumber(), is(offset));
		assertThat(trackerIdSource.getVersion(), is(1));
		assertThat(trackerIdSource.getInitialIngestTimestamp(), lessThanOrEqualTo(System.currentTimeMillis()));

		assertThat(trackerId.getSets(), is(empty()));
		assertThat(trackerId.getSink(), is(nullValue()));
	}

	private static Map<String, String> baseConfig() {
		Map<String, String> baseConfig = new HashMap<>();
		baseConfig.put(NexlaConstants.CONTROL_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		baseConfig.put(NexlaConstants.DATA_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		baseConfig.put(NexlaConstants.CONTROL_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);
		baseConfig.put(NexlaConstants.DATA_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);
		baseConfig.put(UNIT_TEST, "true");
		baseConfig.put(DATASET_REPLICATION, "1");
		baseConfig.put(POLL_MS, "1");
		baseConfig.put(CREDS_ENC, "1");
		baseConfig.put(CREDS_ENC_IV, "1");
		baseConfig.put(CREDENTIALS_DECRYPT_KEY, "1");
		baseConfig.put(SOURCE_ID, String.valueOf(testSourceId));
		baseConfig.put(LISTING_APP_SERVER_URL, "123");
		baseConfig.put(RUN_ID, "123456789");
		return baseConfig;
	}

	@SneakyThrows
	private static Map<String, String> buildByCallersConfig(Map<String, String>... staticUrlProps) {
		return buildByCallersConfigWithBase(baseConfig(), List.of(staticUrlProps));
	}

	@SneakyThrows
	private static Map<String, String> buildByCallersConfigWithBase(Map<String, String> base, Map<String, String>... callers) {
		String callersStr = OBJECT_MAPPER.writeValueAsString(List.of(callers));
		Map<String, String> props = new HashMap<>(base);
		props.put(REST_ITERATIONS_JSON, callersStr);
		return props;
	}
	@SneakyThrows
	private static Map<String, String> buildByCallersConfigWithBase(Map<String, String> base, List<Map<String, String>> callers) {
		String callersStr = OBJECT_MAPPER.writeValueAsString(callers);
		Map<String, String> props = new HashMap<>(base);
		props.put(REST_ITERATIONS_JSON, callersStr);
		props.put(NexlaConstants.CONTROL_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		props.put(NexlaConstants.DATA_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		props.put(NexlaConstants.CONTROL_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);
		props.put(NexlaConstants.DATA_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);
		return props;
	}

	private void fillDataMap(int mapId) {
		DataMap dm = new DataMap();
		dm.setEmitDataDefault(true);
		dm.setUseVersioning(true);
		dm.setMapPrimaryKey("ID");
		dm.setDataSinkId(5001);
		dm.setId(mapId);
		List<Map<String, String>> entries = new ArrayList<>();
		for (int i = 1; i <= 2; i++) {
			Map<String, String> entry = new HashMap<>();
			entry.put("ID", Integer.toString(i));
			entry.put("key", "value" + i);
			entries.add(entry);
		}
		dm.setDataMap(entries);

		RedisCreds redisCreds = new RedisCreds(Sets.newHashSet(toHostPort(redis)), false, Optional.empty(), Optional.empty());
		withLookup(redisCreds, dm.getId(), (lookup) -> {
			lookup.save(dm);
		});
	}

	private static class StepConfigBuilder {
		private final String baseURL;
		private final List<Map<String, String>> steps = new ArrayList<>();
		private final String pollMs;

		private StepConfigBuilder(String baseURL) {
			this.baseURL = baseURL;
			this.pollMs = String.valueOf(1);
		}

		public StepConfigBuilder addStep(String iterationType, String method, String path, String responseFormat, Map<String, String> extra) {
			Map<String, String> base = new HashMap<>();
			base.put(KEY, "step" + (steps.size() + 1));
			base.put(METHOD, method);
			base.put(URL_TEMPLATE, baseURL + path);
			base.put(ITERATION_TYPE, iterationType);
			base.put(POLL_MS, pollMs);
			base.put(RESPONSE_FORMAT, responseFormat);

			base.putAll(extra);

			steps.add(base);

			return this;
		}

		public StepConfigBuilder addStep(String iterationType, String method, String path, Map<String, String> extra) {
			return addStep(iterationType, method, path, "json", extra);
		}

		public StepConfigBuilder addStep(String iterationType, String method, String path, Map<String, String> extra, Runnable setUp) {
			setUp.run();
			return addStep(iterationType, method, path, "json", extra);
		}

		public Map<String, String> build() {
			return buildByCallersConfigWithBase(baseConfig(), this.steps);
		}
	}

	static final class JSON {
		private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

		static {
			OBJECT_MAPPER.findAndRegisterModules();
			OBJECT_MAPPER.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
		}


		@SneakyThrows
		public static String j(String s) {
			return OBJECT_MAPPER.writeValueAsString(OBJECT_MAPPER.readValue(s, Object.class));
		}

		@SneakyThrows
		public static String j(String... s) {
			return j(String.join("\n", s));
		}
	}

	private static void assertMessagesEqual(List<Map<String, String>> exp, List<Map<String, Object>> actual) {
		assertEquals(exp.size(), actual.size());
		for (int i = 0; i < exp.size(); i++) {
			assertEquals(exp.get(i), actual.get(i));
		}
	}

	static class NoLogging implements org.eclipse.jetty.util.log.Logger {
		@Override public String getName() { return "no"; }
		@Override public void warn(String msg, Object... args) { }
		@Override public void warn(Throwable thrown) { }
		@Override public void warn(String msg, Throwable thrown) { }
		@Override public void info(String msg, Object... args) { }
		@Override public void info(Throwable thrown) { }
		@Override public void info(String msg, Throwable thrown) { }
		@Override public boolean isDebugEnabled() { return false; }
		@Override public void setDebugEnabled(boolean enabled) { }
		@Override public void debug(String msg, Object... args) { }
		@Override public void debug(String s, long l) { }
		@Override public void debug(Throwable thrown) { }
		@Override public void debug(String msg, Throwable thrown) { }
		@Override public org.eclipse.jetty.util.log.Logger getLogger(String name) { return this; }
		@Override public void ignore(Throwable ignored) { }
	}

	static class LoggedRunnable implements Runnable {

		private final String testName;
		private final Runnable origRunnable;

		public LoggedRunnable(String testName, Runnable origRunnable) {
			this.testName = testName;
			this.origRunnable = origRunnable;
		}

		@Override
		public void run() {
			MDC.put("test.name", testName);
			origRunnable.run();
			MDC.clear();
		}
	}
}

