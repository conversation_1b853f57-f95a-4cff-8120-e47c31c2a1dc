# Extra logging related to initialization of Log4j
# Set to debug or trace if log4j initialization is failing
status = info
# Name of the configuration
name = NexlaConnectorLogConfig

# Console appender configuration
appender.console.type = Console
appender.console.name = consoleLogger
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = [%date{yyyy-MM-dd HH:mm:ss.SSS}] [TEST_NAME:%X{test.name}] [${sys:git_hash}] %highlight{%-5level} [%20.20thread] %style{%-40logger}{cyan} - %msg%n%throwable

# Root logger
rootLogger.level = info
rootLogger.appenderRef.stdout.ref = consoleLogger

logger.nexla.name = com.nexla
logger.nexla.additivity = false
logger.nexla.level = debug
logger.nexla.appenderRef.stdout.ref = consoleLogger

logger.joestelmach.name = com.joestelmach.natty
logger.joestelmach.additivity = false
logger.joestelmach.level = warn
logger.joestelmach.appenderRef.stdout.ref = consoleLogger

logger.testcontainers.name = org.testcontainers
logger.testcontainers.additivity = false
logger.testcontainers.level = info
logger.testcontainers.appenderRef.stdout.ref = consoleLogger

logger.kafkaclients.name = org.org.apache.kafka.clients
logger.kafkaclients.additivity = false
logger.kafkaclients.level = debug
logger.kafkaclients.appenderRef.stdout.ref = consoleLogger

logger.apachehttp.name = org.apache.http
logger.apachehttp.additivity = false
logger.apachehttp.level = warn
logger.apachehttp.appenderRef.stdout.ref = consoleLogger

logger.dockerjava.name = com.github.dockerjava
logger.dockerjava.additivity = false
logger.dockerjava.level = error
logger.dockerjava.appenderRef.stdout.ref = consoleLogger

logger.zeroturnaround.name = org.zeroturnaround.exec
logger.zeroturnaround.additivity = false
logger.zeroturnaround.level = warn
logger.zeroturnaround.appenderRef.stdout.ref = consoleLogger
