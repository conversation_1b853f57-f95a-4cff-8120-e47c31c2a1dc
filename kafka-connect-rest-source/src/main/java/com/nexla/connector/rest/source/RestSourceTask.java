package com.nexla.connector.rest.source;

import com.google.common.collect.Maps;
import com.nexla.client.ScriptEvalClient;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.RestTemplateBuilder;
import com.nexla.common.datetime.DateTimeUtils;
import com.nexla.common.http.StandardHeaders;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.tracker.SourceItem;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.*;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connector.config.rest.RestSourceConnectorConfig;
import com.nexla.probe.http.RestConnectorService;
import com.nexla.rest.RestIteration;
import com.nexla.rest.RestIterationChainBuilder;
import com.nexla.rest.RestIterationContext;
import com.nexla.rest.RestIterationOffset;
import com.nexla.rest.iterations.BodyAsFile;
import com.nexla.rest.pojo.ErrorWithDescription;
import com.nexla.rest.pojo.RestIterationResult;
import com.nexla.rest.pojo.RestSourceError;
import com.nexla.rest.pojo.ResultEntry;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.net.util.Base64;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.source.SourceRecord;
import org.joda.time.DateTime;
import scala.Function2;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.ConnectionType.REST;
import static com.nexla.common.MetricUtils.calcBytes;
import static com.nexla.common.NexlaMetaData.NX_REQUEST_URL;
import static com.nexla.common.NexlaMetaData.NX_REQUEST_HEADERS;
import static com.nexla.common.NotificationEventType.ERROR;
import static com.nexla.common.NotificationEventType.ERROR_AGGREGATED;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.connector.properties.RestConfigAccessor.PARTITION_KEY;
import static com.nexla.rest.RestIterationOffset.TERMINAL_OFFSET;
import static java.lang.String.format;
import static java.util.Collections.singletonMap;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static org.apache.kafka.connect.data.Schema.STRING_SCHEMA;
import static org.joda.time.DateTimeZone.UTC;

public class RestSourceTask extends BaseSourceTask<RestSourceConnectorConfig> {

	private RestIterationChainBuilder builder;
	private List<RestIterationContext> callerContexts;
	private RestIterationContext lastContext;
	private List<RestIteration> restIterations;
	private RestConnectorService probeService;
	private ScriptEvalClient scriptEvalClient;
	private HashSet<RestIterationResult> processedErrors = new HashSet<>();

	private AtomicInteger pollCounter = new AtomicInteger(0);
	private RestSourceError restSourceError = new RestSourceError(new AtomicInteger(0), "");

	public RestSourceTask() {
	}

	public RestSourceTask(SchemaDetectionUtils schemaDetection) {
		this.schemaDetection = schemaDetection;
	}

	@Override
	protected RestSourceConnectorConfig parseConfig(Map<String, String> props) {
		return new RestSourceConnectorConfig(props);
	}

	protected DateTime now() {
		return DateTimeUtils.nowUTC();
	}

	@SneakyThrows
	@Override
	public CollectRecordsResult collectRecords() {

		if (config.runOnce && pollCounter.get() > 0) {
			logger.info("Task ran once, returning empty records");
			return new CollectRecordsResult(Collections.emptyList());
		}

//		commented, cause {now} always changes URL for a dates like `ssss` or `eeee`
//		if we need to change URL cause of date, uncomment this and lines in RestSourceTaskTest.poll_with_dates
//		if (lastContext.isRecreateChainRequired()) {
//			initContextChain(Optional.empty(), now());
//		}

		boolean schemaDetectionOnce = this.config.schemaDetectionOnce || lastContext.getRestIteration().getConfig().schemaDetectionOnce;
		boolean separateSchemas = lastContext.getRestIteration() instanceof BodyAsFile && !schemaDetectionOnce;

		AtomicReference<String> metricName = new AtomicReference<>("");
		int recordCount = 0;
		AtomicLong byteCount = new AtomicLong(0);
		AtomicLong timestamp = new AtomicLong(DateTimeUtils.nowUTC().getMillis());
		AtomicLong errorCount = new AtomicLong(0);
		AtomicReference<String> errorMessage = new AtomicReference<>("");

		try {
			List<SourceRecord> sourceRecords = detectSchemaIfNecessary(separateSchemas, processRecords(), empty());
			recordCount = sourceRecords.size();

			lastContext.getLastResult().ifPresent(result -> {
				metricName.set(Optional.ofNullable(config.externallySetMetricName).orElse(result.getUrl()));
				byteCount.set(result.getByteCounter().get());
				timestamp.set(result.getMetric().getStartInMillis());
			});

			errorCount.set(restSourceError.errorCounter.get());
			errorMessage.set(restSourceError.getErrorMessage());

			lastContext.getErroneousResults().forEach(res -> {
				if (processedErrors.contains(res)) {
					return;
				}

				ErrorWithDescription ewd = (ErrorWithDescription) res.getErrorMessage().get();

				if (ewd.getLevel() == ErrorWithDescription.Level.ERROR) {
					errorCount.incrementAndGet();
					errorMessage.set(ewd.getError());

					NexlaConnectorUtils.sendNexlaNotificationEvent(
							controlMessageProducer, ERROR_AGGREGATED, runId, SOURCE,
							dataSource.getId(), dataSource.getName(),
							errorCount.get(), ewd.getError());
				}

				publishMonitoringLog(ewd.getError(), NexlaMonitoringLogType.LOG, toSeverity(ewd.getLevel()));

				processedErrors.add(res);
			});

			return new CollectRecordsResult(sourceRecords);
		} catch (Exception e) {
			String msg = e.getCause() != null ? e.getCause().getMessage() + " (" + e.getMessage() + ")": e.getMessage();

			errorCount.incrementAndGet();
			errorMessage.set(msg);

			return new CollectRecordsResult(List.of());
		} finally {
			sendMetrics(metricName.get(), recordCount, byteCount.get(), errorCount.get(), timestamp.get());

			if (errorCount.get() != 0) {
				NexlaConnectorUtils.sendNexlaNotificationEvent(controlMessageProducer, ERROR, runId, SOURCE, dataSource.getId(),
						dataSource.getName(), errorCount.get(), errorMessage.get());
				publishMonitoringLog(errorMessage.get(), NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
			}
		}
	}

	@Override
	public ConfigDef configDef() {
		return RestSourceConnectorConfig.configDef();
	}

	@Override
	public void doStart(Map<String, String> props) throws ConnectException {
		this.sourceTelemetryReporter = Optional.of(new ConnectorTelemetryReporter(config.sourceId, REST.name(), SOURCE, true));
		this.scriptEvalClient = new ScriptEvalClient(config.probeAppUrl, config.nexlaUsername, config.nexlaPassword, restTemplate);
		this.probeService = new RestConnectorService(scriptEvalClient);
		this.probeService.initLogger(SOURCE, config.sourceId, Optional.empty());
		Optional<DetailedFlowInsightsSender> flowInsightsSender = Optional.ofNullable(DetailedFlowInsightsSender.from(
			config.logVerbose,
			getSuccessFlowInsightsSender(),
			getErrorFlowInsightsSender(),
			config.detailedFlowInsightsAbbreviatedLength
		));
		this.builder = new RestIterationChainBuilder(logger, true, of(heartbeatCallback()),
				Optional.of(adminApiClient), Optional.of(probeService), Optional.of(controlMessageProducer),
				Optional.of(dataSource.getId()), Optional.of(restSourceError), config, flowInsightsSender);
		this.restIterations = builder.buildIterations(config.restIterationConfigs);
		initContextChain(now());
	}

	private Runnable heartbeatCallback() {
		return this::heartbeat;
	}

	private void initContextChain(DateTime dateTime) {
		this.callerContexts = builder.buildContexts(restIterations, dateTime);
		this.lastContext = callerContexts.get(callerContexts.size() - 1);
	}

	@SneakyThrows
	private List<SourceRecordCreator> processRecords() {

		List<SourceRecordCreator> records = lastContext
			.iterateOverContext()
			.flatMap(result -> processResult(lastContext))
			.orElse(Collections.emptyList());

		this.pollCounter.incrementAndGet();
		return records;
	}

	private NexlaMonitoringLogSeverity toSeverity(ErrorWithDescription.Level level) {
		switch (level) {
			case WARN:
				return NexlaMonitoringLogSeverity.WARNING;
			case ERROR:
			default:
				return NexlaMonitoringLogSeverity.ERROR;
		}
	}

	private Optional<List<SourceRecordCreator>> processResult(RestIterationContext lastContext) {
		return lastContext.getLastResult().map(result -> {
			Map<String, String> contextOffsets = StreamEx.of(callerContexts)
				.filter(context -> context.getLastResult().isPresent())
				.toMap(
					context -> context.getRestIteration().getCode(),
					context -> context.getLastResult().get().getOffset().toJson()
				);
			List<ResultEntry> entries = result.getEntries();
			Map<String, String> substituteMap = lastContext.createSubstituteMap();
			Stream<SourceRecordCreator> sourceRecordsStream = entries.stream()
				.map(entry -> {
					Map<String, String> sourceOffset = Maps.newHashMap(contextOffsets);

					// if SourceRecord has no next recordNumber, remember this special case marking it as TERMINAL_OFFSET
					RestIterationOffset nextOffset = lastContext.getNextOffset().orElse(TERMINAL_OFFSET);
					sourceOffset.put(lastContext.getRestIteration().getCode(), nextOffset.toJson());
					var data = config.includeHeaders ? entry.getDataMapWithHeaders() : entry.getDataMap();
					return createRecord(
						lastContext.getRestIteration(),
						data,
						Long.valueOf(entry.getOffsetOnPage()),
						entry.getNexlaMetaRangeFrom(),
						result.getUrl(),
						sourceOffset,
						result.getByteCounter(),
						substituteMap,
						entry.getPassthroughTags()
					);
				});

			return sourceRecordsStream.collect(Collectors.toList());
		});
	}

	private SourceRecordCreator createRecord(
		RestIteration caller,
		LinkedHashMap<String, Object> dataMap,
		Long offset,
		Object nexlaMetaRangeFrom,
		String url,
		Map<String, ?> sourceOffset,
		AtomicLong byteCounter,
		Map<String, String> substituteMap,
		Map<String, Object> tags
	) {
		String sourceKey = nexlaMetaRangeFrom != null ? format("%s, from=%s", url, nexlaMetaRangeFrom) : url;
		long now = DateTime.now(UTC).getMillis();

		Function2<Integer, String, NexlaMessage> nexlaMessageCreator = (dataSetId, dataSetTopic) -> {
			SourceItem nexlaSourceTrackerIdItem = SourceItem.fullTracker(
				config.sourceId,
				dataSetId,
				new String(Base64.encodeBase64(url.getBytes())),
				offset != null ? offset : 0,
				config.version,
				now);

			NexlaMetaData metaData = new NexlaMetaData(
				REST,
				now,
				offset,
				sourceKey,
				dataSetTopic,
				SOURCE,
				config.sourceId,
				false,
				new Tracker(Tracker.TrackerMode.FULL, nexlaSourceTrackerIdItem),
				runId);
			metaData.setTags(tags);

			if (caller.getConfig().addHeadersToMeta) {
				Map<String, String> headersWithSubstitutions = caller.getConfig().restHeaders.
						withSubstitutions(substituteMap).requestHeaders;
				addHeadersToMeta(metaData, headersWithSubstitutions, url);
			}

			return new NexlaMessage(dataMap, metaData);
		};

		Function2<Integer, String, SourceRecord> sourceRecordCreator = (dataSetId, dataSetTopic) -> {
			NexlaMessage message = nexlaMessageCreator.apply(dataSetId, dataSetTopic);

			byteCounter.addAndGet(calcBytes(message.toJsonString()));

			return new SourceRecord(
				singletonMap(PARTITION_KEY, caller.getPartition()),
				sourceOffset,
				dataSetTopic,
				STRING_SCHEMA, toJsonString(message));
		};
		return new SourceRecordCreator(dataMap, sourceRecordCreator, nexlaMessageCreator);
	}

	private void addHeadersToMeta(NexlaMetaData metaData, Map<String,String> headers, String url) {
		Map<String, Object> tagsMap = Optional.ofNullable(metaData.getTags()).orElse(Maps.newHashMap());
		Map<String, String> filteredHeaders = EntryStream.of(headers)
				.filterKeys(name -> !StandardHeaders.isStandard(name))
				.toMap();
		tagsMap.put(NX_REQUEST_HEADERS, filteredHeaders);
		tagsMap.put(NX_REQUEST_URL, url);
		metaData.setTags(tagsMap);
	}

	AtomicInteger getPollCounter() {
		return pollCounter;
	}
}
