package com.nexla.connector.sql.poll

import com.dimafeng.testcontainers.lifecycle.and
import com.dimafeng.testcontainers.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MySQLContainer}
import com.dimafeng.testcontainers.scalatest.TestContainersForAll
import com.nexla.admin.client.{AdminApiClient, DataSet, DataSource, DataSourceRunId, FindOrCreateDataSetResult, NexlaSchema}
import com.nexla.common.NexlaConstants.{CLIENT_SSL_ENABLED, CONTROL_KAFKA_BOOTSTRAP_SERVERS, DATA_KAFKA_BOOTSTRAP_SERVERS, CREDENTIALS_DECRYPT_KEY, CREDS_ENC, CREDS_ENC_IV, SOURCE_ID}
import com.nexla.common.StreamUtils.OBJECT_MAPPER
import com.nexla.common.metrics.NexlaRawMetric.RUN_ID
import com.nexla.common.notify.transport.{ControlMessageProducer, DataMessageProducer, NexlaMessageTransport}
import com.nexla.common.schema.SchemaKey.{PROPERTIES, TYPE}
import com.nexla.common.schema.SchemaType
import com.nexla.common.schema.SchemaType.OBJECT
import com.nexla.common.{ConnectionType, NexlaConstants, NexlaKafkaConfig, NexlaSslContext, SchemaUtils}
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils.createSchemaDetection
import com.nexla.connector.ConnectorService.UNIT_TEST
import com.nexla.connector.config.SourceConnectorConfig
import com.nexla.connector.config.jdbc.JdbcAuthConfig.{PASSWORD, URL, USERNAME}
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig
import com.nexla.connector.config.vault.CredentialsStore
import com.nexla.connector.properties.SqlConfigAccessor._
import com.nexla.kafka.service.TopicMetaService
import com.nexla.probe.sql.SqlConnectorService
import com.nexla.transform.schema.FormatDetector

import java.sql.Connection
import java.util
import java.util.{Collections, Optional, UUID}
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.mockito.{ArgumentCaptor, ArgumentMatchers}
import org.scalatest.{BeforeAndAfterAll, BeforeAndAfterEach}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.testcontainers.containers
import org.testcontainers.utility.DockerImageName

import scala.collection.JavaConverters._
import scala.util.Try

@com.nexla.test.ScalaIntegrationTests
class JdbcSourceTaskTest
  extends AnyFlatSpecLike
    with Matchers
    with BeforeAndAfterAll
    with BeforeAndAfterEach
    with TestContainersForAll {

  val TABLE_NAME: String = "TEST" + UUID.randomUUID.toString.substring(0, 8).toUpperCase

  override type Containers = MySQLContainer and KafkaContainer

  override def startContainers(): Containers = {
    FormatDetector.initDefault()

    val myImage: DockerImageName = DockerImageName.parse("debezium/example-mysql:1.2").asCompatibleSubstituteFor("mysql");
    val container1 = MySQLContainer.Def(
      dockerImageName = myImage,
      username = "mysqluser",
      password = "debezium"
    ).start()

    val container2 = KafkaContainer.Def(
      dockerImageName = "confluentinc/cp-kafka:7.2.11"
    ).start()

    container1 and container2
  }

  "jdbc source use schema from db" should "use schema from db for new dataset" in {
    withContainers {
      case mysql and kafka =>
        recreateTable(mysql.container)
        insertRow(mysql, 1, "desc string")
        val ds = new DataSource()
        ds.setId(1)
        ds.setSourceConfig(Collections.emptyMap())
        ds.setDatasets(Collections.emptyList())
        ds.setConnectionType(ConnectionType.MYSQL)
        ds.setRunIds(Collections.singletonList[DataSourceRunId](new DataSourceRunId(1)))
        val mockAdminApiClient = mock(classOf[AdminApiClient])
        when(mockAdminApiClient.getDataSource(ArgumentMatchers.any()))
          .thenReturn(Optional.ofNullable(ds))
        val argumentCaptor = ArgumentCaptor.forClass(classOf[DataSet])
        when(mockAdminApiClient.findOrCreateDataSet(argumentCaptor.capture()))
          .thenReturn(new FindOrCreateDataSetResult(FindOrCreateDataSetResult.Result.CREATED, 3))
        val sqlConnectorService = new SqlConnectorService(mock(classOf[CredentialsStore]), mockAdminApiClient)
        val mockMessageProducer = mock(classOf[ControlMessageProducer])
        val mockMessageTransport = mock(classOf[NexlaMessageTransport])
        val config: util.Map[String, String] = connectorConfig(mysql, kafka)
        val schemaDetection = createSchemaDetection(new JdbcSourceConnectorConfig(config), mockAdminApiClient, mockMessageProducer, Optional.empty())

        val jdbcSourceTask = new JdbcSourceTask(sqlConnectorService, schemaDetection) {
          override protected def createControlMessageProducer(sslContext: NexlaKafkaConfig): ControlMessageProducer = {
            new ControlMessageProducer(mockMessageTransport)
          }
          override protected def createDataMessageProducer(sslContext: NexlaKafkaConfig): DataMessageProducer = {
            new DataMessageProducer(mockMessageTransport)
          }
        }
        jdbcSourceTask.initSourceTask(config, "unit_test", Optional.of(mockAdminApiClient))
        jdbcSourceTask.collectRecords

        verify(mockAdminApiClient, times(1)).findOrCreateDataSet(any())
        val value: DataSet = argumentCaptor.getValue
        val expected = createNexlaSchema(util.Map.of("ID", util.Map.of("type", SchemaType.INTEGER.toString),
          "ID2", util.Map.of("type", SchemaType.NUMBER.toString),
          "DESCRIPTION", util.Map.of("type", SchemaType.STRING.toString),
          "TS", util.Map.of("type", SchemaType.STRING.toString)))

        val schema: NexlaSchema = value.getSourceSchema
        schema shouldEqual expected

        jdbcSourceTask.stop()
    }
  }

  it should "not update schema of existing dataset if type of existing column changed" in {
    // todo this is what we do now but this behavior can/should change when we improve on schema merge and migration, which cuts across all source types
    withContainers {
      case mysql and kafka =>
        recreateTableWithoutTs(mysql.container)
        insertRow(mysql, 1, "desc string")

        val previousSchema = createNexlaSchema(util.Map.of(
          "ID", util.Map.of("type", SchemaType.INTEGER.toString),
          "ID2", util.Map.of("type", SchemaType.NUMBER.toString),
          "DESCRIPTION", util.Map.of("type", SchemaType.STRING.toString),
          "TS", util.Map.of("type", SchemaType.NULL.toString)))
        val dataSetId = 7
        val dataSet = new DataSet()
        dataSet.setId(dataSetId)
        dataSet.setDataSamples(new util.ArrayList())
        dataSet.setSourceSchema(previousSchema)
        val dataSets = List(dataSet).asJava

        val ds = new DataSource()
        ds.setId(1)
        ds.setSourceConfig(Collections.emptyMap())
        ds.setDatasets(dataSets)
        ds.setConnectionType(ConnectionType.MYSQL)
        ds.setRunIds(Collections.emptyList())

        val mockAdminApiClient = mock(classOf[AdminApiClient])
        when(mockAdminApiClient.getDataSource(ArgumentMatchers.any())).thenReturn(Optional.ofNullable(ds))
        when(mockAdminApiClient.getAllDataSetsForSourceWithSamples(any())).thenReturn(dataSets)
        when(mockAdminApiClient.getDataSet(dataSetId)).thenReturn(Optional.of(dataSet))

        val mockTopicMetaService = mock(classOf[TopicMetaService])
        when(mockTopicMetaService.getTopicPartitionsCount(ArgumentMatchers.anyCollection())).thenReturn(new util.HashMap[String, Integer])

        val sqlConnectorService = new SqlConnectorService(mock(classOf[CredentialsStore]), mockAdminApiClient)
        val mockMessageProducer = mock(classOf[ControlMessageProducer])
        val config: util.Map[String, String] = connectorConfig(mysql, kafka)
        val schemaDetection = createSchemaDetection(new JdbcSourceConnectorConfig(config), mockAdminApiClient, mockMessageProducer, Optional.empty())
        val mockMessageTransport = mock(classOf[NexlaMessageTransport])

        val jdbcSourceTask = new JdbcSourceTask(sqlConnectorService, schemaDetection) {
          override protected def createControlMessageProducer(sslContext: NexlaKafkaConfig): ControlMessageProducer = {
            new ControlMessageProducer(mockMessageTransport)
          }
          override protected def createDataMessageProducer(sslContext: NexlaKafkaConfig): DataMessageProducer = {
            new DataMessageProducer(mockMessageTransport)
          }
        }
        jdbcSourceTask.initSourceTask(config, "unit_test", Optional.of(mockAdminApiClient))
        jdbcSourceTask.collectRecords

        verify(mockAdminApiClient, never()).findOrCreateDataSet(any())
        verify(mockAdminApiClient, never()).updateDataSetSchema(any())
    }
  }

  "missing runId" should "throw runtime exception" in {
    withContainers {
      case mysql and kafka =>
        val ds = new DataSource()
        ds.setId(1)
        ds.setSourceConfig(Collections.emptyMap())
        ds.setDatasets(Collections.emptyList())
        ds.setConnectionType(ConnectionType.MYSQL)
        ds.setRunIds(Collections.emptyList[DataSourceRunId]())
        val mockAdminApiClient = mock(classOf[AdminApiClient])
        when(mockAdminApiClient.getDataSource(ArgumentMatchers.any()))
          .thenReturn(Optional.ofNullable(ds))
        val argumentCaptor = ArgumentCaptor.forClass(classOf[DataSet])
        when(mockAdminApiClient.findOrCreateDataSet(argumentCaptor.capture()))
          .thenReturn(new FindOrCreateDataSetResult(FindOrCreateDataSetResult.Result.CREATED, 3))
        val sqlConnectorService = new SqlConnectorService(mock(classOf[CredentialsStore]), mockAdminApiClient)
        val mockMessageTransport = mock(classOf[NexlaMessageTransport])
        val messageProducer = new ControlMessageProducer(mockMessageTransport)
        val config: util.Map[String, String] = connectorConfig(mysql, kafka)
        config.put(RUN_ID, null);
        val schemaDetection = createSchemaDetection(new JdbcSourceConnectorConfig(config), mockAdminApiClient, messageProducer, Optional.empty())
        val jdbcSourceTask = new JdbcSourceTask(sqlConnectorService, schemaDetection) {
          override protected def createControlMessageProducer(sslContext: NexlaKafkaConfig): ControlMessageProducer = {
            new ControlMessageProducer(mockMessageTransport)
          }
          override protected def createDataMessageProducer(sslContext: NexlaKafkaConfig): DataMessageProducer = {
            new DataMessageProducer(mockMessageTransport)
          }
        }
        val exception = intercept[RuntimeException] {
          jdbcSourceTask.initSourceTask(config, "unit_test", Optional.of(mockAdminApiClient))
        }
        exception.getMessage should include ("runId cannot be null")
    }
  }

  protected def connectorConfig(db: MySQLContainer, kafka: KafkaContainer): util.Map[String, String] = {
    val base: util.HashMap[String, String] = new util.HashMap[String, String]
    base.put(CONTROL_KAFKA_BOOTSTRAP_SERVERS, kafka.bootstrapServers)
    base.put(DATA_KAFKA_BOOTSTRAP_SERVERS, kafka.bootstrapServers)
    base.put(CREDS_ENC, "1")
    base.put(CREDS_ENC_IV, "1")
    base.put(CREDENTIALS_DECRYPT_KEY, "1")
    base.put(UNIT_TEST, "true")
    base.put(URL, db.jdbcUrl)
    base.put(USERNAME, db.username)
    base.put(PASSWORD, db.password)
    base.put(SOURCE_ID, "1")
    base.put(RUN_ID, "1")
    base.put(NexlaConstants.CREDENTIALS_TYPE, "mysql")
    base.put(POLL_MS, "1")
    base.put(MONITOR_POLL_MS, "10000")
    base.put(MODE, MODE_NONE)
    base.put(TABLE, TABLE_NAME)
    base.put(BATCH_SIZE_APPROX, "100")
    base.put(SourceConnectorConfig.CREATE_TOPICS, "false")
    base.put(CLIENT_SSL_ENABLED, "false")
    base
  }

  private def createNexlaSchema(properties: util.Map[String, Any]) = {
    var resultMap = new util.HashMap[String, AnyRef]()
    resultMap.put(TYPE.toString, OBJECT.toString)
    resultMap.put(PROPERTIES.toString, properties)
    resultMap = SchemaUtils.addSchemaMetadata(resultMap)
    OBJECT_MAPPER.convertValue(resultMap, classOf[NexlaSchema])
  }

  private def insertRow(db: MySQLContainer, id: Int, description: String): Unit = {
    val connection: Connection = db.container.createConnection("")
    connection.setAutoCommit(false)
    val sql =
      s"""INSERT INTO $TABLE_NAME (id, id2, description)
         |VALUES ($id, $id, '$description')
         |""".stripMargin
    connection.createStatement.execute(sql)
    connection.commit()
  }

  private def recreateTable(db: containers.MySQLContainer[_]) = {
    val connection = db.createConnection("")
    connection.setAutoCommit(false)
    val result = Try {
      connection.createStatement().execute("DROP TABLE IF EXISTS " + TABLE_NAME)
      connection.createStatement().execute(
        s"""CREATE TABLE ${TABLE_NAME}
           |(
           |ID INT PRIMARY KEY NOT NULL,
           |ID2 DOUBLE,
           |DESCRIPTION VARCHAR(100),
           |TS TIMESTAMP
           |)
           |""".stripMargin)
      connection.commit()
    }
    connection.close()
  }

  private def recreateTableWithoutTs(db: containers.MySQLContainer[_]) = {
    val connection = db.createConnection("")
    connection.setAutoCommit(false)
    val result = Try {
      connection.createStatement().execute("DROP TABLE IF EXISTS " + TABLE_NAME)
      connection.createStatement().execute(
        s"""CREATE TABLE ${TABLE_NAME}
           |(
           |ID INT PRIMARY KEY NOT NULL,
           |ID2 DOUBLE,
           |DESCRIPTION VARCHAR(100)
           |)
           |""".stripMargin)
      connection.commit()
    }
    connection.close()
  }

}
