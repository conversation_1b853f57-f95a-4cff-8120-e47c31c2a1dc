package com.nexla.connector.sql.poll;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.common.collect.Lists;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaConstants;
import com.nexla.common.exception.ProbeRetriableException;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.connect.common.connector.BaseSourceConnector;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.ssh.HostPort;
import com.nexla.connector.config.ssh.tunnel.SshTunnel;
import com.nexla.connector.config.ssh.tunnel.SshTunnelSupport;
import com.nexla.connector.config.vault.CredentialsStore;
import com.nexla.kafka.KafkaMessageTransport;
import com.nexla.probe.sql.MinMaxHolder;
import com.nexla.probe.sql.SqlConnectionException;
import com.nexla.probe.sql.SqlConnectorService;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.connector.Task;
import org.apache.kafka.connect.errors.ConnectException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import static com.nexla.connector.config.SourceConnectorConfig.DEFAULT_POLL_MS;
import static com.nexla.connector.properties.SqlConfigAccessor.*;
import static java.lang.Math.max;
import static java.lang.Math.min;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.trimToNull;
import static org.joor.Reflect.on;

/**
 * JdbcSourceConnector is a Kafka Connect Connector implementation that
 * reads data from SQL to a Kafka topic
 */
public class JdbcSourceConnector
	extends BaseSourceConnector<JdbcSourceConnectorConfig>
	implements SshTunnelSupport {

	@VisibleForTesting
	RebalancingTask rebalancingTask;

	private final Supplier<ScheduledExecutorService> executorService = Suppliers.memoize(() -> Executors.newScheduledThreadPool(1));

	private ScheduledFuture<?> rebalancingFuture;

	private long monitorPollMs;

	// Need to use static map to remember task id and reuse it. It gives the following:
	// 1) on first start of connector (= JVM) a new random taskId is generated, new config is applied and new task is able to run
	//    because it differs from previously stored taskId from config topic
	// 2) KafkaConnect can call maxTasks() multiple times on start, so we should somehow remember the discriminator (taskId)
	//    and provide it in the second call that proves to KafkaConnect that nothing changed and
	//    there is no need to rebalance.
	//    Only way to remember it without external storage now - to use a static map.
	private static final ConcurrentHashMap<Integer, String> NONE_MODE_TASK_IDS = new ConcurrentHashMap<>();
	private Optional<SshTunnel> tunnel;

	@Override
	protected String telemetryAppName() {
		return "jdbc-source";
	}

	@Override
	public void start(Map<String, String> props) throws ConnectException {

		super.start(props);

		this.tunnel = createTunnel(logger);

		this.monitorPollMs = ofNullable(trimToNull(config.originalsStrings().get(MONITOR_POLL_MS))).map(Long::parseLong).orElse(DEFAULT_POLL_MS);

		Map<String, String> probeParams = new HashMap<>(config.originalsStrings());

		String dbType = probeParams.get(NexlaConstants.CREDENTIALS_TYPE);
		if (isEmpty(dbType)) {
			throw new ConnectException("dbType must be configured");
		}
		ConnectionType connectionType = ConnectionType.fromString(dbType);
		if (connectionType == null) {
			throw new ConnectException("Invalid dbType: " + dbType);
		}

		if (isEmpty(probeParams.get(TABLE)) && isEmpty(probeParams.get(QUERY))) {
			throw new ConnectException("Table or query must be configured");
		}

		ControlMessageProducer controlMessageProducer = createControlMessageProducer();
		this.rebalancingTask = new RebalancingTask(context, config, new SqlConnectorService(nexlaAppConfigProperties.getStore(),null), controlMessageProducer);
		if (!config.fastMode && !config.isNoneMode()) {
			startMonitoring();
		}
	}

	protected ControlMessageProducer createControlMessageProducer() {
		return new ControlMessageProducer(new KafkaMessageTransport(controlKafkaConfig));
	}

	@Override
	protected JdbcSourceConnectorConfig parseConfig(Map<String, String> props) {
		return new JdbcSourceConnectorConfig(props);
	}

	@VisibleForTesting
	public void startMonitoring() {
		this.rebalancingFuture = executorService.get().scheduleWithFixedDelay(
			rebalancingTask::run, monitorPollMs, monitorPollMs, TimeUnit.MILLISECONDS);
	}

	@Override
	public Class<? extends Task> taskClass() {
		return JdbcSourceTask.class;
	}

	@Override
	public List<Map<String, String>> taskConfigs(int maxTasks) {

		rebalancingTask.pkStateLock.lock();

		try {

			if (config.cdcEnabled && !config.listingMode) {

				logger.info("Mode=Debezium, using 1 task to load data");
				Map<String, String> taskConfig = new HashMap<>(config.originalsStrings());
				return singletonList(taskConfig);

			} else if (config.isNoneMode()) {
				Map<String, String> taskConfig = new HashMap<>(config.originalsStrings());
				logger.info("Mode=NONE, using 1 task to load data");
				if (!NONE_MODE_TASK_IDS.containsKey(config.sourceId)) {
					String taskId = UUID.randomUUID().toString();
					NONE_MODE_TASK_IDS.put(config.sourceId, taskId);
					logger.info("Generating new taskId={}", taskId);
				} else {
					logger.info("Using existing taskId={}", NONE_MODE_TASK_IDS.get(config.sourceId));
				}
				taskConfig.put(JDBC_TASK_ID, NONE_MODE_TASK_IDS.get(config.sourceId));
				return singletonList(taskConfig);
			}

			MinMaxHolder minMaxHolder = rebalancingTask.getMinMax();
			if (minMaxHolder.isTableEmpty()) {
				return emptyList();
			}

			final List<Map<String, String>> configs;
			if (config.isIncrementingMode()) {
				configs = taskConfigsIncrementing(maxTasks, minMaxHolder);
			} else if (config.isTimestampMode() || config.isTimestampAndIncrementingMode()) {
				configs = taskConfigsTimestamp(maxTasks, minMaxHolder);
			} else {
				configs = emptyList();
			}
			return configs;
		} catch (Exception e) {
			// retry on connection error
			if (e instanceof ProbeRetriableException || e instanceof SqlConnectionException) {
				throw e;
			} else {
				return emptyList();
			}
		} finally {
			rebalancingTask.pkStateLock.unlock();
		}
	}

	private List<Map<String, String>> taskConfigsTimestamp(int maxTasks, MinMaxHolder minMax) {

		long minTs = config.timestampLoadFrom
			.map(loadFrom -> max(loadFrom, minMax.getMinTimestamp().getTime()))
			.orElse(minMax.getMinTimestamp().getTime());

		if (minMax.getMaxTimestamp().getTime() < minTs) {
			return emptyList();
		}

		long rangeLength = (minMax.getMaxTimestamp().getTime() - minTs + 1) / maxTasks;

		if (rangeLength == 0) {
			return singletonList(createTaskTimestamp(minTs, minMax.getMaxTimestamp().getTime()));
		}

		long fromTs = minTs;

		List<Map<String, String>> taskConfigs = new ArrayList<>(maxTasks);

		while (fromTs <= minMax.getMaxTimestamp().getTime()) {
			long toTs = min(minMax.getMaxTimestamp().getTime(), fromTs + rangeLength - 1);
			taskConfigs.add(createTaskTimestamp(fromTs, toTs));
			fromTs = toTs + 1;
		}
		return taskConfigs;
	}

	private List<Map<String, String>> taskConfigsIncrementing(int maxTasks, MinMaxHolder minMax) {

		Long minInc = config.incrementingLoadFromExclusive
			.map(loadFrom -> max(loadFrom, minMax.getMinIncrementingValue()))
			.orElse(minMax.getMinIncrementingValue());

		long rangeLength = (minMax.getMaxIncrementingValue() - minInc + 1) / maxTasks;
		if (rangeLength == 0) {
			return singletonList(createTaskIncrementing(minInc, minMax.getMaxIncrementingValue()));
		}

		long fromPk = minInc;

		List<Map<String, String>> taskConfigs = new ArrayList<>(maxTasks);
		while (fromPk <= minMax.getMaxIncrementingValue()) {
			long toPk = min(minMax.getMaxIncrementingValue(), fromPk + rangeLength - 1);
			taskConfigs.add(createTaskIncrementing(fromPk, toPk));
			fromPk = toPk + 1;
		}
		return taskConfigs;
	}

	private Map<String, String> createTaskIncrementing(Long fromInc, Long toInc) {
		Map<String, String> taskProps = new HashMap<>(config.originalsStrings());
		taskProps.put(INTERNAL_INCREMENTING_COLUMN_FROM, fromInc.toString());
		taskProps.put(INTERNAL_INCREMENTING_COLUMN_TO, toInc.toString());
		return taskProps;
	}

	private Map<String, String> createTaskTimestamp(Long fromTs, Long toTs) {
		Map<String, String> taskProps = new HashMap<>(config.originalsStrings());
		taskProps.put(INTERNAL_TIMESTAMP_COLUMN_FROM, fromTs.toString());
		taskProps.put(INTERNAL_TIMESTAMP_COLUMN_TO, toTs.toString());
		return taskProps;
	}

	@Override
	public void stop() throws ConnectException {
		if (rebalancingTask != null) {
			try {
				logger.info("Stopping rebalancing task");
				if (rebalancingFuture != null) {
					rebalancingFuture.cancel(true);
				}
			} catch (Exception e) {
				logger.error("", e);
			} finally {
				logger.info("Stopped rebalancing task");
			}

			try {
				logger.info("Closing rebalancing task resources");
				rebalancingTask.shutdown();
			} catch (Exception e) {
				logger.error("", e);
			} finally {
				logger.info("Closed rebalancing task resources");
			}
		}

		try {
			boolean initialized = on(executorService).field("initialized").get();
			if (initialized) {
				logger.info("Stopping executorService");
				executorService.get().shutdown();
			}
		} catch (Exception e) {
			logger.error("", e);
		} finally {
			logger.info("Stopping executorService");
		}
		this.tunnel.ifPresent(SshTunnel::close);
	}

	@Override
	public Optional<BaseAuthConfig> authConfig() {
		return Optional.of(config.authConfig);
	}

	@Override
	public CredentialsStore credentialsStore() {
		return nexlaAppConfigProperties.getStore();
	}

	@Override
	public List<HostPort> getHostPorts() {
		if (config.authConfig.url != null && !config.authConfig.url.isEmpty()
				&& (config.authConfig.host == null || config.authConfig.port == null)) {
			Optional<HostPort> hostPort = JdbcAuthConfig.extractHostPortFromUrl(config.authConfig.getCredsId(), config.authConfig.url);
			if (hostPort.isPresent()) {
				return Lists.newArrayList(hostPort.get());
			}
		}
		return Lists.newArrayList(new HostPort(config.authConfig.host, config.authConfig.port));
	}

	@Override
	public ConfigDef config() {
		return JdbcSourceConnectorConfig.configDef();
	}
}
