package com.nexla.connector.sql.poll;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataCredentials;
import com.nexla.admin.client.DataSource;
import com.nexla.admin.client.NexlaSchema;
import com.nexla.admin.client.config.EnrichedConfig;
import com.nexla.common.ConnectionType;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.common.notify.transport.DataMessageProducer;
import com.nexla.common.notify.transport.NoopNexlaMessageTransport;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.connector.config.FlowType;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import com.nexla.connector.file.source.BaseKafkaFileMessageReader;
import com.nexla.connector.file.source.FileSourceContext;
import com.nexla.connector.file.source.FileSourceNotificationSender;
import com.nexla.connector.file.source.MessageGrouper;
import com.nexla.connector.file.source.NoopMessageGrouper;
import com.nexla.connector.file.source.NoopOffsetWriter;
import com.nexla.connector.file.source.TransportFile;
import com.nexla.connector.file.source.TransportFileReader;
import com.nexla.file.service.FileConnectorService;
import com.nexla.probe.sql.SqlConnectorService;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.DialectRegistry;
import connect.jdbc.sink.dialect.copy.SourceCopyOperation;
import connect.jdbc.sink.dialect.copy.storage.WarehouseCopyTempStorage;
import connect.jdbc.source.BulkTableQuerier;
import connect.jdbc.source.DataConverter;
import connect.jdbc.source.SqlStatementCreator;
import connect.jdbc.util.WarehouseUtils;
import lombok.Data;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.connect.storage.OffsetStorageReader;
import org.javatuples.Pair;

import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.ResultSetMetaData;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.nexla.admin.client.config.SourceConfigUtils.createSourceConfig;
import static com.nexla.admin.config.ConfigUtils.enrichWithCredentialsStore;
import static com.nexla.admin.config.ConfigUtils.enrichWithDataCredentials;
import static com.nexla.common.NexlaConstants.AZURE_CREDS_ENC;
import static com.nexla.common.NexlaConstants.AZURE_CREDS_ENCIV;
import static com.nexla.common.NexlaConstants.CREDS_ENC;
import static com.nexla.common.NexlaConstants.CREDS_ENC_IV;
import static com.nexla.common.NexlaConstants.PATH;
import static com.nexla.common.NexlaConstants.S3_CREDS_ENC;
import static com.nexla.common.NexlaConstants.S3_CREDS_ENCIV;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.parse.ParserConfigs.Csv.CSV_DELIMITER;
import static com.nexla.common.parse.ParserConfigs.Csv.CSV_QUOTE_CHAR;
import static com.nexla.common.parse.ParserConfigs.SchemaDetection.SCHEMA_DETECTION_ONCE;
import static com.nexla.connector.config.file.FileSourceConnectorConfig.OVERRIDDEN_EXTENSIONS;
import static com.nexla.connector.file.source.OffsetUtils.createPartitionMap;
import static com.nexla.connector.properties.FileConfigAccessor.OFFSET_POSITION_KEY;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;

@Data
public class WarehouseSource {

	private final SqlConnectorService probeService;
	private final JdbcSourceConnectorConfig config;
	private final OffsetStorageReader offsetStorageReader;
	private final SchemaDetectionUtils schemaDetection;
	private final AdminApiClient adminApiClient;
	private final Long runId;
	private final NexlaLogger logger;

	@SneakyThrows
	public Pair<StreamEx<LinkedHashMap<String, Object>>, Optional<NexlaSchema>> readStreamWarehouse() {
		Optional<NexlaSchema> nexlaSchema = empty();
		BulkTableQuerier.QueryMode mode = config.query.map(a -> BulkTableQuerier.QueryMode.QUERY).orElse(BulkTableQuerier.QueryMode.TABLE);

		DbDialect dbDialect = DialectRegistry.getInstance().fromConnectionString(config.authConfig);

		WarehouseCopyTempStorage tempStorage = WarehouseUtils.getCopyOperationTempStorage(config);

		String bucketDest = Paths.get(tempStorage.getTempUploadBucket(), "source-" + config.sourceId, String.valueOf(runId)).toString();
		WarehouseCopyFileFormat copyFileFormat = config.unloadFormat.orElseGet(() -> dbDialect.defaultSourceFileFormat().get());

		try (Connection connection = probeService.getConnection(config.authConfig);
		     SourceCopyOperation copyOperation = dbDialect.newSourceCopyOperation(
				 config, copyFileFormat, logger, false, tempStorage, FlowType.STREAMING)) {

			BulkTableQuerier querier = new BulkTableQuerier(
				dbDialect,
				config,
				false,
				Integer.MAX_VALUE,
				mode,
				config.authConfig.schemaName,
				config.table,
				config.query,
				Optional.empty());

			SqlStatementCreator stmtCreator = new SqlStatementCreator();
			querier.initStatementCreator(connection, stmtCreator);
			String resultQ = stmtCreator.getStatementSql();

			try {
				ResultSetMetaData metaData = stmtCreator.getStatement().getMetaData();
				nexlaSchema = Optional.ofNullable(DataConverter.toNexlaSchema(metaData, dbDialect));
			} catch (Exception e) {
				logger.error("Failed to get schema from query. We will use sample to determine schema", e);
			}

			String copyOperationBucketDest = copyOperation.destinationPath(config.sourceId, runId);
			copyOperation.executeCopyCommand(connection, resultQ, copyOperationBucketDest);
			connection.commit();
		}

		EnrichedConfig.EnrichSourceParams enrichSourceParams = new EnrichedConfig.EnrichSourceParams(
			config.vaultHost,
			config.vaultToken,
			1,
			1,
			config.credentialsSource,
			config.secretManagerRegion,
			config.secretManagerAccessKey,
			config.secretManagerSecretKey,
			config.secretManagerRoleArn,
			config.secretManagerIdentityTokenFile,
			config.secretNames
		);

		ControlMessageProducer controlMessageProducer = new ControlMessageProducer(new NoopNexlaMessageTransport());
		DataMessageProducer dataMessageProducer = new DataMessageProducer(new NoopNexlaMessageTransport());

		Map<String, Object> cfg = Maps.newHashMap();
		cfg.put(SCHEMA_DETECTION_ONCE, Boolean.TRUE.toString());
		cfg.put(PATH, bucketDest);

		DataSource dataSource = new DataSource();
		dataSource.setId(config.sourceId);
		dataSource.setSourceConfig(cfg);
		dataSource.setConnectionType(tempStorage.getConnectionType());
		DataCredentials dataCredentials = new DataCredentials();
		dataCredentials.setCredentialsEnc(config.credsEnc);
		dataCredentials.setCredentialsEncIv(config.credsEncIv);
		dataCredentials.setId(config.credsId);
		dataSource.setDataCredentials(dataCredentials);

		Map<String, String> enrichedConfig = createSourceConfig(dataSource, enrichSourceParams);
		Map<String, String> baseConfig = enrichWithCredentialsStore(enrichedConfig, FileSourceConnectorConfig.configDef());

		Map<String, String> resultConfig = Maps.newHashMap(config.originalsStrings());
		resultConfig.putAll(baseConfig);

		enrichWithDataCredentials(adminApiClient, resultConfig);

		Pair<String, String> creds = getCredsNameByType(tempStorage.getConnectionType());

		resultConfig.put(CREDS_ENC, config.originals().get(creds.getValue0()).toString());
		resultConfig.put(CREDS_ENC_IV, config.originals().get(creds.getValue1()).toString());

		switch (copyFileFormat.format) {
			case CSV_FORMAT:
				resultConfig.put(CSV_DELIMITER, copyFileFormat.delimiter);
				resultConfig.put(CSV_QUOTE_CHAR, "\"");
				resultConfig.put(OVERRIDDEN_EXTENSIONS, ":csv");
				break;
			case JSON_FORMAT:
				resultConfig.put(OVERRIDDEN_EXTENSIONS, ":json");
				break;
		}

		FileConnectorService connectorService = tempStorage.getConnectorService();

		FileSourceConnectorConfig fileConfig = new FileSourceConnectorConfig(resultConfig);
		NoopOffsetWriter offsetWriter = new NoopOffsetWriter();
		connectorService.initLogger(SOURCE, fileConfig.sourceId, empty());

		FileSourceContext fileSourceContext = new FileSourceContext(fileConfig, empty(), runId);
		MessageGrouper messageGrouper = new NoopMessageGrouper();
		FileSourceNotificationSender notificationSender = new FileSourceNotificationSender(
			controlMessageProducer, dataMessageProducer, FlowType.STREAMING, fileSourceContext, logger);

		Consumer<TransportFile> tfConsumer = tf -> {
			if (tempStorage.getDeleteTempBucket()) {
				connectorService.deleteByName(fileConfig, Lists.newArrayList(tf.nexlaFile.getFullPath()));
			}
		};

		TransportFileReader fileReader = new TransportFileReader(
			notificationSender, connectorService, schemaDetection, offsetWriter, messageGrouper,
			logger, Optional.of(offsetStorageReader), empty(), Optional.of(tfConsumer), fileSourceContext,
			empty(), nexlaSchema, false);

		fileReader.setCustomParserEnabled(true);

		List<TransportFile> transportFiles = tempStorage.listObjects(fileConfig, bucketDest)
			.stream()
			.map(file -> {
				Map<String, Object> offset = offsetStorageReader.offset(createPartitionMap(false, file));
				String offsetAsStr = ofNullable(offset)
					.map(x -> x.get(OFFSET_POSITION_KEY))
					.orElse("0")
					.toString();
				return new TransportFile(file, Long.parseLong(offsetAsStr), false);
			})
			.collect(Collectors.toList());

		fileReader.addFiles(transportFiles);

		BaseKafkaFileMessageReader consumer = new BaseKafkaFileMessageReader(
			offsetWriter, messageGrouper, fileSourceContext, logger, connectorService, notificationSender);

		return Pair.with(StreamEx
				.iterate(fileReader.readNextBatch(consumer, adminApiClient).messages, (batch) -> fileReader.readNextBatch(consumer, adminApiClient).messages)
				.takeWhile(Objects::nonNull)
				.flatMap(batch -> batch.stream().map(r -> r.message.getRawMessage())), nexlaSchema);
	}

	private Pair<String, String> getCredsNameByType(ConnectionType connectionType) {
		var credsConstsByConnectionType = Map.of(
				ConnectionType.S3, Pair.with(S3_CREDS_ENC, S3_CREDS_ENCIV),
				ConnectionType.AZURE_BLB, Pair.with(AZURE_CREDS_ENC, AZURE_CREDS_ENCIV)
		);

		return credsConstsByConnectionType.get(connectionType);
	}

}
