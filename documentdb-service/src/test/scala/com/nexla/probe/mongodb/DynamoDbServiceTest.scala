package com.nexla.probe.mongodb

import com.dimafeng.testcontainers.{Container, ForAllTestContainer, GenericContainer, MultipleContainers}
import com.nexla.connector.config.documentdb.dynamodb.DynamoDbAuthConfig
import com.nexla.dynamodb.DynamoDbTestUtils
import com.nexla.probe.dynamodb.DynamoDbService
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfterAll, OneInstancePerTest}
import software.amazon.awssdk.regions.Region

import scala.collection.JavaConverters._

@com.nexla.test.ScalaIntegrationTests
class DynamoDbServiceTest
  extends AnyFlatSpecLike
  with Matchers
  with BeforeAndAfterAll
  with OneInstancePerTest with ForAllTestContainer {

  private val dynamo = GenericContainer(
    dockerImage = "amazon/dynamodb-local:1.19.0",
    exposedPorts = Seq(8000)
  )

  val DEFAULT_REGION: Region = Region.of("us-west-1")

  override val container: Container = MultipleContainers(
    dynamo
  )

  it should "authentication successful" in {
        val service = new DynamoDbService
        val testUtils = new DynamoDbTestUtils(dynamo.host, dynamo.mappedPort(8000))
        val baseConfig = testUtils.baseConfig

        service.authenticate(
          new DynamoDbAuthConfig(
            (baseConfig + ("dynamodb.endpoint" -> testUtils.dynamoDbUrl, "dynamodb.region" -> DEFAULT_REGION)).asJava, 123
          )
        ).success shouldBe true
  }

  it should "authentication failed" in {
    val service = new DynamoDbService
    val testUtils = new DynamoDbTestUtils(dynamo.host, dynamo.mappedPort(8000))
    val baseConfig = testUtils.baseConfig

    service.authenticate(
      new DynamoDbAuthConfig(
        (baseConfig + ("dynamodb.endpoint" -> "http://fake-endpoint.com", "dynamodb.region" -> DEFAULT_REGION)).asJava, 123
      )
    ).success shouldBe false
  }
}
