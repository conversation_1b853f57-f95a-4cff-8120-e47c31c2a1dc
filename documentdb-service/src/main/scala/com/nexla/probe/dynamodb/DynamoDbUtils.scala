package com.nexla.probe.dynamodb

import software.amazon.awssdk.auth.credentials.{AwsBasicCredentials, StaticCredentialsProvider}
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.{DynamoDbClient, DynamoDbClientBuilder}
import com.nexla.connector.config.documentdb.dynamodb.DynamoDbAuthConfig
import org.apache.commons.lang3.StringUtils.isNotEmpty

import java.net.URI

object DynamoDbUtils {

  def buildClient(config: DynamoDbAuthConfig): DynamoDbClient = {
    val credentials = AwsBasicCredentials.create(config.accessKey, config.secretKEy)
    val builder = DynamoDbClient.builder()
      .credentialsProvider(StaticCredentialsProvider.create(credentials))

    if (isNotEmpty(config.endpoint)) {
      builder.endpointOverride(URI.create(config.endpoint))
    } else {
      builder.region(Region.of(config.region))
    }

    builder.build()
  }

}