package com.nexla.connector.apistreams.source;

import com.google.common.collect.Lists;
import com.nexla.admin.client.NexlaSchema;
import com.nexla.apistreams.spec.*;
import com.nexla.client.ScriptEvalClient;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.RestTemplateBuilder;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.tracker.SourceItem;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.*;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.connector.apistreams.source.processing.*;
import com.nexla.connector.config.api_streams.*;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.connector.config.rest.RestSourceConnectorConfig;
import com.nexla.probe.http.RestConnectorService;
import com.nexla.rest.RestIterationChainBuilder;
import com.nexla.rest.pojo.RestSourceError;
import lombok.NoArgsConstructor;
import org.apache.commons.net.util.Base64;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.source.SourceRecord;
import scala.Function2;

import java.util.*;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.ConfigUtils.optBoolean;
import static com.nexla.common.NotificationEventType.ERROR;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.connector.config.api_streams.properties.ApiStreamsConfigAccessor.BASELINE_CONFIG_TYPE;
import static com.nexla.connector.config.api_streams.properties.ApiStreamsConfigAccessor.DATA_FEED_CONFIG_TYPE;
import static java.util.Optional.empty;
import static java.util.stream.Collectors.toList;
import static org.apache.kafka.connect.data.Schema.STRING_SCHEMA;

@NoArgsConstructor
public class ApiStreamsSourceTask extends BaseSourceTask<ApiStreamsSourceConnectorConfig> {

    private static final Integer MAX_RECORDS_PER_POLL = 10000;
    private CommonEltStreamConfig commonConfig;
    private Map<String, BaseEltStreamConfig> streamConfigs;
    private Map<String, DataEltStream> dataStreams;
    private Map<String, DataSelfContainedEltStream> selfContainedStreams;
    private RestConnectorService probeService;
    private final RestSourceError restSourceError = new RestSourceError(new AtomicInteger(0), "");
    private final AtomicInteger pollCounter = new AtomicInteger(0);
    private Optional<DetailedFlowInsightsSender> flowInsightsSender;

    private StreamFetchStrategy streamFetchStrategy;

    public ApiStreamsSourceTask(SchemaDetectionUtils schemaDetection) {
        this.schemaDetection = schemaDetection;
    }

    @Override
    public CollectRecordsResult collectRecords() {
        boolean useSingleSchema = config.singleSchema || opt(config.getString("pipeline.type")).orElse("").equals("elt");

        if (config.listingMode && useSingleSchema) {
            List<SourceRecordCreator> records = getDummyRecordForSchemaDetection();
            List<SourceRecord> recordsWithDataset = detectSchemaIfNecessary(false, records, empty());
            return new CollectRecordsResult(recordsWithDataset);
        }

        if (config.runOnce && pollCounter.get() > 0) {
            return new CollectRecordsResult(Collections.emptyList());
        }

        ScheduledFuture<?> heartbeatScheduler = SCHEDULED_POOL.scheduleWithFixedDelay(this::heartbeat, 1, 2, TimeUnit.MINUTES);

        try {
            List<SourceRecord> allSourceRecords = Lists.newArrayList();
            List<StreamMetricResult> metricsResults = Lists.newArrayList();
            initStreamFetchStrategy();

            while (true) {
                if (allSourceRecords.size() >= MAX_RECORDS_PER_POLL) {
                    break;
                }

                StreamFetchResult streamFetchResult = streamFetchStrategy.iterate();

                if (Objects.isNull(streamFetchResult)) {
                    break;
                }

                logger.info("M=collectRecords, strategy={}, recordsSizeFetched={}", streamFetchStrategy.getClass().getSimpleName(), streamFetchResult.getRecords().size());

                // todo treat single vs multi schema differently here? single could send all records to schema detection at once
                for (String streamName : streamFetchResult.getRecords().keySet()) {
                    List<SourceRecordCreator> records = streamFetchResult.getRecords().get(streamName);
                    if (records == null || records.isEmpty()) {
                        continue;
                    }

                    allSourceRecords.addAll(detectSchemaIfNecessary(!useSingleSchema, records, empty(), Optional.of(streamName)));

                    List<StreamMetricResult> metrics = streamFetchResult.getMetricResult().get(streamName);
                    List<StreamMetricResult> validMetrics = metrics
                        .stream()
                        .filter(sr -> sr.getRecordsCount() > 0)
                        .collect(toList());
                    validMetrics.forEach(this::processResult);
                    metricsResults.addAll(validMetrics);
                }
            }

            sendStreamResultMetrics(metricsResults);

            this.pollCounter.incrementAndGet();

            return new CollectRecordsResult(allSourceRecords);
        } finally {
            heartbeatScheduler.cancel(false);
        }
    }

    private void initStreamFetchStrategy() {
        if (Objects.isNull(streamFetchStrategy)) {
            this.streamFetchStrategy = getStreamFetchStrategy(getAllBaseEltStreams());
        }
    }

    private List<SourceRecordCreator> getDummyRecordForSchemaDetection() {
        BaseEltStream baseEltStream = getAllBaseEltStreams().get(0);
        String url = System.getenv("EMAIL_NEXLA_UI_URL");

        LinkedHashMap<String, Object> dataMap = baseEltStream.toEltRecordData(
            new LinkedHashMap<>(),
            url,
            baseEltStream.getStreamName(),
            List.of("id"),
            Optional.empty(),
            config.singleSchema);

        Function2<Integer, String, NexlaMessage> nexlaMessageCreator = (dataSetId, dataSetTopic) -> {
            long now = System.currentTimeMillis();
            SourceItem nexlaSourceTrackerIdItem = SourceItem.fullTracker(
                config.sourceId,
                dataSetId,
                new String(Base64.encodeBase64(url.getBytes())),
                0L,
                config.version,
                now);

            NexlaMetaData metaData = new NexlaMetaData(
                ConnectionType.API_STREAMS,
                now,
                0L,
                String.format("%s", url),
                dataSetTopic,
                SOURCE,
                config.sourceId,
                false,
                new Tracker(Tracker.TrackerMode.FULL, nexlaSourceTrackerIdItem),
                runId);

            return new NexlaMessage(dataMap, metaData);
        };

        Function2<Integer, String, SourceRecord> sourceRecordCreator = (datasetId, topic) -> {
            NexlaMessage message = nexlaMessageCreator.apply(datasetId, topic);
            return new SourceRecord(Map.of(),
                Map.of(),
                topic,
                null,
                null,
                null,
                STRING_SCHEMA,
                toJsonString(message));
        };

        return List.of(new SourceRecordCreator(dataMap, sourceRecordCreator, nexlaMessageCreator));
    }

    private StreamFetchStrategy getStreamFetchStrategy(List<BaseEltStream> dataStreams) {
        return commonConfig.isSequenceParallel() ?
            new ParallelStreamFetchStrategy(dataStreams, commonConfig.maxParallelStreams) :
            new SerialStreamFetchStrategy(dataStreams);
    }

    private List<BaseEltStream> getAllBaseEltStreams() {
        List<BaseEltStream> dataStreams = Lists.newArrayList(this.dataStreams.values());
        dataStreams.addAll(this.selfContainedStreams.values());
        return dataStreams;
    }

    private void processResult(StreamMetricResult metricsResults) {
        if (metricsResults.getRecordsCount() > 0) {
            publishMonitoringLog(metricsResults.getRecordsCount(), metricsResults.getStreamName());
        }
    }

    private void publishMonitoringLog(int recordsSize, String streamName) {
        var message = String.format("Successfully fetched %s records from ELT feed: \"%s\"", recordsSize, streamName);
        publishMonitoringLog(message, NexlaMonitoringLogType.EVENT, NexlaMonitoringLogSeverity.INFO);

        if (restSourceError.errorCounter.get() != 0) {
            NexlaConnectorUtils.sendNexlaNotificationEvent(controlMessageProducer, ERROR, runId, SOURCE, dataSource.getId(),
                    dataSource.getName(), (long) restSourceError.errorCounter.get(), restSourceError.getErrorMessage());
            publishMonitoringLog(restSourceError.getErrorMessage(), NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
        }
        restSourceError.errorCounter.set(0);
    }

    private void sendStreamResultMetrics(List<StreamMetricResult> metricsResults) {
        metricsResults
            .forEach(metricResult -> {
            var restIteration = metricResult.getResult();
            var recordsSize = metricResult.getRecordsCount();
            sendMetrics(restIteration.getUrl(),
                    recordsSize,
                    restIteration.getByteCounter().get(),
                    restSourceError.errorCounter.get(),
                    restIteration.getMetric().getStartInMillis(),
                    datasetMapping.get(metricResult.getStreamName()));
        });
    }

    private RestIterationChainBuilder initRestIterationChainBuilder(Map<String, String> props, List<RestIterationConfig> restIterationConfigs) {
        return new RestIterationChainBuilder(logger, true, Optional.of(heartbeatCallback()),
                Optional.of(adminApiClient), Optional.of(probeService), Optional.of(controlMessageProducer),
                Optional.of(dataSource.getId()), Optional.of(restSourceError), new RestSourceConnectorConfig(props, config.authConfig, restIterationConfigs), flowInsightsSender);
    }

    @Override
    public void doStart(Map<String, String> props) {
        this.commonConfig = config.commonEltStreamConfig;
        this.streamConfigs = config.streamConfigs;
        this.flowInsightsSender = Optional.ofNullable(DetailedFlowInsightsSender.from(
                config.logVerbose, getSuccessFlowInsightsSender(), getErrorFlowInsightsSender(), config.detailedFlowInsightsAbbreviatedLength));
        var scriptEvalClient = new ScriptEvalClient(config.probeAppUrl, config.nexlaUsername,
                config.nexlaPassword, restTemplate);
        this.probeService = new RestConnectorService(scriptEvalClient);
        this.probeService.initLogger(SOURCE, config.sourceId, empty());

        dataStreams = new HashMap<>();
        selfContainedStreams = new HashMap<>();

        streamConfigs.entrySet().forEach(entry -> {
            String streamKey = entry.getKey();
            BaseEltStreamConfig streamConfig = entry.getValue();

            if (streamConfig.getType().equals(DATA_FEED_CONFIG_TYPE)) {
                Optional<String> refSpec = streamConfig.getRefSpec();
                if (refSpec.isPresent()) {
                    DataStreamConfig dataStreamConfig = (DataStreamConfig) streamConfig;

                    BaseLineEltStream baseLineEltStream = getBaseLineEltStream(refSpec, streamKey, props, dataStreamConfig.getParams())
                            .orElseThrow(() -> new IllegalArgumentException("Base feed is absent for refSpec of feed " + streamKey));
                    dataStreams.put(streamKey, new DataEltStream(commonConfig.streamParams, baseLineEltStream,
                            streamKey, config, runId, dataStreamConfig.getPrimaryKeys(), dataStreamConfig.getMetadata()));
                } else {
                    SelfContainedDataStreamConfig selfContDataStreamConfig = (SelfContainedDataStreamConfig) streamConfig;
                    List<RestIterationConfig> restIterationConfigs = substituteRestIterationConfigVars(
                            streamConfig.getRestIterationConfigs(), selfContDataStreamConfig.getParams());

                    if (restIterationConfigs != null && !restIterationConfigs.isEmpty() && streamConfig.getRefSpec().isEmpty()) {
                        selfContainedStreams.put(streamKey, new DataSelfContainedEltStream(commonConfig.streamParams,
                                initRestIterationChainBuilder(props, restIterationConfigs),
                                restIterationConfigs,
                                streamKey, config, runId,
                                selfContDataStreamConfig.getPrimaryKeys(),
                            selfContDataStreamConfig.getMetadata()));
                    } else {
                        String errorMsg = String.format("Both rest iteration config and ref spec are absent for feed %s", streamKey);
                        publishMonitoringLog(errorMsg, NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
                        throw new IllegalArgumentException(errorMsg);
                    }
                }
            }
        });
    }

    private Optional<BaseLineEltStream> getBaseLineEltStream(Optional<String> refSpec,
                                                             String streamKey,
                                                             Map<String, String> props,
                                                             Optional<Map<String, String>> replacementParams) {
        Optional<BaseLineEltStream> baseLineEltStream = Optional.empty();
        for (Map.Entry<String, BaseEltStreamConfig> entry1 : streamConfigs.entrySet()) {
            String baseLineStreamKey = entry1.getKey();
            BaseEltStreamConfig baseLineStreamConfig = entry1.getValue();

            List<RestIterationConfig> restIterationConfigs = substituteRestIterationConfigVars(
                    baseLineStreamConfig.getRestIterationConfigs(), replacementParams);

            if (baseLineStreamConfig.getType().equals(BASELINE_CONFIG_TYPE)
                    && baseLineStreamKey.equals(refSpec.get())) {
                baseLineEltStream = Optional.of(new BaseLineEltStream(commonConfig.streamParams,
                        initRestIterationChainBuilder(props, restIterationConfigs),
                        restIterationConfigs,
                        streamKey,
                        config,
                        runId));
            }
        }
        return baseLineEltStream;
    }

    private List<RestIterationConfig> substituteRestIterationConfigVars(List<RestIterationConfig> restIterationConfigs, Optional<Map<String, String>> params) {
        return restIterationConfigs.stream()
                .map(restIterationConfig ->
                        params.map(paramsMap -> {
                                    RestIterationConfig a = RestIterationConfigProcessor.substituteRestIterationConfigUrlParams(restIterationConfig, paramsMap);
                                    RestIterationConfig b = RestIterationConfigProcessor.substituteRestIterationResponseDataPath(a, paramsMap);
                                    return b;
                                })
                                .orElse(restIterationConfig)
                ).collect(toList());
    }

    private Runnable heartbeatCallback() {
        return this::heartbeat;
    }

    @Override
    protected ApiStreamsSourceConnectorConfig parseConfig(Map<String, String> props) {
        return new ApiStreamsSourceConnectorConfig(props);
    }

    @Override
    public ConfigDef configDef() {
        return ApiStreamsSourceConnectorConfig.configDef();
    }
}
