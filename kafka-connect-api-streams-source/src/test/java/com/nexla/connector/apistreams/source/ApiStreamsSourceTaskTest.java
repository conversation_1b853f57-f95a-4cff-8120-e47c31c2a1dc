package com.nexla.connector.apistreams.source;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.core.Options;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.google.common.collect.Lists;
import com.nexla.admin.client.*;
import com.nexla.common.NexlaConstants;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connector.config.ConnectorAdminConfig;
import com.nexla.test.IntegrationTests;
import lombok.SneakyThrows;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.source.SourceTaskContext;
import org.apache.kafka.connect.storage.OffsetStorageReader;
import org.junit.*;
import org.junit.experimental.categories.Category;
import org.testcontainers.containers.KafkaContainer;

import java.util.*;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.nexla.admin.client.FindOrCreateDataSetResult.Result.CREATED;
import static com.nexla.admin.client.FindOrCreateDataSetResult.Result.FOUND;
import static com.nexla.common.ConnectionType.*;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.metrics.NexlaRawMetric.RUN_ID;
import static com.nexla.common.parse.ParserConfigs.SchemaDetection.DATASET_REPLICATION;
import static com.nexla.connect.common.elt.ELTConstants.NEXLA_OPERATION_RECORD;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.BaseConnectorConfig.METRICS_TOPIC;
import static com.nexla.connector.config.rest.RestSourceConnectorConfig.SINGLE_SCHEMA;
import static com.nexla.connector.properties.RestConfigAccessor.POLL_MS;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@Category(IntegrationTests.class)
public class ApiStreamsSourceTaskTest extends BaseKafkaTest {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @ClassRule
    public static WireMockRule wireMockServer = new WireMockRule(Options.DYNAMIC_PORT);

    public static KafkaContainer kafka = new KafkaContainer("7.2.11");

    private ApiStreamsSourceTask task;
    private AdminApiClient adminApiClientMock;

    private Map<String,Object> common;
    private Map<String,Object> feeds;

    @BeforeClass
    public static void beforeClass() {
        kafka.withReuse(true);
        kafka.start();
        init(kafka);
    }

    @AfterClass
    public static void afterClass() {
        kafka.stop();
    }

    @Before
    public void before() {
        wireMockServer.resetAll();

        DataSource ds = new DataSource();
        ds.setId(1);
        ds.setSourceConfig(new HashMap<>());
        ds.setDatasets(Lists.newLinkedList());
        ds.setConnectionType(API_MULTI);
        ds.setRunIds(List.of());

        adminApiClientMock = mock(AdminApiClient.class);
        when(adminApiClientMock.getDataSource(any())).thenReturn(Optional.of(ds));
        when(adminApiClientMock.findOrCreateDataSet(any())).thenReturn(new FindOrCreateDataSetResult(FOUND, ds.getId()));

        task = new ApiStreamsSourceTask() {
            @Override
            protected AdminApiClient getAdminApiClient(String adminApiPrefix, ConnectorAdminConfig adminConfig) {
                return adminApiClientMock;
            }
        };

        SourceTaskContext sourceTaskContext = mock(SourceTaskContext.class);
        OffsetStorageReader offsetStorageReader = mock(OffsetStorageReader.class);
        when(sourceTaskContext.offsetStorageReader()).thenReturn(offsetStorageReader);
        when(offsetStorageReader.offset(anyMap())).thenReturn(null);
        task.initialize(sourceTaskContext);

        common = new HashMap<>() {{
            put("sequence", "parallel");
            put("max_parallel_feeds", 2);
            put("order_first", new ArrayList<>());
            put("order_last", new ArrayList<>());
            put("feed_params", new HashMap<>());
        }};
        feeds = new HashMap<>() {{
            put("base_full", new HashMap<>() {{
                put("type", "base");
                put("spec", new HashMap<>() {{
                    put("rest.iterations", new ArrayList<>() {{
                        add(new HashMap<>() {{
                            put("key", "step1");
                            put("iteration.type", "static.url");
                            put("url.template", "http://localhost:" + wireMockServer.port() + "/meta/{feed_spec.object_name}");
                            put("method", "GET");
                            put("response.data.path", "$");
                        }});
                        add(new HashMap<>() {{
                            put("key", "step2");
                            put("iteration.type", "paging.next.url");
                            put("url.template", "http://localhost:" + wireMockServer.port() + "/data/{step1.query_obj}");
                            put("method", "GET");
                            put("response.data.path", "$.records[*]");
                            put("response.next.url.data.path", "$.nextRecordsUrl");
                        }});
                    }});
                }});
            }});
            put("base_incremental", new HashMap<>() {{
                put("type", "base");
                put("spec", new HashMap<>() {{
                    put("rest.iterations", new ArrayList<>()); // todo fill in some iterations
                }});
            }});
            put("Account", new HashMap<>() {{
                put("type", "data");
                put("spec", new HashMap<>() {{
                    put("ref_spec", "base_full");
                    put("primary.keys", new ArrayList<>() {{
                        add("Id");
                    }});
                    put("params", new HashMap<>() {{
                        put("feed_spec.object_name", "Account");
                        put("ui.feed_display_name", "Account");
                    }});
                }});
            }});
            put("Contact", new HashMap<>() {{
                put("type", "data");
                put("spec", new HashMap<>() {{
                    put("ref_spec", "base_full");
                    put("primary.keys", new ArrayList<>() {{
                        add("Id");
                    }});
                    put("params", new HashMap<>() {{
                        put("feed_spec.object_name", "Contact");
                        put("ui.feed_display_name", "Contact");
                    }});
                }});
            }});
        }};
    }

    @After
    public void after() {
        task.stop();
    }

    @Test
    @SneakyThrows
    public void singleSchemaTest () {
        withConsumer((metricsTopic, metricsConsumer) -> {
            // todo move to json file?
            Map<String, String> sourceConfig = new HashMap<>(baseConfig()) {{
                put(PIPELINE_TYPE, "elt");
                try {
                    put("common", OBJECT_MAPPER.writeValueAsString(common));
                    put("feeds", OBJECT_MAPPER.writeValueAsString(feeds));
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }};
            sourceConfig.put(METRICS_TOPIC, metricsTopic);

            // step 1 responses
            stubFor(get(urlEqualTo("/meta/Account"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withBody("{\"query_obj\": \"account\"}")
                    ));
            stubFor(get(urlEqualTo("/meta/Contact"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withBody("{\"query_obj\": \"contact\"}")
                    ));
            // step 2 responses
            stubFor(get(urlEqualTo("/data/account"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withBody("{\"records\": [{\"Id\": 1, \"Name\": \"Account 1\"}], \"nextRecordsUrl\": null}")
                    ));
            stubFor(get(urlEqualTo("/data/contact"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withBody("{\"records\": [{\"Id\": 1, \"Full Name\": \"Alice Bobdottir\"}], \"nextRecordsUrl\": null}")
                    ));

            task.start(sourceConfig);

            List<SourceRecord> records = task.poll();
            Assert.assertEquals(2, records.size());
            Assert.assertEquals(records.get(0).topic(), records.get(1).topic());
            Assert.assertTrue(((String) records.get(0).value()).contains(NEXLA_OPERATION_RECORD));
            List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);
            Assert.assertEquals(2, metrics.size());
            Assert.assertEquals(1, metrics.get(0).getFields().get("dataset_id"));
            Assert.assertEquals(1, metrics.get(1).getFields().get("dataset_id"));
        });
    }

    @Test
    @SneakyThrows
    public void multiSchemaTest () {
        // assume adminApi would return separate dataset ids for the different schemas of the stubbed endpoints
        when(adminApiClientMock.findOrCreateDataSet(any()))
                .thenReturn(new FindOrCreateDataSetResult(FOUND, 1), new FindOrCreateDataSetResult(CREATED, 2));

        withConsumer((metricsTopic, metricsConsumer) -> {
            // todo move to json file?
            Map<String,String> sourceConfig = new HashMap<>(baseConfig()) {{
                put(PIPELINE_TYPE, "streaming");
                put(SINGLE_SCHEMA, "false");
                try {
                    put("common", OBJECT_MAPPER.writeValueAsString(common));
                    put("feeds", OBJECT_MAPPER.writeValueAsString(feeds));
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }};
            sourceConfig.put(METRICS_TOPIC, metricsTopic);

            // todo move these out to a helper
            // step 1 responses
            stubFor(get(urlEqualTo("/meta/Account"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withBody("{\"query_obj\": \"account\"}")
                    ));
            stubFor(get(urlEqualTo("/meta/Contact"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withBody("{\"query_obj\": \"contact\"}")
                    ));
            // step 2 responses
            stubFor(get(urlEqualTo("/data/account"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withBody("{\"records\": [{\"Id\": 1, \"Name\": \"Account 1\"}], \"nextRecordsUrl\": null}")
                    ));
            stubFor(get(urlEqualTo("/data/contact"))
                    .willReturn(aResponse()
                            .withHeader("Content-Type", "application/json")
                            .withBody("{\"records\": [{\"Id\": 1, \"Full Name\": \"Alice Bobdottir\"}], \"nextRecordsUrl\": null}")
                    ));

            task.start(sourceConfig);

            List<SourceRecord> records = task.poll();
            Assert.assertEquals(2, records.size());
            Assert.assertNotEquals(records.get(0).topic(), records.get(1).topic());
            Assert.assertFalse(((String) records.get(0).value()).contains(NEXLA_OPERATION_RECORD));
            List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);
            Assert.assertEquals(2, metrics.size());
            Assert.assertEquals(1, metrics.get(0).getFields().get("dataset_id"));
            Assert.assertEquals(2, metrics.get(1).getFields().get("dataset_id"));
        });
    }

    private static Map<String, String> baseConfig() {
        Map<String, String> baseConfig = new HashMap<>();
        baseConfig.put(NexlaConstants.CONTROL_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
        baseConfig.put(NexlaConstants.DATA_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
        baseConfig.put(NexlaConstants.CONTROL_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);
        baseConfig.put(NexlaConstants.DATA_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);

        baseConfig.put(UNIT_TEST, "true");
        baseConfig.put(DATASET_REPLICATION, "1");
        baseConfig.put(POLL_MS, "1");
        baseConfig.put(CREDS_ENC, "1");
        baseConfig.put(CREDS_ENC_IV, "1");
        baseConfig.put(CREDENTIALS_DECRYPT_KEY, "1");
        baseConfig.put(SOURCE_ID, "1");
        baseConfig.put(LISTING_APP_SERVER_URL, "123");
        baseConfig.put(RUN_ID, "123456789");
        return baseConfig;
    }
}
