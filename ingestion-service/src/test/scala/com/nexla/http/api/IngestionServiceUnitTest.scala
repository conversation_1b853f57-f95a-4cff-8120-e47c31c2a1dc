package com.nexla.http.api

import com.nexla.admin.client.{AdminApiClient, DataSource, ResourceStatus}
import com.nexla.client.JavaJobSchedulerClient
import com.nexla.common.NexlaSslContext
import com.nexla.common.notify.transport.ControlMessageProducer
import com.nexla.http.AppProps
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfterEach
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.util.Optional

@com.nexla.test.ScalaUnitTests
class IngestionServiceUnitTest extends AnyFlatSpecLike with Matchers with BeforeAndAfterEach {
  "IngestionService" should "throw SourceNotActive exception if the source in question is not ACTIVE" in {
    val mockAdminApi = mock(classOf[AdminApiClient])
    val stubDs = new DataSource()
    stubDs.setId(123)
    stubDs.setStatus(ResourceStatus.PAUSED)
    when(mockAdminApi.getDataSource(123)).thenReturn(Optional.of(stubDs))
    val mockKafka = mock(classOf[ControlMessageProducer])
    val mockDetectionCache = mock(classOf[SchemaDetectionCache])
    val mockProps = mock(classOf[AppProps])
    val ctrlClient = mock(classOf[JavaJobSchedulerClient])
    when(mockProps.metricsWindowMs).thenReturn(Integer.MAX_VALUE)
    val instance = new IngestionService(mockAdminApi, mockKafka, mockDetectionCache, ctrlClient, mockProps)

    assertThrows[SourceNotActiveException] {
      instance.ingest(java.util.Map.of(), java.util.Map.of(), java.util.List.of(), 123, false)
    }
  }
}
