package com.nexla.http

import akka.http.scaladsl.Http
import akka.stream.Materializer
import com.nexla.admin.client.{AdminApiClient, AdminApiClientBuilder}
import com.nexla.client.{JavaJobSchedulerClient, KafkaClusterPropertiesDefault}
import com.nexla.common.license.NexlaLicenseManager
import com.nexla.common.metrics.HealthMetricAggregator
import com.nexla.common.notify.monitoring.NexlaMonitoringLogEvent
import com.nexla.common.notify.transport.{ControlMessageProducer, DataMessageProducer, NexlaMessageProducer}
import com.nexla.common.{AppType, NexlaSslContext, RestTemplateBuilder}
import com.nexla.http.IngestionApp.{controlKafkaConfig, controlMessageProducer, ctrlClient}
import com.nexla.common.{AppType, NexlaSslContext, RestTemplateBuilder}
import com.nexla.http.api.{<PERSON><PERSON><PERSON><PERSON><PERSON>, IngestionApi<PERSON>and<PERSON>, IngestionService, SchemaDetectionCache}
import com.nexla.kafka.{AdminControlUpdatesListener, CtrlTopics, KafkaMessageTransport}
import com.nexla.listing.client.FileVaultClient
import com.nexla.sc.util.{AppUtils, Async, StrictNexlaLogging}
import com.nexla.schemasync.SchemaSyncConsumerService
import com.nexla.service.InternalIngestionServiceFactory
import com.nexla.telemetry.jmx.JmxExporter
import com.nexla.transform.schema.FormatDetector
import fr.davit.akka.http.metrics.core.scaladsl.server.HttpMetricsRoute

import java.util.{Optional, UUID}
import scala.compat.java8.OptionConverters._
import scala.concurrent.ExecutionContext
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

object IngestionApp
  extends App
    with AppUtils
    with StrictNexlaLogging {

  implicit private val (props, _, envMap) = loadProps(AppType.INGESTION_SERVICE, new AppProps(_))
  val (telemetry, httpMetricsRegistry) = initTelemetry(props.telemetryConf, AppType.INGESTION_SERVICE.appName, Some(props.dataDog), Some(props.prometheus))
  val jmxExporter: JmxExporter = new JmxExporter(telemetry)

  implicit val system = defaultActorSystem(logger)
  implicit val materializer = Materializer(system)

  implicit val executionContext: ExecutionContext = Async.ioExecutorContext

  implicit val appSslContext: NexlaSslContext = nexlaSslContext(props)

  logger.info(s"""Starting app in ${if (props.internalMode) "internal" else "non-internal"} mode""")

  val controlKafkaConfig = controlKafkaContext(props)
  val controlMessageProducer = if (props.internalMode) {
    new ControlMessageProducer(new KafkaMessageTransport(controlKafkaConfig)) {
      override def publishMonitoringLog(monitoringLogEvent: NexlaMonitoringLogEvent): Unit = ()
    }
  } else {
    new ControlMessageProducer(new KafkaMessageTransport(controlKafkaConfig))
  }

  val dataKafkaConfig = dataKafkaContext(props)
  val dataMessageProducer = new DataMessageProducer(new KafkaMessageTransport(dataKafkaConfig))

  val restTemplate = new RestTemplateBuilder().withSSL(appSslContext).build()

  private val adminApi: AdminApiClient = new AdminApiClientBuilder()
    .setAppName(AppType.INGESTION_SERVICE.appName)
    .setDataPlaneUid(props.dataplaneUid)
    .setEnrichmentUrl(props.credEnrichmentUrl)
    .setNoCache(false)
    .create(props.apiCredentialsServer, props.apiAccessKey, restTemplate)

  val nexlaLicenseManager = new NexlaLicenseManager(restTemplate, props.authHeader, props.nexlaInstallLicenseServiceUrl, props.apiCredentialsServer)

  val ctrlTopics = new CtrlTopics(Some(UUID.randomUUID()), props, controlKafkaConfig, system)

  val staticKafkaProperties = new KafkaClusterPropertiesDefault(controlKafkaConfig.kafkaClusterProperties.get())
  val ctrlClient = new JavaJobSchedulerClient(props.ctrlHttpUrl, props.nexlaCreds.username, props.nexlaCreds.password, staticKafkaProperties, restTemplate)
  val schemaDetectionCache = new SchemaDetectionCache(adminApi, props.dataset.partitions, props.dataset.replication, props.retentionMs, controlMessageProducer, ctrlClient)
  val ingestionService = new IngestionService(adminApi, controlMessageProducer, schemaDetectionCache, ctrlClient, props)
  val internalIngestionServiceFactory = new InternalIngestionServiceFactory(adminApi, controlMessageProducer, schemaDetectionCache, props, dataKafkaConfig, controlKafkaConfig)

  val fileVault = new FileVaultClient(props.fileVaultUrl, props.nexlaCreds.username, props.nexlaCreds.password, new RestTemplateBuilder().withSSL(appSslContext).withBufferRequestBody(false).build())
  val fileIngester = new FileIngester(adminApi, schemaDetectionCache, fileVault, controlMessageProducer, dataMessageProducer, ctrlClient, props)

  FormatDetector.initDefault()

  private def initApp() = {

    val handler = new IngestionApiHandler(adminApi, ingestionService, internalIngestionServiceFactory, controlMessageProducer, fileIngester, nexlaLicenseManager)
    val api = new ApiHandler(props.nexlaCreds, handler, props, httpMetricsRegistry, envMap)
    configureHttpClientSslContext(appSslContext)

    val httpSystem = Http(system)
    val context = appSslContext
      .getServerKeystoreStore
      .asScala
      .map(sslContext => httpsContext(sslContext, appSslContext.getServerTruststoreStore.orElse(null)))
      .getOrElse(httpSystem.defaultServerHttpContext)

    httpMetricsRegistry.map {
      actualReg =>
        val metricsRoute = HttpMetricsRoute(api.metricsRoute).recordMetrics(actualReg)
        val metricsBinding = Http().bindAndHandle(metricsRoute, "0.0.0.0", props.nexlaMetricsPort)
        jmxExporter.enable()

        metricsBinding.andThen {
          case Success(Http.ServerBinding(_)) =>
            logger.info(s"Metrics server started on ${props.podIp}:${props.nexlaMetricsPort}")
          case Failure(e) =>
            logger.error(s"Could not start Metrics server on ${props.nexlaMetricsPort}.", e)
        }
    }

    val binding = httpSystem.bindAndHandle(api.route, "0.0.0.0", props.nexlaAppPort, context)
    appSslContext.getServerKeystoreStore.asScala.foreach(_.clean())
    binding.andThen {
      case Success(Http.ServerBinding(_)) =>

        val metricAggregator = HealthMetricAggregator.getInstance(controlMessageProducer)
        new IngestionCtrlListener(ingestionService, ctrlTopics, adminApi, metricAggregator).startMonitoring()
        new AdminControlUpdatesListener(AppType.INGESTION_SERVICE, ctrlTopics, metricAggregator).startMonitoring()
        if (props.internalMode) new SchemaSyncConsumerService(adminApi, props, controlKafkaConfig, controlMessageProducer, ctrlClient).run()

        logger.info(s"API server started on ${props.podIp}:${props.nexlaAppPort}")

      case Failure(e) =>
        logger.error(s"Could not start API server on ${props.nexlaAppPort}. Exiting...", e)
        System.exit(1)
    }
  }

  initApp()

}
