package com.nexla.http.api;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSource;
import com.nexla.client.JavaJobSchedulerClient;
import com.nexla.common.ConfigUtils;
import com.nexla.common.NexlaKafkaConfig;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.common.runtimes.KafkaClusterPropertiesUnmaterialized;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.kafka.service.TopicMetaService;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static com.nexla.common.parse.ParserConfigs.SchemaDetection.SCHEMA_DETECTION_ONCE;

public class SchemaDetectionCache {

	private final AdminApiClient adminApiClient;
	private final int partitions;
	private final int replication;
	private final long retentionTime;
	private final ControlMessageProducer controlMessageProducer;
	private final ConcurrentHashMap<Integer, SchemaDetectionUtils> schemaDetectionBySource = new ConcurrentHashMap<>();
	private final JavaJobSchedulerClient ctrlClient;

	public SchemaDetectionCache(
		AdminApiClient adminApiClient,
		int partitions,
		int replication,
		long retentionTime, // 259200000 = 3 * 24 * 60 * 60 * 1000,
		ControlMessageProducer controlMessageProducer,
		JavaJobSchedulerClient ctrlClient
	) {
		this.adminApiClient = adminApiClient;
		this.partitions = partitions;
		this.replication = replication;
		this.retentionTime = retentionTime;
		this.controlMessageProducer = controlMessageProducer;
		this.ctrlClient = ctrlClient;
	}

	@SneakyThrows
	public SchemaDetectionUtils getSchemaDetection(int sourceId) {
		return schemaDetectionBySource.computeIfAbsent(sourceId, i ->
		{
			DataSource dataSource = adminApiClient.getDataSource(sourceId).orElseThrow(() -> new IllegalArgumentException("Source not found: " + sourceId));

			KafkaClusterPropertiesUnmaterialized kafkaProperties = ctrlClient.getKafkaPropertiesByResource(dataSource.originNodeId, dataSource.getOrgId());
			NexlaKafkaConfig dataKafkaConfig = new NexlaKafkaConfig(kafkaProperties.materializeWithEnv());
			TopicMetaService topicMetaService = new TopicMetaService(dataKafkaConfig);
			SchemaDetectionUtils schemaDetectionUtils = new SchemaDetectionUtils(sourceId, adminApiClient, controlMessageProducer,
				Optional.ofNullable(topicMetaService), partitions, replication, retentionTime, true);

			boolean isSchemaConfiguredToDetectOnce = Optional.of(dataSource)
				.map(DataSource::getSourceConfig)
				.map(x -> x.get(SCHEMA_DETECTION_ONCE))
				.flatMap(ConfigUtils::optBoolean)
				.orElse(false);

			if (isSchemaConfiguredToDetectOnce) {
				schemaDetectionUtils.setTryCombineSingleSchema();
			}
			return schemaDetectionUtils;
		});
	}

	public void invalidate(int sourceId) {
		schemaDetectionBySource.remove(sourceId);
	}

	@SneakyThrows
	public void deleteDataSet(int dataSetId) {
		EntryStream.of(schemaDetectionBySource)
			.filterValues(v -> v.hasDataSet(dataSetId))
			.keys()
			.forEach(schemaDetectionBySource::remove);
	}
}
