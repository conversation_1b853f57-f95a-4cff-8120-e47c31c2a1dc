package com.nexla.http.api;

import com.nexla.common.NexlaScheduledAccumulator;
import com.nexla.common.ResourceType;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.connect.common.SchemaDetectionResult;
import com.nexla.connector.config.FlowType;
import com.nexla.sc.metric.MetadataBuilder;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import static com.nexla.common.StreamUtils.map;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static java.util.Optional.ofNullable;

public class DelayedKafkaMetricsProducer {

	private static final ScheduledExecutorService SCHEDULED_EXECUTOR_SERVICE = Executors.newScheduledThreadPool(0);

	public final NexlaScheduledAccumulator<Map<AccumulatorAggregateKey, AccumulatorAggregateValue>> accumulator;
	private final ControlMessageProducer controlMessageProducer;

	public DelayedKafkaMetricsProducer(
		ControlMessageProducer controlMessageProducer,
		Long periodMs
	) {
		this.accumulator = new NexlaScheduledAccumulator<>(this::onAccumulate, this::onSchedule, periodMs, SCHEDULED_EXECUTOR_SERVICE);
		this.controlMessageProducer = controlMessageProducer;
	}

	public void sendMetricsDelayed(int sourceId, SchemaDetectionResult dataSetIdAndTopic, long runId, long approxJsonSize, long recordsNum, Integer orgId, Integer ownerId) {
		AccumulatorAggregateKey key = new AccumulatorAggregateKey(ResourceType.SOURCE, sourceId, Optional.of(dataSetIdAndTopic.dataSetId), dataSetIdAndTopic.topic, runId, false, orgId, ownerId);
		AccumulatorAggregateValue value = new AccumulatorAggregateValue(recordsNum, approxJsonSize, Optional.empty());
		accumulator.accumulate(map(key, value));
	}

	public void sendDatasetMetricsDelayed(int datasetId, String fileName, Optional<Boolean> eof, long runId, long approxJsonSize, long recordsNum, Integer orgId, Integer ownerId) {
		AccumulatorAggregateKey key = new AccumulatorAggregateKey(ResourceType.DATASET, datasetId, Optional.empty(), fileName, runId, false, orgId, ownerId);
		AccumulatorAggregateValue value = new AccumulatorAggregateValue(recordsNum, approxJsonSize, eof);
		accumulator.accumulate(map(key, value));
	}

	public void sendErrorDatasetMetricsDelayed(int datasetId, String fileName, Optional<Boolean> eof, long runId, long approxJsonSize, long recordsNum, Integer orgId, Integer ownerId) {
		AccumulatorAggregateKey key = new AccumulatorAggregateKey(ResourceType.DATASET, datasetId, Optional.empty(), fileName, runId, true, orgId, ownerId);
		AccumulatorAggregateValue value = new AccumulatorAggregateValue(recordsNum, approxJsonSize, eof);
		accumulator.accumulate(map(key, value));
	}

	private void onSchedule(Map<AccumulatorAggregateKey, AccumulatorAggregateValue> data) {
		data.forEach(this::publishMetrics);
	}

	private Map<AccumulatorAggregateKey, AccumulatorAggregateValue> onAccumulate(
		Map<AccumulatorAggregateKey, AccumulatorAggregateValue> newMetricMap,
		Map<AccumulatorAggregateKey, AccumulatorAggregateValue> aggMetricMap
	) {
		newMetricMap.forEach((k, v) -> aggMetricMap.merge(k, v, this::aggregateMetric));
		return aggMetricMap;
	}

	private Optional<Boolean> combineOptionals(Optional<Boolean> a, Optional<Boolean> b) {
		if (a.isPresent() && b.isPresent()) {
			return Optional.of(a.get() || b.get());
		}
		return a.or(() -> b);
	}

	private AccumulatorAggregateValue aggregateMetric(
		AccumulatorAggregateValue newMetric,
		AccumulatorAggregateValue oldMetric
	) {
		return new AccumulatorAggregateValue(
				newMetric.getRecordsNumber() + oldMetric.getRecordsNumber(),
				newMetric.getApproxJsonSize() + oldMetric.getApproxJsonSize(),
				combineOptionals(newMetric.getEof(), oldMetric.getEof())
		);
	}

	private void publishMetrics(AccumulatorAggregateKey key, AccumulatorAggregateValue value) {
		long now = nowUTC().getMillis();
		MetadataBuilder metadataBuilder = new MetadataBuilder().withName(key.getMetadataName());
		long errorsCount = key.isError() ? value.getRecordsNumber() : 0L;
		long recordsCount = key.isError() ? 0L : value.getRecordsNumber();
		NexlaRawMetric nexlaRawMetric = NexlaRawMetric.create(
			key.getResourceType(), key.getResourceId(),
			recordsCount, value.getApproxJsonSize(), errorsCount, now, ofNullable(key.getRunId()),
			value.getEof(), Optional.empty(),
			key.getMaybeDatasetId(), Optional.empty(),
			Optional.empty(), Optional.of(metadataBuilder.build()), FlowType.STREAMING, Optional.empty(), Optional.empty(),
				Optional.empty(), key.getOrgId(), key.getOwnerId());

		controlMessageProducer.publishMetrics(nexlaRawMetric);
	}
}
