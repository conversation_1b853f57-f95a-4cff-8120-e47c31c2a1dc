package com.nexla.http.api;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataSet;
import com.nexla.admin.client.DataSource;
import com.nexla.client.JavaJobSchedulerClient;
import com.nexla.common.NexlaKafkaConfig;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaNamingUtils;
import com.nexla.common.ResourceType;
import com.nexla.common.metrics.MetricWithErrors;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogMetricsProducer;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.common.notify.transport.DataMessageProducer;
import com.nexla.common.runtimes.KafkaClusterPropertiesUnmaterialized;
import com.nexla.connect.common.SchemaDetectionResult;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.http.AppProps;
import com.nexla.kafka.KafkaMessageTransport;
import java.util.concurrent.ExecutionException;
import lombok.SneakyThrows;

import org.apache.kafka.clients.producer.ProducerRecord;
import org.javatuples.Pair;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Supplier;

import static com.nexla.admin.client.ResourceStatus.ACTIVE;
import static com.nexla.common.ConfigUtils.optBoolean;
import static com.nexla.common.ConverterUtils.toLong;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.datetime.DateTimeUtils.truncHour;
import static com.nexla.common.parse.ParserConfigs.SchemaDetection.SCHEMA_DETECTION_ONCE;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static java.util.concurrent.TimeUnit.MINUTES;
import static java.util.stream.Collectors.toList;

public class IngestionService {

	private static final Logger logger = LoggerFactory.getLogger(IngestionService.class);

	public static final Random RANDOM = new Random();
	public static final String DEFAULT_SAMPLE_RATE = "0.01";
	public static final String SCHEMA_SAMPLE_RATE_PARAM = "schema.sample.rate";
	public static final String WEBHOOK_BATCH_SIZE = "webhook.batch.size";
	public static final String WEBHOOK_FLUSH_PERIOD = "webhook.flush.period";
	public final long maxRecordSize;

	private final SchemaDetectionCache schemaDetectionCache;
	private final AdminApiClient adminApiClient;
	private final ControlMessageProducer controlMessageProducer;
	private final DelayedKafkaMetricsProducer metricsSender;
  private final LoadingCache<Integer, QueuedProducer<Runnable>> queuedProducers;
  private final LoadingCache<Pair<Integer, Integer>, KafkaClusterPropertiesUnmaterialized> localKafkaFlowRuntimeCache;
  private final Map<String, DataMessageProducer> kafkaDataProducerCache;

	public IngestionService(
		AdminApiClient adminApiClient,
		ControlMessageProducer controlMessageProducer,
		SchemaDetectionCache schemaDetectionCache,
    JavaJobSchedulerClient ctrlClient,
    AppProps appProps
	) {
		this.schemaDetectionCache = schemaDetectionCache;
		this.maxRecordSize = appProps.maxRecordSize();

    this.adminApiClient = adminApiClient;
		this.controlMessageProducer = controlMessageProducer;
		this.metricsSender = new DelayedKafkaMetricsProducer(controlMessageProducer, appProps.metricsWindowMs());
		this.queuedProducers = createCache(adminApiClient, appProps.flushPeriod(), appProps.batchSize());
    this.localKafkaFlowRuntimeCache = CacheBuilder
        .newBuilder()
        .expireAfterWrite(5, MINUTES)
        .build(new CacheLoader<Pair<Integer, Integer>, KafkaClusterPropertiesUnmaterialized>() {
          @Override
          public KafkaClusterPropertiesUnmaterialized load(Pair<Integer, Integer> p) {
            return ctrlClient.getKafkaPropertiesByResource(p.getValue0(), p.getValue1());
          }
        });
    this.kafkaDataProducerCache = Maps.newConcurrentMap();

    Runtime.getRuntime().addShutdownHook(new Thread(() -> {

			System.out.println("Closing queued producer");
			queuedProducers.asMap().values().forEach(QueuedProducer::close);
			System.out.println("Closed queued producer");

			System.out.println("Closing metric accumulator");
			metricsSender.accumulator.close();
			System.out.println("Closed metric accumulator");

			System.out.println("Closing kafka producer");
			kafkaDataProducerCache.values().forEach(DataMessageProducer::close);
			System.out.println("Closed kafka producer");

    }));
	}

	private LoadingCache<Integer, QueuedProducer<Runnable>> createCache(AdminApiClient adminApiClient, int defaultFlushPeriodSeconds, int defaultBatchSize) {
		return CacheBuilder
			.newBuilder()
			.removalListener(notification -> ((QueuedProducer<Runnable>) notification.getValue()).close())
			.expireAfterAccess(15, MINUTES)
			.build(new CacheLoader<>() {
				@Override
				public QueuedProducer<Runnable> load(Integer sourceId) {
					DataSource ds = adminApiClient.getDataSource(sourceId).get();

					Integer flushPeriodSeconds = ofNullable(ds.getSourceConfig().get(WEBHOOK_FLUSH_PERIOD))
						.map(Object::toString)
						.map(Integer::parseInt)
						.orElse(defaultFlushPeriodSeconds);

					Integer batchSize = ofNullable(ds.getSourceConfig().get(WEBHOOK_BATCH_SIZE))
						.map(Object::toString)
						.map(Integer::parseInt)
						.orElse(defaultBatchSize);

					return new QueuedProducer<>(ds, Duration.ofSeconds(flushPeriodSeconds), batchSize);
				}
			});
	}

	public IngestionResponse ingest(
		Map<String, Object> headers,
		Map<String, Object> params,
		List<IngestionMessage> messages,
		Integer sourceId,
		boolean forceSchemaDetection
	) {
		SchemaDetectionResult datasetTopic = getDatasetTopic(messages, sourceId, forceSchemaDetection);
		String topic = datasetTopic.topic;
		long runId = generateRunId();
		List<NexlaMessage> nexlaMessages = getNexlaMessages(headers, params, messages, sourceId, topic, datasetTopic.dataSetId, runId);
		return ingestNexlaMessages(sourceId, datasetTopic, nexlaMessages, runId);
	}

	public IngestionResponse ingestArbitrary(
		Map<String, Object> headers,
		Map<String, Object> params,
		List<LinkedHashMap<String, Object>> messages,
		int sourceId,
		boolean forceSchemaDetection
	) {
		List<NexlaMessage> nexlaMessages = new ArrayList<>();
		SchemaDetectionResult detectionResult = getDatasetTopicWithRawMessages(messages, sourceId, forceSchemaDetection);
		String topic = detectionResult.topic;
		int offset = 0;
		long runId = generateRunId();
		for (Object message : messages) {
			LinkedHashMap rawMessage = validateMapMessage(message);
			NexlaMessage nexlaMessage = IngestionCommons.buildNexlaMessageFromRaw(headers, params, rawMessage, sourceId, topic, offset, detectionResult.dataSetId, runId);
			offset++;
			nexlaMessages.add(nexlaMessage);
		}
		return ingestNexlaMessages(sourceId, detectionResult, nexlaMessages, runId);
	}

	public long generateRunId() {
		DateTime now = nowUTC();
		var newMinute = now.getMinuteOfHour() - now.getMinuteOfHour() % 5;
		return truncHour(now).withMinuteOfHour(newMinute).getMillis();
	}

	private boolean checkSchemaDetectionOnce(DataSource dataSource) {
		boolean isSchemaConfiguredToDetectOnce =
			optBoolean(dataSource.getSourceConfig().get(SCHEMA_DETECTION_ONCE)).orElse(false);

		SchemaDetectionUtils schemaDetection = schemaDetectionCache.getSchemaDetection(dataSource.getId());
		boolean singleSchemaExpected = isSchemaConfiguredToDetectOnce && schemaDetection.getNumSchemas() != 0;

		if (singleSchemaExpected) {
			boolean isSchemaNotUnique = schemaDetection.getNumSchemas() > 1;
			if (isSchemaNotUnique) {
				throw new IllegalStateException("Data source already has multiple datasets attached to it, single schema mode can't be enabled.");
			} else {
				return true;
			}
		} else {
			return false;
		}
	}

	@SneakyThrows
	private synchronized IngestionResponse ingestNexlaMessages(
			int sourceId, SchemaDetectionResult detectionResult, List<NexlaMessage> nexlaMessages, long runId
	) {
		if (detectionResult.updateSamplesCallback != null) {
			detectionResult.updateSamplesCallback.accept(nexlaMessages);
		}

		DataSource dataSource = adminApiClient.getDataSource(sourceId).get();
		Integer orgId = dataSource.getOrgId();
		Integer ownerId = dataSource.getOwnerId();
		QueuedProducer<Runnable> queuedProducer = queuedProducers.get(sourceId);
		DataMessageProducer dataMessageProducer = getDataMessageProducer(dataSource);

		nexlaMessages.forEach(nm -> {
			String json = JsonUtils.toJsonString(nm);
			Runnable supplier = () -> {
				dataMessageProducer.getTransport().publish(detectionResult.topic, json, null);
				metricsSender.sendMetricsDelayed(sourceId, detectionResult, runId, json.length(), 1, orgId, ownerId);
				MetricWithErrors metrics = new MetricWithErrors(1, json.length(), 0);
				NexlaMonitoringLogMetricsProducer.publishMonitoringLogMetrics(
						controlMessageProducer, orgId, runId, sourceId, ResourceType.SOURCE, metrics);
			};
			queuedProducer.add(supplier, detectionResult.dataSetId);
		});

		return new IngestionResponse(detectionResult.dataSetId, nexlaMessages.size());
	}

	private DataMessageProducer getDataMessageProducer(DataSource dataSource) throws ExecutionException {
		KafkaClusterPropertiesUnmaterialized kafkaProperties = localKafkaFlowRuntimeCache.get(Pair.with(dataSource.originNodeId, dataSource.getOrgId()));
		return kafkaDataProducerCache.computeIfAbsent(kafkaProperties.id, k -> {
			NexlaKafkaConfig dataKafkaConfig = new NexlaKafkaConfig(kafkaProperties.materializeWithEnv());
			return new DataMessageProducer(new KafkaMessageTransport(dataKafkaConfig));
		});
	}

	private LinkedHashMap validateMapMessage(Object obj) {
		if (!(obj instanceof LinkedHashMap)) {
			throw new IllegalArgumentException();
		}
		return (LinkedHashMap) obj;
	}

	private SchemaDetectionResult getDatasetTopic(
		List<IngestionMessage> messages,
		int sourceId,
		boolean forceSchemaDetection
	) {
		SchemaDetectionUtils schemaDetection = schemaDetectionCache.getSchemaDetection(sourceId);
		List<LinkedHashMap<String, Object>> msgs = messages
			.stream()
			.map(IngestionMessage::getMessage)
			.collect(toList());
		return getSchemaDetectionResult(schemaDetection, sourceId, msgs, forceSchemaDetection);
	}

	private SchemaDetectionResult getSchemaDetectionResult(
		SchemaDetectionUtils schemaDetection,
		int sourceId,
		List<LinkedHashMap<String, Object>> msgs,
		boolean forceSchemaDetection
	) {
		DataSource dataSource = adminApiClient.getDataSource(sourceId).get();
		if (dataSource.getStatus() != ACTIVE) {
			throw new SourceNotActiveException("Source #" + sourceId + " is " + dataSource.getStatus().name());
		}
		if (checkSchemaDetectionOnce(dataSource)) {
			if (dataSource.getDatasets().size() > 0) {
				Double sampleRate = Double.valueOf(
					dataSource.getSourceConfig().getOrDefault(SCHEMA_SAMPLE_RATE_PARAM, DEFAULT_SAMPLE_RATE).toString()
				);
				boolean tooFewSamples = IngestionCommons.isTooFewSamples(dataSource, schemaDetection);
				if (forceSchemaDetection || RANDOM.nextDouble() < sampleRate || tooFewSamples) {
					return schemaDetection.updateOrCreateDataSet(msgs, empty());
				} else {
					DataSet dataset = dataSource.getDatasets().get(0);
					Consumer<List<NexlaMessage>> listConsumer = nexlaMessages -> {
					};
					return new SchemaDetectionResult(dataset.getId(), NexlaNamingUtils.nameDataSetTopic(dataset.getId()), listConsumer);
				}
			}

		}
		return schemaDetection.updateOrCreateDataSet(msgs, empty());
	}

	private SchemaDetectionResult getDatasetTopicWithRawMessages(
		List<LinkedHashMap<String, Object>> messages,
		int sourceId,
		boolean forceSchemaDetection
	) {
		SchemaDetectionUtils schemaDetection = schemaDetectionCache.getSchemaDetection(sourceId);
		return getSchemaDetectionResult(schemaDetection, sourceId, messages, forceSchemaDetection);
	}

	private List<NexlaMessage> getNexlaMessages(Map<String, Object> headers, Map<String, Object> params, List<IngestionMessage> messages, int sourceId, String topic, int dataSetId, long runId) {
		List<NexlaMessage> nexlaMessages = new ArrayList<>();
		int offset = 0;
		for (IngestionMessage m : messages) {
			nexlaMessages.add(IngestionCommons.buildNexlaMessage(headers, params, m, sourceId, topic, offset, dataSetId, runId));
			offset++;
		}
		return nexlaMessages;
	}


	public void notifyDataSetChanged(Integer datasetId) {
		schemaDetectionCache.deleteDataSet(datasetId);
	}

	public synchronized void notifyDataSourceChanged(Integer dataSourceId) {
		QueuedProducer<Runnable> queuedProducer = queuedProducers.asMap().remove(dataSourceId);
		if (queuedProducer != null) {
			queuedProducer.close();
		}
	}

}