<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.nexla</groupId>
		<artifactId>backend-connectors</artifactId>
		<version>3.3.0-SNAPSHOT</version>
	</parent>
	
	<artifactId>ingestion-service</artifactId>

	<dependencies>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common-sc</artifactId>
			<version>${nexla-backend-common.version}</version>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-stream_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-stream-kafka_${scala.short.version}</artifactId>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>kafka-utils</artifactId>
			<version>${nexla-backend-common.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>log4j-over-slf4j</artifactId>
				</exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common-web</artifactId>
			<version>${nexla-backend-common.version}</version>
		</dependency>

		<!-- Enable JSON logging -->
		<dependency>
			<groupId>net.logstash.logback</groupId>
			<artifactId>logstash-logback-encoder</artifactId>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common</artifactId>
			<version>${nexla-backend-common.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>common-connector</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>file-source-sink</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
		</dependency>

		<dependency>
			<groupId>com.nexla</groupId>
			<artifactId>connector-test</artifactId>
			<version>${project.version}</version>
			<scope>test</scope>
		</dependency>


    <dependency>
      <groupId>io.github.embeddedkafka</groupId>
      <artifactId>embedded-kafka_2.12</artifactId>
      <version>2.8.1</version>
      <scope>test</scope>
    </dependency>
		<dependency>
			<groupId>com.dimafeng</groupId>
			<artifactId>testcontainers-scala-scalatest_${scala.short.version}</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.dimafeng</groupId>
			<artifactId>testcontainers-scala-kafka_${scala.short.version}</artifactId>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
