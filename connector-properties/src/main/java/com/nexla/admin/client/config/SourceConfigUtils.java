package com.nexla.admin.client.config;

import com.google.common.base.MoreObjects;
import com.nexla.admin.client.DataSource;
import com.nexla.common.ConnectionType;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.vault.NexlaAppConfig;
import com.nexla.spec.Configs;
import lombok.SneakyThrows;
import org.apache.kafka.common.config.ConfigDef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.admin.config.ConfigUtils.enrichWithCredentialsStore;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.ResourceType.SOURCE;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.metrics.NexlaRawMetric.RUN_ID;
import static com.nexla.common.parse.ParserConfigs.SchemaDetection.DATASET_PARTITIONS;
import static com.nexla.common.parse.ParserConfigs.SchemaDetection.DATASET_REPLICATION;
import static com.nexla.connector.config.BaseConnectorConfig.VAULT_HOST;
import static com.nexla.connector.config.BaseConnectorConfig.VAULT_TOKEN;
import static com.nexla.connector.config.api_streams.properties.ApiStreamsConfigAccessor.COMMON_CONFIG_JSON;
import static com.nexla.connector.config.api_streams.properties.ApiStreamsConfigAccessor.FEEDS;
import static com.nexla.connector.properties.SqlConfigAccessor.CDC_TABLE_CONFIG;
import static java.util.Optional.ofNullable;

public class SourceConfigUtils {

	private static final Logger logger = LoggerFactory.getLogger(SourceConfigUtils.class);

	private static final String DEFAULT_MONITOR_POLL_MS = String.valueOf(60000 * 5);

	private static final String ZOOKEEPER_CONNECT = "zookeeper.connect";

	@SneakyThrows
	public static ConfigDef sourceConfigDef(ConnectionType ct) {
		return Configs.findSpec(ct).sourceConfig().get().config();
	}

	@SneakyThrows
	public static ConfigDef sinkConfigDef(ConnectionType ct) {
		return Configs.findSpec(ct).sinkConfig().get().config();
	}

	public static Map<String, String> fullDataSourceConfig(NexlaAppConfig nexlaAppConfig, DataSource ds, EnrichedConfig.EnrichSourceParams params, NexlaConfigDef configDef) {
		Map<String, String> resultConfig = createSourceConfig(ds, params);
		return enrichWithCredentialsStore(nexlaAppConfig, configDef, resultConfig);
	}

	public static Map<String, String> createSourceConfig(DataSource ds, EnrichedConfig.EnrichSourceParams props) {

		Map<String, String> config = baseSourceConfig(ds);
		enrichSourceParams(ds, props, config);

		config.put(RUN_ID, Long.toString(nowUTC().getMillis()));

		return config;
	}

	public static Map<String, String> baseSourceConfig(DataSource ds) {
		Map<String, String> config = new HashMap<>();

		config.put(SOURCE_TYPE, ds.getConnectionType().name());

		for (String key : ds.getSourceConfig().keySet()) {
			Object value = ds.getSourceConfig().get(key);
			if (value != null) {
				boolean convertToJson = REST_ITERATIONS_JSON.equals(key) || SOAP_PARAMS.equals(key) || CDC_TABLE_CONFIG.equals(key)
						|| COMMON_CONFIG_JSON.equals(key) || FEEDS.equals(key);
				String valStr = convertToJson ? toJsonString(value) : value.toString();
				config.put(key, valStr);
			}
		}

		Integer dockerInstances =
			ofNullable(ds.getSourceConfig().get(DOCKER_INSTANCES)).map(Object::toString).map(Integer::parseInt).orElse(1);

		config.merge(TASKS_MAX, dockerInstances.toString(), MoreObjects::firstNonNull);
		if (Configs.isConnector(SOURCE, ds.getConnectionType())) {
			config.put(CONNECTOR_CLASS, Configs.findConnectorClass(SOURCE, ds.getConnectionType()));
		}

		if (ds.getConnectionType().isDatabase()) {
			config.put(CREDENTIALS_TYPE, ds.getConnectionType().name());
		}

		if (!ds.getSourceConfig().containsKey(MONITOR_POLL_MS)) {
			config.put(MONITOR_POLL_MS, DEFAULT_MONITOR_POLL_MS);
		}

		Optional.ofNullable(ds.getDataCredentials()).ifPresent(creds -> {
			config.put(CREDS_ID, creds.getId().toString());
		});

		config.put(SOURCE_ID, ds.getId().toString());
		config.put(SOURCE_TYPE, ds.getConnectionType().name());

		//Redis Source Config for Rest Connector with Map
		if (ds.getConnectionType().equals(ConnectionType.REST)) {
			if (config.containsKey(MAP_ID)) {
				config.put(MAP_ID, config.get(MAP_ID));
			}
		}
		return config;
	}

	private static void enrichSourceParams(DataSource ds, EnrichedConfig.EnrichSourceParams props, Map<String, String> config) {
		config.put(DATASET_PARTITIONS,
			ofNullable(ds.getSourceConfig().get(DATASET_PARTITIONS))
				.map(Object::toString)
				.orElse(props.datasetPartitions + "")
		);

		config.put(DATASET_REPLICATION,
			ofNullable(ds.getSourceConfig().get(DATASET_REPLICATION))
				.map(Object::toString)
				.orElse(props.datasetReplication + "")
		);

		config.put(CREDENTIALS_SOURCE, props.credentialsSource);

		props.vaultHost.ifPresent(x -> config.put(VAULT_HOST, x));
		props.vaultToken.ifPresent(x -> config.put(VAULT_TOKEN, x));
		props.secretManagerRegion.ifPresent(x -> config.put(AWS_SECRET_MANAGER_REGION, x));
		props.secretManagerAccessKey.ifPresent(x -> config.put(AWS_SECRET_MANAGER_ACCESS_KEY, x));
		props.secretManagerSecretKey.ifPresent(x -> config.put(AWS_SECRET_MANAGER_SECRET_KEY, x));
		props.secretManagerRoleArn.ifPresent(x -> config.put(AWS_SECRET_MANAGER_ROLE_ARN, x));
		props.secretManagerIdentityTokenFile.ifPresent(x -> config.put(AWS_SECRET_MANAGER_IDENTITY_TOKEN_FILE, x));
		props.secretNames.ifPresent(sn -> config.put(SECRET_NAMES, sn));
	}

	public static String configClassName(DataSource ds) {
		return Configs.findSpec(ds.getConnectionType()).sourceConfig().get().configClass().getName();
	}

}
