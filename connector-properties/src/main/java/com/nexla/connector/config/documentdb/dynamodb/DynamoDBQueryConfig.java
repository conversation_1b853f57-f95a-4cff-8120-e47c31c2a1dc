package com.nexla.connector.config.documentdb.dynamodb;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.documentdb.DocumentDBQueryConfig;
import org.apache.kafka.common.config.ConfigDef;

import java.util.Map;

import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;

public class DynamoDBQueryConfig extends DocumentDBQueryConfig {

    public static final String FILTER_EXPRESSION = "dynamodb.filter.expression";
    public static final String FILTER_ATTRIBUTES_NAMES = "dynamodb.filter.attributes.names";
    public static final String FILTER_ATTRIBUTES_VALUES = "dynamodb.filter.attributes.values";

    public final String filterExpression;
    public final String attributesNames;
    public final String attributesValues;

    public DynamoDBQueryConfig (Map<String, String> queryConfigMap) {
        super(dynamoDBConfigDef(), queryConfigMap);

        this.filterExpression = getString(FILTER_EXPRESSION);
        this.attributesNames = getString(FILTER_ATTRIBUTES_NAMES);
        this.attributesValues = getString(FILTER_ATTRIBUTES_VALUES);
    }

    public static NexlaConfigDef dynamoDBConfigDef() {
        return new NexlaConfigDef()
                .withKey(nexlaKey(FILTER_EXPRESSION, ConfigDef.Type.STRING, null))
                .withKey(nexlaKey(FILTER_ATTRIBUTES_NAMES, ConfigDef.Type.STRING, null))
                .withKey(nexlaKey(FILTER_ATTRIBUTES_VALUES, ConfigDef.Type.STRING, null));
    }
}
