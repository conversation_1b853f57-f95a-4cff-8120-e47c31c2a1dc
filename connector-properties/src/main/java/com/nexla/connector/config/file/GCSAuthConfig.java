package com.nexla.connector.config.file;

import com.nexla.connector.config.NexlaConfigDef;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.file.FileSourceConnectorConfig.normalizePath;
import static java.util.Collections.emptyList;
import static java.util.Optional.empty;
import static org.apache.kafka.common.config.ConfigDef.Type.LIST;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class GCSAuthConfig extends GCPAuthConfig {
	public static final String TEST_PATH = "test.path";
	public static final String SHARED_BUCKETS = "shared.buckets";

	public final Optional<String> testPath;
	public final List<String> sharedBuckets;

	public GCSAuthConfig(Map<String, ?> parsedConfig, Integer credsId) {
		super(authConfigDef(), parsedConfig, credsId);
		this.sharedBuckets = getList(SHARED_BUCKETS);
		if (parsedConfig.containsKey(TEST_PATH)) {
			this.testPath = opt(normalizePath(getString(TEST_PATH)));
		} else if (parsedConfig.containsKey("test.bucket")) {
			this.testPath = opt(normalizePath(opt(getString("test.bucket")).orElse("") + "/" + opt(getString("test.prefix")).orElse("")));
		} else {
			this.testPath = empty();
		}
	}

	public static NexlaConfigDef authConfigDef() {
		return GCPAuthConfig.configDef()
				.withKey(nexlaKey(TEST_PATH, STRING, null)
						.documentation("Test path")
						.displayName("Test path"))
				.withKey(nexlaKey(SHARED_BUCKETS, LIST, emptyList())
						.documentation("Test path")
						.displayName("Test path"));
	}

}
