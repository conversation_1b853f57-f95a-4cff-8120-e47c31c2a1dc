package com.nexla.connector.config.file;

import com.google.common.collect.ImmutableSet;
import com.nexla.common.ConnectionType;
import com.nexla.common.parse.FilePropertiesDetector;
import com.nexla.common.time.NexlaTimeUnit;
import com.nexla.connector.config.*;
import com.nexla.connector.config.deltalake.DeltaLakeAuthConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.rest.RecourceAccessCallback;
import com.nexla.connector.config.rest.RestAuthConfig;
import com.nexla.spec.Configs;
import one.util.streamex.EntryStream;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.config.ConfigException;

import java.net.URL;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.*;
import java.util.regex.Matcher;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.ConfigUtils.optInt;
import static com.nexla.common.ConnectionType.*;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.common.parse.ParserConfigs.Csv.CONFIGURED;
import static com.nexla.common.parse.ParserConfigs.Csv.CSV_DELIMITER;
import static com.nexla.common.parse.ParserConfigs.Csv.CSV_QUOTE_CHAR;
import static com.nexla.common.parse.ParserConfigs.Csv.CSV_SCHEMA;
import static com.nexla.common.parse.ParserConfigs.Csv.CSV_SCHEMA_DETECTION;
import static com.nexla.common.parse.ParserConfigs.Csv.GENERATED;
import static com.nexla.common.parse.ParserConfigs.Csv.HEADER;
import static com.nexla.common.parse.ParserConfigs.Edi.EDI_XPATH;
import static com.nexla.common.parse.ParserConfigs.FORMAT_DETECTION_MAX_LINES;
import static com.nexla.common.parse.ParserConfigs.Json.JSON_MODE;
import static com.nexla.common.parse.ParserConfigs.Json.JSON_PATH;
import static com.nexla.common.parse.ParserConfigs.Unstructured.MODE_ENTIRE_FILE;
import static com.nexla.common.parse.ParserConfigs.Unstructured.MODE_ROW;
import static com.nexla.common.parse.ParserConfigs.Xml.ADDITIONAL_XML_XPATHS;
import static com.nexla.common.parse.ParserConfigs.Xml.XML_MODE;
import static com.nexla.common.parse.ParserConfigs.Xml.XML_XPATH;
import static com.nexla.common.time.VarUtils.VAR_PATTERN;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.file.S3Constants.REGION;
import static com.nexla.connector.properties.FileConfigAccessor.MONITOR_POLL_MS;
import static java.util.Collections.EMPTY_LIST;
import static java.util.Collections.emptyList;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toSet;
import static one.util.streamex.StreamEx.iterate;
import static one.util.streamex.StreamEx.of;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.apache.kafka.common.config.ConfigDef.NO_DEFAULT_VALUE;
import static org.apache.kafka.common.config.ConfigDef.Type;
import static org.apache.kafka.common.config.ConfigDef.Type.BOOLEAN;
import static org.apache.kafka.common.config.ConfigDef.Type.LIST;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;
import static org.apache.kafka.common.config.ConfigDef.ValidString;

public class FileSourceConnectorConfig extends SourceConnectorConfig implements FileConnectorAuth {

	/**
	 * If previously read file was changed, connector will continue reading it, assuming record added in the end.
	 */
	public static final String APPEND_MODE = "append.mode";
	public static final String BATCH_ENABLED = "batch.enabled";
	public static final String DATETIME_PADDING = "datetime.padding";

	public static final String FILE_SOURCE_GROUP = "source";
	public static final String FTP_SOURCE_GROUP = "ftp-source";
	public static final String S3_SOURCE_GROUP = "s3-source";

	public static final String LOOKBACK_TIME = "lookback.time";
	public static final String TIMEZONE = "timezone";

	public static final String BATCH_ROWS = "batch.rows";
	// Grouping Config : The key column to group the other columns by
	public static final String GROUP_BY_KEYS = "group.by.keys";
	// Grouping Config : The column name to be grouped under the name
	public static final String GROUP_FIELD_NAME = "group.field.name";
	public static final String GROUP_PUBLISH_NULL_KEY = "group.publish.null.key";
	public static final String GROUP_IN_MEMORY = "group.in.memory";

	public static final String IGNORE_FILES_OLDER_THAN_MS = "ignore.files.older.than.ms";
	public static final String IGNORE_FILES_OLDER_THAN_GAP_MS = "ignore.files.older.than.gap.ms";

	public static final String DOWNLOAD_LIMIT = "download.limit.kb";
	public static final String ADVANCED_SETTINGS = "advanced_settings";
	public static final String OVERRIDDEN_EXTENSIONS = "overriden.extensions";
	public static final String POST_PROCESSOR = "post.processor";
	public static final String OVERRIDDEN_EXTENSIONS_ANY = "*";
	public static final String MAX_FILE_SIZE_SCHEMA_DETECT = "max.file.size.schema.detect";
	public static final String OVERRIDEN_EXTENSION = "extension";
	public static final String BINARY_EXTENSION = "binary";
	public static final String LISTING_MAX_DURATION_MINUTES = "listing.max.duration";
	public static final String TEST_LISTING_PAUSE_MINUTES = "test.listing.pause.minutes";
	public static final String LISTING_MULTIPATH_ENABLED = "listing.multipath.enabled";

	public static final String LISTING_FILE_TAKE_RETRIES = "listing.file.take.retries";

	public static final String UNSTRUCTURED_API_URL = "unstructured.api.url";
	public static final String UNSTRUCTURED_API_KEY = "unstructured.api.key";

	public static final String AGE_FILTER_DATE_STRATEGY = "age.filter.date.strategy";

	public String path;

	public final Set<String> whiteList;
	public final Set<String> blackList;
	public final Set<String> compressWhiteList;
	public final Set<String> compressBlackList;

	public final ConnectionType sourceType;
	public final Long monitorPollMs;

	public final String advancedSettings;
	public final Map<String, String> overridenExtensions;

	public final Set<String> timeVariables;
	public final boolean dateTimePadding;

	public final Integer lookbackTime;

	public final String timezone;

	public final boolean batchEnabled;
	public final int batchRows;

	public final boolean appendMode;

	public final Integer depth;
	public DirScanningMode dirScanningMode;

	public final String region;

	public final BaseAuthConfig authConfig;

	public final Optional<GroupingProps> groupingProps;

	public final Optional<Long> ignoreOlderThanMs;

	public final Optional<Long> ignoreOlderThanGapMs;

	public final int formatDetectionMaxLines;

	public final Optional<Integer> downloadLimit;

	public final Optional<FileEncryptConfig> fileEncryptConfig;

	public final Integer maxFileSizeSchemaDetect;
	public final Optional<RecourceAccessCallback> resourceAccessCallback;
	public final Integer maxListingDurationMinutes;
	public final int testListingPauseMinutes;
	public final boolean listingMultipathEnabled;
	public final Optional<FileReadRetryConfig> fileReadRetryConfig;

	public final CustomRtMode customRtMode;
	public final CustomRtEmissionMode customRtEmissionMode;
	public final AWSAuthConfig customRtAuthConfig;
	public final String customRtDestination;
	public final String customRtExactDestination;
	public final boolean customRtDryRun;

	public AWSAuthConfig textractAwsAuthConfig;
	public String nexlaTextractS3Destination;

	public final boolean fileBatchingEnabled;
	public final int fileBatchingBatchSize;
	public final int listingFileTakeRetries;

	public final String unstructuredApiUrl;

	public final String ageFilterDateStrategy;
	public final String unstructuredApiKey;

	public FileSourceConnectorConfig(Map<String, ?> originals) {
		super(configDef(), originals);

		this.path = getString(PATH);
		if (originals.containsKey(PATH)) {
			if (!pathIsUrl(path) && StringUtils.isBlank(FilenameUtils.getExtension(path))) {
				this.path = normalizePath(path);
			}
		} else {
			this.path = normalizePath(opt(getString("bucket")).orElse("") + "/" + opt(getString("prefix")).orElse(""));
		}

		this.timeVariables = ofNullable(path).map(FileSourceConnectorConfig::parseVariables).orElseGet(Collections::emptySet);
		checkVariables(timeVariables);

		this.dateTimePadding = getBoolean(DATETIME_PADDING);

		this.whiteList = ImmutableSet.copyOf(getList(WHITELIST_PATHMATCHERS));
		this.blackList = ImmutableSet.copyOf(getList(BLACKLIST_PATHMATCHERS));

		this.compressWhiteList = ImmutableSet.copyOf(getList(COMPRESS_WHITELIST_PATHMATCHERS));
		this.compressBlackList = ImmutableSet.copyOf(getList(COMPRESS_BLACKLIST_PATHMATCHERS));

		if (isNotEmpty(whiteList) && isNotEmpty(blackList)) {
			throw new ConfigException("Only one of whitelist or blacklist should be specified");
		}

		if (isNotEmpty(compressWhiteList) && isNotEmpty(compressBlackList)) {
			throw new ConfigException("Only one of compress whitelist or compress blacklist should be specified");
		}

		this.sourceType = ConnectionType.fromString(getString(SOURCE_TYPE).toUpperCase());

		this.monitorPollMs = getLong(MONITOR_POLL_MS);

		this.timezone = getString(TIMEZONE);

		this.lookbackTime = getInt(LOOKBACK_TIME);

		this.batchRows = getInt(BATCH_ROWS);
		this.batchEnabled = getBoolean(BATCH_ENABLED);

		this.appendMode = getBoolean(APPEND_MODE);

		this.depth = getInt(DEPTH);
		this.dirScanningMode = DirScanningMode.valueOf(getString(DIR_SCANNING_MODE));

		this.region = getString(REGION);

		Map<String, String> authMap = unitTest
			? EntryStream.of(originals).filterValues(Objects::nonNull).mapValues(Object::toString).toMap()
			: getCreds(decryptKey, credsEnc, credsEncIv);

		this.fileEncryptConfig = Optional.of(new FileEncryptConfig(authMap))
			.filter(x -> x.encryptStandard.isPresent());

		this.authConfig = Configs.authConfigCreator(sourceType, authMap, credsId);

		List<String> groupByKeyName = getList(GROUP_BY_KEYS);
		String groupFieldName = getString(GROUP_FIELD_NAME);

		if (isEmpty(groupByKeyName) ^ StringUtils.isEmpty(groupFieldName)) {
			throw new IllegalArgumentException(
				"Invalid config for grouping: both grouping keys and field should be present");
		}
		this.groupingProps = (isNotEmpty(groupByKeyName) && StringUtils.isNotEmpty(groupFieldName)) ?
			Optional.of(new GroupingProps(groupByKeyName, groupFieldName, getBoolean(GROUP_PUBLISH_NULL_KEY), getBoolean(GROUP_IN_MEMORY))) :
			empty();

		this.ignoreOlderThanMs = ofNullable(getLong(IGNORE_FILES_OLDER_THAN_MS));
		this.ignoreOlderThanGapMs = ofNullable(getLong(IGNORE_FILES_OLDER_THAN_GAP_MS));

		this.formatDetectionMaxLines = getInt(FORMAT_DETECTION_MAX_LINES);
		this.downloadLimit = ofNullable(getInt(DOWNLOAD_LIMIT)).map(x -> x * 1024);

		this.advancedSettings = getString(ADVANCED_SETTINGS);

		this.overridenExtensions = of(getList(OVERRIDDEN_EXTENSIONS))
			.mapToEntry(m -> m.split(":")[0].toLowerCase(), m -> m.split(":")[1].toLowerCase())
			.toMap();

		opt(getString(OVERRIDEN_EXTENSION)).map(String::toLowerCase).ifPresent(ext -> {
			if (!overridenExtensions.isEmpty()) {
				overridenExtensions.put(OVERRIDDEN_EXTENSIONS_ANY, ext);
			}
		});
		this.maxFileSizeSchemaDetect = getInt(MAX_FILE_SIZE_SCHEMA_DETECT);
		this.resourceAccessCallback = authConfig.resourceAccessCallbackConfig
			.map(c -> new JpmcFileResourceAccessCallback(
				c.takeResourceUrl.get(),
				c.takeResourceBody,
				c.releaseResourceUrl.get(),
				c.releaseResourceBody
			));
		this.maxListingDurationMinutes = getInt(LISTING_MAX_DURATION_MINUTES);
		this.testListingPauseMinutes = getInt(TEST_LISTING_PAUSE_MINUTES);
		this.listingMultipathEnabled = getBoolean(LISTING_MULTIPATH_ENABLED);
		this.fileReadRetryConfig = parseFileReadRetryConfig();

		if (originals.containsKey(CUSTOM_RT_CREDS_ENC)) {
			/*
			 This config is used to access Nexla's temp S3 storage for Custom Runtime.
			 */
			this.customRtAuthConfig = new AWSAuthConfig(
				unitTest
					? originals
					: getCreds(decryptKey, getString(CUSTOM_RT_CREDS_ENC), getString(CUSTOM_RT_CREDS_ENCIV)),
				credsId
			);
		} else {
			this.customRtAuthConfig = null;
		}

        this.customRtDestination = getString(CUSTOM_RT_UPLOAD_PATH);
		this.customRtExactDestination = getString(CUSTOM_RT_UPLOAD_EXACT_PATH);

		this.customRtMode = CustomRtMode.valueOf(getString(CUSTOM_RT_MODE).toUpperCase());
		this.customRtDryRun = getBoolean(CUSTOM_RT_DRY_RUN);
		this.customRtEmissionMode = CustomRtEmissionMode.valueOf(getString(CUSTOM_RT_MESSAGE_EMISSION_MODE).toUpperCase());

		if (originals.containsKey(TEXTRACT_CREDS_ENC)) {
			/*
			This config is used to access Textract and S3 storage to upload source files for recognition.
			 */
			this.textractAwsAuthConfig = new AWSAuthConfig(
					unitTest
							? originals
							: getCreds(decryptKey, getString(TEXTRACT_CREDS_ENC), getString(TEXTRACT_CREDS_ENCIV)),
					credsId
			);
			this.nexlaTextractS3Destination = getString(TEXTRACT_S3_UPLOAD_PATH);
		}

		this.fileBatchingEnabled = getBoolean(FILE_BATCHING_ENABLED);
		this.fileBatchingBatchSize = getInt(FILE_BATCHING_BATCH_SIZE);
		this.listingFileTakeRetries = getInt(LISTING_FILE_TAKE_RETRIES);

		this.unstructuredApiUrl = getString(UNSTRUCTURED_API_URL);
		this.unstructuredApiKey = getString(UNSTRUCTURED_API_KEY);
		this.ageFilterDateStrategy = getString(AGE_FILTER_DATE_STRATEGY);
	}

	private Optional<FileReadRetryConfig> parseFileReadRetryConfig() {
		try {
			var fileSourceRetryMaxBackoff = optInt(getInt(FILE_SOURCE_RETRY_MAX_BACKOFF_MS_CONFIG_PATH)).map(Duration::ofMillis);
			var fileSourceRetryMinBackoff = optInt(getInt(FILE_SOURCE_RETRY_MIN_BACKOFF_MS_CONFIG_PATH)).map(Duration::ofMillis);
			var fileSourceRetryAttempts = optInt(getInt(FILE_SOURCE_RETRY_ATTEMPTS_CONFIG_PATH));
			if (fileSourceRetryAttempts.isPresent() && fileSourceRetryMinBackoff.isPresent() && fileSourceRetryMaxBackoff.isPresent()) {
				return Optional.of(new FileReadRetryConfig(fileSourceRetryMinBackoff.get(), fileSourceRetryMaxBackoff.get(), fileSourceRetryAttempts.get()));
			} else {
				return Optional.empty();
			}
		} catch (Exception e) {
			return Optional.empty();
		}
	}

	private static boolean pathIsUrl(String path) {
		try {
			new URL(path);
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	public static String normalizePath(String prefix) {
		return opt(prefix).filter("/"::equals)
			.orElseGet(() ->
				ofNullable(prefix)
					.map(p -> Paths.get(p).toString() + "/")
					.orElse("/"));
	}

	private static Set<String> parseVariables(String content) {
		Matcher m = VAR_PATTERN.matcher(content);
		return iterate((String) null, i -> m.group())
			.takeWhileInclusive(s -> m.find())
			.skip(1)
			.map(s -> s.substring(1, s.length() - 1))
			.collect(toSet());
	}

	private static void checkVariables(Set<String> variables) {
		for (String timeVariable : variables) {
			NexlaTimeUnit timeUnit = NexlaTimeUnit.findByPattern(timeVariable);
			if (timeUnit == null) {
				throw new ConfigException("Unrecognizable time unit: " + timeVariable);
			}
		}
	}

	public BaseAuthConfig getAuthConfig() {
		return authConfig;
	}

	@Override
	public String getRegion() {
		return region;
	}

	@Override
	public String getPath() {
		return path;
	}

	@Override
	public ConnectionType getConnectionType() {
		return sourceType;
	}

	@Override
	public Optional<RecourceAccessCallback> recourceAccessCallback() {
		return resourceAccessCallback;
	}

	@Override
	public BaseConnectorConfig getConnectorConfig() {
		return this;
	}

	public static NexlaConfigDef configDef() {
		NexlaConfigDef connectorConfigDef = new NexlaConfigDef(sourceConfigDef())

			.withKey(nexlaKey(PATH, STRING, null)
				.documentation("Path to file or directory")
				.group(FILE_SOURCE_GROUP)
				.displayName("Path to file or directory"))

			// now PATH=BUCKET / PREFIX, leaving them for backward compatibility
			.withKey(nexlaKey("bucket", STRING, null)
				.documentation("Bucket")
				.group(FILE_SOURCE_GROUP)
				.displayName("Bucket"))
			.withKey(nexlaKey("prefix", STRING, null)
				.documentation("Prefix")
				.group(FILE_SOURCE_GROUP)
				.displayName("Bucket"))

			.withKey(nexlaKey(WHITELIST_PATHMATCHERS, LIST, emptyList())
					.documentation("Comma separated list of paths to be scanned")
					.group(FILE_SOURCE_GROUP)
					.displayName("Paths to be scanned"))

			.withKey(nexlaKey(BLACKLIST_PATHMATCHERS, LIST, emptyList())
					.documentation("Comma separated list of paths not to be scanned")
					.group(FILE_SOURCE_GROUP)
					.displayName("Paths NOT to be scanned"))

			.withKey(nexlaKey(COMPRESS_WHITELIST_PATHMATCHERS, LIST, emptyList())
					.documentation("Comma separated list of internal paths of compressed file to be scanned")
					.group(FILE_SOURCE_GROUP)
					.displayName("Paths to be scanned"))

			.withKey(nexlaKey(COMPRESS_BLACKLIST_PATHMATCHERS, LIST, emptyList())
					.documentation("Comma separated list of internal paths of compressed file not to be scanned")
					.group(FILE_SOURCE_GROUP)
					.displayName("Paths NOT to be scanned"))

			.withKey(nexlaKey("extension", STRING, null)
				.documentation("Default extension for unrecognized file types")
				.group(FILE_SOURCE_GROUP)
				.displayName("Default extension"))

			.withKey(nexlaKey(APPEND_MODE, BOOLEAN, false)
				.documentation("Enable append mode for changed files")
				.group(FILE_SOURCE_GROUP)
				.displayName("Enable append mode for changed files"))

				.withKey(nexlaKey(FILE_BATCHING_ENABLED, BOOLEAN, false)
						.documentation("Enable batching mode")
						.group(FILE_SOURCE_GROUP)
						.displayName("Enable batching mode"))

				.withKey(nexlaKey(FILE_BATCHING_BATCH_SIZE, Type.INT, 150)
						.documentation("Batching mode file batch size")
						.group(FILE_SOURCE_GROUP)
						.displayName("Batching mode file batch size"))

				.withKey(nexlaKey(TEXTRACT_S3_UPLOAD_PATH, STRING, null)
						.documentation("Destination S3 Path for Textract Analysis")
						.group(FILE_SOURCE_GROUP)
						.displayName("Destination S3 Path for Textract Analysis"))

			.withKey(nexlaKey(LOOKBACK_TIME, Type.INT, 2)
				.documentation("Look back time (in minimal time units from prefix pattern definition)")
				.group(FILE_SOURCE_GROUP)
				.displayName("Look back time"))

			.withKey(nexlaKey(DATETIME_PADDING, BOOLEAN, true)
				.documentation("Enable padding for date components")
				.group(FILE_SOURCE_GROUP)
				.displayName("Enable padding for date components"))

			.withKey(nexlaKey(REGION, STRING, "us-east-1")
				.validator(ValidString.in(
					"us-east-2",
					"us-east-1",
					"us-west-1",
					"us-west-2",
					"ap-south-1",
					"ap-northeast-2",
					"ap-southeast-1",
					"ap-southeast-2",
					"ap-northeast-1",
					"ca-central-1",
					"eu-central-1",
					"eu-west-1",
					"eu-west-2",
					"sa-east-1"
				))
				.documentation("AWS Region for buckets, defaults to us-east-1")
				.group(S3_SOURCE_GROUP)
				.displayName("Region"))

			.withKey(nexlaKey(PATH_FORMAT, STRING, null)
				.documentation("Path format with partitions")
				.group(FILE_SOURCE_GROUP)
				.displayName("Path format"))

			.withKey(nexlaKey(SCAN_INTERVAL_TYPE, STRING, null)
				.documentation("Scan interval type")
				.group(FILE_SOURCE_GROUP)
				.displayName("Scan interval type"))

			.withKey(nexlaKey(SCAN_INTERVAL_VALUE, STRING, null)
				.documentation("Scan interval value")
				.group(FILE_SOURCE_GROUP)
				.displayName("Scan interval value"))

			.withKey(nexlaKey(TIMEZONE, STRING, "UTC")
				.documentation("Timezone")
				.group(FILE_SOURCE_GROUP)
				.displayName("Timezone for path format"))

			.withKey(nexlaKey(BATCH_ENABLED, BOOLEAN, true)
				.documentation("Enable batching")
				.group(FILE_SOURCE_GROUP)
				.displayName("Enable batching"))

			.withKey(nexlaKey(BATCH_ROWS, Type.INT, 10_000)
				.documentation("Batch size")
				.group(FILE_SOURCE_GROUP)
				.displayName("Batch size"))

			.withKey(nexlaKey(CSV_DELIMITER, STRING, null)
				.importance(LOW)
				.documentation("CSV delimiter")
				.group(FILE_SOURCE_GROUP)
				.displayName("CSV delimiter"))

			.withKey(nexlaKey(CSV_QUOTE_CHAR, STRING, null)
				.importance(LOW)
				.documentation("CSV quote char")
				.group(FILE_SOURCE_GROUP)
				.displayName("CSV quote char"))

			.withKey(nexlaKey(CSV_SCHEMA, LIST, null)
				.importance(LOW)
				.documentation("Comma-delimited list of columns")
				.group(FILE_SOURCE_GROUP)
				.displayName("Comma-delimited list of columns"))

			.withKey(nexlaKey(CSV_SCHEMA_DETECTION, STRING, HEADER)
				.validator(ValidString.in(HEADER, GENERATED, CONFIGURED))
				.importance(LOW)
				.documentation("CSV schema detection mode")
				.group(FILE_SOURCE_GROUP)
				.displayName("CSV schema detection mode"))

			.withKey(nexlaKey(XML_MODE, STRING, MODE_ENTIRE_FILE)
				.validator(ValidString.in(MODE_ROW, MODE_ENTIRE_FILE))
				.importance(LOW)
				.documentation("XML mode")
				.group(FILE_SOURCE_GROUP)
				.displayName("XML mode"))

			.withKey(nexlaKey(XML_XPATH, STRING, null)
				.importance(LOW)
				.documentation("XPath")
				.group(FILE_SOURCE_GROUP)
				.displayName("XPath"))

			.withKey(nexlaKey(ADDITIONAL_XML_XPATHS, LIST, emptyList())
				.importance(LOW)
				.documentation("Additional XPath")
				.group(FILE_SOURCE_GROUP)
				.displayName("Additional XPath"))

			.withKey(nexlaKey(JSON_MODE, STRING, MODE_ENTIRE_FILE)
				.validator(ValidString.in(MODE_ROW, MODE_ENTIRE_FILE))
				.importance(LOW)
				.documentation("Json mode")
				.group(FILE_SOURCE_GROUP)
				.displayName("Json mode"))

			.withKey(nexlaKey(JSON_PATH, STRING, null)
				.importance(LOW)
				.documentation("JsonPath")
				.group(FILE_SOURCE_GROUP)
				.displayName("JsonPath"))

			.withKey(nexlaKey(SOURCE_TYPE, STRING, NO_DEFAULT_VALUE)
				.validator(EnumValidator.in(
					ConnectionType.FTP,
					ConnectionType.S3,
					ConnectionType.MERCURY_S3,
					ConnectionType.NEPTUNE_S3,
					ConnectionType.MIN_IO_S3,
					ConnectionType.BOX,
					ConnectionType.GCS,
					ConnectionType.DROPBOX,
					ConnectionType.WEBDAV,
					ConnectionType.GDRIVE,
					ConnectionType.ONEDRIVE,
					ConnectionType.REST,
					ConnectionType.FILE_UPLOAD,
					ConnectionType.AZURE_BLB,
					ConnectionType.AZURE_DATA_LAKE,
					ConnectionType.DELTA_LAKE_S3,
					ConnectionType.DELTA_LAKE_AZURE_BLB,
					ConnectionType.DELTA_LAKE_AZURE_DATA_LAKE,
					ConnectionType.SHAREPOINT))
				.importance(LOW)
				.documentation("Connection type")
				.group(FILE_SOURCE_GROUP)
				.displayName("Connection type"))

			.withKey(nexlaKey(MONITOR_POLL_MS, Type.LONG, 60000L)
				.importance(LOW)
				.documentation("Monitoring interval")
				.group(CONNECTOR_GROUP)
				.displayName("Monitoring interval, ms"))

			.withKey(nexlaKey(DEPTH, Type.INT, 5)
				.importance(LOW)
				.documentation("Max directory scanning depth")
				.group(CONNECTOR_GROUP)
				.displayName("Max directory scanning depth"))

			.withKey(nexlaKey(DIR_SCANNING_MODE, STRING, DirScanningMode.FILES.name())
				.validator(EnumValidator.in(DirScanningMode.values()))
				.importance(LOW)
				.documentation("Collection mode")
				.group(CONNECTOR_GROUP)
				.displayName("Collect file names or directory names or both"))

			.withKey(nexlaKey(GROUP_BY_KEYS, LIST, EMPTY_LIST)
				.importance(LOW)
				.documentation("Grouping key attribute")
				.group(CONNECTOR_GROUP)
				.displayName("Grouping key attribute"))

			.withKey(nexlaKey(GROUP_FIELD_NAME, STRING, "")
				.importance(LOW)
				.documentation("Grouped field name")
				.group(CONNECTOR_GROUP)
				.displayName("Grouped field name"))

			.withKey(nexlaKey(GROUP_PUBLISH_NULL_KEY, BOOLEAN, false)
				.importance(LOW)
				.documentation("Publish value of null key in grouping")
				.group(CONNECTOR_GROUP)
				.displayName("Publish value of null key in grouping"))

			.withKey(nexlaKey(GROUP_IN_MEMORY, BOOLEAN, true)
				.importance(LOW)
				.documentation("Do grouping of entire file in memory")
				.group(CONNECTOR_GROUP)
				.displayName("Do grouping of entire file in memory"))

			.withKey(nexlaKey(CUSTOM_RT_CREDS_ENC, STRING, null)
				.importance(LOW)
				.maskValue()
				.documentation("S3 Creds enc")
				.group(FILE_SOURCE_GROUP)
				.displayName("S3 Creds enc to access internal S3 store"))

			.withKey(nexlaKey(CUSTOM_RT_CREDS_ENCIV, STRING, null)
				.importance(LOW)
				.maskValue()
				.documentation("S3 Creds enc IV")
				.group(FILE_SOURCE_GROUP)
				.displayName("S3 Creds enc IV to access internal S3 store"))

			.withKey(nexlaKey(CUSTOM_RT_UPLOAD_PATH, STRING, null)
				.importance(LOW)
				.maskValue()
				.documentation("S3 upload path")
				.group(FILE_SOURCE_GROUP)
				.displayName("S3 upload path"))

			.withKey(nexlaKey(CUSTOM_RT_UPLOAD_EXACT_PATH, STRING, null)
				.importance(LOW)
				.maskValue()
				.documentation("Exact upload path")
				.group(FILE_SOURCE_GROUP)
				.displayName("Exact upload path"))

			.withKey(nexlaKey(CUSTOM_RT_MODE, STRING, "off")
				.importance(LOW)
				.documentation("Custom Runtime mode[parsing,replication,off]")
				.group(FILE_SOURCE_GROUP)
				.displayName("Custom Runtime mode[parsing,replication,off]"))

			.withKey(nexlaKey(CUSTOM_RT_DRY_RUN, BOOLEAN, "false")
				.importance(LOW)
				.documentation("Custom Runtime Dry Run")
				.group(FILE_SOURCE_GROUP)
				.displayName("Custom Runtime Dry Run"))

			.withKey(nexlaKey(CUSTOM_RT_MESSAGE_EMISSION_MODE, STRING, CustomRtEmissionMode.FULL_INGESTION.name())
				.importance(LOW)
				.documentation("Custom Runtime replication mode[file,full_ingestion]")
				.group(FILE_SOURCE_GROUP)
				.displayName("Custom rt message mode[file,full_ingestion]"))

			.withKey(nexlaKey(EDI_XPATH, STRING, null)
				.importance(LOW)
				.documentation("EDI XPATH")
				.group(FILE_SOURCE_GROUP)
				.displayName("EDI XPATH"))

			.withKey(nexlaKey(IGNORE_FILES_OLDER_THAN_MS, Type.LONG, null)
				.importance(LOW)
				.documentation("Ignore files older than in ms")
				.group(FILE_SOURCE_GROUP)
				.displayName("Ignore files older than in ms"))

			.withKey(nexlaKey(IGNORE_FILES_OLDER_THAN_GAP_MS, Type.LONG, null)
				.importance(LOW)
				.documentation("Ignore files older than gap (relative filtering) in ms")
				.group(FILE_SOURCE_GROUP)
				.displayName("Ignore files older than gap (relative filtering) in ms"))

			.withKey(nexlaKey(FORMAT_DETECTION_MAX_LINES, Type.INT, FilePropertiesDetector.MAX_LINES)
				.importance(LOW)
				.documentation("Max number of lines for format detection")
				.group(FILE_SOURCE_GROUP)
				.displayName("Max number of lines for format detection"))

			.withKey(nexlaKey(DOWNLOAD_LIMIT, Type.INT, null)
				.importance(LOW)
				.documentation("Download limit (kb)")
				.group(FILE_SOURCE_GROUP)
				.displayName("Download limit (kb)"))

			.withKey(nexlaKey(ADVANCED_SETTINGS, STRING, AdvancedSettings.AUTO_DETECT)
				.importance(LOW)
				.documentation("Advanced settings")
				.group(FILE_SOURCE_GROUP)
				.displayName("Advanced settings"))

			.withKey(nexlaKey(OVERRIDDEN_EXTENSIONS, LIST, emptyList())
				.importance(LOW)
				.documentation("Overridden extensions mapping")
				.group(FILE_SOURCE_GROUP)
				.displayName("Overridden extensions mapping"))

			.withKey(nexlaKey(MAX_FILE_SIZE_SCHEMA_DETECT, Type.INT, 52428800)
				.importance(LOW)
				.documentation("Do not detect schema on Connector for files smaller than this value")
				.group(FILE_SOURCE_GROUP)
				.displayName("Do not detect schema on Connector for files smaller than this value"))

			.withKey(nexlaKey(LISTING_MAX_DURATION_MINUTES, Type.INT, 30)
				.importance(LOW)
				.documentation("Expected max listing duration (minutes)")
				.group(FILE_SOURCE_GROUP)
				.displayName("Expected max listing duration (minutes)"))

			.withKey(nexlaKey(TEST_LISTING_PAUSE_MINUTES, Type.INT, 0)
				.importance(LOW)
				.documentation("Emulates long-lasting listing (minutes)")
				.group(FILE_SOURCE_GROUP)
				.displayName("Emulates long-lasting listing (minutes)"))

			.withKey(nexlaKey(LISTING_MULTIPATH_ENABLED, Type.BOOLEAN, false)
				.importance(LOW)
				.documentation("Allows multi-path in listing using # separator symbol.")
				.group(FILE_SOURCE_GROUP)
				.displayName("Allows multi-path in listing using # separator symbol."))

			.withKey(nexlaKey(FILE_SOURCE_RETRY_ATTEMPTS_CONFIG_PATH, Type.INT, null)
				.importance(LOW)
				.documentation("Number of retries made while reading a file")
				.group(FILE_SOURCE_GROUP)
				.displayName("Number of retries made while reading a file"))

			.withKey(nexlaKey(FILE_SOURCE_RETRY_MIN_BACKOFF_MS_CONFIG_PATH, Type.INT, null)
				.importance(LOW)
				.documentation("Maximum time before the next try to read a file")
				.group(FILE_SOURCE_GROUP)
				.displayName("Maximum time before the next try to read a file"))

			.withKey(nexlaKey(FILE_SOURCE_RETRY_MAX_BACKOFF_MS_CONFIG_PATH, Type.INT, null)
				.importance(LOW)
				.documentation("Minimum time before the next try to read a file")
				.group(FILE_SOURCE_GROUP)
				.displayName("Minimum time before the next try to read a file"))

			.withKey(nexlaKey(TEXTRACT_CREDS_ENC, STRING, null)
				.importance(LOW)
				.maskValue()
				.documentation("Textract Creds enc")
				.group(FILE_SOURCE_GROUP)
				.displayName("Textract Creds enc"))

			.withKey(nexlaKey(TEXTRACT_CREDS_ENCIV, STRING, null)
				.importance(LOW)
				.maskValue()
				.documentation("Textract Creds enc IV")
				.group(FILE_SOURCE_GROUP)
				.displayName("Textract Creds enc IV"))

			.withKey(nexlaKey(LISTING_FILE_TAKE_RETRIES, Type.INT, 3)
				.importance(LOW)
				.documentation("Number of retries to take file")
				.group(FILE_SOURCE_GROUP)
				.displayName("Number of retries to take file"))

			.withKey(nexlaKey(UNSTRUCTURED_API_URL, STRING, null)
				.importance(LOW)
				.documentation("Unstructured.io api url for serverless API")
				.group(FILE_SOURCE_GROUP)
				.displayName("Unstructured api url"))
				
			.withKey(nexlaKey(UNSTRUCTURED_API_KEY, STRING, null)
				.importance(LOW)
				.documentation("Unstructured.io api key for serverless API")
				.group(FILE_SOURCE_GROUP)
				.displayName("Unstructured api key"))

			.withKey(nexlaKey(AGE_FILTER_DATE_STRATEGY, STRING, "latest")
						.importance(LOW)
						.documentation("Optimize file ingestion based on file dates. The strategy will determine which date to use when applying the ignore.files.older.than.ms filter: Strategies:" +
								"latest (default): Use the maximum of the creation and last modified dates when both exist." +
								"created: For files not in the database (working set), use the creation date. For files already in the working set, use the last modified date." +
								"modified: Use the modification date only.")
						.group(FILE_SOURCE_GROUP)
						.displayName("Age Filter Date Strategy"));

		return FileEncryptConfig.configDef(connectorConfigDef);

	}
}