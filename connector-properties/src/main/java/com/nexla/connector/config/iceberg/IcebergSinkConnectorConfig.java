package com.nexla.connector.config.iceberg;

import com.nexla.common.ConnectionType;
import com.nexla.connector.config.BaseConnectorConfig;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.rest.RecourceAccessCallback;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.kafka.common.config.ConfigDef;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

@ToString
@EqualsAndHashCode(callSuper = true)
public class IcebergSinkConnectorConfig extends SinkConnectorConfig implements FileConnectorAuth, IcebergConnectorConfig {

    public static final String GROUP_ICEBERG_SINK = "iceberg-sink";

    public static final String WAREHOUSE_DIR = "iceberg.warehouse.dir";
    public static final String BUCKET_ARN = "iceberg.bucket.arn";
    public static final String CATALOG = "iceberg.catalog";
    public static final String DATABASE_NAME = "iceberg.database.name";
    public static final String TABLE_NAME = "iceberg.table.name";
    public static final String PARTITION_KEYS = "iceberg.partition-keys";
    public static final String ID_FIELDS = "iceberg.id-fields";
    public static final String COMMIT_INTERVAL_MS = "iceberg.cdc.commit.interval-ms";
    public static final String COMMIT_POLL_TIMEOUT_MS = "iceberg.cdc.commit.poll-timeout-ms";
    public static final String ALTER_TABLE_ACCEPT_ANY_SCHEMA = "iceberg.alter.table.accept.any.schema";

    public static final String INSERT_MODE = "iceberg.insert.mode";

    public static final String ALLOW_NO_PK_TABLES = "iceberg.allow-no-pk-tables";
    public static final String TEMP_DATA_HANDLING_TYPE = "temp.data.handling.type";

    public final AWSAuthConfig authConfig;
    public final String warehouseDir;
    public final String bucketArn;
    public final IcebergSparkCatalog catalog;
    public final String databaseName;
    public final String tableName;
    public final String idFields;
    public final Long commitIntervalMs;
    public final Long commitPollTimeoutMs;
    public final Optional<String> partitionKeys;
    public final InsertMode insertMode;
    public final boolean allowNoPkTables;
    public final boolean alterTableAcceptAnySchema;
    public final TempDataHandlingType tempDataHandlingType;

    public IcebergSinkConnectorConfig(Map<String, String> originals) {
        super(configDef(), originals);

        this.warehouseDir = (String) this.values().get(WAREHOUSE_DIR);
        this.bucketArn = (String) this.values().get(BUCKET_ARN);
        this.catalog = IcebergSparkCatalog.getByCatalogName((String) this.values().get(CATALOG));

        String tableName = (String) values().get(TABLE_NAME);
        if (tableName != null && tableName.split("\\.").length == 2) {
            this.databaseName = tableName.split("\\.")[0];
            this.tableName = tableName.split("\\.")[1];
        } else {
            this.databaseName = (String) values().get(DATABASE_NAME);
            this.tableName = (String) values().get(TABLE_NAME);
        }

        this.partitionKeys = Optional.ofNullable((String) this.values().get(PARTITION_KEYS));
        this.idFields = (String) this.values().get(ID_FIELDS);
        this.commitIntervalMs = (Long) this.values().get(COMMIT_INTERVAL_MS);
        this.commitPollTimeoutMs = (Long) this.values().get(COMMIT_POLL_TIMEOUT_MS);
        this.insertMode = InsertMode.valueOf(((String) this.values().get(INSERT_MODE)).toUpperCase());
        this.allowNoPkTables = (Boolean) this.values().get(ALLOW_NO_PK_TABLES);
        this.alterTableAcceptAnySchema = (Boolean) this.values().get(ALTER_TABLE_ACCEPT_ANY_SCHEMA);
        this.tempDataHandlingType = this.values().containsKey(TEMP_DATA_HANDLING_TYPE)
            ? TempDataHandlingType.valueOf(((String) this.values().get(TEMP_DATA_HANDLING_TYPE)).toUpperCase())
            : TempDataHandlingType.LOCAL_FILE_SYSTEM;

        Map<String, String> authMap = !unitTest ? getCreds(decryptKey, credsEnc, credsEncIv) : originals;
        this.authConfig = new AWSAuthConfig(authMap, credsId);
    }

    public static NexlaConfigDef configDef() {
        NexlaConfigDef configDef = new NexlaConfigDef(sinkConfigDef());

        configDef = configDef.withKey(
            nexlaKey(CATALOG, STRING, IcebergSparkCatalog.SPARK_CATALOG.getCatalogName())
                    .validator(ValidStringNoCase.in(Arrays.stream(IcebergSparkCatalog.values())
                            .map(IcebergSparkCatalog::getCatalogName).toArray(String[]::new)))
                    .documentation("Catalog type.")
                    .group(GROUP_ICEBERG_SINK)
                    .displayName("Catalog type")).withKey(
            nexlaKey(DATABASE_NAME, ConfigDef.Type.STRING, IcebergSparkCatalog.SPARK_CATALOG.getDefaultDatabase())
                    .documentation("Name of the database where data will be appended.")
                    .group(GROUP_ICEBERG_SINK)
                    .displayName("Database name")).withKey(
            nexlaKey(TABLE_NAME, ConfigDef.Type.STRING, null)
                .documentation("Name of the table from which data will be read. "
                    + "Nexla uses the path-based Hadoop catalog to discover tables in S3. "
                    + "e.g. A path value of s3://my-nexla-bucket with a database name 'product' and table name 'sales' will "
                    + "find the iceberg table at s3://my-nexla-bucket/product/sales/. "
                    + "It also supports the legacy way to set the database as a prefix of the table name, such as 'product.sales'.")
                .group(GROUP_ICEBERG_SINK)
                .displayName("Table name")).withKey(
            nexlaKey(WAREHOUSE_DIR, ConfigDef.Type.STRING, null)
                .documentation("Path where the data warehouse is located. For S3 that is the bucket name. "
                    + "It is required by spark and glue catalogs.")
                .group(GROUP_ICEBERG_SINK)
                .displayName("Warehouse location")).withKey(
            nexlaKey(BUCKET_ARN, ConfigDef.Type.STRING, null)
                .documentation("S3 Bucket arn where the warehouse is located. "
                    + "It is required by s3 tables catalog and needs to follow the s3 tables bucket naming convention from AWS documentation.")
                .group(GROUP_ICEBERG_SINK)
                .displayName("S3 Bucket arn")).withKey(
            nexlaKey(PARTITION_KEYS, ConfigDef.Type.STRING, null)
                .documentation("Comma separated list of column names to use for partitioning "
                    + "when creating a new table. If the table already exists we will not modify the table spec and "
                    + "this option will be ignored.")
                .group(GROUP_ICEBERG_SINK)
                .displayName("Partition keys")).withKey(
            nexlaKey(ID_FIELDS, ConfigDef.Type.STRING, null)
                .documentation("Comma separated list of id fields to use for upserts and CDC operations")
                .group(GROUP_ICEBERG_SINK)
                .displayName("Identity fields")).withKey(
            nexlaKey(INSERT_MODE, ConfigDef.Type.STRING, "insert")
                .documentation("Whether records should always be appended or if they should be upserted based on id fields.")
                .group(GROUP_ICEBERG_SINK)
                .displayName("Mode")).withKey(
            nexlaKey(COMMIT_INTERVAL_MS, ConfigDef.Type.LONG, 300000L)
                .documentation("How often (in milliseconds) should the CDC commit coordinator attempt a table commit? "
                    + "A higher commit interval leads to more records per commit and longer latency to the destination, "
                    + "but fewer individual commits to manage in the table. Increasing the commit interval may require "
                    + "increasing the commit coordinator poll timeout.")
                .group(GROUP_ICEBERG_SINK)
                .displayName("CDC commit interval (milliseconds)")).withKey(
            nexlaKey(COMMIT_POLL_TIMEOUT_MS, ConfigDef.Type.LONG, 30000L)
                .documentation("Duration to wait (in milliseconds) for the CDC committer to process written data files."
                    + "A higher value might be required when the commit interval is increased, as more time is needed "
                    + "to process all of the data files.")
                .group(GROUP_ICEBERG_SINK)
                .displayName("CDC commit coordinator poll timeout (milliseconds)")).withKey(
            nexlaKey(ALLOW_NO_PK_TABLES, ConfigDef.Type.BOOLEAN, false)
                .documentation("Enables processing of tables without primary key definition for CDC flows.")
                .group(GROUP_ICEBERG_SINK)
                .displayName("Enable no Pk table processing for multi table CDC pipelines")).withKey(
            nexlaKey(ALTER_TABLE_ACCEPT_ANY_SCHEMA, ConfigDef.Type.BOOLEAN, true)
                .documentation("Enables automatic alter table to accept any schema. "
                    + "It will set ('write.spark.accept-any-schema'='true') in Iceberg table properties.")
                .group(GROUP_ICEBERG_SINK)
                .displayName("Enables automatic alter table to accept any schema")).withKey(
            nexlaKey(TEMP_DATA_HANDLING_TYPE, STRING, "LOCAL_FILE_SYSTEM")
                .documentation("Use file system to store temporary data before writing to Iceberg table. "
                    + "It will set ('temp.data.handling.type'='LOCAL_FILE_SYSTEM') in Iceberg table properties.")
                .group(GROUP_ICEBERG_SINK)
                .displayName("Option to handle temporary data before writing to Iceberg table")
        );

        return configDef;
    }

    @Override
    public BaseAuthConfig getAuthConfig() {
        return authConfig;
    }

    @Override
    public String getRegion() {
        return authConfig.region;
    }

    @Override
    public String getPath() {
        return warehouseDir;
    }

    @Override
    public ConnectionType getConnectionType() {
        return ConnectionType.S3_ICEBERG;
    }

    @Override
    public Optional<RecourceAccessCallback> recourceAccessCallback() {
        return Optional.empty();
    }

    @Override
    public BaseConnectorConfig getConnectorConfig() {
        return this;
    }

    @Override
    public String getBucketArn() {
        return bucketArn;
    }

    @Override
    public IcebergSparkCatalog getCatalog() {
        return catalog;
    }

    @Override
    public String getDatabaseName() {
        return databaseName;
    }

    @Override
    public String getTableName() {
        return tableName;
    }

    @Override
    public boolean isCdc() {
        return cdcEnabled;
    }

    public static enum InsertMode {
        INSERT,
        UPSERT
    }

    public enum TempDataHandlingType {
      DELTA_LAKE,
      CACHED_DATASET,
      LOCAL_FILE_SYSTEM;
    }
}
