package com.nexla.connector.config.iceberg;

import com.nexla.common.ConnectionType;
import com.nexla.connector.config.BaseConnectorConfig;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SinkConnectorConfig;
import com.nexla.connector.config.SourceConnectorConfig;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.rest.RecourceAccessCallback;

import java.util.Arrays;

import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.kafka.common.config.ConfigDef;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

@ToString
@EqualsAndHashCode(callSuper = true)
public class IcebergSourceConnectorConfig extends SourceConnectorConfig implements FileConnectorAuth, IcebergConnectorConfig {

    public static final String GROUP_ICEBERG_SOURCE = "iceberg-source";
    public static final String TIMETRAVEL_MODE = "iceberg.timetravel.mode";
    public static final String WAREHOUSE_DIR = "iceberg.warehouse.dir";
    public static final String BUCKET_ARN = "iceberg.bucket.arn";
    public static final String CATALOG = "iceberg.catalog";
    public static final String DATABASE_NAME = "iceberg.database.name";
    public static final String TABLE_NAME = "iceberg.table.name";
    public static final String AS_OF_BRANCH = "iceberg.timetravel.as.of.branch";
    public static final String AS_OF_TAG = "iceberg.timetravel.as.of.tag";
    public static final String AS_OF_SNAPSHOT_ID = "iceberg.timetravel.as.of.snapshot";
    public static final String AS_OF_TIMESTAMP = "iceberg.timetravel.as.of.timestamp";
    public static final String INCREMENTAL_READ_START = "iceberg.incremental.start-snapshot-id";
    public static final String INCREMENTAL_READ_END = "iceberg.incremental.end-snapshot-id";
    public static final String SPARK_SESSION_CONFIGS = "spark.session.configs";
    public static final String DEFAULT_SPARK_CATALOG = "spark_catalog";

    public final IcebergSparkCatalog catalog;
    public final TimeTravelMode timeTravelMode;
    public final String databaseName;
    public final String tableName;
    public final String warehouseDir;
    public final String bucketArn;
    public final Optional<String> timeTravelBranch;
    public final Optional<String> timeTravelTag;
    public final Optional<String> timeTravelSnapshotId;
    public final Optional<String> timeTravelTimestamp;
    public final Optional<String> incrementalReadStartId;
    public final Optional<String> incrementalReadEndId;
    public final Optional<String> sparkSessionConfigs;
    public final AWSAuthConfig authConfig;

    public IcebergSourceConnectorConfig(Map<String, String> originals) {
        super(configDef(), originals);

        this.timeTravelMode = TimeTravelMode.getByValue((String) values().get(TIMETRAVEL_MODE));
        this.warehouseDir = originals.get(WAREHOUSE_DIR);
        this.bucketArn = (String) this.values().get(BUCKET_ARN);
        this.timeTravelBranch = Optional.ofNullable(originals.get(AS_OF_BRANCH));
        this.timeTravelTag = Optional.ofNullable(originals.get(AS_OF_TAG));
        this.timeTravelTimestamp = Optional.ofNullable(originals.get(AS_OF_TIMESTAMP));
        this.timeTravelSnapshotId = Optional.ofNullable(originals.get(AS_OF_SNAPSHOT_ID));
        this.incrementalReadStartId = Optional.ofNullable(originals.get(INCREMENTAL_READ_START));
        this.incrementalReadEndId = Optional.ofNullable(originals.get(INCREMENTAL_READ_END));
        this.sparkSessionConfigs = Optional.ofNullable(originals.get(SPARK_SESSION_CONFIGS));

        this.catalog = IcebergSparkCatalog.getByCatalogName((String) values().get(CATALOG));
        String tableName = (String) values().get(TABLE_NAME);
        if (tableName != null && tableName.split("\\.").length == 2) {
            this.databaseName = tableName.split("\\.")[0];
            this.tableName = tableName.split("\\.")[1];
        } else {
            this.databaseName = (String) values().get(DATABASE_NAME);
            this.tableName = (String) values().get(TABLE_NAME);
        }

        Map<String, String> authMap = !unitTest ? getCreds(decryptKey, credsEnc, credsEncIv) : originals;
        this.authConfig = new AWSAuthConfig(authMap, credsId);
    }

    public static NexlaConfigDef configDef() {
        return sourceConfigDef().withKey(
            nexlaKey(WAREHOUSE_DIR, ConfigDef.Type.STRING, null)
                .documentation("Path where the data warehouse is located. For S3 that is the bucket name. "
                    + "It is required by spark and glue catalogs.")
                .group(GROUP_ICEBERG_SOURCE)
                .displayName("Warehouse location")).withKey(
            nexlaKey(TIMETRAVEL_MODE, ConfigDef.Type.STRING, TimeTravelMode.NONE.getValue())
                .documentation("Mode to query using Apache Iceberg time travel features. "
                    + "Supported values: " + String.join(", ", Arrays.stream(TimeTravelMode.values())
                        .map(TimeTravelMode::getValue).toArray(String[]::new)))
                .group(GROUP_ICEBERG_SOURCE)
                .displayName("Time travel mode")).withKey(
            nexlaKey(BUCKET_ARN, ConfigDef.Type.STRING, null)
                .documentation("S3 Bucket arn where the warehouse is located. "
                    + "It is required by s3 tables catalog and needs to follow the s3 tables bucket naming convention from AWS documentation.")
                .group(GROUP_ICEBERG_SOURCE)
                .displayName("S3 Bucket arn")).withKey(
            nexlaKey(CATALOG, STRING, IcebergSparkCatalog.SPARK_CATALOG.getCatalogName())
                    .validator(SinkConnectorConfig.ValidStringNoCase.in(Arrays.stream(IcebergSparkCatalog.values())
                            .map(IcebergSparkCatalog::getCatalogName).toArray(String[]::new)))
                    .documentation("Catalog type.")
                    .group(GROUP_ICEBERG_SOURCE)
                    .displayName("Catalog type")).withKey(
            nexlaKey(DATABASE_NAME, ConfigDef.Type.STRING, IcebergSparkCatalog.SPARK_CATALOG.getDefaultDatabase())
                    .documentation("Name of the database from which data will be read.")
                    .group(GROUP_ICEBERG_SOURCE)
                    .displayName("Database name")).withKey(
            nexlaKey(TABLE_NAME, ConfigDef.Type.STRING, null)
                .documentation("Name of the table from which data will be read. "
                    + "Nexla uses the path-based Hadoop catalog to discover tables in S3. "
                    + "e.g. A path value of s3://my-nexla-bucket with a database name 'product' and table name 'sales' will "
                    + "find the iceberg table at s3://my-nexla-bucket/product/sales/. "
                    + "It also supports the legacy way to set the database as a prefix of the table name, such as 'product.sales'.")
                .notNullable()
                .group(GROUP_ICEBERG_SOURCE)
                .displayName("Table name")).withKey(
            nexlaKey(AS_OF_BRANCH, ConfigDef.Type.STRING, null)
                .documentation("Branch to query using Apache Iceberg time travel features. "
                    + "Not compatible with time travel by timestamp. ")
                .group("iceberg-timetravel")
                .displayName("Query table as-of branch")).withKey(
            nexlaKey(AS_OF_TAG, ConfigDef.Type.STRING, null)
                .documentation("Tag to query using Apache Iceberg time travel features. "
                    + "Not compatible with time travel by timestamp. ")
                .group("iceberg-timetravel")
                .displayName("Query table as-of tag")).withKey(
            nexlaKey(AS_OF_SNAPSHOT_ID, ConfigDef.Type.STRING, null)
                .documentation(
                    "Snapshot ID to query using Apache Iceberg time travel features")
                .group("iceberg-timetravel")
                .displayName("Query table as-of snapshot id")).withKey(
            nexlaKey(AS_OF_TIMESTAMP, ConfigDef.Type.STRING, null)
                .documentation("Timestamp to query using Apache Iceberg time travel features. "
                    + "Supported formats: `YYYY-MM-DD HH:MM:SS` or Unix timestamp in seconds")
                .group("iceberg-timetravel")
                .displayName("Query table as-of timestamp")).withKey(
            nexlaKey(INCREMENTAL_READ_START, ConfigDef.Type.STRING, null)
                .documentation("Read appended data between two snapshots with the incremental read feature. "
                    + "This is the starting snapshot ID and is required for this feature. Incremental reads "
                    + "are not compatible with other time travel features nor with upserts and deletes.")
                .group("iceberg-incremental")
                .displayName("Incremental read starting snapshot id")).withKey(
            nexlaKey(INCREMENTAL_READ_END, ConfigDef.Type.STRING, null)
                .documentation("Read appended data between two snapshots with the incremental read feature. "
                    + "This is the ending snapshot ID and is optional, if the starting snapshot ID is "
                    + "specified but the ending snapshot ID is not provided will read all subsequent data. "
                    + "Incremental reads are not compatible with other time travel features nor with "
                    + "upserts and deletes.")
                .group("iceberg-incremental")
                .displayName("Incremental read ending snapshot id"));
    }

    @Override
    public BaseAuthConfig getAuthConfig() {
        return authConfig;
    }

    @Override
    public String getRegion() {
        return authConfig.region;
    }

    @Override
    public String getPath() {
        return warehouseDir;
    }

    @Override
    public ConnectionType getConnectionType() {
        return ConnectionType.S3_ICEBERG;
    }

    @Override
    public Optional<RecourceAccessCallback> recourceAccessCallback() {
        return Optional.empty();
    }

    @Override
    public BaseConnectorConfig getConnectorConfig() {
        return this;
    }

    @Override
    public String getBucketArn() {
        return bucketArn;
    }

    @Override
    public IcebergSparkCatalog getCatalog() {
        return catalog;
    }

    @Override
    public String getDatabaseName() {
        return databaseName;
    }

    @Override
    public String getTableName() {
        return tableName;
    }

    @Override
    public boolean isCdc() {
        return false;
    }
}
