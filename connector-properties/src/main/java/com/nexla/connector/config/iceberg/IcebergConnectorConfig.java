package com.nexla.connector.config.iceberg;

public interface IcebergConnectorConfig  {
    String getBucketArn();
    IcebergSparkCatalog getCatalog();
    String getDatabaseName();
    String getTableName();
    boolean isCdc();

    default String getFullTableName(){
        return getFullTableName(getTableName());
    }

    default String getFullTableName(String tableName){
        if (isCdc()) {
            return tableName;
        } else if (IcebergSparkCatalog.SPARK_CATALOG.getCatalogName().equals(getCatalog().getCatalogName())) {
            return String.format("`%s`.`%s`", getDatabaseName(), tableName);
        }
        return String.format("%s.`%s`.`%s`", getCatalog().getCatalogName(), getDatabaseName(), tableName);
    }
}
