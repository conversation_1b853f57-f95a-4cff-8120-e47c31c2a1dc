# File-based Source Connectors

Confidence Score: Reviewed

## Overview

File-based source connectors allow you to import data from various file storage systems into Nexla. These connectors support reading files from:

- FTP/SFTP/FTPS servers
- WebDAV servers
- Amazon S3
- Azure Blob Storage
- Google Cloud Storage
- Box
- Delta Lake (on S3 or Azure)
- Local file uploads

Common features across all file connectors include:

- Pattern-based file matching (supports [Apache Ant Path Patterns](https://ant.apache.org/manual/dirtasks.html))
- Incremental file processing
- Compression support
- Directory monitoring
- Batch processing
- File encryption/decryption (PGP, KMS, SSE, etc.)
- Advanced file parsing (auto-detect, custom, XML, JSON, PDF, etc.)

> **Tip:** Use pattern-based matching and incremental processing for large or frequently updated directories.

## Common Configuration Parameters

### Basic Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `path` | String | Yes | - | Root folder or path pattern to scan for files. Supports wildcards and date macros (e.g., `/data/${yyyy}/${MM}/*.csv`). |
| `whitelist.pathmatchers` | List | No | [] | List of file patterns to include (e.g., `**/Done/`). Only files matching these patterns are scanned. |
| `blacklist.pathmatchers` | List | No | [] | List of file patterns to exclude (e.g., `**/archive/`). Files matching these patterns are ignored. |
| `depth` | Integer | No | - | Directory recursion depth. Set to limit how deep to scan subfolders. |
| `dir.scanning.mode` | String | No | - | Directory scanning mode. Options: `recursive`, `flat`. |
| `timezone` | String | No | UTC | Timezone for path macros and file timestamp operations. |
| `monitor.poll.ms` | Long | No | - | Polling interval (ms) for file changes. |

### File Processing

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `append.mode` | Boolean | No | false | Continue reading changed files from end (for log/append-only files). |
| `batch.enabled` | Boolean | No | false | Enable batch processing of files. |
| `batch.rows` | Integer | No | - | Number of rows per batch. |
| `datetime.padding` | Boolean | No | false | Pad datetime values in file names. |
| `lookback.time` | Integer | No | - | Look back window (days) for file discovery. |

### File Filtering

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `ignore.files.older.than.ms` | Long | No | - | Ignore files older than this age (ms). |
| `ignore.files.older.than.gap.ms` | Long | No | - | Gap between file timestamps to ignore. |
| `download.limit.kb` | Integer | No | - | Max file size to download (KB). |
| `max.file.size.schema.detect` | Integer | No | - | Max file size for schema detection. |

### Advanced Features

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `group.by.keys` | List | No | [] | Keys to group records by (e.g., `order_number`). |
| `group.field.name` | String | No | - | Field name for grouped records (e.g., `order_details`). |
| `group.publish.null.key` | Boolean | No | false | Publish records with null keys in grouping. |
| `group.in.memory` | Boolean | No | false | Perform grouping in memory. |
| `listing.max.duration` | Integer | No | - | Max duration (ms) for file listing. |
| `listing.multipath.enabled` | Boolean | No | false | Enable multi-path listing. |
| `listing.file.take.retries` | Integer | No | - | Number of retries for file operations. |

## Storage-specific Configuration

### FTP/SFTP/FTPS

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `ftp.type` | String | Yes | ftp | FTP connection type. Options: `ftp`, `ftps`, `sftp`. |
| `host` | String | Yes | - | FTP server hostname (exclude protocol). |
| `port` | Integer | Yes | 21/22 | FTP/SFTP port. 21 for FTP/FTPS, 22 for SFTP. |
| `username` | String | Yes | - | FTP username. |
| `password` | String | Yes | - | FTP password. |
| `private.key` | String | No | - | SFTP private key (alternative to password). |
| `passphrase` | String | No | - | Passphrase for private key. |
| `ftp.mode` | String | No | passive.local | FTP mode. Options: `active`, `passive.local`, `passive.remote`. |
| `anonymous` | Boolean | No | false | Enable anonymous access (FTP/FTPS only). |

> **Best Practice:** Use SFTP with key-based authentication for production workloads.

### Amazon S3

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `s3_auth_type` | String | Yes | Access Key | Authentication method. Options: `Access Key`, `ARN`, `Instance Role`. |
| `access_key_id` | String | Yes* | - | AWS access key ID. Required if using Access Key auth. |
| `secret_key` | String | Yes* | - | AWS secret access key. Required if using Access Key auth. |
| `region` | String | No | us-east-1 | AWS region. |
| `arn` | String | No | - | IAM ARN for federated access. |
| `external_id` | String | Yes* | - | External ID for federated access. |
| `iam.role` | String | No | - | IAM role ARN (alternative to access keys). |
| `bucket` | String | Yes | - | S3 bucket name. |
| `prefix` | String | No | - | S3 key prefix. |
| `has_client_encryption` | Boolean | No | false | Enable client-side encryption (KMS). |
| `encryption_mode` | String | No | EncryptionOnly | KMS encryption mode. Options: `EncryptionOnly`, `AuthenticatedEncryption`, `StrictAuthenticatedEncryption`. |
| `kms_key` | String | No | - | KMS key for encryption/decryption. |
| `sse.enabled` | Boolean | No | false | Enable server-side encryption (SSE-S3/SSE-KMS). |
| `sse.kms_key.arn` | String | No | - | KMS Key ARN for SSE. |
| `file_encryption_enabled` | Boolean | No | false | Enable file-level encryption/decryption (PGP). |
| `encrypt.standard` | String | No | pgp | File encryption protocol. |
| `external.user.id` | String | No | - | User ID for encryption/decryption. |
| `external.public.key` | String | No | - | Public key for encryption/decryption. |
| `encrypt.user.id` | String | No | - | Your user ID for private key. |
| `encrypt.private.password` | String | No | - | Password for private key. |
| `encrypt.private.key` | String | No | - | Your private key. |

> **Tip:** Use IAM roles or federated access for production. Enable encryption for sensitive data.

### Azure Blob Storage

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `auth.type` | String | Yes | SAS Token | Authentication type. Options: `SAS Token`, `Connection String`, `Storage Account Key`. |
| `storage.account.name` | String | Yes | - | Azure storage account name. |
| `sas.token` | String | Yes* | - | Shared Access Signature token. |
| `key.connection.string` | String | No | - | Azure connection string. |
| `storage.account.key` | String | No | - | Storage account key. |
| `container` | String | Yes | - | Container name. |
| `prefix` | String | No | - | Blob prefix. |

### Google Cloud Storage

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `is_service_account` | String | Yes | true | Authentication type. Options: `true` (service account), `false` (end user). |
| `project_id` | String | Yes | - | GCP project ID. |
| `json_creds` | File | Yes* | - | Service account credentials JSON. |
| `bucket` | String | Yes | - | GCS bucket name. |
| `prefix` | String | No | - | Object prefix. |
| `file_encryption_enabled` | Boolean | No | false | Enable file-level encryption/decryption (PGP). |
| `encrypt.standard` | String | No | pgp | File encryption protocol. |
| `external.user.id` | String | No | - | User ID for encryption/decryption. |
| `external.public.key` | String | No | - | Public key for encryption/decryption. |
| `encrypt.user.id` | String | No | - | Your user ID for private key. |
| `encrypt.private.password` | String | No | - | Password for private key. |
| `encrypt.private.key` | String | No | - | Your private key. |

### Box

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `client.id` | String | Yes | - | Box client ID. |
| `client.secret` | String | Yes | - | Box client secret. |
| `access.token` | String | Yes | - | Box access token. |
| `refresh.token` | String | Yes | - | Box refresh token. |

## Advanced File Parsing

### File Processor (advanced_settings)

Select a pre-built file processor to match your file format:

- **Automatic**: Auto-detects and parses files (AVRO, CSV, EDI, Excel, JSON, ORC, Parquet, TSV, XML, etc.)
- **Custom Text Parser**: Configure delimiter, qualifier, schema detection, etc.
- **Override with Custom File Processor**: Use custom file processors for parsing.
- **XML/JSON/EDI/Log/ORC/AVRO/Parquet/Excel/Fixed Width/PDF/Unstructured/SWIFT**: Specialized parsers for each format, with customizable options.

#### Example: Custom Text Parser

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `csv.delimiter` | String | , | Delimiter for splitting rows. Options: `,`, `\t`, `\n`, `;`, `|`,`^`. |
| `csv.quote.char` | String | " | Text qualifier character. |
| `csv.escape.char` | String | \\ | Escape character for delimiters/qualifiers. |
| `csv.schema.detection` | String | header | Schema detection mode: `header` (use header row), `generated` (auto-generate names). |
| `csv.skip.first.lines` | Int | 0 | Number of lines to skip at file head. |

#### Example: Excel Parser

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `sheets` | String | - | Cell ranges to ingest (e.g., `sheet1!A1:B5`). |
| `excel.schema.detection` | String | header | Schema detection mode. |
| `excel.skip.merged.cells` | Boolean | false | Skip merged cells. |

#### Example: JSON Parser

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `json.mode` | String | row | Ingestion mode: `row` (JSON lines), `entire.file` (whole file as object). |
| `json.path` | String | - | JSON path to data (when `entire.file`). |

#### Example: PDF Parser

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `pdf.parsing.strategy` | String | text | Parsing mode: `text`, `semi-auto`, `auto-textract`, `auto-tesseract`, `image`. |
| `pdf.rendering.strategy` | String | default | Page metadata inclusion mode. |
| `pdf.document.password` | String | - | Password for protected PDFs. |

> **Tip:** For complex or unstructured files, use the Unstructured.io parser or contact Nexla support for advanced parsing guidance.

## Examples

### Basic FTP File Import

```json
{
  "path": "/data/incoming/*.csv",
  "ftp.type": "ftp",
  "username": "ftpuser",
  "password": "ftppass",
  "host": "ftp.example.com",
  "whitelist.pathmatchers": ["*.csv"],
  "batch.enabled": true,
  "batch.rows": 1000
}
```

### S3 with Pattern Matching

```json
{
  "path": "mybucket/data/${yyyy}/${MM}/${dd}/*.json",
  "s3_auth_type": "Access Key",
  "access_key_id": "AKIAXXXXXXXXXXXXXXXX",
  "secret_key": "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "region": "us-west-2",
  "datetime.padding": true,
  "timezone": "UTC",
  "lookback.time": 7
}
```

### Azure Blob with Compression

```json
{
  "path": "mycontainer/archive/*.gz",
  "auth.type": "SAS Token",
  "storage.account.name": "mystorageaccount",
  "sas.token": "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "compress.whitelist.pathmatchers": ["*.gz"],
  "ignore.files.older.than.ms": ********
}
```

### GCS with Advanced Features

```json
{
  "path": "mybucket/logs/",
  "is_service_account": "true",
  "project_id": "my-project",
  "json_creds": "{...}",
  "depth": 3,
  "group.by.keys": ["user_id", "session_id"],
  "group.field.name": "events",
  "group.in.memory": true
}
```

### Box File Import

```json
{
  "path": "/reports/*.xlsx",
  "client.id": "my-client-id",
  "client.secret": "my-client-secret",
  "access.token": "my-access-token",
  "refresh.token": "my-refresh-token"
}
```

---

> **Best Practices:**
>
> - Use incremental or pattern-based modes for large datasets.
> - Always use the least privileged credentials required.
> - Validate your file patterns and test with a small dataset before scaling up.
> - Enable encryption for sensitive data.
> - For advanced parsing, leverage built-in processors or contact support.
