# API Source Connectors

Confidence Score: Unreviewed

## Overview

API source connectors allow you to import data from various web services and APIs. These connectors support:

- REST APIs
- SOAP Web Services
- GraphQL APIs
- Pagination
- Authentication
- Rate limiting
- Parallel requests
- Response transformation

## REST Source Connector

### Basic Configuration

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `rest.iterations.json` | String | Yes | - | JSON configuration for API calls. See examples below for structure. | [RestSourceConnectorConfig.java#L34](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestSourceConnectorConfig.java#L34) |
| `run.once` | Boolean | No | false | Run connector once and stop. | [RestSourceConnectorConfig.java#L33](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestSourceConnectorConfig.java#L33) |
| `include.headers` | Boolean | No | false | Include response headers in output. | [RestSourceConnectorConfig.java#L37](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestSourceConnectorConfig.java#L37) |

### REST Iteration Configuration

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `url` | String | Yes | - | API endpoint URL. | [RestIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestIterationConfig.java) |
| `method` | String | No | GET | HTTP method (GET, POST, etc.). | [RestIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestIterationConfig.java) |
| `headers` | Map | No | {} | HTTP headers. Add any additional request headers as comma-separated values, e.g. `header1:value1,header2:value2`. | [RestIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestIterationConfig.java) |
| `query_params` | Map | No | {} | Query parameters. | [RestIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestIterationConfig.java) |
| `body` | String | No | - | Request body. | [RestIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestIterationConfig.java) |
| `body_type` | String | No | json | Body content type. | [RestIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestIterationConfig.java) |
| `response_type` | String | No | json | Response content type. | [RestIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestIterationConfig.java) |

### Pagination Configuration

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `pagination.type` | String | No | - | Pagination type (`offset`, `cursor`, `link`). | [RestPaginationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestPaginationConfig.java) |
| `pagination.limit` | Integer | No | - | Items per page. | [RestPaginationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestPaginationConfig.java) |
| `pagination.max_pages` | Integer | No | - | Maximum pages to fetch. | [RestPaginationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestPaginationConfig.java) |
| `pagination.param` | String | No | - | Pagination parameter name. | [RestPaginationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestPaginationConfig.java) |
| `pagination.cursor_path` | String | No | - | JSON path to cursor value. | [RestPaginationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestPaginationConfig.java) |
| `pagination.link_rel` | String | No | next | Link header relation for next page. | [RestPaginationConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestPaginationConfig.java) |

### Rate Limiting

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `rate_limit.requests` | Integer | No | - | Maximum requests per interval. | [RestRateLimitConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestRateLimitConfig.java) |
| `rate_limit.interval_ms` | Integer | No | - | Rate limit interval in milliseconds. | [RestRateLimitConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestRateLimitConfig.java) |
| `rate_limit.retry_after` | Boolean | No | true | Honor Retry-After header. | [RestRateLimitConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestRateLimitConfig.java) |
| `request.parallelism.count` | Integer | No | 1 | Number of parallel requests. | [RestSourceConnectorConfig.java#L36](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestSourceConnectorConfig.java#L36) |

### Authentication

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `auth.type` | String | No | - | Authentication type. Supported values: `NONE` (no auth), `BASIC` (username/password), `API_KEY` (key-value pair), `TOKEN` (short-lived token), `OAUTH1`, `OAUTH2`, `AWS_SIGNATURE`, `gcp_service_account`. See below for details. | [RestAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestAuthConfig.java) |
| `auth.username` | String | No | - | Basic auth username. Required if `auth.type` is `BASIC` or `TOKEN`. | [RestAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestAuthConfig.java) |
| `auth.password` | String | No | - | Basic auth password. Required if `auth.type` is `BASIC` or `TOKEN`. | [RestAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestAuthConfig.java) |
| `auth.token` | String | No | - | Bearer token. Required if `auth.type` is `TOKEN` or `OAUTH2`. | [RestAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestAuthConfig.java) |
| `auth.key` | String | No | - | API key. Required if `auth.type` is `API_KEY`. | [RestAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestAuthConfig.java) |
| `auth.key_location` | String | No | header | API key location (`header` or `query`). | [RestAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/rest/RestAuthConfig.java) |

> **Tips:**
>
> - Use `NONE` for public APIs or if authentication is handled via custom headers or query parameters.
> - For OAuth2, ensure you have the correct client ID, client secret, and token URL. Use `TOKEN` if you need to fetch a short-lived token before making API calls.
> - For AWS/GCP, use the appropriate signature or service account authentication.
> - See the [REST API Auth Reference](#) for more details and advanced scenarios.

---

## SOAP Source Connector

### Basic Configuration

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `rest.iterations.json` | String | Yes | - | JSON configuration for SOAP calls | [SoapSourceConnectorConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapSourceConnectorConfig.java) |
| `run.once` | Boolean | No | false | Run connector once and stop | [SoapSourceConnectorConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapSourceConnectorConfig.java) |

### SOAP Iteration Configuration

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `wsdl_url` | String | Yes | - | WSDL URL | [SoapIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapIterationConfig.java) |
| `service_name` | String | Yes | - | SOAP service name | [SoapIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapIterationConfig.java) |
| `port_name` | String | Yes | - | SOAP port name | [SoapIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapIterationConfig.java) |
| `operation_name` | String | Yes | - | SOAP operation to call | [SoapIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapIterationConfig.java) |
| `request_template` | String | Yes | - | SOAP request template | [SoapIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapIterationConfig.java) |
| `request_params` | Map | No | {} | Parameters for request template | [SoapIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapIterationConfig.java) |
| `response_type` | String | No | xml | Response content type | [SoapIterationConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapIterationConfig.java) |

### Authentication

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `auth.type` | String | No | - | Authentication type | [SoapAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapAuthConfig.java) |
| `auth.username` | String | No | - | WS-Security username | [SoapAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapAuthConfig.java) |
| `auth.password` | String | No | - | WS-Security password | [SoapAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapAuthConfig.java) |
| `auth.token` | String | No | - | WS-Security token | [SoapAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/soap/SoapAuthConfig.java) |

## Examples

### Basic REST API Call

```json
{
  "rest.iterations.json": {
    "iterations": [{
      "url": "https://api.example.com/v1/users",
      "method": "GET",
      "headers": {
        "Accept": "application/json"
      },
      "query_params": {
        "limit": "100"
      }
    }]
  }
}
```

### REST API with Pagination

```json
{
  "rest.iterations.json": {
    "iterations": [{
      "url": "https://api.example.com/v1/orders",
      "pagination": {
        "type": "offset",
        "limit": 50,
        "max_pages": 100,
        "param": "page"
      },
      "rate_limit": {
        "requests": 10,
        "interval_ms": 1000
      }
    }]
  }
}
```

### REST API with OAuth2

```json
{
  "rest.iterations.json": {
    "iterations": [{
      "url": "https://api.example.com/v1/data",
      "auth": {
        "type": "OAUTH2",
        "client_id": "YOUR_CLIENT_ID",
        "client_secret": "YOUR_CLIENT_SECRET",
        "access_token_url": "https://auth.example.com/oauth2/token"
      },
      "request.parallelism.count": 3
    }]
  }
}
```

### REST API with JWT and Custom Headers

```json
{
  "rest.iterations.json": {
    "iterations": [{
      "url": "https://api.example.com/v1/secure",
      "headers": {
        "Authorization": "Bearer YOUR_JWT_TOKEN",
        "X-Custom-Header": "value"
      },
      "method": "POST",
      "body": "{\"data\":123}",
      "body_type": "json"
    }]
  }
}
```

### SOAP Web Service

```json
{
  "rest.iterations.json": {
    "iterations": [{
      "wsdl_url": "http://example.com/service?wsdl",
      "service_name": "WeatherService",
      "port_name": "WeatherPort",
      "operation_name": "GetWeather",
      "request_template": "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soapenv:Body><GetWeather><city>${city}</city></GetWeather></soapenv:Body></soapenv:Envelope>",
      "request_params": {
        "city": "London"
      }
    }]
  }
}
```

---

> **Best Practices:**
>
> - Always use the least privileged credentials required for your API.
> - For pagination, prefer cursor-based methods for large datasets.
> - Use rate limiting to avoid API throttling and ensure reliability.
> - When using OAuth2 or JWT, securely store and rotate secrets.
> - Test your configuration with a small data set before scaling up.
