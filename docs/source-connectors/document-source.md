# Document Store Source Connectors

Confidence Score: Unreviewed

## Overview

Document store source connectors allow you to import data from various document-oriented and NoSQL databases. These connectors support:

- MongoDB
- DynamoDB
- Firebase
- Flexible schema handling
- Query-based data selection
- Change streams
- Batch processing

## Common Configuration Parameters

### Basic Configuration

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `collection` | String | Yes | - | Collection/table name. For MongoDB, this is the collection; for DynamoDB, the table; for Firebase, the path. | [DocumentDbSourceConnectorConfig.java#L30](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DocumentDbSourceConnectorConfig.java#L30) |
| `mode` | String | No | default | Reading mode. Supported values: `default` (read all), `timestamp` (start from timestamp), `query` (filter with query). See below for details. | [DocumentDbSourceConnectorConfig.java#L31](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DocumentDbSourceConnectorConfig.java#L31) |
| `batch.size` | Integer | No | 1000 | Number of documents per batch. | [DocumentDbSourceConnectorConfig.java#L38](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DocumentDbSourceConnectorConfig.java#L38) |
| `timestamp.key` | String | No | - | Field name for timestamp-based reads. Required if `mode` is `timestamp`. | [DocumentDbSourceConnectorConfig.java#L34](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DocumentDbSourceConnectorConfig.java#L34) |
| `timestamp.load.from` | String | No | - | Start timestamp (ISO format or millis). Required if `mode` is `timestamp`. | [DocumentDbSourceConnectorConfig.java#L35](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DocumentDbSourceConnectorConfig.java#L35) |
| `timestamp.load.to` | String | No | - | End timestamp (ISO format or millis). | [DocumentDbSourceConnectorConfig.java#L36](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DocumentDbSourceConnectorConfig.java#L36) |

> **Tip:** Use `mode: query` for advanced filtering. For large historical datasets, use `timestamp` mode to avoid full scans.

## MongoDB Source Connector

### Authentication

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `connection.string` | String | Yes | - | MongoDB connection string (e.g., `************************:port/db`). Alternatively, use host/port/username/password/database fields. | [MongoAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/MongoAuthConfig.java) |
| `database` | String | Yes | - | Database name. | [MongoAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/MongoAuthConfig.java) |
| `username` | String | No | - | MongoDB username (required if not using connection string). | [MongoAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/MongoAuthConfig.java) |
| `password` | String | No | - | MongoDB password (required if not using connection string). | [MongoAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/MongoAuthConfig.java) |
| `auth.source` | String | No | admin | Authentication database. | [MongoAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/MongoAuthConfig.java) |

### Query Configuration

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `mode` | String | No | default | Reading mode. `default` (read all), `timestamp` (start from timestamp), `query` (filter with query). | [DocumentDBQueryConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DocumentDBQueryConfig.java) |
| `timestamp.key` | String | No | - | Timestamp attribute for partial loads (e.g., `created_at`). Required if `mode` is `timestamp`. |
| `timestamp.load.from` | String | No | - | Starting timestamp (UNIX epoch or ISO date). Required if `mode` is `timestamp`. |
| `timestamp.load.to` | String | No | - | Ending timestamp (UNIX epoch or ISO date). |
| `query` | String | No | {} | MongoDB query in JSON format. Example: `{"status": "active"}`. See [MongoDB Query Docs](https://www.mongodb.com/docs/compass/current/query/filter/). Required if `mode` is `query`. |
| `projection` | String | No | - | Fields to include/exclude. |
| `sort` | String | No | - | Sort specification. |
| `change.stream.enabled` | Boolean | No | false | Enable change streams for real-time updates. |
| `resume.token` | String | No | - | Change stream resume token. |

> **Best Practices:**
>
> - Use connection string for managed MongoDB (e.g., Atlas). Use host/port/username/password for on-prem.
> - For large collections, use `timestamp` or `query` mode to avoid full scans.
> - Use change streams for CDC or real-time sync.

## DynamoDB Source Connector

### Authentication

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `access.key.id` | String | Yes | - | AWS access key ID. | [DynamoDBAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DynamoDBAuthConfig.java) |
| `secret.access.key` | String | Yes | - | AWS secret access key. | [DynamoDBAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DynamoDBAuthConfig.java) |
| `region` | String | Yes | us-east-1 | AWS region. | [DynamoDBAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DynamoDBAuthConfig.java) |
| `iam.role` | String | No | - | IAM role ARN (optional, for role-based access). | [DynamoDBAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DynamoDBAuthConfig.java) |

### Query Configuration

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `mode` | String | No | default | Reading mode. `default` (read all), `timestamp` (start from timestamp), `query` (filter with query). |
| `timestamp.key` | String | No | - | Timestamp attribute for partial loads. Required if `mode` is `timestamp`. |
| `timestamp.load.from` | String | No | - | Starting timestamp (UNIX epoch or ISO date). Required if `mode` is `timestamp`. |
| `timestamp.load.to` | String | No | - | Ending timestamp (UNIX epoch or ISO date). |
| `query.expression` | String | No | - | DynamoDB query expression. Example: `#name = :name_value`. See [DynamoDB Query Docs](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/Query.html#Query.FilterExpression). Required if `mode` is `query`. |
| `expression.attributes` | Map | No | - | Attribute values for query expression. Example: `{":name_value": {"S": "My Name"}}`. |
| `filter.expression` | String | No | - | Filter expression for additional filtering. |
| `consistent.read` | Boolean | No | false | Use strongly consistent reads. |

> **Best Practices:**
>
> - Use IAM roles for production workloads.
> - Use `timestamp` or `query` mode for incremental or filtered loads.
> - Use strongly consistent reads only if required (higher cost).

## Firebase Source Connector

### Authentication

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `project.id` | String | Yes | - | Firebase project ID. | [FirebaseAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/FirebaseAuthConfig.java) |
| `credentials.json` | String | Yes | - | Service account credentials JSON file. | [FirebaseAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/FirebaseAuthConfig.java) |
| `database.url` | String | Yes | - | Realtime Database URL. | [FirebaseAuthConfig](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/FirebaseAuthConfig.java) |

### Query Configuration

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `mode` | String | No | default | Reading mode. `default` (read all), `timestamp` (start from timestamp), `query` (filter with query). |
| `timestamp.key` | String | No | - | Timestamp attribute for partial loads. Required if `mode` is `timestamp`. |
| `timestamp.load.from` | String | No | - | Starting timestamp (UNIX epoch or ISO date). Required if `mode` is `timestamp`. |
| `timestamp.load.to` | String | No | - | Ending timestamp (UNIX epoch or ISO date). |
| `query` | String | No | - | Firebase query in Nexla Firebase query DSL. Example: `{"filters": [{ "fieldName": "name", "operator": "EQUAL", "fieldType": "string", "stringValue": "test" }]}`. Required if `mode` is `query`. |
| `query.orderBy` | String | No | - | Order by field. |
| `query.limitToFirst` | Integer | No | - | Limit results from start. |
| `query.limitToLast` | Integer | No | - | Limit results from end. |
| `query.startAt` | String | No | - | Start value. |
| `query.endAt` | String | No | - | End value. |

> **Best Practices:**
>
> - Use service account credentials for automation and production.
> - Use `timestamp` or `query` mode for incremental or filtered loads.
> - For complex queries, contact Nexla support for guidance on query DSL.

---

## Examples

### Basic MongoDB Import

```json
{
  "collection": "users",
  "database": "myapp",
  "connection.string": "mongodb://localhost:27017",
  "batch.size": 500,
  "query": "{\"status\": \"active\"}"
}
```

### MongoDB with Change Streams

```json
{
  "collection": "orders",
  "database": "ecommerce",
  "connection.string": "mongodb+srv://cluster0.example.mongodb.net",
  "username": "dbuser",
  "password": "dbpass",
  "change.stream.enabled": true,
  "resume.token": "AAAAAAAAAAAA..."
}
```

### DynamoDB Query

```json
{
  "collection": "transactions",
  "region": "us-west-2",
  "access.key.id": "AKIAXXXXXXXXXXXXXXXX",
  "secret.access.key": "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "query.expression": "user_id = :uid AND transaction_date > :date",
  "expression.attributes": {
    ":uid": "12345",
    ":date": "2023-01-01"
  }
}
```

### Firebase Real-time Data

```json
{
  "project.id": "my-project",
  "credentials.json": "{...}",
  "database.url": "https://my-project.firebaseio.com",
  "path": "/users",
  "query.orderBy": "lastLogin",
  "query.limitToLast": 100,
  "query.startAt": "2023-01-01T00:00:00Z"
}
```

---

> **Tips:**
>
> - For all connectors, use incremental or query-based modes for large datasets.
> - Always use the least privileged credentials required.
> - Validate your query syntax and test with a small dataset before scaling up.
