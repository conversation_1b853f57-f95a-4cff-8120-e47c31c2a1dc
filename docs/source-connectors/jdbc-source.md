# JDBC Source Configuration

Confidence Score: Reviewed

## Overview

The JDBC source connector allows you to import data from any JDBC-compatible database into Nexla. It supports:

- One-time full table import
- Incremental import (timestamp, auto-incrementing ID, or both)
- Custom SQL query import
- Change Data Capture (CDC) for supported databases
- High-throughput and warehouse-specific options

> **Tip:** Use incremental or CDC modes for large or frequently updated tables. Use SSH tunneling for secure access to private databases.

## Authentication Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `host` | String | Yes | - | Database hostname (exclude protocol, e.g., `db.company.com`). |
| `port` | Integer | Yes | - | Database port (e.g., 5432 for Postgres). |
| `username` | String | Yes | - | Database username. |
| `password` | String | Yes | - | Database password. |
| `database_name` | String | Yes | - | Database name. |
| `schema_name` | String | No | - | Schema name (optional, for databases with multiple schemas). |
| `has_ssh_tunnel` | Boolean | No | false | Enable SSH tunnel for private databases. |
| `tunnel.bastion.host` | String | No | - | SSH bastion host (if using SSH tunnel). |
| `tunnel.bastion.port` | Integer | No | 22 | SSH bastion port. |
| `tunnel.bastion.user` | String | No | nexla | SSH username. |

## Source Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `db_query_mode` | String | Yes | Default | Fetch mode: `Default` (table), `Query` (custom SQL). |
| `table` | String | Yes* | - | Table name (required if `db_query_mode` is `Default`). |
| `query` | String | Yes* | - | Custom SQL query (required if `db_query_mode` is `Query`). |
| `cdc.enabled` | Boolean | No | false | Enable Change Data Capture (requires DBA setup). |
| `cdc.snapshot.enabled` | Boolean | No | false | Ingest initial snapshot (for CDC). |
| `cdc.capture.delete` | Boolean | No | false | Track deletions (for CDC). |
| `mode` | String | No | none | Table scan mode: `none` (full), `incrementing`, `timestamp`, `incrementing,timestamp`. |
| `incrementing.column.name` | String | No | - | ID column for incremental loads (numeric). |
| `incrementing.load.from` | String | No | - | Starting ID for incremental loads. |
| `timestamp.column.name` | String | No | - | Timestamp column for incremental loads (datetime). |
| `timestamp.load.from` | String | No | - | Starting timestamp (UNIX epoch or ISO date). |
| `commit.on.read` | Boolean | No | false | Commit after read (rarely needed). |
| `hc_scale_factor` | String | No | 1 | Throughput: `1`, `2`, `4`, `8`. |

> **Best Practices:**
>
> - Use SSH tunneling for private or on-prem databases.
> - Use CDC for real-time sync; incremental for batch loads.
> - Use `mode` to avoid full scans on large tables.

## Advanced Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `query.probe.timeout` | Integer | No | 120000 | Query probe timeout (ms). |
| `file.buffer` | Boolean | No | false | Use file buffering. |
| `schema.source` | String | No | - | Schema source: `DATABASE` or `SAMPLE`. |
| `sync.messages.limit` | Integer | No | - | Max messages to sync. |
| `cdc.mode` | String | No | - | CDC mode (database-specific). |
| `cdc.postgres.logical.decoder` | String | No | - | PostgreSQL logical decoder plugin (e.g., `wal2json`). |

## Warehouse-specific Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `temp.storage.type` | String | No | - | Temp storage type for warehouse ops. |
| `direct.unload` | Boolean | No | false | Enable direct unload. |
| `unload.max.file.size` | Long | No | - | Max file size for unloads. |
| `unload.single.file` | Boolean | No | false | Unload to a single file. |
| `enable.spanner.data.boost` | Boolean | No | false | Enable Spanner data boost. |
| `netsuite.tz.utc` | Boolean | No | false | Use UTC for NetSuite. |

## Examples

### Basic Table Import

```json
{
  "db_query_mode": "Default",
  "table": "customers",
  "database_name": "sales"
}
```

### Incremental Import by Timestamp

```json
{
  "db_query_mode": "Default",
  "mode": "timestamp",
  "table": "orders",
  "timestamp.column.name": "last_modified",
  "timestamp.load.from": "2023-01-01T00:00:00"
}
```

### Custom Query with Incremental ID

```json
{
  "db_query_mode": "Query",
  "query": "SELECT * FROM transactions WHERE amount > 1000",
  "mode": "incrementing",
  "incrementing.column.name": "transaction_id",
  "incrementing.load.from": 1000000
}
```

### CDC Configuration for PostgreSQL

```json
{
  "db_query_mode": "Default",
  "table": "inventory",
  "cdc.enabled": true,
  "cdc.capture.delete": true,
  "cdc.snapshot.enabled": true,
  "cdc.postgres.logical.decoder": "wal2json"
}
```

---

> **Tips:**
>
> - For large tables, always use incremental or CDC modes.
> - Use schema and table filters to limit data volume.
> - For CDC, ensure your DBA has enabled logical replication and granted necessary permissions.
> - Test queries and connection settings with a small dataset before scaling up.
