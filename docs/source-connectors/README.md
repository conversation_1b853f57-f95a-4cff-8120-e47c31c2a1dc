# Source Connectors

This directory contains documentation for all available source connectors in Nexla.

## Database Connectors

- [<PERSON><PERSON><PERSON> (PostgreSQL, MySQL, SQL Server, etc.)](jdbc-source.md)
- [BigQuery](bigquery-source.md)
- [MongoDB](mongodb-source.md)
- [Firebase](firebase-source.md)
- [DynamoDB](dynamodb-source.md)

## File-based Connectors

- [FTP/SFTP](file-source.md#ftp)
- [WebDAV](file-source.md#webdav)
- [Amazon S3](file-source.md#s3)
- [Azure Blob Storage](file-source.md#azure-blob)
- [Google Cloud Storage](file-source.md#gcs)
- [Box](file-source.md#box)
- [Delta Lake](file-source.md#delta-lake)
- [File Upload](file-source.md#file-upload)

## Messaging/Streaming Connectors

- [Kafka](kafka-source.md)
- [Confluent Kafka](kafka-source.md#confluent)
- [JMS](jms-source.md)
- [Google PubSub](pubsub-source.md)

## API Connectors

- [REST](rest-source.md)
- [SOAP](soap-source.md)

## Documentation Structure

Each connector documentation follows this structure:

1. Overview - Description and use cases
2. Authentication Parameters - Required credentials
3. Source Configuration Parameters - Core configuration
4. Advanced Configuration Parameters - Optional settings
5. Examples - Common configuration examples
