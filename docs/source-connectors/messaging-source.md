# Messaging/Streaming Source Connectors

Confidence Score: Reviewed

## Overview

Messaging and streaming source connectors allow you to consume data from various message brokers and streaming platforms. These connectors support:

- Apache Kafka
- Google Cloud Pub/Sub
- JMS (TIBCO, ActiveMQ)
- Real-time data consumption
- Schema evolution
- Parallel processing
- Offset management
- Message format handling

> **Tip:** Use parallelism and offset management for high-throughput, reliable streaming. Use secure authentication and encryption for production workloads.

## Kafka Source Connector

### Basic Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `topic` | String | Yes | - | Kafka topic name. |
| `key.deserializer` | String | No | org.apache.kafka.common.serialization.StringDeserializer | Key deserializer class. |
| `value.deserializer` | String | No | org.apache.kafka.common.serialization.StringDeserializer | Value deserializer class. |
| `parser.type` | String | No | json | Data format. Options: `json`, `csv`, `tsv`, `txt`, `xml`. |
| `serialization.variant` | String | No | none | Serialization variant (advanced). |

### Offset Management

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `offset.mode` | String | No | earliest | Offset mode. Options: `earliest`, `latest`, `from_date`, `manual`. |
| `start.from.date` | String | No | - | Start consuming from this date (when `offset.mode=from_date`). |
| `start.from.offsets` | String | No | - | JSON map of partition offsets (when `offset.mode=manual`). |

### Schema Registry

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `schema` | String | No | - | Kafka message schema. |
| `schema.registry.url` | String | No | - | Schema Registry URL. |
| `schema.registry.allow.untrusted` | Boolean | No | false | Allow untrusted certificates. |
| `schema.registry.api.key` | String | No | - | Schema Registry API Key. |
| `schema.registry.api.secret` | String | No | - | Schema Registry API Secret. |

### Advanced Features

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `parallelism` | Integer | No | 3 | Parallelism level for consumption. |
| `schema.detection.once` | Boolean | No | true | Detect schema only once. |
| `schema.detection.confluent.cache.ttl` | Integer | No | 60 | Schema cache TTL (minutes). |

### Authentication

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `bootstrap.servers` | String | Yes | - | Kafka bootstrap servers. |
| `security.protocol` | String | No | PLAINTEXT | Security protocol. Options: `PLAINTEXT`, `SASL_PLAINTEXT`, `SASL_SSL`, `SSL`. |
| `sasl.mechanism` | String | No | - | SASL mechanism (if using SASL). |
| `sasl.jaas.config` | String | No | - | JAAS configuration for SASL. |
| `ssl.truststore.location` | String | No | - | SSL truststore location. |
| `ssl.truststore.password` | String | No | - | SSL truststore password. |
| `ssl.keystore.location` | String | No | - | SSL keystore location. |
| `ssl.keystore.password` | String | No | - | SSL keystore password. |
| `has_ssh_tunnel` | Boolean | No | false | Enable SSH tunnel for private Kafka clusters. |
| `tunnel.bastion.host` | String | No | - | SSH bastion host. |
| `tunnel.bastion.port` | Integer | No | 22 | SSH bastion port. |
| `tunnel.bastion.user` | String | No | nexla | SSH username. |

> **Best Practices:**
>
> - Use SASL/SSL for secure production deployments.
> - Use SSH tunneling for private clusters.
> - Set `parallelism` to match expected throughput.

## Google Cloud Pub/Sub Source Connector

### Basic Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `subscription` | String | Yes | - | Pub/Sub subscription ID. |
| `parser.type` | String | No | json | Data format. Options: `json`, `csv`, `tsv`, `txt`, `xml`. |
| `parallelism` | Integer | No | 1 | Parallelism level for consumption. |

### Authentication

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `is_service_account` | String | Yes | true | Authentication type. Options: `true` (service account), `false` (end user). |
| `project_id` | String | Yes | - | GCP project ID. |
| `json_creds` | File | Yes* | - | Service account credentials JSON. |

## JMS Source Connector

### Basic Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `jms.vendor` | String | No | tibco | JMS vendor. Options: `tibco`, `activemq`. |
| `url` | String | Yes | - | JMS server URL. |
| `username` | String | Yes | - | JMS username. |
| `password` | String | Yes | - | JMS password. |
| `source.type` | String | Yes | topic | Source type. Options: `topic`, `queue`. |
| `source.name` | String | Yes | - | Topic or queue name. |
| `parser.type` | String | No | json | Data format. Options: `json`, `csv`, `tsv`, `txt`, `xml`. |
| `has_ssh_tunnel` | Boolean | No | false | Enable SSH tunnel for private JMS brokers. |
| `tunnel.bastion.host` | String | No | - | SSH bastion host. |
| `tunnel.bastion.port` | Integer | No | 22 | SSH bastion port. |
| `tunnel.bastion.user` | String | No | nexla | SSH username. |

> **Best Practices:**
>
> - Use secure authentication and encryption for production.
> - Use SSH tunneling for private brokers.
> - Set `parser.type` to match your message format.

## Examples

### Basic Kafka Consumer

```json
{
  "topic": "my-topic",
  "bootstrap.servers": "kafka1:9092,kafka2:9092",
  "group.id": "my-group",
  "offset.mode": "earliest",
  "parser.type": "json",
  "parallelism": 3
}
```

### Kafka with Schema Registry

```json
{
  "topic": "avro-topic",
  "bootstrap.servers": "kafka:9092",
  "schema.registry.url": "http://schema-registry:8081",
  "parser.type": "avro",
  "key.deserializer": "io.confluent.kafka.serializers.KafkaAvroDeserializer",
  "value.deserializer": "io.confluent.kafka.serializers.KafkaAvroDeserializer"
}
```

### Kafka with SASL/SSL

```json
{
  "topic": "secure-topic",
  "bootstrap.servers": "kafka:9093",
  "security.protocol": "SASL_SSL",
  "sasl.mechanism": "PLAIN",
  "sasl.jaas.config": "org.apache.kafka.common.security.plain.PlainLoginModule required username='user' password='pass';",
  "ssl.truststore.location": "/path/to/truststore.jks",
  "ssl.truststore.password": "truststore-pass"
}
```

### Pub/Sub Consumer

```json
{
  "subscription": "my-subscription",
  "project_id": "my-project",
  "json_creds": "{...}",
  "parser.type": "json",
  "parallelism": 2
}
```

### JMS Topic Consumer

```json
{
  "jms.vendor": "tibco",
  "url": "tcp://jms-broker:7222",
  "username": "jmsuser",
  "password": "jmspass",
  "source.type": "topic",
  "source.name": "my-topic",
  "parser.type": "json"
}
```

---

> **Tips:**
>
> - Use parallelism to scale throughput.
> - Always use the least privileged credentials required.
> - Validate your topic/queue names and test with a small dataset before scaling up.
> - Enable encryption and secure authentication for sensitive data.
> - For advanced scenarios, contact Nexla support for guidance.
