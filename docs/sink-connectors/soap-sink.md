# SOAP Sink Configuration

Confidence Score: Unreviewed

## Overview

The SOAP sink connector enables writing data to SOAP web services. It supports SOAP 1.1 and 1.2 protocols, WS-Security, and custom SOAP headers for integration with enterprise SOAP services.

## Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `soap.wsdl.url` | String | Yes | - | WSDL URL |
| `soap.operation` | String | Yes | - | SOAP operation name |
| `soap.headers` | Map | No | {} | SOAP headers |
| `soap.body.template` | String | No | - | Request body template |
| `soap.version` | String | No | 1.1 | SOAP version |
| `soap.retry.count` | Integer | No | 3 | Number of retry attempts |
| `soap.auth.type` | String | No | none | Authentication type |
| `soap.auth.username` | String | No | - | Authentication username |
| `soap.auth.password` | String | No | - | Authentication password |
| `soap.ssl.verify` | Boolean | No | true | Verify SSL certificates |
| `soap.proxy.url` | String | No | - | Proxy server URL |
| `soap.timeout.ms` | Long | No | 30000 | Request timeout |
| `soap.namespace` | String | No | - | Custom namespace |
| `soap.action` | String | No | - | SOAPAction header |

## Parameter Details

### WSDL Configuration

- `soap.wsdl.url`: WSDL location:
  - URL to WSDL file
  - Local file path
  - Example: `https://service.example.com/service?wsdl`

- `soap.operation`: Service operation:
  - Operation name from WSDL
  - Case-sensitive
  - Example: `createOrder`

### Message Customization

- `soap.headers`: SOAP headers:

  ```xml
  <soap:Header>
    <Security xmlns="...">
      <UsernameToken>
        <Username>${username}</Username>
        <Password>${password}</Password>
      </UsernameToken>
    </Security>
  </soap:Header>
  ```

- `soap.body.template`: Request template:
  - XML template
  - Variable substitution
  - Example:

    ```xml
    <soap:Envelope>
      <soap:Body>
        <ns:createOrder>
          <order>${record}</order>
        </ns:createOrder>
      </soap:Body>
    </soap:Envelope>
    ```

### Protocol Settings

- `soap.version`: SOAP versions:
  - `1.1`: SOAP 1.1
  - `1.2`: SOAP 1.2
  - Namespace differences
  - Header requirements

- `soap.namespace`: XML namespaces:
  - Custom namespace URIs
  - Prefix mappings
  - Default namespaces

### Authentication

- `soap.auth.type`: Auth methods:
  - `none`: No authentication
  - `basic`: Basic auth
  - `wss`: WS-Security
  - `certificate`: Client certificate
  - `custom`: Custom auth

- `soap.auth.username` and `soap.auth.password`:
  - Basic authentication
  - WS-Security credentials
  - Token generation

## Example Configurations

### Basic SOAP Service Example

```json
{
  "soap.wsdl.url": "https://service.example.com/orders?wsdl",
  "soap.operation": "createOrder",
  "soap.version": "1.1",
  "soap.body.template": "<ns:createOrder><order>${record}</order></ns:createOrder>",
  "soap.retry.count": 3,
  "soap.timeout.ms": 5000
}
```

### WS-Security Example

```json
{
  "soap.wsdl.url": "https://secure.example.com/service?wsdl",
  "soap.operation": "processData",
  "soap.auth.type": "wss",
  "soap.auth.username": "${username}",
  "soap.auth.password": "${password}",
  "soap.headers": {
    "Security": {
      "UsernameToken": {
        "Username": "${username}",
        "Password": "${password}"
      }
    }
  },
  "soap.ssl.verify": true
}
```

### Custom Namespace Example

```json
{
  "soap.wsdl.url": "https://api.example.com/v2/service?wsdl",
  "soap.operation": "submitData",
  "soap.namespace": "http://example.com/v2/schema",
  "soap.action": "http://example.com/v2/submitData",
  "soap.version": "1.2",
  "soap.body.template": "<ns2:submitData xmlns:ns2=\"http://example.com/v2/schema\"><data>${record}</data></ns2:submitData>"
}
```

### Enterprise Service Example

```json
{
  "soap.wsdl.url": "https://enterprise.example.com/service?wsdl",
  "soap.operation": "processTransaction",
  "soap.proxy.url": "http://proxy:8080",
  "soap.timeout.ms": 60000,
  "soap.retry.count": 5,
  "soap.headers": {
    "MessageID": "${uuid}",
    "Timestamp": "${timestamp}"
  }
}
```

## Best Practices

1. **WSDL Management**
   - Cache WSDL locally
   - Version control WSDL
   - Monitor WSDL changes
   - Handle schema updates

2. **Security**
   - Use WS-Security
   - Implement encryption
   - Sign messages
   - Secure credentials

3. **Error Handling**
   - Parse SOAP faults
   - Implement retries
   - Log failures
   - Monitor errors

4. **Performance**
   - Optimize message size
   - Use connection pooling
   - Monitor response times
   - Handle timeouts

## Common Issues and Solutions

1. **Schema Validation**
   - Issue: Invalid message format
   - Solution: Validate against WSDL

2. **Authentication**
   - Issue: Security token errors
   - Solution: Check credentials, token expiry

3. **Connectivity**
   - Issue: Service unavailable
   - Solution: Check network, proxy settings

4. **Performance**
   - Issue: Slow responses
   - Solution: Optimize message size, check timeouts

## Service-Specific Notes

### SOAP 1.1

- SOAPAction header
- Fault structure
- Namespace handling
- Transport bindings

### SOAP 1.2

- Content-Type changes
- Fault improvements
- Role attribute
- Header blocks

### WS-Security

- Username tokens
- X.509 certificates
- SAML tokens
- Encryption

### Enterprise Integration

- ESB compatibility
- Message routing
- Transaction handling
- Auditing requirements
