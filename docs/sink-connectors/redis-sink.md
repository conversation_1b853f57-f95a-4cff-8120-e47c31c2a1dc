# Redis Sink Configuration

Confidence Score: Unreviewed

## Overview

The Redis sink connector enables writing data to Redis databases. It supports various Redis data structures (strings, hashes, lists, sets, sorted sets), clustering, sentinel configuration, and different serialization formats.

## Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `redis.host` | String | Yes | - | Redis server host |
| `redis.port` | Integer | No | 6379 | Redis server port |
| `redis.database` | Integer | No | 0 | Redis database number |
| `redis.password` | String | No | - | Redis authentication password |
| `redis.data.type` | String | No | string | Redis data type |
| `redis.key.prefix` | String | No | - | Prefix for all keys |
| `redis.key.pattern` | String | No | - | Key generation pattern |
| `redis.ttl.seconds` | Integer | No | -1 | Time to live in seconds |
| `redis.batch.size` | Integer | No | 100 | Batch size for writes |
| `redis.cluster.enabled` | Boolean | No | false | Enable cluster mode |
| `redis.cluster.nodes` | List | No | [] | Cluster node addresses |
| `redis.sentinel.enabled` | Boolean | No | false | Enable sentinel mode |
| `redis.sentinel.master` | String | No | mymaster | Sentinel master name |
| `redis.sentinel.nodes` | List | No | [] | Sentinel node addresses |
| `redis.ssl.enabled` | Boolean | No | false | Enable SSL/TLS |
| `redis.retry.count` | Integer | No | 3 | Number of retry attempts |
| `redis.timeout.ms` | Long | No | 2000 | Connection timeout |

## Parameter Details

### Connection Settings

- `redis.host`: Server address:
  - Hostname
  - IP address
  - Example: `redis.example.com`

- `redis.database`: Database selection:
  - Range: 0-15
  - Default: 0
  - Logical separation

### Data Structure

- `redis.data.type`: Supported types:
  - `string`: Simple key-value
  - `hash`: Field-value pairs
  - `list`: Ordered elements
  - `set`: Unique elements
  - `zset`: Sorted set

### Key Management

- `redis.key.pattern`: Pattern variables:
  - `${field}`: Record field
  - `${timestamp}`: Current time
  - `${uuid}`: Random UUID
  - `${sequence}`: Auto-increment

### High Availability

- `redis.cluster.nodes`: Cluster format:
  - List of host:port
  - Example: `["host1:6379", "host2:6379"]`

- `redis.sentinel.nodes`: Sentinel format:
  - List of host:port
  - Example: `["sentinel1:26379", "sentinel2:26379"]`

## Example Configurations

### Simple String Example

```json
{
  "redis.host": "redis.example.com",
  "redis.port": 6379,
  "redis.password": "${REDIS_PASSWORD}",
  "redis.data.type": "string",
  "redis.key.prefix": "users:",
  "redis.key.pattern": "${id}",
  "redis.ttl.seconds": 3600,
  "redis.batch.size": 100
}
```

### Hash Structure Example

```json
{
  "redis.host": "redis.internal",
  "redis.port": 6379,
  "redis.database": 1,
  "redis.data.type": "hash",
  "redis.key.prefix": "product:",
  "redis.key.pattern": "${category}:${id}",
  "redis.batch.size": 50,
  "redis.ssl.enabled": true,
  "redis.timeout.ms": 5000
}
```

### Cluster Configuration Example

```json
{
  "redis.cluster.enabled": true,
  "redis.cluster.nodes": [
    "redis-1:6379",
    "redis-2:6379",
    "redis-3:6379"
  ],
  "redis.password": "${REDIS_PASSWORD}",
  "redis.data.type": "hash",
  "redis.key.prefix": "session:",
  "redis.key.pattern": "${userId}:${sessionId}",
  "redis.ttl.seconds": 1800,
  "redis.retry.count": 5
}
```

### Sentinel Configuration Example

```json
{
  "redis.sentinel.enabled": true,
  "redis.sentinel.master": "mymaster",
  "redis.sentinel.nodes": [
    "sentinel-1:26379",
    "sentinel-2:26379",
    "sentinel-3:26379"
  ],
  "redis.password": "${REDIS_PASSWORD}",
  "redis.data.type": "zset",
  "redis.key.prefix": "scores:",
  "redis.key.pattern": "${gameId}",
  "redis.batch.size": 200,
  "redis.timeout.ms": 3000
}
```

## Best Practices

1. **Key Management**
   - Meaningful prefixes
   - Consistent patterns
   - TTL strategy
   - Key length

2. **Performance**
   - Batch operations
   - Pipeline usage
   - Connection pooling
   - Memory monitoring

3. **High Availability**
   - Replication setup
   - Failover testing
   - Monitoring
   - Backup strategy

4. **Security**
   - Authentication
   - SSL/TLS
   - Network isolation
   - Access control

## Common Issues and Solutions

1. **Memory**
   - Issue: Memory exhaustion
   - Solution: TTL, eviction policies

2. **Connection**
   - Issue: Connection drops
   - Solution: Retry, timeout settings

3. **Performance**
   - Issue: Slow operations
   - Solution: Batch size, pipelining

4. **Data Loss**
   - Issue: Write failures
   - Solution: Persistence config, replication

## Deployment-Specific Notes

### Standalone

- Single instance
- Persistence options
- Memory limits
- Backup strategy

### Cluster

- Sharding
- Node management
- Slot migration
- Resharding

### Sentinel

- Quorum settings
- Failover timeout
- Notification scripts
- Client configuration

### Enterprise

- RBAC
- Multi-tenancy
- Analytics
- Support options
