# File Sink Configuration

Confidence Score: Reviewed

## Overview

The File sink connector enables writing data to various file formats and storage systems. It supports local file systems, cloud storage (S3, GCS, Azure Blob), and network file systems with features like compression, rotation, batching, and encryption.

> **Tip:** Choose authentication and encryption options based on your organization's security policies. Use file rotation and partitioning for large-scale or streaming workloads.

## Common Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `file.path` | String | Yes | - | Output file path. Supports local, S3 (`s3://`), GCS (`gs://`), Azure (`wasb://`). |
| `file.name.template` | String | No | - | Template for output filenames. Supports `{timestamp}`, `{date}`, `{partition}`, `{uuid}`. |
| `file.compression` | String | No | none | Compression type. Options: `none`, `gzip`, `snappy`, `zlib`, `lzo`, `lz4`. |
| `file.max.records` | Integer | No | - | Maximum records per file. Set to 0 for no limit. |
| `file.max.size.mb` | Long | No | 4096 | Maximum file size in MB. |
| `file.rotation.time.ms` | Long | No | - | File rotation interval in ms. |
| `file.rotation.size.bytes` | Long | No | - | File size threshold for rotation in bytes. |
| `file.overwrite` | Boolean | No | false | Overwrite existing files if true. |
| `file.flush.records` | Integer | No | 1000 | Records before flush. |
| `hc_scale_factor` | String | No | 1 | Speed Factor: `1x`, `2x`, `4x`, `8x`, `16x`, `32x`. Higher values increase throughput and cost. |

### Storage Authentication Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `aws.access.key.id` | String | No | - | AWS access key for S3. |
| `aws.secret.access.key` | String | No | - | AWS secret key for S3. |
| `aws.region` | String | No | us-east-1 | AWS region. |
| `gcp.project.id` | String | No | - | GCP project ID. |
| `gcp.credentials.json` | String | No | - | GCP service account JSON. |
| `azure.account.name` | String | No | - | Azure storage account. |
| `azure.account.key` | String | No | - | Azure storage key. |
| `ftp.type` | String | Yes | ftp | FTP connection type: `ftp`, `ftps`, `sftp`. |
| `ftp.mode` | String | No | passive.local | FTP mode: `active`, `passive.local`, `passive.remote`. |
| `box.access.token` | String | Yes | - | Box access token. |

### Advanced/Conditional Logic

- **Compression**: Choose based on downstream compatibility and storage cost. `gzip` is widely supported; `snappy`, `lz4`, etc. for analytics.
- **Rotation**: Use time or size-based rotation for streaming or high-volume use cases.
- **Overwrite**: Set to `true` only if you want to replace files; otherwise, use unique file naming.
- **Speed Factor**: Increase for high-throughput needs, but monitor for cost and resource usage.
- **Encryption**: Enable file or client/server-side encryption for sensitive data. Configure KMS or PGP as needed.
- **Authentication**: Use IAM roles, service accounts, or key-based auth as appropriate for your cloud provider.

## Example Configurations

### Amazon S3 Example

```json
{
  "file.path": "s3://my-bucket/data/output",
  "file.name.template": "data_{date}/{uuid}.parquet",
  "file.compression": "snappy",
  "file.max.records": 500000,
  "file.rotation.size.bytes": **********,
  "file.flush.records": 10000,
  "aws.access.key.id": "AKIA...",
  "aws.secret.access.key": "...",
  "aws.region": "us-west-2",
  "hc_scale_factor": "4"
}
```

### Google Cloud Storage Example

```json
{
  "file.path": "gs://my-bucket/data/output",
  "file.name.template": "{date}/data_{timestamp}.csv.gz",
  "file.compression": "gzip",
  "file.rotation.time.ms": 1800000,
  "file.flush.records": 2000,
  "gcp.project.id": "my-gcp-project",
  "gcp.credentials.json": "..."
}
```

### Azure Blob Storage Example

```json
{
  "file.path": "wasb://<EMAIL>/data",
  "file.name.template": "year={date}/data_{uuid}.csv",
  "file.compression": "none",
  "file.max.records": 100000,
  "file.flush.records": 1000,
  "azure.account.name": "myaccount",
  "azure.account.key": "..."
}
```

### FTP/SFTP Example

```json
{
  "file.path": "/data/output",
  "file.name.template": "data_{timestamp}_{partition}.csv",
  "file.compression": "gzip",
  "ftp.type": "sftp",
  "ftp.mode": "passive.local",
  "username": "ftpuser",
  "password": "..."
}
```

### Box Example

```json
{
  "file.path": "/nexla/output",
  "file.name.template": "data_{date}_{uuid}.csv",
  "box.access.token": "..."
}
```

## Best Practices

- Use date/time and unique identifiers in file names to avoid conflicts.
- Set rotation and flush thresholds based on downstream processing and storage limits.
- Use compression and encryption for sensitive or large data sets.
- Monitor storage usage and permissions regularly.
- Test with small batches before scaling up.

## Common Issues and Solutions

- **Permission Issues**: Check credentials, IAM roles, and storage policies.
- **Performance**: Adjust buffer/flush settings and speed factor.
- **Storage Space**: Use rotation and cleanup policies.
- **File Naming Conflicts**: Use unique file name templates.

## Storage-Specific Notes

- **S3**: Supports multipart upload, storage classes, and server/client-side encryption.
- **GCS**: Supports service accounts, storage classes, and object lifecycle management.
- **Azure**: Supports SAS tokens, account keys, and connection strings.
- **FTP/SFTP**: Use key-based auth for SFTP when possible.
- **Box**: Requires OAuth2 access tokens.
