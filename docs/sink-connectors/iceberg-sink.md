# Iceberg Sink Configuration

Confidence Score: Reviewed

## Overview

The Iceberg sink connector enables writing data to Apache Iceberg tables. It supports various storage backends (S3, HDFS, local), table formats, partitioning schemes, schema evolution, and high-throughput options.

> **Tip:** Use upsert mode for change data capture (CDC) or deduplication. Use partitioning and compression for large-scale analytics.

## Common Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `iceberg.warehouse.dir` | String | Yes | - | Warehouse directory path (e.g., `s3://bucket/warehouse`). |
| `iceberg.table.name` | String | Yes | - | Table name to write to. |
| `iceberg.partition-keys` | String | No | - | Comma-separated partition keys. |
| `iceberg.id-fields` | String | No | - | Comma-separated identity fields for upserts. |
| `iceberg.insert.mode` | String | No | insert | Insert mode: `insert` (append), `upsert` (update if exists). |
| `hc_scale_factor` | String | No | 1 | Speed Factor: `1x`, `2x`, `4x`, `8x`, `16x`, `32x`. Higher values increase throughput and cost. |

### Storage Authentication Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `aws.access.key.id` | String | Yes* | - | AWS access key for S3. |
| `aws.secret.access.key` | String | Yes* | - | AWS secret key for S3. |
| `aws.region` | String | Yes* | us-east-1 | AWS region. |

*Required for S3-based catalogs

### Advanced/Conditional Logic

- **Insert Mode**: Use `insert` for append-only workloads. Use `upsert` for CDC or deduplication. Set `iceberg.id-fields` for upserts.
- **Partitioning**: Use partition keys for efficient queries and large datasets. Comma-separated list.
- **Speed Factor**: Increase for high-throughput needs, but monitor for cost and resource usage.
- **Table Name Prefix/Suffix**: Use for dynamic table naming in multi-tenant or sharded environments.

## Example Configurations

### S3 Iceberg Sink Example

```json
{
  "iceberg.warehouse.dir": "s3://my-nexla-bucket/product",
  "iceberg.table.name": "sales",
  "iceberg.insert.mode": "upsert",
  "iceberg.id-fields": "order_id,customer_id",
  "iceberg.partition-keys": "region,year",
  "aws.access.key.id": "AKIA...",
  "aws.secret.access.key": "...",
  "aws.region": "us-west-2",
  "hc_scale_factor": "4"
}
```

### HDFS Iceberg Sink Example

```json
{
  "iceberg.warehouse.dir": "hdfs://namenode:8020/warehouse/tablespace/managed/hive",
  "iceberg.table.name": "events",
  "iceberg.insert.mode": "insert",
  "iceberg.partition-keys": "event_type,month",
  "hc_scale_factor": "2"
}
```

### Local Development Example

```json
{
  "iceberg.warehouse.dir": "/tmp/iceberg/warehouse",
  "iceberg.table.name": "test_table",
  "iceberg.insert.mode": "insert"
}
```

## Best Practices

- Use upsert mode for CDC or deduplication scenarios.
- Partition by high-cardinality fields for query efficiency.
- Use compression and optimize file size for analytics.
- Monitor throughput and adjust speed factor as needed.
- Test with small batches before scaling up.

## Common Issues and Solutions

- **Permission Issues**: Check AWS credentials and S3 bucket policies.
- **Performance**: Adjust speed factor and partitioning.
- **Schema Evolution**: Enable schema evolution for changing data models.
- **File Size**: Tune file size and commit intervals for optimal performance.

## Storage-Specific Notes

- **S3**: Use IAM roles or access keys. Ensure bucket policies allow Iceberg operations.
- **HDFS**: Ensure proper permissions and network access.
- **Local**: Use for development and testing only.

---

For more advanced configuration options, see the [Iceberg S3 Example Config](https://github.com/nexla/connector_configs/ref/specs/connectors/split/default/connections/file/s3_iceberg.json).
