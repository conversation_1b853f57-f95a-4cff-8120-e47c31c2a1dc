# Google Cloud Pub/Sub Sink Configuration

Confidence Score: Unreviewed

## Overview
The Google Cloud Pub/Sub sink connector enables writing data to Pub/Sub topics. It supports various publishing options, batching configurations, ordering keys, and different authentication methods.

## Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `pubsub.project.id` | String | Yes | - | Google Cloud project ID |
| `pubsub.topic` | String | Yes | - | Pub/Sub topic name |
| `pubsub.ordering.key` | String | No | - | Message ordering key field |
| `pubsub.ordering.enabled` | Boolean | No | false | Enable message ordering |
| `pubsub.batch.size` | Integer | No | 100 | Messages per batch |
| `pubsub.batch.bytes` | Long | No | 1048576 | Batch size in bytes |
| `pubsub.batch.timeout.ms` | Long | No | 10000 | Batch timeout |
| `pubsub.auth.type` | String | No | service_account | Authentication type |
| `pubsub.credentials` | String | No | - | Service account credentials |
| `pubsub.endpoint` | String | No | - | Custom endpoint URL |
| `pubsub.compression` | String | No | none | Compression type |
| `pubsub.retry.count` | Integer | No | 3 | Number of retry attempts |
| `pubsub.retry.backoff.ms` | Long | No | 1000 | Retry backoff time |
| `pubsub.message.attributes` | Map | No | {} | Message attributes |
| `pubsub.message.ordering.key` | String | No | - | Message ordering key |

## Parameter Details

### Project Configuration
- `pubsub.project.id`: GCP project:
  - Project identifier
  - Example: `my-project-123`

- `pubsub.topic`: Topic name:
  - Full name: `projects/my-project/topics/my-topic`
  - Short name: `my-topic`

### Message Settings
- `pubsub.ordering.key`: Key field:
  - Field name
  - Expression
  - Example: `userId`

- `pubsub.message.attributes`: Attributes:
  - Key-value pairs
  - Metadata
  - Filtering

### Performance Settings
- `pubsub.batch.size`: Batch limits:
  - Messages: 1-1000
  - Memory usage
  - Latency impact

- `pubsub.batch.bytes`: Size limits:
  - Maximum: 10MB
  - Per message: 10MB
  - Optimization

### Authentication
- `pubsub.auth.type`: Auth methods:
  - `service_account`: Service account JSON
  - `compute_engine`: GCE metadata
  - `user_credentials`: User auth
  - `default`: ADC

## Example Configurations

### Simple Configuration Example
```json
{
  "pubsub.project.id": "my-project-123",
  "pubsub.topic": "user-events",
  "pubsub.batch.size": 100,
  "pubsub.auth.type": "service_account",
  "pubsub.credentials": "${GOOGLE_CREDENTIALS}"
}
```

### Ordered Messages Example
```json
{
  "pubsub.project.id": "my-project-123",
  "pubsub.topic": "order-events",
  "pubsub.ordering.enabled": true,
  "pubsub.ordering.key": "orderId",
  "pubsub.batch.size": 50,
  "pubsub.batch.timeout.ms": 5000,
  "pubsub.message.attributes": {
    "environment": "production",
    "version": "1.0"
  }
}
```

### High-Throughput Example
```json
{
  "pubsub.project.id": "my-project-123",
  "pubsub.topic": "analytics-events",
  "pubsub.batch.size": 1000,
  "pubsub.batch.bytes": 9437184,
  "pubsub.batch.timeout.ms": 1000,
  "pubsub.compression": "gzip",
  "pubsub.retry.count": 5,
  "pubsub.retry.backoff.ms": 2000
}
```

### Custom Endpoint Example
```json
{
  "pubsub.project.id": "my-project-123",
  "pubsub.topic": "test-events",
  "pubsub.endpoint": "pubsub.googleapis.com:443",
  "pubsub.auth.type": "compute_engine",
  "pubsub.batch.size": 200,
  "pubsub.message.attributes": {
    "source": "nexla",
    "type": "test"
  },
  "pubsub.message.ordering.key": "${timestamp}"
}
```

## Best Practices

1. **Message Design**
   - Size optimization
   - Attribute usage
   - Schema evolution
   - Ordering strategy

2. **Performance Tuning**
   - Batch configuration
   - Compression usage
   - Timeout settings
   - Retry strategy

3. **Error Handling**
   - Retry policy
   - Error logging
   - Dead letter topics
   - Monitoring

4. **Cost Management**
   - Message size
   - Batch optimization
   - Subscription cleanup
   - Usage monitoring

## Common Issues and Solutions

1. **Throughput**
   - Issue: Publishing quota
   - Solution: Batch size, retries

2. **Message Size**
   - Issue: Size limits
   - Solution: Compression, splitting

3. **Authentication**
   - Issue: Credentials
   - Solution: IAM roles, key rotation

4. **Ordering**
   - Issue: Message order
   - Solution: Ordering keys

## Feature-Specific Notes

### Message Ordering
- Key selection
- Performance impact
- Limitations
- Use cases

### Batching
- Size limits
- Timeout configuration
- Memory management
- Error handling

### Compression
- Algorithms
- Size reduction
- CPU usage
- Cost benefits

### Authentication
- Service accounts
- Workload identity
- Key management
- Security best practices

### Monitoring
- Metrics
- Alerting
- Dashboard
- Troubleshooting 