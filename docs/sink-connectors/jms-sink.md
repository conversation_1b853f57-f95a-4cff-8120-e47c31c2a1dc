# JMS Sink Configuration

Confidence Score: Reviewed

## Overview

The JMS (Java Message Service) sink connector enables writing data to JMS destinations (queues and topics). It supports various JMS providers (ActiveMQ, TIBCO EMS), message types, authentication methods, and SSH tunneling for secure access.

> **Tip:** Choose the correct vendor and destination type for your use case. Use SSH tunneling for secure, private deployments.

## Common Configuration Parameters

| Parameter      | Type    | Required | Default | Description |
|----------------|---------|----------|---------|-------------|
| `target.name`  | String  | Yes      | -       | Destination topic or queue name. |
| `jms.vendor`   | String  | Yes      | tibco   | JMS provider: `tibco`, `activemq`. |
| `target.type`  | String  | No       | topic   | Destination type: `queue`, `topic`. |
| `url`          | String  | Yes      | -       | JMS server URL. |
| `username`     | String  | Yes      | -       | Authentication username. |
| `password`     | String  | Yes      | -       | Authentication password. |
| `include.metadata` | Boolean | No   | false   | Include metadata in messages. |
| `read.concurrency` | Integer | No  | 1       | Read concurrency level. |
| `has_ssh_tunnel` | Boolean | No    | false   | Enable SSH tunnel for private servers. |
| `tunnel.bastion.host` | String | No | -      | SSH tunnel host (if enabled). |
| `tunnel.bastion.port` | Number | No | 22     | SSH tunnel port (if enabled). |
| `tunnel.bastion.user` | String | No | nexla  | SSH tunnel username (if enabled). |

### Advanced/Conditional Logic

- **Vendor**: Select `tibco` or `activemq` based on your JMS provider.
- **Target Type**: Use `queue` for point-to-point, `topic` for pub/sub.
- **SSH Tunnel**: Enable for secure, private deployments. Configure host, port, and user as needed.
- **Data Format**: Default is JSON; can be set to CSV, TSV, TXT, XML, or JSON for advanced use cases.

## Example Configurations

### ActiveMQ Queue Example

```json
{
  "target.name": "ORDER.QUEUE",
  "jms.vendor": "activemq",
  "target.type": "queue",
  "url": "tcp://activemq-host:61616",
  "username": "user",
  "password": "password",
  "include.metadata": false,
  "read.concurrency": 1
}
```

### TIBCO Topic Example

```json
{
  "target.name": "EVENT.TOPIC",
  "jms.vendor": "tibco",
  "target.type": "topic",
  "url": "tcp://tibco-host:7222",
  "username": "user",
  "password": "password",
  "include.metadata": true,
  "read.concurrency": 2
}
```

### SSH Tunnel Example

```json
{
  "target.name": "SECURE.QUEUE",
  "jms.vendor": "tibco",
  "target.type": "queue",
  "url": "tcp://private-host:7222",
  "username": "user",
  "password": "password",
  "has_ssh_tunnel": true,
  "tunnel.bastion.host": "bastion.example.com",
  "tunnel.bastion.port": 22,
  "tunnel.bastion.user": "nexla"
}
```

## Best Practices

- Use environment variables for credentials.
- Set appropriate concurrency for your workload.
- Use SSH tunneling for secure, private deployments.
- Monitor message delivery and error logs.
- Choose the correct data format for your downstream consumers.

## Common Issues and Solutions

- **Connection**: Verify URL, credentials, and SSH tunnel settings.
- **Performance**: Adjust read concurrency and monitor resource usage.
- **Authentication**: Ensure correct username/password and permissions.

## Provider-Specific Notes

- **ActiveMQ**: Use `tcp://` URLs, configure vhost and permissions as needed.
- **TIBCO EMS**: Use native TIBCO protocol, advanced security options available.

---

For more advanced configuration options, see the [JMS Example Config](https://github.com/nexla/connector_configs/ref/specs/connectors/split/default/connections/kafka/jms.json).
