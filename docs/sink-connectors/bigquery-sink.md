# BigQuery Sink Configuration

Confidence Score: Unreviewed

## Overview

The BigQuery sink connector enables writing data to Google BigQuery tables. It supports various loading methods, schema evolution, partitioning, clustering, and different authentication mechanisms.

## Configuration Parameters

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `bigquery.database` | String | Yes | - | BigQuery dataset name | [BigQuerySinkConnectorConfig.java#L31](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L31) |
| `bigquery.table` | String | Yes | - | BigQuery table name | [BigQuerySinkConnectorConfig.java#L32](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L32) |
| `bigquery.load.mode` | String | No | BATCH | Load method (STREAMING, BATCH) | [BigQuerySinkConnectorConfig.java#L30](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L30) |
| `bigquery.intermediate.format` | String | No | JSON | Intermediate format (JSON, CSV) | [BigQuerySinkConnectorConfig.java#L33](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L33) |
| `bigquery.max.bad.records` | Integer | No | 100000 | Maximum number of bad records allowed | [BigQuerySinkConnectorConfig.java#L34](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L34) |
| `bigquery.ignore.unknown` | Boolean | No | true | Ignore unknown fields | [BigQuerySinkConnectorConfig.java#L35](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L35) |
| `bigquery.allow.addition.relaxation` | Boolean | No | false | Allow schema updates | [BigQuerySinkConnectorConfig.java#L36](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L36) |
| `bigquery.truncate.before.load` | Boolean | No | false | Truncate table before loading | [BigQuerySinkConnectorConfig.java#L37](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L37) |
| `bigquery.id.fields` | List | No | [] | Identity fields for upserts | [BigQuerySinkConnectorConfig.java#L38](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L38) |
| `bigquery.array.fields` | List | No | [] | Fields to be treated as arrays | [BigQuerySinkConnectorConfig.java#L39](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L39) |
| `bigquery.partitioning.column` | String | No | - | Partitioning field | [BigQuerySinkConnectorConfig.java#L40](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L40) |
| `bigquery.clustering.columns` | List | No | [] | Clustering fields | [BigQuerySinkConnectorConfig.java#L41](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L41) |
| `bigquery.upsert.temp.table` | String | No | - | Temporary table for upserts | [BigQuerySinkConnectorConfig.java#L42](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L42) |
| `bigquery.upsert.merge.query` | String | No | - | Custom merge query for upserts | [BigQuerySinkConnectorConfig.java#L43](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L43) |
| `bigquery.cleanup.temp.tables` | Boolean | No | false | Clean up temporary tables | [BigQuerySinkConnectorConfig.java#L44](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L44) |
| `bigquery.auto.detect` | Boolean | No | true | Auto-detect schema | [BigQuerySinkConnectorConfig.java#L46](/connector-properties/src/main/java/com/nexla/connector/config/big_query/BigQuerySinkConnectorConfig.java#L46) |

### Authentication Parameters

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `bigquery.project.id` | String | Yes | - | Google Cloud project ID | [BigQueryAuthConfig.java](/connector-properties/src/main/java/com/nexla/connector/config/file/BigQueryAuthConfig.java) |
| `bigquery.credentials.json` | String | Yes | - | Service account credentials JSON | [BigQueryAuthConfig.java](/connector-properties/src/main/java/com/nexla/connector/config/file/BigQueryAuthConfig.java) |

## Parameter Details

### Project Configuration

- `bigquery.database`: Dataset name:
  - Must exist
  - Region-specific
  - Example: `analytics_data`

### Table Settings

- `bigquery.table`: Table name:
  - Auto-created if needed
  - Example: `user_events`

### Loading Configuration

- `bigquery.load.mode`: Methods:
  - `STREAMING`: Real-time inserts
  - `BATCH`: Bulk loading
  - Cost vs latency tradeoff

### Schema Management

- `bigquery.allow.addition.relaxation`: Schema changes:
  - Add columns
  - Relax nullability
  - Type promotion
  - No breaking changes

## Example Configurations

### Simple Streaming Example

```json
{
  "bigquery.database": "analytics",
  "bigquery.table": "events",
  "bigquery.load.mode": "STREAMING"
}
```

### Batch Loading Example

```json
{
  "bigquery.database": "warehouse",
  "bigquery.table": "daily_sales",
  "bigquery.load.mode": "BATCH"
}
```

### Partitioned Table Example

```json
{
  "bigquery.database": "logs",
  "bigquery.table": "application_logs",
  "bigquery.load.mode": "STREAMING",
  "bigquery.partitioning.column": "timestamp",
  "bigquery.clustering.columns": ["service", "level"]
}
```

### Merge Configuration Example

```json
{
  "bigquery.database": "customers",
  "bigquery.table": "profiles",
  "bigquery.load.mode": "STREAMING",
  "bigquery.allow.addition.relaxation": true
}
```

## Best Practices

1. **Performance**
   - Batch size optimization
   - Loading method selection
   - Partition strategy
   - Clustering design

2. **Cost Management**
   - Streaming vs batch
   - Storage optimization
   - Query optimization
   - Partition pruning

3. **Schema Evolution**
   - Forward compatibility
   - Type selection
   - Default values
   - Migration strategy

4. **Error Handling**
   - Retry strategy
   - Error logging
   - Data validation
   - Monitoring

## Common Issues and Solutions

1. **Quota Limits**
   - Issue: Rate limiting
   - Solution: Batch size adjustment

2. **Schema Mismatch**
   - Issue: Type conflicts
   - Solution: Schema updates

3. **Performance**
   - Issue: Slow loads
   - Solution: Loading method

4. **Cost**
   - Issue: High costs
   - Solution: Optimize strategy

## Feature-Specific Notes

### Streaming

- Latency considerations
- Deduplication
- Cost per record
- Buffer management

### Batch Loading

- File format options
- Load job monitoring
- Error handling
- Recovery strategy

### Partitioning

- Partition schemes
- Expiration policy
- Query performance
- Cost implications

### Clustering

- Column selection
- Order importance
- Maintenance
- Query patterns
