# Base Sink Configuration

Confidence Score: Reviewed

## Overview

The base sink configuration provides common parameters that apply to all sink connectors. These parameters control core functionality such as sink identification, credentials, tracking, flush behavior, and mapping.

> **Tip:** Use encrypted credentials, set flush parameters based on data volume and latency needs, and monitor tracking for data lineage and troubleshooting.

## Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `sink.id` | Integer | Yes | - | Unique identifier for the sink instance. |
| `dataset.id` | Integer | Yes | - | Dataset that this sink is associated with. |
| `version` | Integer | No | 1 | Data sink version. |
| `creds.enc` | String | No | - | Encrypted credentials string. |
| `creds.enc.iv` | String | No | - | Initialization vector for credential decryption. |
| `creds.id` | Integer | No | -1 | Reference to stored credentials. |
| `mapping` | String | No | - | Output schema mapping (e.g., `$.input -> $.output`). |
| `create.datasource` | Boolean | No | false | Create DataSource for sink. |
| `tracker.encryption.enabled` | Boolean | No | false | Enable encryption for tracking data. |
| `tracker.encryption.key` | String | No | - | Key used for tracker encryption. |
| `fixed.tracker.value` | String | No | - | Static value for 1-to-many row tracking. |
| `node.tag` | String | No | - | Node tag for routing. |
| `flush.cron` | String | No | - | Cron expression for flush schedule. |
| `inactivity.timeout.before.flush.min` | Integer | No | 5 | Flush after N minutes of inactivity. |
| `flush.batch.size` | Integer | No | - | Force flush after N records. |
| `first.flush.delay.min` | Integer | No | 2 | Initial flush delay in minutes. |
| `flush.combine.run.ids` | Boolean | No | false | Combine run IDs in WRITE_DONE event. |
| `cdc.enabled` | Boolean | No | false | Enable Change Data Capture. |
| `pipeline.type` | String | No | - | Pipeline type identifier. |

## Parameter Details

### Identification

- `sink.id`: Unique identifier for the sink instance.
- `dataset.id`: Associates the sink with a specific dataset.
- `version`: Version number for the sink configuration.

### Credentials

- `creds.enc`: Encrypted credentials string. Always use encrypted credentials for security.
- `creds.enc.iv`: Initialization vector for credential decryption.
- `creds.id`: Reference to stored credentials. Prefer using credential IDs over embedded credentials.

### Schema and Mapping

- `mapping`: Defines how input fields map to output fields. Use JSONPath or similar mapping expressions.
- `create.datasource`: Controls automatic DataSource creation for the sink.

### Tracking

- `tracker.encryption.enabled`: Enables encryption for tracking data. Recommended for sensitive data.
- `tracker.encryption.key`: Key used for tracker encryption.
- `fixed.tracker.value`: Static value for 1-to-many row tracking. Use for consistent tracking across batches.

### Flush Control

- `flush.cron`: Schedule-based flush using cron expressions. Use for time-based flushes.
- `inactivity.timeout.before.flush.min`: Time-based flush trigger (minutes of inactivity).
- `flush.batch.size`: Size-based flush trigger (number of records).
- `first.flush.delay.min`: Initial delay before first flush (minutes).
- `flush.combine.run.ids`: Combines multiple run IDs in events for batch processing.

### Advanced Features

- `cdc.enabled`: Enables Change Data Capture functionality for real-time sync.
- `pipeline.type`: Specifies the pipeline processing type (e.g., streaming, batch).
- `node.tag`: Controls routing to specific processing nodes for advanced workflows.

## Example Configuration

```json
{
  "sink.id": 12345,
  "dataset.id": 67890,
  "version": 1,
  "mapping": "$.input -> $.output",
  "flush.cron": "0 */15 * * * ?",
  "flush.batch.size": 1000,
  "inactivity.timeout.before.flush.min": 10,
  "tracker.encryption.enabled": true,
  "tracker.encryption.key": "your-encryption-key",
  "cdc.enabled": true
}
```

## Best Practices

1. **Credentials Management**
   - Always use encrypted credentials and rotate keys periodically.
   - Use credential IDs instead of embedding credentials directly.

2. **Flush Configuration**
   - Set batch sizes and timeouts based on data volume and latency requirements.
   - Use cron expressions for scheduled flushes in predictable workloads.

3. **Tracking**
   - Enable tracker encryption for sensitive or regulated data.
   - Use fixed tracker values for consistent tracking across batches.
   - Monitor tracking events for data lineage and troubleshooting.

4. **Performance**
   - Adjust flush parameters to balance throughput and latency.
   - Use node tags for optimal routing in distributed environments.

5. **Change Data Capture (CDC)**
   - Enable CDC for sinks that require real-time or incremental updates.
   - Monitor CDC events for data consistency.

## Common Issues and Solutions

1. **Flush Timing**
   - Issue: Data not flushing at expected intervals.
   - Solution: Check cron expression, batch size, and timeout settings.

2. **Credential Issues**
   - Issue: Unable to decrypt credentials.
   - Solution: Verify encryption key and IV values; ensure credentials are up to date.

3. **Mapping Problems**
   - Issue: Data not mapping correctly.
   - Solution: Validate mapping expression syntax and field names.

4. **Performance**
   - Issue: High latency during flushes.
   - Solution: Adjust batch sizes, timeouts, and review node tag routing.

---

> **Tips:**
>
> - Use the least privileged credentials required.
> - Test flush and mapping settings with a small dataset before scaling up.
> - For advanced scenarios, contact Nexla support for guidance.
