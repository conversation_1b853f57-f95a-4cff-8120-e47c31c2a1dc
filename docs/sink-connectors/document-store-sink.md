# Document Store Sink Connectors

Confidence Score: Reviewed

## Overview

Document store sink connectors allow you to write data to various document-oriented and NoSQL databases. These connectors support:

- MongoDB
- DynamoDB
- Firebase
- Flexible schema handling
- Batch and streaming writes
- High-throughput options

> **Tip:** Use high-throughput options for large data volumes, but monitor billing and consult your Account Manager if unsure.

## Common Configuration Parameters

| Parameter         | Type   | Required | Default | Description |
|-------------------|--------|----------|---------|-------------|
| `database`        | String | Yes      | -       | Target database name. For MongoDB, this is the database to write to. |
| `collection`      | String | Yes      | -       | Target collection/table name. |
| `hc_scale_factor` | String | No       | 1       | **Speed Factor:** Controls pipeline output speed. Options: `1x`, `2x`, `4x`, `8x`, `16x`, `32x`. Higher values increase throughput and cost. |

### Field Details & Best Practices

- **database**: Must match the target database in your NoSQL instance. For MongoDB Atlas, use the database name from your connection string.
- **collection**: The collection/table to write to. Ensure it exists or the connector has permission to create it.
- **hc_scale_factor**: Use higher values for large or time-sensitive data loads. Default is `1x`. Increasing this will increase infrastructure usage and billing.

### Example: MongoDB Sink

```json
{
  "database": "analytics",
  "collection": "events",
  "hc_scale_factor": "4"
}
```

### Example: DynamoDB Sink

```json
{
  "database": "my-dynamodb-db",
  "collection": "user_sessions",
  "hc_scale_factor": "2"
}
```

### Example: Firebase Sink

```json
{
  "database": "firebase-db",
  "collection": "logs",
  "hc_scale_factor": "1"
}
```

### Advanced/Conditional Logic

- **Speed Factor**: Only increase if you need higher throughput. Monitor for throttling or cost spikes.
- **Permissions**: Ensure the connector has write permissions to the target collection/table.

### Best Practices

- Use descriptive collection names for traceability.
- For high-throughput or streaming use cases, test with a small batch before scaling up.
- Monitor connector logs for errors or write failures.

---

For more advanced configuration options, see the [MongoDB Source Example Config](https://github.com/nexla/connector_configs/ref/specs/connectors/split/default/connections/nosqldoc/mongo.json).

## Configuration Parameters

The following parameters can be used directly without any prefix:

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `id.fields` | List | No | [] | List of fields to use as document ID | [DocumentDbConfigAccessor.java#L17](/connector-properties/src/main/java/com/nexla/connector/properties/DocumentDbConfigAccessor.java#L17), [DocumentDbSinkConnectorConfig.java#L28](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DocumentDbSinkConnectorConfig.java#L28) |
| `insert.mode` | String | No | insert | Document insert mode (insert, upsert) | [DocumentDbConfigAccessor.java#L18-19](/connector-properties/src/main/java/com/nexla/connector/properties/DocumentDbConfigAccessor.java#L18-L19), [DocumentDbSinkConnectorConfig.java#L29](/connector-properties/src/main/java/com/nexla/connector/config/documentdb/DocumentDbSinkConnectorConfig.java#L29) |
| `batch.size` | Integer | No | - | Number of records to batch before writing | [DocumentDbConfigAccessor.java#L4](/connector-properties/src/main/java/com/nexla/connector/properties/DocumentDbConfigAccessor.java#L4) |

## Parameter Details

### Collection Settings

- `collection`: Target collection name
  - Case-sensitive
  - Must have appropriate permissions
  - Auto-created if doesn't exist

### Document ID Management

- `id.fields`: Fields to use as document ID
  - Empty list means auto-generated IDs
  - Multiple fields will be combined
  - Case-sensitive field names
  - Non-existent fields are ignored

### Write Operations

- `insert.mode`: Available modes:
  - `insert`: Insert new documents only
  - `upsert`: Insert or update based on ID fields

- `batch.size`: Batch operation control:
  - Number of records to accumulate before writing
  - Affects performance and memory usage
  - Larger batches generally better performance
  - Must balance with memory constraints

## Example Configurations

### Basic MongoDB Example

```json
{
  "collection": "mycollection",
  "insert.mode": "insert",
  "batch.size": 1000
}
```

### Upsert with Custom ID Example

```json
{
  "collection": "customers",
  "id.fields": ["customer_id", "region"],
  "insert.mode": "upsert",
  "batch.size": 500
}
```

### High Performance Example

```json
{
  "collection": "logs",
  "insert.mode": "insert",
  "batch.size": 5000
}
```

## Best Practices

1. **Collection Management**
   - Use clear naming conventions
   - Consider data volume
   - Plan for scaling
   - Monitor collection size

2. **Document IDs**
   - Choose appropriate ID fields
   - Consider uniqueness constraints
   - Plan for data distribution
   - Avoid sequential IDs if possible

3. **Performance Optimization**
   - Adjust batch size appropriately
   - Monitor memory usage
   - Consider network latency
   - Test with production volumes

4. **Error Handling**
   - Monitor failed operations
   - Implement retry logic
   - Log errors appropriately
   - Set up alerts

## Common Issues and Solutions

1. **Connection Issues**
   - Issue: Connection timeouts
   - Solution: Check network, adjust timeouts

2. **Performance**
   - Issue: Slow write speeds
   - Solution: Adjust batch size, check indexes

3. **Memory Usage**
   - Issue: High memory consumption
   - Solution: Reduce batch size

4. **Document Size**
   - Issue: Document too large
   - Solution: Check size limits, split data

## Database-Specific Notes

### MongoDB

- Document size limit: 16MB
- Index key limit: 1024 bytes
- Collection naming rules
- Sharding considerations

### Performance Considerations

- Index usage
- Batch size impact
- Network latency
- Memory usage

### Security

- Authentication options
- Collection permissions
- Audit logging
