# Spreadsheet Sink Configuration

Confidence Score: Unreviewed

## Overview

The Spreadsheet sink connector enables writing data to various spreadsheet formats including Google Sheets, Microsoft Excel (XLSX), and CSV files. It supports multiple worksheets, formatting options, and cell customization.

## Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `spreadsheet.type` | String | Yes | - | Spreadsheet type (google_sheets, excel, csv) |
| `spreadsheet.file.path` | String | Yes | - | File path or spreadsheet ID |
| `spreadsheet.worksheet` | String | No | Sheet1 | Worksheet/tab name |
| `spreadsheet.range` | String | No | - | Cell range (A1 notation) |
| `spreadsheet.header.row` | Boolean | No | true | Include header row |
| `spreadsheet.start.row` | Integer | No | 1 | Starting row number |
| `spreadsheet.start.column` | String | No | A | Starting column letter |
| `spreadsheet.format` | Map | No | {} | Cell formatting options |
| `spreadsheet.auth.type` | String | No | service_account | Authentication type |
| `spreadsheet.auth.credentials` | String | No | - | Authentication credentials |
| `spreadsheet.batch.size` | Integer | No | 1000 | Batch size for writes |
| `spreadsheet.append.mode` | String | No | overwrite | Write mode (overwrite, append) |
| `spreadsheet.create.if.missing` | Boolean | No | false | Create if not exists |
| `spreadsheet.retry.count` | Integer | No | 3 | Number of retry attempts |
| `spreadsheet.timeout.ms` | Long | No | 30000 | Operation timeout |

## Parameter Details

### Basic Configuration

- `spreadsheet.type`: Supported types:
  - `google_sheets`: Google Sheets
  - `excel`: Microsoft Excel
  - `csv`: CSV files

- `spreadsheet.file.path`: Location:
  - Google Sheets ID
  - Excel file path
  - CSV file path

### Worksheet Settings

- `spreadsheet.worksheet`: Sheet name:
  - Tab/worksheet name
  - Created if not exists
  - Example: `Sales Data`

- `spreadsheet.range`: Cell range:
  - A1 notation
  - Examples:
    - `A1:D10`
    - `Sheet1!A1:D10`
    - `Named Range`

### Format Options

- `spreadsheet.format`: Cell formatting:
  - Number format
  - Font styles
  - Colors
  - Borders
  - Alignment

### Authentication

- `spreadsheet.auth.type`: Auth methods:
  - `service_account`: Service account
  - `oauth2`: OAuth 2.0
  - `api_key`: API key
  - `none`: No auth (local files)

## Example Configurations

### Google Sheets Example

```json
{
  "spreadsheet.type": "google_sheets",
  "spreadsheet.file.path": "1abc...xyz",
  "spreadsheet.worksheet": "Sales Data",
  "spreadsheet.range": "A1:F",
  "spreadsheet.header.row": true,
  "spreadsheet.auth.type": "service_account",
  "spreadsheet.auth.credentials": "${GOOGLE_CREDENTIALS}",
  "spreadsheet.format": {
    "numberFormat": {
      "type": "CURRENCY",
      "pattern": "$#,##0.00"
    },
    "backgroundColor": {
      "red": 0.9,
      "green": 0.9,
      "blue": 0.9
    }
  }
}
```

### Excel Example

```json
{
  "spreadsheet.type": "excel",
  "spreadsheet.file.path": "/path/to/sales.xlsx",
  "spreadsheet.worksheet": "Monthly Report",
  "spreadsheet.start.row": 2,
  "spreadsheet.header.row": true,
  "spreadsheet.batch.size": 500,
  "spreadsheet.append.mode": "append",
  "spreadsheet.format": {
    "font": {
      "bold": true,
      "color": "RED"
    },
    "border": {
      "style": "THIN",
      "color": "BLACK"
    }
  }
}
```

### CSV Example

```json
{
  "spreadsheet.type": "csv",
  "spreadsheet.file.path": "/path/to/data.csv",
  "spreadsheet.header.row": true,
  "spreadsheet.append.mode": "overwrite",
  "spreadsheet.format": {
    "delimiter": ",",
    "quoteChar": "\"",
    "escapeChar": "\\",
    "dateFormat": "yyyy-MM-dd"
  }
}
```

### Advanced Google Sheets Example

```json
{
  "spreadsheet.type": "google_sheets",
  "spreadsheet.file.path": "1abc...xyz",
  "spreadsheet.worksheet": "Financial Data",
  "spreadsheet.range": "A1:H",
  "spreadsheet.header.row": true,
  "spreadsheet.batch.size": 200,
  "spreadsheet.auth.type": "service_account",
  "spreadsheet.auth.credentials": "${GOOGLE_CREDENTIALS}",
  "spreadsheet.format": {
    "alternatingRowStyle": true,
    "headerStyle": {
      "backgroundColor": {
        "red": 0.2,
        "green": 0.3,
        "blue": 0.4
      },
      "textFormat": {
        "foregroundColor": {
          "red": 1,
          "green": 1,
          "blue": 1
        },
        "bold": true
      }
    },
    "dataValidation": {
      "condition": {
        "type": "NUMBER_GREATER",
        "values": [{"userEnteredValue": "0"}]
      }
    }
  }
}
```

## Best Practices

1. **Performance**
   - Use batch writes
   - Minimize API calls
   - Cache credentials
   - Monitor quotas

2. **Data Formatting**
   - Consistent formats
   - Header alignment
   - Data validation
   - Error handling

3. **Authentication**
   - Secure credentials
   - Token management
   - Permission scopes
   - Audit access

4. **Error Handling**
   - Retry strategy
   - Data validation
   - Backup options
   - Recovery plan

## Common Issues and Solutions

1. **Rate Limits**
   - Issue: API quota exceeded
   - Solution: Adjust batch size, implement backoff

2. **Authentication**
   - Issue: Token expiration
   - Solution: Refresh tokens, check permissions

3. **Data Format**
   - Issue: Invalid cell values
   - Solution: Validate data types, handle nulls

4. **Performance**
   - Issue: Slow writes
   - Solution: Optimize batch size, reduce API calls

## Platform-Specific Notes

### Google Sheets

- API quotas
- Service accounts
- Sharing settings
- Formula handling

### Excel

- File locking
- Memory usage
- Formula calculation
- Template support

### CSV

- Encoding issues
- Delimiter handling
- Escape characters
- Line endings
