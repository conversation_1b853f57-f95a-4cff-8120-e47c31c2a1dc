# Sink Connector Configurations

## Overview

Nexla supports multiple sink connectors for writing data to various destinations. Each sink connector has its own set of configuration parameters to control how data is written and managed.

## Common Sink Types

### Base Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `sink.id` | Integer | Yes | - | Unique identifier for the sink |
| `dataset.id` | Integer | Yes | - | Dataset that this sink is associated with |
| `version` | Integer | No | 1 | Data sink version |
| `creds.enc` | String | No | - | Encrypted credentials |
| `creds.enc.iv` | String | No | - | Initialization vector for encrypted credentials |
| `creds.id` | Integer | No | -1 | Credentials identifier |
| `mapping` | String | No | - | Output schema mapping |
| `create.datasource` | Boolean | No | false | Create DataSource for sink |
| `tracker.encryption.enabled` | Boolean | No | false | Enable tracker encryption |
| `tracker.encryption.key` | String | No | - | Encryption key for tracker |
| `fixed.tracker.value` | String | No | - | Fixed tracker value for 1-to-many rows |
| `node.tag` | String | No | - | Node tag for routing |
| `flush.cron` | String | No | - | Cron expression for flush schedule |
| `inactivity.timeout.before.flush.min` | Integer | No | 5 | Flush after N minutes of inactivity |
| `flush.batch.size` | Integer | No | - | Force flush after N records |
| `first.flush.delay.min` | Integer | No | 2 | Initial flush delay in minutes |
| `flush.combine.run.ids` | Boolean | No | false | Combine run IDs in WRITE_DONE event |
| `cdc.enabled` | Boolean | No | false | Enable Change Data Capture |
| `pipeline.type` | String | No | - | Pipeline type identifier |

### JDBC Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `connection.url` | String | Yes | - | JDBC connection URL |
| `connection.user` | String | Yes | - | Database username |
| `connection.password` | String | Yes | - | Database password |
| `table.name.format` | String | No | %s | Table name format |
| `batch.size` | Integer | No | 3000 | Batch size for inserts |
| `max.retries` | Integer | No | 3 | Maximum retry attempts |
| `retry.backoff.ms` | Long | No | 1000 | Retry backoff in milliseconds |
| `auto.create` | Boolean | No | false | Auto-create tables |
| `auto.evolve` | Boolean | No | false | Auto-evolve table schema |
| `insert.mode` | String | No | insert | Insert mode (insert, upsert, update) |
| `pk.mode` | String | No | none | Primary key mode |
| `pk.fields` | String | No | - | Primary key field names |
| `delete.enabled` | Boolean | No | false | Enable DELETE operations |

### File Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `file.path` | String | Yes | - | Output file path |
| `file.name.template` | String | No | - | Template for output filenames |
| `file.compression` | String | No | none | Compression type (none, gzip) |
| `file.max.records` | Integer | No | - | Maximum records per file |
| `file.rotation.time.ms` | Long | No | - | File rotation interval |
| `file.rotation.size.bytes` | Long | No | - | File size threshold for rotation |
| `file.overwrite` | Boolean | No | false | Overwrite existing files |
| `file.flush.records` | Integer | No | 1000 | Records before flush |

### Document Store Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `connection.uri` | String | Yes | - | Connection URI |
| `database` | String | Yes | - | Database name |
| `collection` | String | Yes | - | Collection name |
| `write.concern` | String | No | w1 | Write concern level |
| `bulk.enabled` | Boolean | No | true | Enable bulk operations |
| `bulk.size` | Integer | No | 1000 | Bulk operation size |
| `ordered.bulk` | Boolean | No | true | Ordered bulk operations |
| `document.id.strategy` | String | No | none | Document ID strategy |
| `max.retries` | Integer | No | 3 | Maximum retry attempts |

### Messaging Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `topic` | String | Yes | - | Target topic/queue name |
| `key.field` | String | No | - | Field to use as message key |
| `partition.field` | String | No | - | Field for partitioning |
| `message.format` | String | No | json | Message format |
| `compression.type` | String | No | none | Message compression type |
| `batch.size` | Integer | No | 16384 | Producer batch size |
| `linger.ms` | Long | No | 0 | Producer linger time |
| `max.request.size` | Integer | No | 1048576 | Maximum request size |
| `retries` | Integer | No | 3 | Number of retries |

### REST Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `rest.url` | String | Yes | - | REST endpoint URL |
| `rest.method` | String | No | POST | HTTP method |
| `rest.headers` | Map | No | {} | HTTP headers |
| `rest.body.template` | String | No | - | Request body template |
| `rest.batch.size` | Integer | No | 1 | Batch size for requests |
| `rest.retry.count` | Integer | No | 3 | Number of retry attempts |
| `rest.retry.backoff.ms` | Long | No | 1000 | Retry backoff in milliseconds |
| `rest.timeout.ms` | Long | No | 30000 | Request timeout |

### SOAP Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `soap.wsdl.url` | String | Yes | - | WSDL URL |
| `soap.operation` | String | Yes | - | SOAP operation name |
| `soap.headers` | Map | No | {} | SOAP headers |
| `soap.body.template` | String | No | - | Request body template |
| `soap.version` | String | No | 1.1 | SOAP version |
| `soap.retry.count` | Integer | No | 3 | Number of retry attempts |

### Vector Database Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `vector.connection.url` | String | Yes | - | Vector DB connection URL |
| `vector.index` | String | Yes | - | Vector index name |
| `vector.dimension` | Integer | Yes | - | Vector dimension size |
| `vector.metric` | String | No | cosine | Distance metric type |
| `vector.batch.size` | Integer | No | 100 | Batch size for inserts |
| `vector.field` | String | Yes | - | Field containing vector data |

### Spreadsheet Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `spreadsheet.id` | String | Yes | - | Google Spreadsheet ID |
| `sheet.name` | String | No | Sheet1 | Target sheet name |
| `range` | String | No | A1 | Starting cell range |
| `append.mode` | Boolean | No | true | Append vs overwrite mode |
| `include.header` | Boolean | No | true | Include header row |
| `date.format` | String | No | yyyy-MM-dd | Date format string |

### Iceberg Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `warehouse.path` | String | Yes | - | Iceberg warehouse path |
| `table.name` | String | Yes | - | Target table name |
| `write.format` | String | No | parquet | File format (parquet, avro, orc) |
| `partition.by` | String | No | - | Partition field names |
| `commit.interval` | Long | No | 60000 | Commit interval in ms |
| `write.properties` | Map | No | {} | Additional write properties |

### Redis Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `redis.host` | String | Yes | - | Redis host |
| `redis.port` | Integer | No | 6379 | Redis port |
| `redis.password` | String | No | - | Redis password |
| `redis.database` | Integer | No | 0 | Redis database number |
| `key.pattern` | String | Yes | - | Key generation pattern |
| `value.serializer` | String | No | string | Value serialization format |
| `ttl.ms` | Long | No | - | Key TTL in milliseconds |

### BigQuery Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `project.id` | String | Yes | - | GCP project ID |
| `dataset` | String | Yes | - | BigQuery dataset |
| `table` | String | Yes | - | Target table name |
| `location` | String | No | US | Dataset location |
| `auto.create` | Boolean | No | false | Auto-create tables |
| `batch.size` | Integer | No | 1000 | Batch size for inserts |
| `write.disposition` | String | No | WRITE_APPEND | Write disposition |

### Kinesis Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `stream.name` | String | Yes | - | Kinesis stream name |
| `region` | String | Yes | - | AWS region |
| `partition.key.field` | String | No | - | Partition key field |
| `batch.size` | Integer | No | 500 | Records per batch |
| `batch.timeout.ms` | Long | No | 5000 | Batch timeout |
| `max.connections` | Integer | No | 24 | Max connections |

### PubSub Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `project.id` | String | Yes | - | GCP project ID |
| `topic.id` | String | Yes | - | PubSub topic ID |
| `ordering.key.field` | String | No | - | Message ordering key field |
| `batch.size` | Integer | No | 100 | Messages per batch |
| `retry.count` | Integer | No | 3 | Number of retries |
| `message.ordering` | Boolean | No | false | Enable message ordering |

### JMS Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `jms.url` | String | Yes | - | JMS broker URL |
| `destination.name` | String | Yes | - | Queue/Topic name |
| `destination.type` | String | No | queue | Destination type (queue/topic) |
| `delivery.mode` | Integer | No | 2 | Delivery mode (1=non-persistent, 2=persistent) |
| `message.type` | String | No | text | Message type (text, bytes, map) |
| `priority` | Integer | No | 4 | Message priority (0-9) |

## Examples

### JDBC Sink Example

```json
{
  "connection.url": "*************************************",
  "connection.user": "user",
  "connection.password": "password",
  "table.name.format": "schema.%s",
  "batch.size": 5000,
  "auto.create": true,
  "insert.mode": "upsert",
  "pk.mode": "fields",
  "pk.fields": "id,version"
}
```

### REST Sink Example

```json
{
  "rest.url": "https://api.example.com/data",
  "rest.method": "POST",
  "rest.headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer ${token}"
  },
  "rest.body.template": "{\"data\": ${record}}",
  "rest.batch.size": 10,
  "rest.retry.count": 5
}
```

### Vector Database Sink Example

```json
{
  "vector.connection.url": "http://localhost:6333",
  "vector.index": "products",
  "vector.dimension": 1536,
  "vector.metric": "cosine",
  "vector.batch.size": 100,
  "vector.field": "embeddings"
}
```

### BigQuery Sink Example

```json
{
  "project.id": "my-project",
  "dataset": "my_dataset",
  "table": "my_table",
  "location": "US",
  "auto.create": true,
  "batch.size": 1000,
  "write.disposition": "WRITE_APPEND"
}
```

### Iceberg Sink Example

```json
{
  "warehouse.path": "s3://warehouse/",
  "table.name": "db.table",
  "write.format": "parquet",
  "partition.by": "date,region",
  "commit.interval": 120000,
  "write.properties": {
    "write.format.default": "parquet",
    "write.parquet.compression-codec": "snappy"
  }
}
```

### PubSub Sink Example

```json
{
  "project.id": "my-project",
  "topic.id": "my-topic",
  "ordering.key.field": "timestamp",
  "batch.size": 200,
  "retry.count": 5,
  "message.ordering": true
}
```
