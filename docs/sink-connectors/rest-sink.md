# REST Sink Configuration

Confidence Score: Unreviewed

## Overview

The REST sink connector enables writing data to HTTP/REST endpoints. It supports various HTTP methods, authentication mechanisms, custom headers, and request templating for flexible integration with REST APIs.

## Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `rest.url` | String | Yes | - | REST endpoint URL |
| `rest.method` | String | No | POST | HTTP method |
| `rest.headers` | Map | No | {} | HTTP headers |
| `rest.body.template` | String | No | - | Request body template |
| `rest.batch.size` | Integer | No | 1 | Batch size for requests |
| `rest.retry.count` | Integer | No | 3 | Number of retry attempts |
| `rest.retry.backoff.ms` | Long | No | 1000 | Retry backoff in milliseconds |
| `rest.timeout.ms` | Long | No | 30000 | Request timeout |
| `rest.auth.type` | String | No | none | Authentication type |
| `rest.auth.token` | String | No | - | Authentication token |
| `rest.ssl.verify` | Boolean | No | true | Verify SSL certificates |
| `rest.proxy.url` | String | No | - | Proxy server URL |
| `rest.rate.limit` | Integer | No | 0 | Rate limit (requests/second) |
| `rest.success.codes` | String | No | 200-299 | Success status codes |

## Parameter Details

### Endpoint Configuration

- `rest.url`: Target endpoint URL:
  - Supports HTTPS and HTTP
  - Variable substitution
  - Example: `https://api.example.com/data`

- `rest.method`: HTTP methods:
  - `GET`: Retrieve data
  - `POST`: Create data
  - `PUT`: Update data
  - `PATCH`: Partial update
  - `DELETE`: Remove data

### Request Customization

- `rest.headers`: Custom headers:

  ```json
  {
    "Content-Type": "application/json",
    "Authorization": "Bearer ${token}",
    "Custom-Header": "value"
  }
  ```

- `rest.body.template`: Request template:
  - Supports JSON templates
  - Variable substitution
  - Example: `{"data": ${record}, "timestamp": "${timestamp}"}`

### Performance

- `rest.batch.size`: Records per request:
  - Single vs batch requests
  - API limits consideration
  - Memory usage impact

- `rest.timeout.ms`: Timeout settings:
  - Connection timeout
  - Read timeout
  - Write timeout

### Authentication

- `rest.auth.type`: Auth methods:
  - `none`: No authentication
  - `basic`: Basic auth
  - `bearer`: Bearer token
  - `oauth2`: OAuth 2.0
  - `custom`: Custom auth

- `rest.auth.token`: Auth credentials:
  - Bearer tokens
  - API keys
  - OAuth tokens

### Error Handling

- `rest.retry.count`: Retry strategy:
  - Number of attempts
  - Failure conditions
  - Success criteria

- `rest.success.codes`: Valid responses:
  - Status code ranges
  - Custom success codes
  - Error handling

## Example Configurations

### Basic REST API Example

```json
{
  "rest.url": "https://api.example.com/data",
  "rest.method": "POST",
  "rest.headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer ${token}"
  },
  "rest.body.template": "{\"data\": ${record}}",
  "rest.retry.count": 3,
  "rest.timeout.ms": 5000
}
```

### Batch Upload Example

```json
{
  "rest.url": "https://api.example.com/batch",
  "rest.method": "POST",
  "rest.headers": {
    "Content-Type": "application/json",
    "API-Key": "${api_key}"
  },
  "rest.body.template": "{\"records\": ${records}, \"batch_id\": \"${uuid}\"}",
  "rest.batch.size": 100,
  "rest.retry.count": 5,
  "rest.rate.limit": 10
}
```

### OAuth2 Authentication Example

```json
{
  "rest.url": "https://api.secure.com/v1/data",
  "rest.method": "PUT",
  "rest.auth.type": "oauth2",
  "rest.auth.token": "${oauth_token}",
  "rest.headers": {
    "Content-Type": "application/json"
  },
  "rest.ssl.verify": true,
  "rest.timeout.ms": 10000
}
```

### Custom Error Handling Example

```json
{
  "rest.url": "https://api.example.com/data",
  "rest.method": "POST",
  "rest.success.codes": "200,201,202",
  "rest.retry.count": 3,
  "rest.retry.backoff.ms": 2000,
  "rest.proxy.url": "http://proxy:8080"
}
```

## Best Practices

1. **Authentication**
   - Use secure authentication
   - Rotate credentials regularly
   - Implement token refresh
   - Use environment variables

2. **Error Handling**
   - Define retry strategies
   - Log failed requests
   - Monitor error rates
   - Handle rate limits

3. **Performance**
   - Optimize batch sizes
   - Monitor response times
   - Handle timeouts
   - Use compression

4. **Security**
   - Use HTTPS
   - Verify SSL certificates
   - Secure sensitive data
   - Follow API guidelines

## Common Issues and Solutions

1. **Authentication Failures**
   - Issue: Invalid credentials
   - Solution: Check token validity, refresh mechanism

2. **Rate Limiting**
   - Issue: Too many requests
   - Solution: Implement backoff, adjust batch size

3. **Timeout Issues**
   - Issue: Slow responses
   - Solution: Adjust timeouts, check network

4. **Data Format**
   - Issue: Invalid payload
   - Solution: Validate templates, check content type

## API-Specific Notes

### REST Best Practices

- Idempotency keys
- Request signing
- Content negotiation
- Versioning

### Security Considerations

- API key management
- OAuth flows
- IP restrictions
- Request logging

### Monitoring

- Response times
- Error rates
- Success rates
- Data throughput

### Compliance

- Data privacy
- Rate limits
- API terms
- Documentation
