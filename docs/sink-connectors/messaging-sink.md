# Messaging Sink Configuration

Confidence Score: Reviewed

## Overview

The Messaging sink connector enables writing data to message brokers and queuing systems like Apache Kafka, Amazon MSK, RabbitMQ, and ActiveMQ. It supports various message formats, partitioning strategies, delivery guarantees, security protocols, and authentication methods (including SASL, SSL, and SSH tunneling).

> **Tip:** Choose the right security protocol and authentication for your environment. Use batching and compression for high-throughput workloads.

## Common Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `topic` | String | Yes | - | Target topic/queue name. |
| `key.field` | String | No | - | Field to use as message key. |
| `partition.field` | String | No | - | Field for partitioning. |
| `message.format` | String | No | json | Message format: `json`, `avro`, `protobuf`, `string`, `bytes`. |
| `compression.type` | String | No | none | Compression: `none`, `gzip`, `snappy`, `lz4`, `zstd`. |
| `batch.size` | Integer | No | 16384 | Producer batch size (bytes). |
| `linger.ms` | Long | No | 0 | Producer linger time (ms). |
| `max.request.size` | Integer | No | 1048576 | Maximum request size (bytes). |
| `retries` | Integer | No | 3 | Number of retries. |
| `acks` | String | No | all | Acknowledgment: `0`, `1`, `all`. |
| `delivery.timeout.ms` | Integer | No | 120000 | Delivery timeout (ms). |
| `max.in.flight.requests.per.connection` | Integer | No | 5 | Max parallel requests. |
| `enable.idempotence` | Boolean | No | false | Enable idempotent producer. |
| `transactional.id` | String | No | - | Transaction identifier. |
| `security.protocol` | String | No | PLAINTEXT | Security protocol: `PLAINTEXT`, `SASL_PLAINTEXT`, `SASL_SSL`, `SSL`. |
| `sasl.jaas.config` | String | No | - | SASL JAAS config for authentication. |
| `has_ssh_tunnel` | Boolean | No | false | Enable SSH tunnel for private brokers. |
| `tunnel.bastion.host` | String | No | - | SSH tunnel host (if enabled). |
| `tunnel.bastion.port` | Number | No | 22 | SSH tunnel port (if enabled). |
| `tunnel.bastion.user` | String | No | nexla | SSH tunnel username (if enabled). |

### Advanced/Conditional Logic

- **Security Protocol**: Use `SASL_SSL` or `SSL` for encrypted/authenticated channels. Use `PLAINTEXT` only for trusted networks.
- **SASL Authentication**: Provide `sasl.jaas.config` for SASL mechanisms. Required for `SASL_PLAINTEXT` and `SASL_SSL`.
- **SSH Tunnel**: Enable for secure, private deployments. Configure host, port, and user as needed.
- **Batching/Compression**: Use larger batches and compression for high-throughput, cost-sensitive workloads.

## Example Configurations

### Kafka Secure Example

```json
{
  "topic": "secure-topic",
  "key.field": "user.id",
  "message.format": "json",
  "compression.type": "gzip",
  "security.protocol": "SASL_SSL",
  "sasl.jaas.config": "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"user\" password=\"pass\";",
  "acks": "all",
  "retries": 5
}
```

### Kafka SSH Tunnel Example

```json
{
  "topic": "private-topic",
  "key.field": "session.id",
  "message.format": "avro",
  "compression.type": "snappy",
  "has_ssh_tunnel": true,
  "tunnel.bastion.host": "bastion.example.com",
  "tunnel.bastion.port": 22,
  "tunnel.bastion.user": "nexla"
}
```

### Kafka High Throughput Example

```json
{
  "topic": "high-volume-topic",
  "key.field": "user.id",
  "partition.field": "region",
  "message.format": "avro",
  "compression.type": "snappy",
  "batch.size": 131072,
  "linger.ms": 100,
  "acks": "1",
  "retries": 3
}
```

### Kafka Exactly-Once Example

```json
{
  "topic": "critical-data-topic",
  "key.field": "transaction.id",
  "message.format": "json",
  "compression.type": "zstd",
  "enable.idempotence": true,
  "transactional.id": "tx-producer-1",
  "acks": "all",
  "retries": 5
}
```

### RabbitMQ Example

```json
{
  "topic": "orders.queue",
  "message.format": "json",
  "batch.size": 100,
  "delivery.timeout.ms": 30000,
  "retries": 3
}
```

### ActiveMQ Example

```json
{
  "topic": "events.topic",
  "key.field": "eventType",
  "message.format": "string",
  "batch.size": 50,
  "delivery.timeout.ms": 60000
}
```

## Best Practices

- Use secure protocols and authentication for production.
- Use batching and compression for high-throughput workloads.
- Set appropriate acks and retries for reliability.
- Monitor broker metrics and adjust configuration as needed.
- Test with small batches before scaling up.

## Common Issues and Solutions

- **Message Loss**: Increase acks, enable idempotence, and monitor delivery.
- **High Latency**: Tune batch size and linger.ms.
- **Duplicate Messages**: Enable idempotence, set transactional.id.
- **Resource Usage**: Adjust batch size, compression, and monitor system metrics.

## Broker-Specific Notes

- **Kafka**: Use partitioning, replication, and consumer groups for scale and reliability. Configure security and authentication as required.
- **RabbitMQ**: Use durable queues, dead letter exchanges, and flow control for reliability.
- **ActiveMQ**: Choose between queue and topic, configure persistence and network of brokers.

---

For more advanced configuration options, see the [Kafka Example Config](https://github.com/nexla/connector_configs/ref/specs/connectors/split/default/connections/kafka/kafka.json).
