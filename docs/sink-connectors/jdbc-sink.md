# JDBC Sink Configuration

Confidence Score: Reviewed

## Overview

The JDBC sink connector enables writing data to any database that supports JDBC connections. This includes popular databases like PostgreSQL, MySQL, Oracle, SQL Server, and more. Supports batch inserts, upserts, schema evolution, and high-throughput options.

> **Tip:** Use upsert mode for CDC or deduplication. Use batch size and parallelism for high-throughput scenarios.

## Common Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `connection.url` | String | Yes | - | JDBC connection URL. Format varies by database. |
| `connection.user` | String | Yes | - | Database username. |
| `connection.password` | String | Yes | - | Database password. |
| `database` | String | No | - | Target database name. |
| `table` | String | Yes | - | Target table name. |
| `insert.mode` | String | No | insert | Insert mode: `insert`, `upsert`. |
| `primary.key` | String | No | - | Comma-separated primary key fields (required for upsert). |
| `batch.size` | Integer | No | 1000 | Batch size for inserts. |
| `parallelism` | Integer | No | 1 | Number of parallel upload threads. |
| `truncate.before.load` | Boolean | No | false | Truncate table before loading. |
| `auto.create` | Boolean | No | false | Auto-create tables if not present. |
| `auto.evolve` | Boolean | No | false | Auto-evolve table schema. |
| `hc_scale_factor` | String | No | 1 | Speed Factor: `1x`, `2x`, `4x`, `8x`, `16x`, `32x`. Higher values increase throughput and cost. |

### Advanced/Conditional Logic

- **Insert Mode**: Use `insert` for append-only. Use `upsert` for CDC or deduplication. Set `primary.key` for upserts.
- **Batch Size**: Larger batches improve performance but increase memory usage. Tune based on workload.
- **Parallelism**: Increase for higher throughput, but monitor DB load.
- **Truncate Before Load**: Use for full refresh scenarios.
- **Auto-create/Evolve**: Enable for dynamic schema management. Test in non-production first.
- **Speed Factor**: Increase for high-throughput, but monitor for cost and DB resource usage.

## Example Configurations

### PostgreSQL Example

```json
{
  "connection.url": "*************************************",
  "connection.user": "user",
  "connection.password": "password",
  "table": "public.sales",
  "insert.mode": "upsert",
  "primary.key": "id,version",
  "batch.size": 5000,
  "auto.create": true,
  "hc_scale_factor": "4"
}
```

### MySQL Example

```json
{
  "connection.url": "********************************",
  "connection.user": "user",
  "connection.password": "password",
  "table": "sales",
  "insert.mode": "insert",
  "batch.size": 3000,
  "auto.create": true,
  "auto.evolve": true
}
```

### Oracle Example

```json
{
  "connection.url": "*************************************",
  "connection.user": "user",
  "connection.password": "password",
  "table": "SALES",
  "insert.mode": "update",
  "primary.key": "id"
}
```

## Best Practices

- Use upsert mode for CDC or deduplication scenarios.
- Set primary keys for upserts to avoid duplicate errors.
- Adjust batch size and parallelism for optimal throughput.
- Enable auto-create/evolve for dynamic schemas, but monitor changes.
- Test with small batches before scaling up.

## Common Issues and Solutions

- **Permission Issues**: Check DB credentials and user privileges.
- **Performance**: Adjust batch size, parallelism, and speed factor.
- **Schema Conflicts**: Enable auto-evolve or manually align schemas.
- **Primary Key Violations**: Use upsert mode and set correct primary keys.

## Database-Specific Notes

- **PostgreSQL**: Supports JSONB, efficient upsert with ON CONFLICT.
- **MySQL**: Auto-increment handling, transaction size limits.
- **Oracle**: MERGE for upserts, sequence management.
- **SQL Server**: Use appropriate isolation levels and batch settings.

---

For more advanced configuration options, see the [PostgreSQL Example Config](https://github.com/nexla/connector_configs/ref/specs/connectors/split/default/connections/database/postgres.json) and [MySQL Example Config](https://github.com/nexla/connector_configs/ref/specs/connectors/split/default/connections/database/mysql.json).

## Configuration Parameters

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `batch.size` | Integer | No | - | Batch size for inserts | [JdbcSinkConnectorConfig.java#L72](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L72) |
| `insert.mode` | String | No | insert | Insert mode (insert, upsert) | [JdbcSinkConnectorConfig.java#L555-557](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L555-557) |
| `stop.on.error` | Boolean | No | false | Whether to stop on error | [JdbcSinkConnectorConfig.java#L55](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L55) |
| `empty.to.null` | Boolean | No | false | Convert empty strings to NULL | [JdbcSinkConnectorConfig.java#L64](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L64) |
| `null.as` | String | No | - | String to use for NULL values | [JdbcSinkConnectorConfig.java#L65](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L65) |
| `truncate.before.load` | Boolean | No | false | Truncate table before loading | [JdbcSinkConnectorConfig.java#L74](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L74) |
| `direct.upload` | Boolean | No | false | Use direct upload when possible | [JdbcSinkConnectorConfig.java#L81](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L81) |
| `parallelism` | Integer | No | 1 | Number of parallel upload threads | [JdbcSinkConnectorConfig.java#L71](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L71) |
| `post.flush.sql.script` | String | No | - | SQL script to execute after flush | [JdbcSinkConnectorConfig.java#L94](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L94) |
| `partitioning.column` | String | No | - | Column to use for partitioning | [JdbcSinkConnectorConfig.java#L92](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L92) |
| `clustering.columns` | List | No | - | Columns to use for clustering | [JdbcSinkConnectorConfig.java#L96](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L96) |
| `skip.header` | Boolean | No | false | Skip header row in data | [JdbcSinkConnectorConfig.java#L102](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L102) |
| `fallback.single.record.mode` | Boolean | No | false | Use single record mode as fallback | [JdbcSinkConnectorConfig.java#L110](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L110) |
| `table.name.format` | String | No | %s | Table name format | [JdbcAuthConfig.java#L50](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcAuthConfig.java#L50) |
| `max.retries` | Integer | No | 3 | Maximum retry attempts | [JdbcAuthConfig.java#L68](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcAuthConfig.java#L68) |
| `retry.backoff.ms` | Long | No | 1000 | Retry backoff in milliseconds | [JdbcAuthConfig.java#L68](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcAuthConfig.java#L68) |
| `auto.create` | Boolean | No | false | Auto-create tables | [JdbcSinkConnectorConfig.java#L555-557](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L555-557) |
| `auto.evolve` | Boolean | No | false | Auto-evolve table schema | [JdbcSinkConnectorConfig.java#L555-557](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L555-557) |
| `pk.mode` | String | No | none | Primary key mode | [JdbcSinkConnectorConfig.java#L555-557](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L555-557) |
| `pk.fields` | String | No | - | Primary key field names | [JdbcSinkConnectorConfig.java#L555-557](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L555-557) |
| `delete.enabled` | Boolean | No | false | Enable DELETE operations | [JdbcSinkConnectorConfig.java#L555-557](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L555-557) |
| `table` | String | Yes | - | Target table name | [SqlConfigAccessor.java#L19](/connector-properties/src/main/java/com/nexla/connector/properties/SqlConfigAccessor.java#L19) |
| `primary.key` | String | No | - | Primary key field names | [SqlConfigAccessor.java#L12](/connector-properties/src/main/java/com/nexla/connector/properties/SqlConfigAccessor.java#L12) |
| `jdbc.parameters` | List | No | - | Additional JDBC connection parameters | [SqlConfigAccessor.java#L51](/connector-properties/src/main/java/com/nexla/connector/properties/SqlConfigAccessor.java#L51) |
| `jdbc.iam.auth.enabled` | Boolean | No | false | Enable IAM authentication for AWS | [SqlConfigAccessor.java#L52](/connector-properties/src/main/java/com/nexla/connector/properties/SqlConfigAccessor.java#L52) |

## Parameter Details

### Connection Settings

- `connection.url`: JDBC URL format varies by database type:
  - PostgreSQL: `************************************`
  - MySQL: `*******************************`
  - Oracle: `*******************************`
  - SQL Server: `************************************************`

- `connection.user` and `connection.password`: Database credentials
  - Consider using encrypted credentials via `creds.enc`
  - Support for connection pools and SSL configurations

### Table Configuration

- `table.name.format`: Supports placeholders:
  - `%s`: Replaced with the dataset name
  - `%d`: Replaced with the dataset ID
  - Example: `schema.%s_table`

### Write Behavior

- `batch.size`: Number of records per batch insert
  - Larger batches improve performance
  - Consider memory constraints
  - Recommended range: 1000-5000

- `insert.mode`: Supported modes:
  - `insert`: Standard INSERT statements
  - `upsert`: INSERT with ON CONFLICT/MERGE
  - `update`: UPDATE existing records

### Schema Management

- `auto.create`: Creates tables if they don't exist
  - Uses schema from incoming data
  - Considers data type mappings

- `auto.evolve`: Modifies table schema
  - Adds new columns
  - Modifies column types if compatible
  - Does not remove columns

### Primary Keys

- `pk.mode`: Primary key modes:
  - `none`: No primary key
  - `fields`: Use specified fields
  - `record_key`: Use record key
  - `record_value`: Use record value

- `pk.fields`: Comma-separated field names
  - Example: `id,version`
  - Used for upsert operations

### Error Handling

- `max.retries`: Retry attempts for failures
- `retry.backoff.ms`: Wait time between retries
- Exponential backoff strategy

## Example Configurations

### PostgreSQL Example

```json
{
  "connection.url": "*************************************",
  "connection.user": "user",
  "connection.password": "password",
  "table.name.format": "schema.%s",
  "batch.size": 5000,
  "auto.create": true,
  "insert.mode": "upsert",
  "pk.mode": "fields",
  "pk.fields": "id,version"
}
```

### MySQL Example

```json
{
  "connection.url": "********************************",
  "connection.user": "user",
  "connection.password": "password",
  "table.name.format": "%s_table",
  "batch.size": 3000,
  "auto.create": true,
  "auto.evolve": true,
  "insert.mode": "insert"
}
```

### Oracle Example

```json
{
  "connection.url": "*************************************",
  "connection.user": "user",
  "connection.password": "password",
  "table.name.format": "%s",
  "batch.size": 2000,
  "insert.mode": "update",
  "pk.mode": "fields",
  "pk.fields": "id"
}
```

## Best Practices

1. **Connection Management**
   - Use connection pooling for better performance
   - Configure appropriate timeouts
   - Enable SSL for secure connections

2. **Batch Processing**
   - Adjust batch size based on record size
   - Monitor memory usage
   - Consider database load

3. **Schema Evolution**
   - Test auto-evolve in non-production first
   - Monitor schema changes
   - Keep track of column modifications

4. **Error Handling**
   - Configure appropriate retry counts
   - Monitor failed records
   - Implement dead letter queues

## Common Issues and Solutions

1. **Connection Issues**
   - Issue: Connection timeouts
   - Solution: Check network, increase timeouts

2. **Performance**
   - Issue: Slow writes
   - Solution: Increase batch size, check indexes

3. **Schema Conflicts**
   - Issue: Type mismatches
   - Solution: Verify schema mappings

4. **Primary Key Violations**
   - Issue: Duplicate key errors
   - Solution: Use upsert mode, verify pk fields

## Database-Specific Notes

### PostgreSQL

- Supports JSONB data type
- Efficient upsert with ON CONFLICT
- Concurrent write capabilities

### MySQL

- Auto-increment handling
- Transaction size limits
- Table locking considerations

### Oracle

- MERGE statement for upserts
- Sequence management
- LOB handling

### SQL Server

- Identity column behavior
- Bulk copy operations
- Temporal table support

### Database-Specific Parameters

#### Redshift

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `redshift.time.format` | String | No | - | Time format for Redshift | [JdbcSinkConnectorConfig.java#L67](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L67) |
| `redshift.date.format` | String | No | - | Date format for Redshift | [JdbcSinkConnectorConfig.java#L69](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L69) |
| `redshift.serverless` | Boolean | No | false | Use Redshift Serverless | [JdbcSinkConnectorConfig.java#L68](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L68) |
| `redshift.distkey` | String | No | - | Distribution key for table | [JdbcSinkConnectorConfig.java#L98](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L98) |
| `redshift.sortkeys` | List | No | - | Sort keys for table | [JdbcSinkConnectorConfig.java#L100](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L100) |

#### Oracle

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `oracle.date.format` | String | No | - | Date format for Oracle | [JdbcSinkConnectorConfig.java#L78](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L78) |
| `oracle.timestamp.format` | String | No | - | Timestamp format for Oracle | [JdbcSinkConnectorConfig.java#L79](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L79) |
| `oracle.autonomous.observable.exceptions` | List | No | - | Observable exceptions in autonomous mode | [JdbcSinkConnectorConfig.java#L91](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L91) |
| `nls.date.format` | String | No | - | NLS date format | [JdbcSinkConnectorConfig.java#L106](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L106) |
| `nls.timestamp.format` | String | No | - | NLS timestamp format | [JdbcSinkConnectorConfig.java#L108](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L108) |

#### Firebolt

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `firebolt.table.type` | String | No | - | Firebolt table type | [JdbcSinkConnectorConfig.java#L76](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L76) |

#### Intermediate File Settings

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `intermediate.file.format` | String | No | - | Format for intermediate files | [JdbcSinkConnectorConfig.java#L84](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L84) |
| `intermediate.file.quote.char` | String | No | - | Quote character for intermediate files | [JdbcSinkConnectorConfig.java#L86](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L86) |
| `intermediate.file.delimiter` | String | No | - | Delimiter for intermediate files | [JdbcSinkConnectorConfig.java#L88](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L88) |

#### Temporary Storage Settings

| Parameter | Type | Required | Default | Description | Implementation Reference |
|-----------|------|----------|---------|-------------|------------------------|
| `temp.s3.upload.bucket` | String | No | - | S3 bucket for temporary storage | [JdbcSinkConnectorConfig.java#L53](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L53) |
| `temp.s3.upload.prefix` | String | No | - | S3 prefix for temporary storage | [JdbcSinkConnectorConfig.java#L54](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L54) |
| `temp.s3.delete` | Boolean | No | - | Delete S3 files after use | [JdbcSinkConnectorConfig.java#L56](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L56) |
| `temp.azure.blob.upload.bucket` | String | No | - | Azure blob container for temporary storage | [JdbcSinkConnectorConfig.java#L59](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L59) |
| `temp.azure.blob.upload.prefix` | String | No | - | Azure blob prefix for temporary storage | [JdbcSinkConnectorConfig.java#L60](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L60) |
| `temp.azure.blob.delete` | Boolean | No | - | Delete Azure blobs after use | [JdbcSinkConnectorConfig.java#L61](/connector-properties/src/main/java/com/nexla/connector/config/jdbc/JdbcSinkConnectorConfig.java#L61) |
