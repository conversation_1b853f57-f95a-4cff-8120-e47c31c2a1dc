# Kinesis Sink Configuration

Confidence Score: Reviewed

## Overview

The Kinesis sink connector enables writing data to Amazon Kinesis Data Streams. It supports various partitioning strategies, batching configurations, authentication methods (default, static, IAM role), record aggregation, and compression for optimal stream writing.

> **Tip:** Use record aggregation and compression to optimize throughput and reduce costs. Choose partitioning strategies to avoid hot partitions.

## Common Configuration Parameters

| Parameter                | Type     | Required | Default | Description |
|--------------------------|----------|----------|---------|-------------|
| `kinesis.stream.name`    | String   | Yes      | -       | Kinesis stream name. Must exist. |
| `kinesis.region`         | String   | Yes      | -       | AWS region (e.g., `us-east-1`). |
| `kinesis.partition.key`  | String   | No       | -       | Field or expression for partition key. |
| `kinesis.partition.method` | String | No       | field   | Partition method: `field`, `random`, `round_robin`, `expression`. |
| `kinesis.batch.size`     | Integer  | No       | 500     | Records per batch (max 500). |
| `kinesis.batch.timeout.ms` | Long   | No       | 5000    | Batch timeout in ms. |
| `kinesis.auth.type`      | String   | No       | default | Auth type: `default`, `static`, `role`, `profile`. |
| `kinesis.access.key`     | String   | No       | -       | AWS access key (if static). |
| `kinesis.secret.key`     | String   | No       | -       | AWS secret key (if static). |
| `kinesis.role.arn`       | String   | No       | -       | IAM role ARN (if role-based). |
| `kinesis.endpoint`       | String   | No       | -       | Custom endpoint URL. |
| `kinesis.compression`    | String   | No       | none    | Compression: `none`, `gzip`. |
| `kinesis.retry.count`    | Integer  | No       | 3       | Number of retry attempts. |
| `kinesis.retry.backoff.ms` | Long   | No       | 1000    | Retry backoff in ms. |
| `kinesis.record.max.size` | Integer | No       | 1048576 | Max record size (bytes). |
| `kinesis.aggregation.enabled` | Boolean | No   | true    | Enable record aggregation. |

### Advanced/Conditional Logic

- **Partitioning**: Use `field` for deterministic, `random` or `round_robin` for even distribution, `expression` for custom logic.
- **Auth Type**: Use `default` for environment credentials, `static` for explicit keys, `role` for cross-account, `profile` for named profiles.
- **Aggregation**: Enable to combine records and reduce costs; disable for low-latency use cases.
- **Compression**: Use `gzip` for large records or cost savings; monitor CPU usage.

## Example Configurations

### Simple Configuration Example

```json
{
  "kinesis.stream.name": "user-events",
  "kinesis.region": "us-east-1",
  "kinesis.partition.key": "userId",
  "kinesis.batch.size": 500,
  "kinesis.auth.type": "default",
  "kinesis.aggregation.enabled": true
}
```

### Static Credentials Example

```json
{
  "kinesis.stream.name": "order-events",
  "kinesis.region": "eu-west-1",
  "kinesis.partition.key": "orderId",
  "kinesis.partition.method": "field",
  "kinesis.batch.size": 200,
  "kinesis.batch.timeout.ms": 3000,
  "kinesis.auth.type": "static",
  "kinesis.access.key": "${AWS_ACCESS_KEY}",
  "kinesis.secret.key": "${AWS_SECRET_KEY}",
  "kinesis.compression": "gzip"
}
```

### IAM Role Example

```json
{
  "kinesis.stream.name": "log-events",
  "kinesis.region": "us-west-2",
  "kinesis.partition.method": "expression",
  "kinesis.partition.key": "${timestamp % 100}",
  "kinesis.auth.type": "role",
  "kinesis.role.arn": "arn:aws:iam::123456789012:role/KinesisRole",
  "kinesis.batch.size": 400,
  "kinesis.retry.count": 5,
  "kinesis.retry.backoff.ms": 2000
}
```

### Advanced Configuration Example

```json
{
  "kinesis.stream.name": "analytics-events",
  "kinesis.region": "ap-southeast-1",
  "kinesis.partition.method": "round_robin",
  "kinesis.batch.size": 500,
  "kinesis.batch.timeout.ms": 10000,
  "kinesis.aggregation.enabled": true,
  "kinesis.compression": "gzip",
  "kinesis.record.max.size": 524288,
  "kinesis.endpoint": "https://kinesis.custom.endpoint",
  "kinesis.retry.count": 3,
  "kinesis.retry.backoff.ms": 1000
}
```

## Best Practices

- Use aggregation and compression for high-throughput, cost-sensitive workloads.
- Choose partitioning to avoid hot partitions and ensure even distribution.
- Monitor batch size, latency, and error rates.
- Use IAM roles for secure, cross-account access.
- Test with small batches before scaling up.

## Common Issues and Solutions

- **Throughput**: Use aggregation, optimize partitioning, and monitor shard limits.
- **Record Size**: Compress or split large records.
- **Authentication**: Check IAM permissions and credential configuration.
- **Performance**: Tune batch size, timeout, and retry settings.

## Feature-Specific Notes

- **Record Aggregation**: Reduces cost, increases throughput, but may add latency.
- **Compression**: Reduces size, may increase CPU usage.
- **Partitioning**: Key to scaling and performance.
- **Authentication**: Use secure methods and rotate credentials regularly.
