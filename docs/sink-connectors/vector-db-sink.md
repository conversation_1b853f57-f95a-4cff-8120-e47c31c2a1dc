# Vector Database Sink Configuration

Confidence Score: Unreviewed

## Overview

The Vector Database sink connector enables writing vector embeddings and metadata to vector databases like Pinecone, Weaviate, Milvus, and others. It supports various vector similarity search algorithms, metadata filtering, and batch operations.

## Configuration Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `vector.db.type` | String | Yes | - | Vector database type (pinecone, weaviate, milvus) |
| `vector.db.url` | String | Yes | - | Database endpoint URL |
| `vector.db.api.key` | String | Yes | - | API key for authentication |
| `vector.db.namespace` | String | No | default | Database namespace/collection |
| `vector.embedding.field` | String | Yes | - | Field containing vector embeddings |
| `vector.metadata.fields` | List | No | [] | Fields to store as metadata |
| `vector.dimension` | Integer | Yes | - | Vector dimension size |
| `vector.metric` | String | No | cosine | Similarity metric (cosine, euclidean, dot) |
| `vector.batch.size` | Integer | No | 100 | Batch size for upserts |
| `vector.index.type` | String | No | hnsw | Index type for vectors |
| `vector.index.params` | Map | No | {} | Index-specific parameters |
| `vector.retry.count` | Integer | No | 3 | Number of retry attempts |
| `vector.timeout.ms` | Long | No | 30000 | Operation timeout |
| `vector.consistency.level` | String | No | eventual | Consistency level |

## Parameter Details

### Database Configuration

- `vector.db.type`: Supported databases:
  - `pinecone`: Pinecone vector database
  - `weaviate`: Weaviate vector database
  - `milvus`: Milvus vector database
  - `qdrant`: Qdrant vector database
  - `redis`: Redis vector database

- `vector.db.url`: Database endpoint:
  - Pinecone: `https://<index>-<project>.svc.<region>.pinecone.io`
  - Weaviate: `https://instance.weaviate.network`
  - Milvus: `http://localhost:19530`

### Vector Settings

- `vector.embedding.field`: Vector data:
  - Field name containing embeddings
  - Must be numeric array
  - Example: `embedding`

- `vector.dimension`: Vector size:
  - Must match embedding size
  - Common sizes: 768, 1024, 1536
  - Model-dependent

- `vector.metric`: Similarity metrics:
  - `cosine`: Cosine similarity
  - `euclidean`: L2 distance
  - `dot`: Dot product
  - Database-specific support

### Metadata Configuration

- `vector.metadata.fields`: Additional data:
  - Field names to store
  - Searchable metadata
  - Example: `["title", "description", "tags"]`

### Performance Settings

- `vector.batch.size`: Batch operations:
  - Optimal size varies
  - Memory vs speed
  - Database limits

- `vector.index.type`: Index types:
  - `hnsw`: Hierarchical NSW
  - `flat`: Flat index
  - `ivf`: IVF index
  - Database-specific

## Example Configurations

### Pinecone Example

```json
{
  "vector.db.type": "pinecone",
  "vector.db.url": "https://index-proj.svc.us-east-1.pinecone.io",
  "vector.db.api.key": "${PINECONE_API_KEY}",
  "vector.embedding.field": "embedding",
  "vector.dimension": 1536,
  "vector.metadata.fields": ["title", "url", "content"],
  "vector.batch.size": 100,
  "vector.metric": "cosine"
}
```

### Weaviate Example

```json
{
  "vector.db.type": "weaviate",
  "vector.db.url": "https://my-instance.weaviate.network",
  "vector.db.api.key": "${WEAVIATE_API_KEY}",
  "vector.namespace": "Articles",
  "vector.embedding.field": "vector",
  "vector.dimension": 768,
  "vector.metadata.fields": ["title", "author", "category"],
  "vector.index.type": "hnsw",
  "vector.index.params": {
    "maxConnections": 64,
    "efConstruction": 128
  }
}
```

### Milvus Example

```json
{
  "vector.db.type": "milvus",
  "vector.db.url": "http://localhost:19530",
  "vector.namespace": "documents",
  "vector.embedding.field": "embedding",
  "vector.dimension": 1024,
  "vector.metric": "l2",
  "vector.index.type": "ivf_flat",
  "vector.index.params": {
    "nlist": 1024,
    "nprobe": 16
  },
  "vector.consistency.level": "strong"
}
```

### Qdrant Example

```json
{
  "vector.db.type": "qdrant",
  "vector.db.url": "http://localhost:6333",
  "vector.db.api.key": "${QDRANT_API_KEY}",
  "vector.namespace": "products",
  "vector.embedding.field": "vector",
  "vector.dimension": 512,
  "vector.metric": "cosine",
  "vector.batch.size": 50,
  "vector.index.type": "hnsw",
  "vector.index.params": {
    "m": 16,
    "ef_construct": 100
  }
}
```

## Best Practices

1. **Vector Management**
   - Normalize vectors
   - Match dimensions
   - Validate embeddings
   - Handle missing data

2. **Performance**
   - Optimize batch size
   - Index configuration
   - Connection pooling
   - Monitor latency

3. **Metadata**
   - Schema design
   - Field selection
   - Data types
   - Query optimization

4. **Error Handling**
   - Retry strategy
   - Data validation
   - Error logging
   - Monitoring

## Common Issues and Solutions

1. **Vector Dimension**
   - Issue: Mismatched dimensions
   - Solution: Validate before insert

2. **Performance**
   - Issue: Slow upserts
   - Solution: Adjust batch size, index

3. **Consistency**
   - Issue: Stale reads
   - Solution: Adjust consistency level

4. **Resource Usage**
   - Issue: Memory pressure
   - Solution: Optimize batch size, connection pooling

## Database-Specific Notes

### Pinecone

- Pod types
- Sharding
- Namespaces
- Query filtering

### Weaviate

- Schema design
- Cross-references
- Semantic search
- GraphQL API

### Milvus

- Partitioning
- Load balancing
- Resource groups
- Hybrid search

### Qdrant

- Payload indexing
- Filtering
- Snapshots
- Collections
