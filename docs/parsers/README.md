# Parser Configurations

## Overview

Nexla supports multiple data formats and provides parsers to handle each format. Each parser has its own set of configuration parameters to control how data is read and interpreted.

## Common Parser Types

Confidence Score: Unreviewed

### CSV/Delimited Files

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `delimiter` | String | No | , | Field delimiter character |
| `quote` | String | No | " | Quote character |
| `escape` | String | No | \ | Escape character |
| `write.header` | Boolean | No | true | Whether to write header row |
| `header` | Boolean | No | true | Whether file has header row |
| `skip.lines` | Integer | No | 0 | Number of lines to skip |
| `comment.char` | String | No | # | Comment line character |
| `null.value` | String | No | - | String to interpret as NULL |
| `rename.duplicate.headers` | Boolean | No | false | Automatically rename duplicate column headers |
| `schema.detection` | String | No | header | Schema detection mode (header, none) |
| `scalar.coercion` | Boolean | No | false | Convert string values to appropriate scalar types |
| `schema` | String | No | - | Comma-separated list of column names |
| `validate.number.of.columns` | Boolean | No | false | Validate that each row has the same number of columns |
| `enable.quote.detection` | Boolean | No | true | Enable automatic quote character detection |
| `skip.first.lines` | Integer | No | 0 | Number of lines to skip at start |
| `skip.last.lines` | Integer | No | 0 | Number of lines to skip at end |
| `quote.disabled` | Boolean | No | false | Disable quote character handling |
| `quote.remove.forced` | Boolean | No | false | Force removal of quotes from values |
| `use.detected.delimiter` | Boolean | No | false | Use automatically detected delimiter |
| `include.skipped.lines` | Boolean | No | false | Include skipped lines in output |

### JSON

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `json.path` | String | No | $ | JSONPath expression to extract data |
| `json.mode` | String | No | auto | Reading mode (row, entire_file, array, auto) |
| `charset` | String | No | UTF-8 | Character encoding |
| `charset.detection.confidence` | Float | No | 0.8 | Confidence threshold for charset detection |
| `format.detection.max.lines` | Integer | No | 1000 | Max lines to scan for format detection |
| `additional.json.paths` | String | No | - | Additional JSONPath expressions to extract |
| `fail.on.duplicates` | Boolean | No | true | Fail parsing if duplicate keys found |
| `json.output.template` | String | No | - | Template for JSON output |

### XML

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `xml.mode` | String | No | auto | Reading mode (row, entire_file, auto) |
| `xml.xpath` | String | No | - | XPath expression to extract data |
| `additional.xml.xpaths` | String | No | - | Additional XPath expressions to extract |
| `charset` | String | No | UTF-8 | Character encoding |
| `charset.detection.confidence` | Float | No | 0.8 | Confidence threshold for charset detection |
| `format.detection.max.lines` | Integer | No | 1000 | Max lines to scan for format detection |
| `xml.root.tag` | String | No | root | Root element tag name |
| `xml.output.template` | String | No | - | Template for XML output |

### Excel

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `excel.type` | String | No | auto | Excel file type (xls, xlsx, auto) |
| `excel.mode` | String | No | concise | Reading mode (concise, verbose) |
| `excel.schema.detection` | String | No | header | Schema detection mode (header, none) |
| `excel.schema` | String | No | - | Comma-separated list of column names |
| `excel.ranges` | String | No | - | Sheet ranges to read (e.g. "Sheet1!A1:D10,Sheet2!B2:E20") |
| `excel.additional.range` | String | No | - | Additional ranges to read as separate messages |
| `excel.skip.merged.cells` | Boolean | No | false | Skip merged cells when reading |
| `excel.transpose` | Boolean | No | false | Transpose rows and columns |
| `excel.streamed` | Boolean | No | false | Use streaming mode for large files |
| `excel.file.name` | String | No | - | Custom name for the Excel file |
| `preserve.empty.strings` | Boolean | No | false | Preserve empty string values |
| `preserve.null.strings` | Boolean | No | false | Preserve null string values |
| `rename.duplicate.headers` | Boolean | No | false | Automatically rename duplicate column headers |
| `null.as.empty.string` | Boolean | No | false | Convert null values to empty strings |

### Fixed Width

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `field.lengths` | String | Yes | - | Comma-separated list of field lengths |
| `padding.character` | String | No | space | Character used for padding |
| `line.separator` | String | No | auto | Line separator character(s) |
| `line.separator.detection.enabled` | Boolean | No | true | Enable automatic line separator detection |
| `fixed.width.schema.detection.mode` | String | No | header | Schema detection mode (header, generated) |
| `scalar.coercion` | Boolean | No | true | Convert string values to appropriate scalar types |
| `remove.quotes.forced` | Boolean | No | false | Force removal of quotes from values |
| `quote.char` | String | No | " | Quote character |
| `skip.first.lines` | Integer | No | 0 | Number of lines to skip at start |
| `skip.last.lines` | Integer | No | 0 | Number of lines to skip at end |
| `include.skipped.lines` | Boolean | No | false | Include skipped lines in output |

### Grok

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `grok.pattern` | String | Yes | - | Grok pattern to parse logs |

### Protobuf

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `proto` | String | Yes | - | Protobuf schema definition |
| `file.set.descriptor` | String | No | - | FileDescriptorSet for imports |
| `root.type` | String | Yes | - | Root message type name |

### EDI

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `edi.options` | Map | No | {} | EDI-specific options |

### SWIFT

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `swift.message.type` | String | Yes | - | SWIFT message type |
| `swift.version` | String | No | latest | SWIFT version |

### Unstructured Data

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `unstructured.api.url` | String | No | <https://api.unstructuredapp.io/general/v0/general> | API endpoint URL |
| `unstructured.api.key` | String | Yes | - | API key for authentication |
| `unstructured.gz.uncompressed.content.type` | String | No | application/pdf | Content type for uncompressed files |
| `unstructured.output.format` | String | No | application/json | Output format |
| `unstructured.coordinates` | String | No | false | Include element coordinates |
| `unstructured.encoding` | String | No | utf-8 | Character encoding |
| `unstructured.strategy` | String | No | auto | Parsing strategy |
| `unstructured.hi.res.model.name` | String | No | - | High resolution model name |
| `unstructured.include.page.breaks` | String | No | false | Include page break markers |
| `unstructured.languages` | String | No | - | Comma-separated list of languages |
| `unstructured.pdf.infer.table.structure` | String | No | false | Infer table structure in PDFs |
| `unstructured.skip.infer.table.types` | String | No | pdf,jpg,png | File types to skip table inference |
| `unstructured.xml.keep.tags` | String | No | false | Keep XML tags in output |
| `unstructured.chunking.strategy` | String | No | - | Text chunking strategy |
| `unstructured.multipage.sections` | String | No | true | Enable multipage sections |
| `unstructured.combine.under.n.chars` | Integer | No | 500 | Combine chunks under N chars |
| `unstructured.new.after.n.chars` | Integer | No | 1500 | Create new chunk after N chars |
| `unstructured.max.characters` | Integer | No | 1500 | Maximum characters per chunk |

### Compression

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `compress.whitelist.pathmatchers` | List | No | [] | List of compression patterns to include |
| `compress.blacklist.pathmatchers` | List | No | [] | List of compression patterns to exclude |
| `compression.type` | String | No | gzip | Compression type (gzip, none) |

### Schema Detection

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `schema.detection.once` | Boolean | No | false | Only detect schema once |
| `schema.detection.timeout.ms` | Long | No | 60000 | Schema detection timeout in milliseconds |
| `csv.schema.detection` | String | No | header | CSV schema detection mode (header, generated, configured) |
| `excel.schema.detection` | String | No | header | Excel schema detection mode (header, none) |
| `fixed.width.schema.detection.mode` | String | No | header | Fixed width schema detection mode (header, generated) |
| `file.schema.detection.parallelism` | Integer | No | 1 | Number of parallel schema detection tasks |

## Non-Parser File Configurations

Confidence Score: Unreviewed

### File Format

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `file.format` | String | No | - | File format for data transfer (csv, json, parquet, orc) |
| `intermediate.file.format` | String | No | - | Intermediate file format for staging |
| `file.type` | String | No | - | File type for storage systems (BINARY, ASCII) |

### File Transfer

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `file.transfer.mode` | String | No | binary | Transfer mode (binary, ascii) |
| `file.transfer.buffer.size` | Integer | No | 8192 | Buffer size for file transfers |
| `file.transfer.timeout` | Integer | No | 30000 | Timeout in milliseconds |

### File Storage

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `temp.storage.type` | String | No | local | Temporary storage type (local, s3, azure, gcs) |
| `temp.storage.path` | String | No | /tmp | Path for temporary storage |
| `temp.storage.cleanup` | Boolean | No | true | Clean up temporary files after processing |

### File Naming

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `file.name.pattern` | String | No | - | Pattern for generated file names |
| `file.name.prefix` | String | No | - | Prefix for generated file names |
| `file.name.suffix` | String | No | - | Suffix for generated file names |
| `file.name.timestamp.format` | String | No | yyyyMMddHHmmss | Timestamp format in file names |

### File Processing

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `file.processing.threads` | Integer | No | 1 | Number of parallel processing threads |
| `file.processing.batch.size` | Integer | No | 1000 | Batch size for file processing |
| `file.processing.max.retries` | Integer | No | 3 | Maximum number of retry attempts |
| `file.processing.retry.interval` | Integer | No | 1000 | Retry interval in milliseconds |

### File Monitoring

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `file.monitor.interval` | Integer | No | 60000 | Monitoring interval in milliseconds |
| `file.monitor.recursive` | Boolean | No | false | Monitor subdirectories recursively |
| `file.monitor.pattern` | String | No | * | File pattern to monitor |
| `file.monitor.stable.time` | Integer | No | 5000 | Time in ms to wait before processing |
| `file.monitor.max.files` | Integer | No | 1000 | Maximum number of files to monitor |
| `file.monitor.ignore.hidden` | Boolean | No | true | Ignore hidden files |

### File Monitoring Configuration

```json
{
  "file.monitor.interval": 30000,
  "file.monitor.recursive": true,
  "file.monitor.pattern": "*.{csv,json}",
  "file.monitor.stable.time": 10000,
  "file.monitor.max.files": 500,
  "file.monitor.ignore.hidden": true
}
```

## Examples

### CSV with Custom Delimiter

```json
{
  "delimiter": "|",
  "quote": "'",
  "header": true,
  "skip.lines": 2,
  "null.value": "NULL"
}
```

### JSON with JSONPath

```json
{
  "json.mode": "array",
  "json.path": "$.data[*]",
  "additional.json.paths": [
    "$.metadata.timestamp",
    "$.metadata.source"
  ]
}
```

### XML with XPath

```json
{
  "xml.mode": "row",
  "xml.xpath": "//record",
  "xml.root.tag": "records",
  "additional.xml.xpaths": [
    "//metadata/created",
    "//metadata/author"
  ]
}
```

### Fixed Width with Schema

```json
{
  "field.lengths": [10, 20, 15, 8],
  "padding.character": " ",
  "fixed.width.schema.detection.mode": "header",
  "skip.first.lines": 1
}
```

### Excel with Custom Range

```json
{
  "excel.type": "xlsx",
  "excel.mode": "concise",
  "excel.ranges": "Sheet1!A1:D10,Sheet2!B2:E20",
  "excel.schema.detection": "header",
  "rename.duplicate.headers": true
}
```

### Unstructured Data with Chunking

```json
{
  "unstructured.api.key": "your-api-key",
  "unstructured.strategy": "auto",
  "unstructured.chunking.strategy": "by_length",
  "unstructured.combine.under.n.chars": 500,
  "unstructured.new.after.n.chars": 1500,
  "unstructured.max.characters": 1500
}
```

### File Format Configuration

```json
{
  "file.format": "parquet",
  "intermediate.file.format": "csv",
  "file.type": "BINARY"
}
```

### File Transfer Configuration

```json
{
  "file.transfer.mode": "binary",
  "file.transfer.buffer.size": 16384,
  "file.transfer.timeout": 60000
}
```

### File Storage Configuration

```json
{
  "temp.storage.type": "s3",
  "temp.storage.path": "s3://bucket/temp",
  "temp.storage.cleanup": true
}
```

### File Naming Configuration

```json
{
  "file.name.pattern": "data_{timestamp}_{sequence}",
  "file.name.prefix": "export_",
  "file.name.suffix": ".dat",
  "file.name.timestamp.format": "yyyy-MM-dd-HHmmss"
}
```

### File Processing Configuration

```json
{
  "file.processing.threads": 4,
  "file.processing.batch.size": 5000,
  "file.processing.max.retries": 5,
  "file.processing.retry.interval": 2000
}
```

## Sink Configurations

Confidence Score: Unreviewed

### Base Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `sink.id` | Integer | Yes | - | Unique identifier for the sink |
| `dataset.id` | Integer | Yes | - | Dataset that this sink is associated with |
| `version` | Integer | No | 1 | Data sink version |
| `creds.enc` | String | No | - | Encrypted credentials |
| `creds.enc.iv` | String | No | - | Initialization vector for encrypted credentials |
| `creds.id` | Integer | No | -1 | Credentials identifier |
| `mapping` | String | No | - | Output schema mapping |
| `create.datasource` | Boolean | No | false | Create DataSource for sink |
| `tracker.encryption.enabled` | Boolean | No | false | Enable tracker encryption |
| `tracker.encryption.key` | String | No | - | Encryption key for tracker |
| `fixed.tracker.value` | String | No | - | Fixed tracker value for 1-to-many rows |
| `node.tag` | String | No | - | Node tag for routing |
| `flush.cron` | String | No | - | Cron expression for flush schedule |
| `inactivity.timeout.before.flush.min` | Integer | No | 5 | Flush after N minutes of inactivity |
| `flush.batch.size` | Integer | No | - | Force flush after N records |
| `first.flush.delay.min` | Integer | No | 2 | Initial flush delay in minutes |
| `flush.combine.run.ids` | Boolean | No | false | Combine run IDs in WRITE_DONE event |
| `cdc.enabled` | Boolean | No | false | Enable Change Data Capture |
| `pipeline.type` | String | No | - | Pipeline type identifier |

### JDBC Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `connection.url` | String | Yes | - | JDBC connection URL |
| `connection.user` | String | Yes | - | Database username |
| `connection.password` | String | Yes | - | Database password |
| `table.name.format` | String | No | %s | Table name format |
| `batch.size` | Integer | No | 3000 | Batch size for inserts |
| `max.retries` | Integer | No | 3 | Maximum retry attempts |
| `retry.backoff.ms` | Long | No | 1000 | Retry backoff in milliseconds |
| `auto.create` | Boolean | No | false | Auto-create tables |
| `auto.evolve` | Boolean | No | false | Auto-evolve table schema |
| `insert.mode` | String | No | insert | Insert mode (insert, upsert, update) |
| `pk.mode` | String | No | none | Primary key mode |
| `pk.fields` | String | No | - | Primary key field names |
| `delete.enabled` | Boolean | No | false | Enable DELETE operations |

### File Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `file.path` | String | Yes | - | Output file path |
| `file.name.template` | String | No | - | Template for output filenames |
| `file.compression` | String | No | none | Compression type (none, gzip) |
| `file.max.records` | Integer | No | - | Maximum records per file |
| `file.rotation.time.ms` | Long | No | - | File rotation interval |
| `file.rotation.size.bytes` | Long | No | - | File size threshold for rotation |
| `file.overwrite` | Boolean | No | false | Overwrite existing files |
| `file.flush.records` | Integer | No | 1000 | Records before flush |

### Document Store Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `connection.uri` | String | Yes | - | Connection URI |
| `database` | String | Yes | - | Database name |
| `collection` | String | Yes | - | Collection name |
| `write.concern` | String | No | w1 | Write concern level |
| `bulk.enabled` | Boolean | No | true | Enable bulk operations |
| `bulk.size` | Integer | No | 1000 | Bulk operation size |
| `ordered.bulk` | Boolean | No | true | Ordered bulk operations |
| `document.id.strategy` | String | No | none | Document ID strategy |
| `max.retries` | Integer | No | 3 | Maximum retry attempts |

### Messaging Sink Configuration

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `topic` | String | Yes | - | Target topic/queue name |
| `key.field` | String | No | - | Field to use as message key |
| `partition.field` | String | No | - | Field for partitioning |
| `message.format` | String | No | json | Message format |
| `compression.type` | String | No | none | Message compression type |
| `batch.size` | Integer | No | 16384 | Producer batch size |
| `linger.ms` | Long | No | 0 | Producer linger time |
| `max.request.size` | Integer | No | 1048576 | Maximum request size |
| `retries` | Integer | No | 3 | Number of retries |

### Examples

### JDBC Sink Configuration

```json
{
  "connection.url": "*************************************",
  "connection.user": "user",
  "connection.password": "password",
  "table.name.format": "schema.%s",
  "batch.size": 5000,
  "auto.create": true,
  "insert.mode": "upsert",
  "pk.mode": "fields",
  "pk.fields": "id,version"
}
```

### File Sink Configuration

```json
{
  "file.path": "/data/output",
  "file.name.template": "data_{timestamp}_{partition}.csv",
  "file.compression": "gzip",
  "file.max.records": 1000000,
  "file.rotation.time.ms": 3600000,
  "file.overwrite": false,
  "file.flush.records": 5000
}
```

### Document Store Sink Configuration

```json
{
  "connection.uri": "mongodb://localhost:27017",
  "database": "mydb",
  "collection": "mycollection",
  "write.concern": "w2",
  "bulk.enabled": true,
  "bulk.size": 2000,
  "document.id.strategy": "uuid"
}
```

### Messaging Sink Configuration

```json
{
  "topic": "output-topic",
  "key.field": "id",
  "partition.field": "region",
  "message.format": "avro",
  "compression.type": "snappy",
  "batch.size": 32768,
  "linger.ms": 100
}
```
