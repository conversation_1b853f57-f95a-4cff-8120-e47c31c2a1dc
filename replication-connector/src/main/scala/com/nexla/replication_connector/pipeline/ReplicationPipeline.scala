package com.nexla.replication_connector.pipeline

import akka.Done
import akka.actor.{ActorSystem, Scheduler}
import akka.stream.scaladsl.{Keep, Sink}
import akka.stream.{KillSwitches, Materializer, UniqueKillSwitch}
import com.nexla.admin.client.AdminApiClient
import com.nexla.inmemory_connector_common.PipelineKiller
import com.nexla.inmemory_connector_common.listing.ListingStreamSource
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.listing.client.{FileVaultClient, ListingClient => JavaListingClient}
import com.nexla.replication_connector.AppProps
import com.nexla.replication_connector.connectors.sinks.ReplicationSinkConnector
import com.nexla.replication_connector.connectors.{SinkConnectorFactory, SourceConnectorFactory}
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.flow_logs.FlowLogsSender
import com.nexla.replication_connector.messaging.ReplicationMessageProducer
import com.nexla.replication_connector.metrics.MetricsSender
import com.nexla.replication_connector.pipeline.metadata.RemoteReplicationMetadata
import com.nexla.replication_connector.state.State
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum
import com.nexla.sc.client.listing.ListingAppClient
import com.nexla.sc.config.ConfigEnricher
import com.nexla.sc.util.StrictNexlaLogging

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class ReplicationPipeline(
                           ctx: Context,
                           pipelineState: State,
                           adminApiClient: AdminApiClient,
                           listingClient: ListingAppClient,
                           listingStreamSource: ListingStreamSource,
                           javaListingClient: JavaListingClient,
                           fileVaultClient: FileVaultClient,
                           messageProducer: ReplicationMessageProducer,
                           props: AppProps,
                           probeFactory: ProbeFactory,
                           flowLogsSender: FlowLogsSender,
                           metricsSender: MetricsSender,
                         )
                         (implicit system: ActorSystem,
                          mat: Materializer,
                          scheduler: Scheduler,
                          ec: ExecutionContext)
  extends StrictNexlaLogging with ConfigEnricher {

  private val sourceConnectorFactory = new SourceConnectorFactory(ctx, pipelineState)(adminApiClient, listingStreamSource, javaListingClient, fileVaultClient, probeFactory, props)
  private val sinkConnectorFactory = new SinkConnectorFactory(ctx, adminApiClient, javaListingClient, fileVaultClient, messageProducer, props, probeFactory)
  private val sourceDownloader = new SourceDownloader(ctx, pipelineState, flowLogsSender, metricsSender)
  private val destinationUploader = new SinkUploader(ctx, listingClient, flowLogsSender, metricsSender, pipelineState)

  private def runStream(remoteReplicationMetadata: RemoteReplicationMetadata, sinkConnector: ReplicationSinkConnector): (UniqueKillSwitch, Future[Done]) = {
    val (killSwitch, pipelineF) = sourceDownloader.downloadToLocal(remoteReplicationMetadata, sinkConnector.config)
      .via(destinationUploader.uploadToDestination(sinkConnector))
      .viaMat(KillSwitches.single)(Keep.right)
      .toMat(Sink.ignore)(Keep.both)
      .run()
    (killSwitch, pipelineF)
  }

  def startPipeline(pipelineKiller: PipelineKiller): Future[Unit] = {
    pipelineState.setPipelineStatus(PipelineTaskStateEnum.Running)
    val res = for {
      sourceConnector <- Future.fromTry(sourceConnectorFactory.create())
      sinkConnector <- Future.fromTry(sinkConnectorFactory.create())
      remoteReplicationMetadata <- sourceConnector.prepareReplicationSourceRemote()
      (killSwitch, pipelineF) = runStream(remoteReplicationMetadata, sinkConnector)
      _ = pipelineKiller.addKillFunction(() => {killSwitch.shutdown(); true} )
      _ <- pipelineF
    } yield ()
    res.transformWith {
      case Success(_) =>
        pipelineState.setPipelineStatus(PipelineTaskStateEnum.Finished)
        logger.info("Pipeline finished successfully")
        Future.unit
      case Failure(ex) =>
        pipelineState.setPipelineStatus(PipelineTaskStateEnum.Failed)
        logger.error("Pipeline failed", ex)
        Future.failed(ex)
    }
  }

}
