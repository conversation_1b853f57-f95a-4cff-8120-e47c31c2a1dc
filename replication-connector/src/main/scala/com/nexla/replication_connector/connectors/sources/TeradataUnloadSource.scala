package com.nexla.replication_connector.connectors.sources

import akka.stream.scaladsl.Source
import com.nexla.admin.client.AdminApiClient
import com.nexla.admin.config.ConfigUtils.enrichWithDataCredentials
import com.nexla.common.ConnectionType.DROPBOX
import com.nexla.common.NexlaConstants._
import com.nexla.connector.ConnectorService.UNIT_TEST
import com.nexla.connector.config.file.DirScanningMode.DIRECTORIES
import com.nexla.connector.config.file.DropBoxAuthConfig.ACCESS_TOKEN
import com.nexla.connector.config.file.FileSourceConnectorConfig
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat._
import com.nexla.connector.config.jdbc.{JdbcSourceConnectorConfig, WarehouseCopyFileFormat}
import com.nexla.file.service.LocalConnectorService
import com.nexla.probe.sql.SqlConnectorService
import com.nexla.replication_connector.AppProps
import com.nexla.replication_connector.connectors.sources.utils.{TPTJobGenerator, TPTProcessLogger}
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.pipeline.metadata.{FileToReplicate, RemoteReplicationMetadata}
import com.nexla.sc.config.ConfigEnricher
import com.nexla.sc.util.{StrictNexlaLogging, WithLogging}
import com.nexla.util.HdfsUtil.hadoopConfiguration
import connect.jdbc.sink.dialect.{DbDialect, DialectRegistry}
import org.apache.hadoop.fs.Path
import org.apache.parquet.hadoop.ParquetFileWriter
import org.apache.parquet.hadoop.metadata.CompressionCodecName
import org.apache.parquet.hadoop.util.HadoopOutputFile
import org.apache.parquet.schema.PrimitiveType.PrimitiveTypeName
import org.apache.parquet.schema.{LogicalTypeAnnotation, MessageType, Type, Types}
import wvlet.airframe.parquet.ParquetWriterAdapter.RecordWriterBuilder

import java.io.File
import java.nio.file.{FileSystems, Files, Paths}
import java.util
import scala.collection.JavaConverters._
import scala.collection.mutable.ListBuffer
import scala.compat.java8.OptionConverters._
import scala.concurrent.{ExecutionContext, Future}
import scala.sys.process._

// @nowarn("msg=method as in class Builder is deprecated")
class TeradataUnloadSource(
                            pipelineContext: Context,
                            adminApiClient: AdminApiClient,
                            props: AppProps,
                          )(implicit ec: ExecutionContext)
  extends ReplicationSourceConnector
    with StrictNexlaLogging
    with ConfigEnricher
    with WithLogging {

  lazy val dialectRegistry: DialectRegistry = DialectRegistry.getInstance()

  def prepareReplicationSourceRemote(): Future[RemoteReplicationMetadata] = Future {
    if (sourceConfig.query.isPresent) {
      // todo: theoretically, this should be doable with TPT - we define our schema on our own and select statement
      // can include necessary things
      throw new IllegalArgumentException("Query mode is not currently supported for Teradata TPT Flow")
    }
    logger.info(s"Starting Teradata unload source")
    val sourceDialect = dialectRegistry.fromConnectionString(sourceConfig.authConfig)
    val probeService = new SqlConnectorService(props.config.getStore, adminApiClient)
    val connection = probeService.getConnection(sourceConfig.authConfig)
    val tableName = sourceConfig.table.get()
    val schemaName = sourceConfig.authConfig.databaseName
    // this should be just a location on the machine running fast connector
    val unloadFormat: WarehouseCopyFileFormat = unloadFileFormat(sourceConfig, sourceDialect)
    logger.info(s"Got Teradata unload format: [$unloadFormat]")
    // step 1: get location of tmp files on this machine
    // step 2: get the table we're about to export using tpt - count of rows, columns and their types.
    // step 3: get all the values and insert them to a template file
    // step 4: generate tpt job definition for this source
    val jobGen = TPTJobGenerator(schemaName, tableName, connection)
    val tptJob = jobGen.generate(pipelineContext.source.getId, sourceConfig, connection)

    // step 5: execute the tpt using the tools on the local machine, make sure this is running and wait till completion
    // -C is important, as it asks teradata to round-robin between files and export using X vCores
    val cmdToRun = s"${tptJob.tbuildExecutable} -e UTF8 -f ${tptJob.tptRunDirectory}${TPTJobGenerator.DEFAULT_RUN_FILE_NAME} -C"
    logger.info(s"Will execute command '$cmdToRun' for the TPT export")

    val result = cmdToRun.!(new TPTProcessLogger(pipelineContext.taskId))
    if (result == 0) {
      logger.info("TPT job exit code 0, continuing with the output file conversion and direct listing")
      val conversionDone = convertFilesIfNecessary(sourceConfig, jobGen.generateSchema(connection), tptJob.tptRunDirectory)
      listUnloadedFilesDirect(sourceConfig, unloadFormat, tableName, tptJob.tptRunDirectory, conversionDone)
    } else {
      throw new IllegalArgumentException(s"TPT job exit code $result, unable to continue with the direct listing")
    }

  }

  private def noExtensionFilesInDir(dirPath: String): List[File] = {
    logger.info(s"Listing files in dir $dirPath")
    val file = new File(dirPath)
    val output = file.listFiles.filter(_.isFile)
      .filter(fileName => hasNoExtension(fileName.getAbsolutePath))
      .toList

    logger.info(s"Got [${output.size}] files in the output")
    output
  }

  private def toParquetTypes(srcSchema: util.LinkedHashMap[String, String]): List[Type] = {
    val types: ListBuffer[Type] = new ListBuffer[Type]()

    for (entry <- srcSchema.entrySet().asScala) {
      val (srcName, srcType) = (entry.getKey, entry.getValue)
      val columnType = srcType match {
        // small ints
        case smallInt if smallInt.contains("SMALLINT") ||
          smallInt.contains("BYTEINT") => Types.optional(PrimitiveTypeName.INT32).as(LogicalTypeAnnotation.intType(8, true).toOriginalType)
        // big ints
        case bigInt if bigInt.contains("BIGINT") => Types.optional(PrimitiveTypeName.INT96)
        // usual ints
        case num if num.contains("NUMERIC") ||
          num.contains("INTEGER") ||
          num.contains("NUMBER") => Types.optional(PrimitiveTypeName.INT64).as(LogicalTypeAnnotation.intType(64, true).toOriginalType)
        // UTF-8 arbitrary strings
        case char if char.contains("CHAR") ||
          char.contains("VARCHAR") ||
          char.contains("CLOB") ||
          char.contains("XML") => Types.optional(PrimitiveTypeName.BINARY).as(LogicalTypeAnnotation.stringType().toOriginalType)
        // binary/blob
        case binary if binary.contains("BLOB") ||
          binary.contains("BYTE") ||
          binary.contains("VARBYTE") => Types.optional(PrimitiveTypeName.BINARY)
        // stringified date/tz
        case strDate if strDate.contains("INTDATE") => Types.optional(PrimitiveTypeName.BINARY).as(LogicalTypeAnnotation.stringType().toOriginalType)
        case strTime if strTime.contains("TIMESTAMP") ||
          strTime.contains("TIME") ||
          strTime.contains("TIMESTAMP WITH TIME ZONE") ||
          strTime.contains("TIME WITH TIME ZONE") => Types.optional(PrimitiveTypeName.INT64).as(LogicalTypeAnnotation.timeType(true, LogicalTypeAnnotation.TimeUnit.MILLIS).toOriginalType)

        case f if f.contains("FLOAT") || f.contains("DOUBLE") => Types.optional(PrimitiveTypeName.DOUBLE)
        case unsupportedFormat =>
          // USER‑DEFINED TYPE
          // etc
          throw new IllegalArgumentException(s"Source type $unsupportedFormat could not be converted from CSV to Parquet")
      }
      val result = columnType.named(srcName)

      types.append(result)
    }

    types.toList
  }

  def handleConversion(srcFile: File, srcSchema: util.LinkedHashMap[String, String],
                       targetFormat: WarehouseCopyFileFormat, tableName: String): File = {
    val targetFile = new File(stripExtension(srcFile.getAbsolutePath).concat(".").concat(targetFormat.extension))

    if (targetFile.exists()) {
      logger.warn(s"Target file ${targetFile.getAbsolutePath} already exists, cleaning it")
      targetFile.delete()
    }
    logger.info(s"Handling conversion of source file ${srcFile.getAbsolutePath} to output file ${targetFile.getAbsolutePath}")
    val source = scala.io.Source.fromFile(srcFile.getAbsolutePath)
    val iterator = source.getLines()
    val tgtColumns = toParquetTypes(srcSchema)
    val tgtSchema = new MessageType(tableName, tgtColumns: _*)

    val conf = hadoopConfiguration()
    val fsPath = new Path(targetFile.getAbsolutePath)
    val file = HadoopOutputFile.fromPath(fsPath, conf)
    val rwBuilder = new RecordWriterBuilder(tgtSchema, file, Seq()).withConf(conf)

    val rwBuilderWithCompressionWMode = targetFormat.compression match {
      case Compression.SNAPPY => rwBuilder
        .withCompressionCodec(CompressionCodecName.SNAPPY)
        .withWriteMode(ParquetFileWriter.Mode.OVERWRITE)
      case Compression.GZIP => rwBuilder
        .withCompressionCodec(CompressionCodecName.GZIP)
        .withWriteMode(ParquetFileWriter.Mode.OVERWRITE)
      case Compression.NONE => rwBuilder
        .withCompressionCodec(CompressionCodecName.UNCOMPRESSED)
        .withWriteMode(ParquetFileWriter.Mode.OVERWRITE)
    }

    val writer = rwBuilderWithCompressionWMode.build()
    try {
      for (line <- iterator) {
        // explicit "empty, but not null" fields handling for parquet
        val rawLine = line.split(",").map(possiblyEmpty => if (possiblyEmpty.equals("")) null else possiblyEmpty)
        writer.write(rawLine.toSeq)
      }

    } finally {
      logger.info(s"Written ${writer.getDataSize} bytes to parquet file, closing the source and writer")
      writer.close()
      source.close()
    }

    targetFile
  }

  private def findNonEmptyTptResults(dir: String, pathPredicate: String => Boolean): List[java.nio.file.Path] = {
    Files.list(Paths.get(dir))
      .iterator()
      .asScala
      .toList
      // default flow, we have CSVs now, time to rename and upload them
      // we are interested only in files not having an extension, since
      // TPT doesn't know how to append the sequence file number behind the extension
      // https://support.teradata.com/community?id=community_question&sys_id=041b8faf1b97fb00682ca8233a4bcb08
      .filter(path => pathPredicate(path.toFile.getAbsolutePath))
      .filter(path => Files.size(path) > 0)
  }

  private def convertFilesIfNecessary(sourceConfig: JdbcSourceConnectorConfig, sourceDataSchema: util.LinkedHashMap[String, String],
                                      tptRunDirectory: String): Boolean = {
    val targetFormat = unloadFileFormat(sourceConfig, dialectRegistry.fromConnectionString(sourceConfig.authConfig))
    val defaultFormat = dialectRegistry.fromConnectionString(sourceConfig.authConfig).defaultSourceFileFormat().get()

    // files need to be converted if the unload and default formats are different
    // TPT exports all things to extension-less files, so we either list all files without extensions and append ".CSV" to them
    // or convert them to format and append ".format_extension"

    if (targetFormat.equals(defaultFormat)) {
      logger.info(s"Target warehouse export format is default, no conversion necessary")
      logger.info(s"Appending correct extension to non-empty files and will upload them later")
      findNonEmptyTptResults(tptRunDirectory, hasNoExtension).map(path => appendDefaultExtension(path.toFile))
      false
    } else {
      logger.info(s"Export format is CSV, but target format is $targetFormat, doing the conversion")
      // tpt bug/unconfirmed behavior: compression on TPT side works only for the case when you have a single file.
      // for multiple files, you'll need to compress them on your own - https://teradata165.rssing.com/chan-6447515/all_p81.html
      if (Set(PARQUET_UNCOMPRESSED, PARQUET_GZIP, PARQUET_SNAPPY).contains(targetFormat)) {
        logger.info(s"Converting output files to $targetFormat")
        val filesToConvert = noExtensionFilesInDir(tptRunDirectory)
        for (srcFile <- filesToConvert) {
          try {
            // todo: configure for better flexibility
            val targetFile = handleConversion(srcFile, sourceDataSchema, targetFormat, sourceConfig.table.get())
            logger.info(s"File ${srcFile.getName} converted to ${targetFile.getName}, removing the original")
            srcFile.delete()
          } catch {
            case e: Exception =>
              logger.error(s"File ${srcFile.getAbsolutePath} conversion failed, cause: ", e)
              throw new IllegalArgumentException(e)
          }
        }
        true
      } else {
        logger.error(s"Unsupported target format [$targetFormat], cannot convert the files before export. Will upload them as CSV")
        val tptFiles = findNonEmptyTptResults(tptRunDirectory, hasNoExtension)
        tptFiles.map(path => appendDefaultExtension(path.toFile))
        false
      }
    }
  }

  private def listUnloadedFilesDirect(sourceCfg: JdbcSourceConnectorConfig,
                                      unloadFormat: WarehouseCopyFileFormat,
                                      tableName: String,
                                      tempFolder: String,
                                      conversionPerformed: Boolean) = {
    val allFiles = Files.list(Paths.get(tempFolder))
      .iterator()
      .asScala
      .toList
    logger.info(s"Got ${allFiles.size} in total in output dir $tempFolder, leaving only necessary for further processing")

    val unloadFiles = if (conversionPerformed) {
      logger.info(s"Files were converted from CSV to ${unloadFormat.extension}, finding files which contain it in name/extension")
      findNonEmptyTptResults(tempFolder, _ => true)
        .filter(path => path.toAbsolutePath.toString.contains(unloadFormat.extension))
        .map(path => path.toFile)
    } else {
      logger.info(s"Conversion was not performed, listing the csv files")
      findNonEmptyTptResults(tempFolder, _ => true)
        .filter(path => path.toAbsolutePath.toString.contains("csv"))
        .map(path => path.toFile)
    }

    logger.info(s"After filtering, got ${unloadFiles.size} to upload for source ${sourceCfg.sourceId}")

    val files = Source(unloadFiles).map(obj => FileToReplicate(None, obj.getAbsolutePath, Some(unloadFormat), Some(obj), tableName))
    RemoteReplicationMetadata(files.map(List(_)), dummyConfig, new LocalConnectorService)
  }

  private def hasNoExtension(absPath: String): Boolean = {
    val lastSep = absPath.lastIndexOf(FileSystems.getDefault.getSeparator)
    val lastDot = absPath.lastIndexOf(".")

    lastSep > lastDot
  }

  private def stripExtension(absPath: String): String = {
    if (absPath.lastIndexOf(".") != -1) {
      absPath.substring(0, absPath.lastIndexOf("."))
    } else {
      logger.warn(s"File $absPath has no extension already, returning original path/file name")
      absPath
    }
  }

  private def appendDefaultExtension(origFile: File): File = {
    val newFile = new File(origFile.getAbsolutePath + ".csv")
    val operationResult = origFile.renameTo(newFile)
    logger.info(s"File ${origFile.getName} in output dir renamed to ${newFile.getName}, result successful: $operationResult")
    newFile
  }

  // todo remove it when refactor to inplace download on Unload Source level
  private def dummyConfig = {
    val config = new util.HashMap[String, String]
    config.put(SOURCE_ID, "1234")
    config.put(CREDS_ENC, "1")
    config.put(CREDS_ENC_IV, "1")
    config.put(CREDENTIALS_DECRYPT_KEY, "1")
    config.put(DEPTH, "1")
    config.put(ACCESS_TOKEN, "test_token")
    config.put(SOURCE_TYPE, DROPBOX.name)
    config.put(UNIT_TEST, "true")
    config.put(LISTING_APP_SERVER_URL, "123")
    config.put(DIR_SCANNING_MODE, DIRECTORIES.name)

    new FileSourceConnectorConfig(config)
  }

  private def unloadFileFormat(sourceCfg: JdbcSourceConnectorConfig,
                               sourceDialect: DbDialect): WarehouseCopyFileFormat = {
    val desiredSourceFormat = sourceCfg.unloadFormat.asScala
      .orElse(sourceDialect.defaultSourceFileFormat().asScala)
    logger.info(s"source will be exported as [${desiredSourceFormat.getOrElse("N/A")}]")
    desiredSourceFormat.get
  }

  // todo: refactoring for making it testable easier
  lazy val sourceConfig: JdbcSourceConnectorConfig = {
    val dataSource = pipelineContext.source
    val sourceConfig = fullDataSourceConfig(props.config, dataSource, props.enrichSourceParams, JdbcSourceConnectorConfig.configDef())

    enrichWithDataCredentials(adminApiClient, sourceConfig)
    new JdbcSourceConnectorConfig(sourceConfig)
  }
}
