package com.nexla.replication_connector.connectors

import akka.actor.ActorSystem
import akka.stream.Materializer
import com.nexla.admin.client.AdminApiClient
import com.nexla.common.ConnectionType
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.listing.client.{FileVaultClient, ListingClient => JavaListingClient}
import com.nexla.replication_connector.AppProps
import com.nexla.replication_connector.connectors.sinks.{BigQuerySink, FileSink, ReplicationSinkConnector, WarehouseUploadSink}
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.messaging.ReplicationMessageProducer
import com.nexla.sc.util.StrictNexlaLogging

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Try}

class SinkConnectorFactory(
                            pipelineContext: Context,
                            adminApiClient: AdminApiClient,
                            javaListingClient: JavaListingClient,
                            fileVaultClient: FileVaultClient,
                            messageProducer: ReplicationMessageProducer,
                            props: AppProps,
                            probeFactory: ProbeFactory,
                          )
                          (implicit system: ActorSystem,
                           mat: Materializer,
                           ec: ExecutionContext) extends StrictNexlaLogging {

  def create(): Try[ReplicationSinkConnector] = {
    val connectionType = pipelineContext.sink.getConnectionType
    if (connectionType.isFile) {
      Try(new FileSink(
        pipelineContext,
        javaListingClient,
        adminApiClient,
        fileVaultClient,
        props,
        probeFactory,
      ))
    } else if (connectionType.equals(ConnectionType.BIGQUERY)) {
      Try(new BigQuerySink(pipelineContext, adminApiClient, props))
    } else if (connectionType.isWarehouseSink) {
      Try(new WarehouseUploadSink(pipelineContext, adminApiClient, props))
    } else {
      Failure(new Exception(s"Unsupported: $connectionType"))
    }
  }

}