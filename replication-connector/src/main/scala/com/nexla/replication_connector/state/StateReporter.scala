package com.nexla.replication_connector.state

import akka.actor.{ActorSystem, Cancellable}
import cats.implicits.toTraverseOps
import com.nexla.admin.client.PipelineRunState
import com.nexla.admin.utils.SourceUtils
import com.nexla.common.datetime.DateTimeUtils
import com.nexla.common.{AppType, NexlaConstants, Resource, ResourceType}
import com.nexla.control.ListingFileStatus
import com.nexla.control.coordination.{HeartbeatConnectorCoordination, HeartbeatType, SetFileStatusCoordination}
import com.nexla.control.message.{ControlEventType, SourceControlMessage}
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.messaging.ReplicationMessageProducer
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum
import com.nexla.sc.client.job_scheduler.PipelineTaskStateEnum.PipelineTaskState
import com.nexla.sc.client.listing.CoordinationAppClient
import com.nexla.sc.util.StrictNexlaLogging

import java.time.LocalDateTime
import java.util.{Optional, UUID}
import scala.annotation.nowarn
import scala.compat.java8.OptionConverters.RichOptionForJava8
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}

class StateReporter(coordinationClient: CoordinationAppClient,
                    controlMessageProducer: ReplicationMessageProducer,
                    initialDelay: FiniteDuration,
                    heartbeatPeriod: FiniteDuration)
                   (implicit val system: ActorSystem, ec: ExecutionContext) extends StrictNexlaLogging {
  private var cancellable: Option[Cancellable] = None

  private def heartbeatMessage(pipelineContext: Context, now: LocalDateTime, resource: Resource, pipelineTaskState: PipelineTaskState): HeartbeatConnectorCoordination = {
    val msg = new HeartbeatConnectorCoordination(
      UUID.randomUUID().toString,
      resource.`type`,
      resource.id,
      pipelineContext.runId,
      HeartbeatType.DATA,
      DateTimeUtils.toMillis(now),
    )
    msg.getContext.put("flowType", pipelineContext.flowType.name())
    msg.getContext.put("flowId", String.valueOf(pipelineContext.flowId))
    msg.getContext.put("sourceId", String.valueOf(pipelineContext.source.getId))
    msg.getContext.put("state", taskStateToRunState(pipelineTaskState).name())
    msg
  }

  private def taskStateToRunState(pipelineTaskState: PipelineTaskState): PipelineRunState = pipelineTaskState match {
    case PipelineTaskStateEnum.NotRunning => PipelineRunState.NOT_STARTED
    case PipelineTaskStateEnum.Running => PipelineRunState.IN_PROGRESS
    case PipelineTaskStateEnum.Finished => PipelineRunState.DONE
    case PipelineTaskStateEnum.Failed => PipelineRunState.ERROR
    case PipelineTaskStateEnum.Decomissioning => PipelineRunState.IN_PROGRESS
  }

  private def heartbeatFiles(pipelineContext: Context, pipelineState: State): Future[Unit] = {
    val fileIds = pipelineState.getFileIdsForHeartbeat()
    if (fileIds.nonEmpty) coordinationClient.batchHeartBeat(pipelineContext.source.getId, fileIds)
    else Future.unit
  }

  private def markRemainingFilesAsStopped(pipelineContext: Context, pipelineState: State): Future[Unit] = {
    val fileIds = pipelineState.getFileIdsForHeartbeat()
    if (fileIds.nonEmpty) {
      logger.info(s"Marking remaining files as stopped: [${pipelineState.getFileIdsForHeartbeat()}]")
      @nowarn("cat=deprecation")
      val events = fileIds.toList.map(fileId =>
        new SetFileStatusCoordination(UUID.randomUUID().toString, pipelineContext.source.getId.toLong, fileId, ListingFileStatus.STOPPED, null, "File processing incomplete", System.currentTimeMillis() + 1.hours.toMillis)
      )
      events.traverse { event =>
        Future(controlMessageProducer.sendCoordinationEvent(event))
      }.map(_ => ())
    } else Future.unit
  }

  private var lastSeenSourceStatus: Option[PipelineTaskState] = None

  private def heartbeatSource(pipelineContext: Context, pipelineState: State) = {
    val currentStatus = pipelineState.getReadStatus()
    val stateChanged = !lastSeenSourceStatus.contains(currentStatus)
    lastSeenSourceStatus = Some(currentStatus)
    if (currentStatus == PipelineTaskStateEnum.Running || stateChanged) {
      val now = DateTimeUtils.nowUtc()
      val sourceMsg = heartbeatMessage(pipelineContext, now, new Resource(pipelineContext.source.getId, ResourceType.SOURCE), currentStatus)
      Future(controlMessageProducer.sendHeartbeat(sourceMsg))
    } else Future.unit
  }

  private var lastSeenSinkStatus: Option[PipelineTaskState] = None

  private def heartbeatSink(pipelineContext: Context, pipelineState: State) = {
    val currentStatus = pipelineState.getWriteStatus()
    val stateChanged = !lastSeenSinkStatus.contains(currentStatus)
    lastSeenSinkStatus = Some(currentStatus)
    if (currentStatus == PipelineTaskStateEnum.Running || stateChanged) {
      val now = DateTimeUtils.nowUtc()
      val sinkMsg = heartbeatMessage(pipelineContext, now, new Resource(pipelineContext.sink.getId, ResourceType.SINK), currentStatus)
      Future(controlMessageProducer.sendHeartbeat(sinkMsg))
    } else Future.unit
  }

  private def emitEventForCtrlToFreeUpResources(pipelineContext: Context): Future[Unit] = {
    val dockerInstances = pipelineContext.source.getSourceConfig.getOrDefault("docker.instances", "1").toString.toInt
    if (dockerInstances == 1) {
      val context = java.util.Map.of(NexlaConstants.HARD_STOP, String.valueOf(true))
      val dataSource = pipelineContext.source

      val stopMsg = new SourceControlMessage(
        UUID.randomUUID(),
        dataSource.getId,
        ControlEventType.PAUSE,
        dataSource.getConnectionType,
        AppType.REPLICATION_CONNECTOR.appName,
        context,
        Option(SourceUtils.toResourceDto(dataSource)).asJava,
        None.asJava,
      )
      Future {
        controlMessageProducer.sendControlMessage(stopMsg)
        logger.info("Final state report and STOP message sent")
      }
    } else {
      logger.info(s"Final state report sent. Skipping STOP message because docker.instances = $dockerInstances")
      Future.unit
    }
  }

  def start(pipelineContext: Context, pipelineState: State): Unit = {
    logger.info(s"Scheduling state reporter runs")
    val c = system.scheduler.scheduleWithFixedDelay(initialDelay, heartbeatPeriod)(() => {
      val _ = reportState(pipelineContext, pipelineState)
    })
    this.cancellable = Some(c)
  }

  def finalReportAndFinish(pipelineContext: Context, pipelineState: State): Future[Unit] = {
    logger.info(s"Reporting state for the last time...")
    this.cancellable.foreach(_.cancel())
    for {
      _ <- reportState(pipelineContext, pipelineState)
      _ <- markRemainingFilesAsStopped(pipelineContext, pipelineState)
      _ <- emitEventForCtrlToFreeUpResources(pipelineContext)
    } yield {
      controlMessageProducer.flush()
    }
  }

  private def reportState(pipelineContext: Context, pipelineState: State): Future[Unit] = {
    logger.info(s"Reporting state: ${pipelineState.summary()}")
    Future.sequence(List(
      heartbeatSource(pipelineContext, pipelineState),
      heartbeatSink(pipelineContext, pipelineState),
      heartbeatFiles(pipelineContext, pipelineState),
    )).map(_ => ())
  }

}
