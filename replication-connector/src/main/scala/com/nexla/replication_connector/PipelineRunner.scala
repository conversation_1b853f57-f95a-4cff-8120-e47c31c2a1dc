package com.nexla.replication_connector

import akka.actor.{ActorSystem, Scheduler}
import com.nexla.admin.client.AdminApiClient
import com.nexla.inmemory_connector_common.PipelineKiller
import com.nexla.inmemory_connector_common.listing.ListingStreamSource
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.listing.client.{FileVaultClient, ListingClient => JavaListingClient}
import com.nexla.replication_connector.context.{Context, ContextBuilder}
import com.nexla.replication_connector.messaging.ReplicationMessageProducer
import com.nexla.replication_connector.pipeline._
import com.nexla.replication_connector.state.{State, StateReporter}
import com.nexla.sc.client.listing.{CoordinationAppClient, ListingAppClient}
import com.nexla.sc.util.StrictNexlaLogging

import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}


class PipelineRunner(flowId: Int,
                     runId: Long,
                     adminApi: AdminApiClient,
                     listingClient: ListingAppClient,
                     listingStreamSource: ListingStreamSource,
                     javaListingClient: JavaListingClient,
                     coordinationClient: CoordinationAppClient,
                     fileVault: FileVaultClient,
                     probeFactory: ProbeFactory,
                     props: AppProps,
                     ControlMessageProducer: ReplicationMessageProducer)(implicit system: ActorSystem, ec: ExecutionContext, sch: Scheduler) extends StrictNexlaLogging {

  private val pipelineContextBuilder = new ContextBuilder(adminApi)
  private val pipelineBuilder = new PipelineBuilder(adminApi, listingClient, listingStreamSource, javaListingClient, fileVault, ControlMessageProducer, props, probeFactory)
  private val stateReporter = new StateReporter(coordinationClient, ControlMessageProducer, initialDelay = 10.seconds, heartbeatPeriod = 1.minute)

  def run(pipelineKiller: PipelineKiller): Future[Unit] = for {
    pipelineContext <- pipelineContextBuilder.createPipelineContext(flowId, runId)
    pipelineState = new State()
    pipeline = pipelineBuilder.createPipeline(pipelineContext, pipelineState)
    _ = stateReporter.start(pipelineContext, pipelineState)
    _ <- startPipeline(pipeline, pipelineContext, pipelineState, pipelineKiller)
  } yield ()

  private def startPipeline(pipeline: ReplicationPipeline, pipelineContext: Context, pipelineState: State, pipelineKiller: PipelineKiller): Future[Unit] = {
    logger.info("Pipeline started")
    pipeline.startPipeline(pipelineKiller)
      .transformWith { r =>
        stateReporter.finalReportAndFinish(pipelineContext, pipelineState).flatMap(_ => Future.fromTry(r))
      }
      .map(_ => ())
  }

}
