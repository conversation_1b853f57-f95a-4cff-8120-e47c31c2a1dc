package com.nexla.replication_connector.utils

import com.nexla.admin.client._
import com.nexla.admin.client.config.EnrichedConfig.{EnrichSinkParams, EnrichSourceParams}
import com.nexla.admin.client.flownode.{AdminApiFlow, FlowNodeDatasink, FlowNodeDatasource, NexlaFlow}
import com.nexla.common.ConnectionType
import com.nexla.connector.config.FlowType
import com.nexla.connector.config.vault.{ConfigEnv, CredentialsStore, NexlaAppConfig}
import com.nexla.replication_connector.AppProps
import com.nexla.sc.client.listing.{FileSourceTypes, FileStatuses, ListedFile, ListingAppClient, ListingResult}
import com.typesafe.config.Config
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers
import org.mockito.Mockito.{mock, when}

import java.time.LocalDateTime
import java.util
import java.util.Optional
import scala.concurrent.Future
import scala.util.Random

object Fixtures {
  val stubProps: AppProps = {
    val instance = mock(classOf[AppProps])
    when(instance.imcFlowId).thenReturn(Random.nextInt())
    when(instance.imcRunId).thenReturn(Random.nextLong())
    when(instance.trackerDisabled).thenReturn(true)
    val esp = new EnrichSourceParams(Optional.empty(), Optional.empty(), 1, 1,
      "smth", Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty()
    )
    val esp2 = new EnrichSinkParams("b", Optional.empty(), Optional.empty(),
      Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty()
    )

    when(instance.enrichSourceParams).thenReturn(esp)
    when(instance.enrichSinkParams).thenReturn(esp2)
    val mockEnv = mock(classOf[ConfigEnv])
    when(mockEnv.getName).thenReturn("local")
    val nxCfg = new NexlaAppConfig("abc", mockEnv, mock(classOf[Config]), mock(classOf[CredentialsStore]),
      new java.util.ArrayList[String](), new java.util.HashMap[String, String]())
    when(instance.config).thenReturn(nxCfg)
    instance
  }

  def adminDataSource(id: Int, config: java.util.Map[String, Object]): DataSource = {
    val stubSource = new DataSource
    stubSource.setId(id)
    stubSource.setConnectionType(ConnectionType.FILE_UPLOAD)
    stubSource.setSourceConfig(config)
    stubSource
  }

  def mockAdminApiClient(flowId: Int, dataSource: DataSource, dataSink: DataSink): AdminApiClient = {
    val instance = mock(classOf[AdminApiClient])
    when(instance.getDataSource(dataSource.getId)).thenReturn(Optional.of(dataSource))
    when(instance.getDataSink(dataSink.getId)).thenReturn(Optional.of(dataSink))
    val flowNodeDataSource = new FlowNodeDatasource(dataSource.getId, null, null, null, null, null, null, null, null, null, null, null, null, null, null)
    val flowNodeDataSink = new FlowNodeDatasink(dataSink.getId, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)
    val adminApiFlow = new AdminApiFlow(flowId, null, null, null, null, FlowType.REPLICATION, null, new util.ArrayList())
    val flow = new NexlaFlow(util.Arrays.asList(adminApiFlow), util.Arrays.asList(flowNodeDataSource), util.List.of(), util.Arrays.asList(flowNodeDataSink))
    when(instance.getNexlaFlow(flowId)).thenReturn(Optional.of(flow))
    instance
  }

  def mockOrg(orgId: Int): Org = {
    new Org(orgId, "a", "b", null, Optional.empty[OrgTier], false, DateTime.now(), AccountStatus.ACTIVE, DateTime.now())
  }

  def mockListing(sourceId: Int, files: List[String]): ListingAppClient = {
    val instance = mock(classOf[ListingAppClient])

    val listedFiles = files.zipWithIndex.map { case (fileName, idx) =>
      Future.successful(
        ListingResult(Option(ListedFile(idx, fileName, None, None, None, None, FileSourceTypes.Listing, FileStatuses.New, None, None, LocalDateTime.now())), inProgress = false)
      )
    }
    val empty: Future[ListingResult] = Future.successful(ListingResult(None, inProgress = false))

    val results = listedFiles ::: List(empty)
    when(instance.setFileStatus(ArgumentMatchers.anyInt(), ArgumentMatchers.anyLong(), ArgumentMatchers.any[FileStatuses.FileStatus](), ArgumentMatchers.any[Option[Long]](), ArgumentMatchers.any[Option[String]]())).thenReturn(Future.unit)
    when(instance.takeFile(sourceId)).thenReturn(results.head, results.tail: _*)
    instance
  }

}
