package com.nexla.replication_connector

import akka.actor.{ActorSystem, Scheduler}
import akka.stream.Materializer
import com.fasterxml.jackson.databind.ObjectMapper
import com.nexla.admin.client._
import com.nexla.admin.client.oauth2.RefreshingTokenProvider
import com.nexla.common.NexlaConstants.{TOPIC_METRICS, TOPIC_MONITORING_LOGS, TOPIC_NOTIFY}
import com.nexla.common.notify.monitoring.{NexlaMonitoringLogEvent, NexlaMonitoringLogType}
import com.nexla.common.notify.transport.ControlMessageProducer
import com.nexla.common.{ConnectionType, NexlaKafkaConfig, NexlaSslContext, ResourceType}
import com.nexla.connector.config.FlowType
import com.nexla.file.service.FileConnectorService
import com.nexla.inmemory_connector_common.PipelineKiller
import com.nexla.inmemory_connector_common.listing.ListingStreamSource
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.kafka.KafkaMessageTransport
import com.nexla.listing.client.{FileVaultClient, ListingClient => JavaListingClient}
import com.nexla.probe.ftp.FileConnectorServiceBuilder
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.context.Context.RetryConfig
import com.nexla.replication_connector.flow_logs.FlowLogsSender
import com.nexla.replication_connector.messaging.ReplicationMessageProducer
import com.nexla.replication_connector.metrics.MetricsSender
import com.nexla.replication_connector.pipeline.ReplicationPipeline
import com.nexla.replication_connector.state.State
import com.nexla.replication_connector.utils.{Fixtures, LocalFileSystemConnectorService, ShutdownActorSystem}
import com.nexla.sc.client.job_scheduler.TaskRequest.TaskId
import io.github.embeddedkafka.Codecs.stringDeserializer
import io.github.embeddedkafka.EmbeddedKafka.withConsumer
import io.github.embeddedkafka.{EmbeddedK, EmbeddedKafka, EmbeddedKafkaConfig, EmbeddedServer}
import org.mockito.Mockito.mock
import org.scalatest.{Assertion, BeforeAndAfterEach}
import org.scalatest.concurrent.Eventually.eventually
import org.scalatest.concurrent.PatienceConfiguration.Timeout
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

import java.nio.file.{Files, Paths}
import java.util
import scala.concurrent.ExecutionContextExecutor
import scala.concurrent.duration.DurationInt
import scala.jdk.CollectionConverters.iterableAsScalaIterableConverter
import scala.util.Random

class FlowLogsSenderSpec extends AnyFunSuite with Matchers with ShutdownActorSystem with BeforeAndAfterEach {
  implicit val system: ActorSystem = ActorSystem()
  private implicit val ec: ExecutionContextExecutor = system.dispatcher
  private implicit val mat: Materializer = Materializer(system)
  private implicit val sch: Scheduler = system.scheduler

  private val SourceRootPath = getClass.getResource("/test_data/input1").getPath
  private val DestinationRootPath = Files.createTempDirectory(getClass.getSimpleName).toAbsolutePath.toString

  private var kafkaConfig: EmbeddedK = _

  override protected def beforeEach(): Unit = {
    super.beforeEach()
    kafkaConfig = EmbeddedKafka.start()(EmbeddedKafkaConfig(0, 0))
  }

  override protected def afterEach(): Unit = {
    super.afterEach()
    EmbeddedKafka.stop(kafkaConfig)
  }

  private val jsonObjectMapper = new ObjectMapper()

  private def createDataSource(sourceId: Int, org: Org) = {
    val dataSource = new DataSource()
    dataSource.setId(sourceId)
    dataSource.setOrg(org)
    dataSource.setConnectionType(ConnectionType.FILE_UPLOAD)
    dataSource.setSourceConfig(new util.HashMap[String, AnyRef] {
      {
        put("path", SourceRootPath)
      }
    })
    dataSource
  }

  private def createDataSink(sinkId: Int, org: Org) = {
    val dataSink = new DataSink()
    dataSink.setId(sinkId)
    dataSink.setOrg(org)
    dataSink.setConnectionType(ConnectionType.FTP)
    dataSink.setSinkConfig(new util.HashMap[String, AnyRef] {
      {
        put("path", DestinationRootPath)
      }
    })
    val ds = new DataSet
    ds.setId(1)
    dataSink.setDataSet(ds)
    dataSink
  }

  test("successful pipeline should emit proper flow logs") {
    val runId = Random.nextLong()
    val flowId = Random.nextInt()
    val org = Fixtures.mockOrg(Random.nextInt())
    val dataSource = createDataSource(Random.nextInt(), org)
    val dataSink = createDataSink(Random.nextInt(), org)

    val ctx = Context(runId, "task-1", FlowType.REPLICATION, flowId, dataSource, dataSink.getDataSet, dataSink, RetryConfig(1, 1))
    val state = new State
    val srcFile1Path = Paths.get(SourceRootPath, "/file1.csv").toString
    val srcFile2Path = Paths.get(SourceRootPath, "/file2.csv").toString

    val listingAppClient = Fixtures.mockListing(dataSource.getId, List(srcFile1Path, srcFile2Path))
    val adminApiClient = Fixtures.mockAdminApiClient(flowId, dataSource, dataSink)
    val javaListingClient = mock(classOf[JavaListingClient])
    val mockFileVault: FileVaultClient = mock(classOf[FileVaultClient])

    val props = Fixtures.stubProps
    val probeFactory = new ProbeFactory {
     override def getProbeService(adminApiClient: AdminApiClient, listingClient: JavaListingClient, decryptKey: String, sourceType: ConnectionType, fileVaultClient: FileVaultClient): FileConnectorService[_] = {
        if (sourceType.equals(ConnectionType.FTP)) {
          new LocalFileSystemConnectorService
        } else if (sourceType.equals(ConnectionType.FILE_UPLOAD)) {
          new LocalFileSystemConnectorService
        } else {
          throw new Exception(s"Unsupported type $sourceType")
        }
      }
    }

    implicit val embeddedKafkaConfig: EmbeddedKafkaConfig = kafkaConfig.config
      withConsumer[String, String, Assertion] { flowLogsConsumer =>
        flowLogsConsumer.subscribe(util.Arrays.asList(TOPIC_MONITORING_LOGS))

        val messageProducer = new ReplicationMessageProducer(new ControlMessageProducer(new KafkaMessageTransport(new NexlaKafkaConfig(s"localhost:${kafkaConfig.config.kafkaPort}"))))

        val flowLogsSender = new FlowLogsSender(messageProducer)(ec)
        val metricsSender = new MetricsSender(messageProducer)(ec)

        val listingStreamSource = new ListingStreamSource(listingAppClient, listingCooldownPeriod = 1.second)
        val pip = new ReplicationPipeline(ctx, state, adminApiClient, listingAppClient, listingStreamSource, javaListingClient, mockFileVault, messageProducer,
          props, probeFactory, flowLogsSender, metricsSender)

        pip.startPipeline(new PipelineKiller).futureValue(Timeout(100.seconds))
        messageProducer.flush()

        eventually {
          val records = flowLogsConsumer.poll(java.time.Duration.ofMillis(10.seconds.toMillis)).asScala.toList

          records.nonEmpty shouldBe true

          val receivedEvents = records.map(r => jsonObjectMapper.readValue(r.value(), classOf[NexlaMonitoringLogEvent]))
            .sortBy(e => (if (e.getResourceType == ResourceType.SOURCE) 0 else 1, e.getEventTimeMillis))

          val expectedEvents: List[(Integer, ResourceType, TaskId, NexlaMonitoringLogType, Integer, Long)] = List(
            (dataSource.getId, ResourceType.SOURCE, "Task initialization is done", NexlaMonitoringLogType.LOG, dataSource.getOrg.getId, ctx.runId),
            (dataSource.getId, ResourceType.SOURCE, "Start processing data", NexlaMonitoringLogType.LOG, dataSource.getOrg.getId, ctx.runId),
            (dataSource.getId, ResourceType.SOURCE, "Stopping task...", NexlaMonitoringLogType.LOG, dataSource.getOrg.getId, ctx.runId),
            (dataSource.getId, ResourceType.SOURCE, "Total of 23 (SUCCESS=23, ERROR=0) records processed", NexlaMonitoringLogType.LOG, dataSource.getOrg.getId, ctx.runId),
            (dataSink.getId, ResourceType.SINK, "Task initialization is done", NexlaMonitoringLogType.LOG, dataSink.getOrg.getId, ctx.runId),
            (dataSink.getId, ResourceType.SINK, "Start processing data", NexlaMonitoringLogType.LOG, dataSink.getOrg.getId, ctx.runId),
            (dataSink.getId, ResourceType.SINK, "Stopping task...", NexlaMonitoringLogType.LOG, dataSource.getOrg.getId, ctx.runId),
            (dataSink.getId, ResourceType.SINK, "Total of 23 (SUCCESS=23, ERROR=0) records processed", NexlaMonitoringLogType.LOG, dataSource.getOrg.getId, ctx.runId),
          )

          receivedEvents.zip(expectedEvents).foreach { case (received, expected) =>
            val selectedFields = (received.getResourceId, received.getResourceType, received.getLog, received.getLogType, received.getOrgId, received.getRunId)
            selectedFields shouldEqual expected
          }
          succeed
        }

    }

  }
}
