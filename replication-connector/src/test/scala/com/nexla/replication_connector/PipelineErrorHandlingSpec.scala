package com.nexla.replication_connector

import akka.actor.{ActorSystem, Scheduler}
import akka.stream.Materializer
import com.fasterxml.jackson.databind.ObjectMapper
import com.nexla.admin.client.{AdminApiClient, DataSet, DataSink, DataSource}
import com.nexla.common.NexlaConstants.{TOPIC_METRICS, TOPIC_MONITORING_LOGS, TOPIC_NOTIFY}
import com.nexla.common.metrics.NexlaRawMetric
import com.nexla.common.notify.monitoring.{NexlaMonitoringLogEvent, NexlaMonitoringLogSeverity}
import com.nexla.common.notify.transport.ControlMessageProducer
import com.nexla.common.{ConnectionType, NexlaKafkaConfig, NexlaSslContext, ResourceType}
import com.nexla.connector.config.FlowType
import com.nexla.connector.config.file.FileConnectorAuth
import com.nexla.file.service.{FileConnectorService, FileDetails}
import com.nexla.inmemory_connector_common.PipelineKiller
import com.nexla.inmemory_connector_common.listing.ListingStreamSource
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.kafka.KafkaMessageTransport
import com.nexla.listing.client.{FileVaultClient, ListingClient => JavaListingClient}
import com.nexla.replication_connector.context.Context
import com.nexla.replication_connector.context.Context.RetryConfig
import com.nexla.replication_connector.flow_logs.FlowLogsSender
import com.nexla.replication_connector.messaging.ReplicationMessageProducer
import com.nexla.replication_connector.metrics.MetricsSender
import com.nexla.replication_connector.pipeline.ReplicationPipeline
import com.nexla.replication_connector.state.State
import com.nexla.replication_connector.utils.{Fixtures, LocalFileSystemConnectorService, ShutdownActorSystem}
import com.nexla.sc.client.job_scheduler.TaskRequest.TaskId
import io.github.embeddedkafka.Codecs.stringDeserializer
import io.github.embeddedkafka.EmbeddedKafka.withConsumer
import io.github.embeddedkafka.{EmbeddedK, EmbeddedKafka, EmbeddedKafkaConfig}
import org.apache.commons.io.FileUtils
import org.mockito.Mockito.mock
import org.scalatest.concurrent.Eventually.eventually
import org.scalatest.concurrent.PatienceConfiguration.Timeout
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers
import org.scalatest.{Assertion, BeforeAndAfterEach}

import java.io.{File, InputStream}
import java.nio.file.{Files, Paths}
import java.util
import scala.concurrent.ExecutionContextExecutor
import scala.concurrent.duration.DurationInt
import scala.jdk.CollectionConverters.{iterableAsScalaIterableConverter, mapAsScalaMapConverter}
import scala.util.Random

class PipelineErrorHandlingSpec extends AnyFunSuite with Matchers with ShutdownActorSystem with BeforeAndAfterEach {
  implicit val system: ActorSystem = ActorSystem()
  private implicit val ec: ExecutionContextExecutor = system.dispatcher
  private implicit val mat: Materializer = Materializer(system)
  private implicit val sch: Scheduler = system.scheduler

  private val jsonObjectMapper = new ObjectMapper()
  private val javaListingClient = mock(classOf[JavaListingClient])
  private val mockFileVault: FileVaultClient = mock(classOf[FileVaultClient])
  private val mockMsgProducer: ReplicationMessageProducer = mock(classOf[ReplicationMessageProducer])
  private var kafkaConfig: EmbeddedK = _

  override protected def beforeEach(): Unit = {
    super.beforeEach()
    kafkaConfig = EmbeddedKafka.start()(EmbeddedKafkaConfig(0, 0))
  }

  override protected def afterEach(): Unit = {
    super.afterEach()
    EmbeddedKafka.stop(kafkaConfig)
  }

  private val props = Fixtures.stubProps


  private def createDataSource(sourceId: Int, sourceRootPath: String) = {
    val dataSource = new DataSource()
    dataSource.setId(sourceId)
    dataSource.setConnectionType(ConnectionType.FILE_UPLOAD)
    dataSource.setSourceConfig(new util.HashMap[String, AnyRef] {
      {
        put("path", sourceRootPath)
      }
    })
    dataSource
  }

  private def createDataSink(sinkId: Int, destinationRootPath: String) = {
    val dataSink = new DataSink()
    dataSink.setId(sinkId)
    dataSink.setConnectionType(ConnectionType.FTP)
    dataSink.setSinkConfig(new util.HashMap[String, AnyRef] {
      {
        put("path", destinationRootPath)
      }
    })
    val ds = new DataSet
    ds.setId(1)
    dataSink.setDataSet(ds)
    dataSink
  }

  test("should handle problems with downloading file") {
    val sourceRootPath = getClass.getResource("/test_data/input1").getPath
    val destinationRootPath = Files.createTempDirectory(getClass.getSimpleName).toAbsolutePath.toString
    val probeFactory = new ProbeFactory {
     override def getProbeService(adminApiClient: AdminApiClient, listingClient: JavaListingClient, decryptKey: String, sourceType: ConnectionType, fileVaultClient: FileVaultClient): FileConnectorService[_] = {
        if (sourceType.equals(ConnectionType.FTP)) {
          new LocalFileSystemConnectorService
        } else if (sourceType.equals(ConnectionType.FILE_UPLOAD)) {
          new LocalFileSystemConnectorService {
            override def readInputStreamInternal(c: FileConnectorAuth, key: TaskId): InputStream =
              if (key.endsWith("/file1.csv")) {
                throw new Exception("Some problem with reading file")
              } else {
                super.readInputStreamInternal(c, key)
              }
          }
        } else {
          throw new Exception(s"Unsupported type $sourceType")
        }
      }
    }

    val state = new State
    val runId = Random.nextLong()
    val flowId = Random.nextInt()
    val dataSource = createDataSource(Random.nextInt(), sourceRootPath)
    val dataSink = createDataSink(Random.nextInt(), destinationRootPath)
    val ctx = Context(runId, s"task-$flowId", FlowType.REPLICATION, flowId, dataSource, dataSink.getDataSet, dataSink, RetryConfig(1, 1))

    val srcFile1Path = Paths.get(sourceRootPath, "/file1.csv").toString
    val srcFile2Path = Paths.get(sourceRootPath, "/file2.csv").toString
    val expectedDstFile2Path = Paths.get(destinationRootPath, "/file2.csv").toString

    val listingAppClient = Fixtures.mockListing(dataSource.getId, List(srcFile1Path, srcFile2Path))
    val adminApiClient = Fixtures.mockAdminApiClient(flowId, dataSource, dataSink)

    val flowLogsSender = new FlowLogsSender(mockMsgProducer)(ec)
    val metricsSender = new MetricsSender(mockMsgProducer)(ec)
    val listingStreamSource = new ListingStreamSource(listingAppClient, listingCooldownPeriod = 1.second)
    val pip = new ReplicationPipeline(ctx, state, adminApiClient, listingAppClient, listingStreamSource, javaListingClient, mockFileVault, mockMsgProducer,
      props, probeFactory, flowLogsSender, metricsSender)
    pip.startPipeline(new PipelineKiller).futureValue(Timeout(70.seconds))

    val areFiles21Equal = FileUtils.contentEquals(new File(expectedDstFile2Path), new File(srcFile2Path))
    Files.exists(Paths.get(expectedDstFile2Path)) shouldBe true
    areFiles21Equal shouldBe true
  }

  test("should dispatch proper flow logs and metrics when there is problem with source") {
    val sourceRootPath = getClass.getResource("/test_data/input1").getPath
    val destinationRootPath = Files.createTempDirectory(getClass.getSimpleName).toAbsolutePath.toString
    val probeFactory = new ProbeFactory {
     override def getProbeService(adminApiClient: AdminApiClient, listingClient: JavaListingClient, decryptKey: String, sourceType: ConnectionType, fileVaultClient: FileVaultClient): FileConnectorService[_] = {
        if (sourceType.equals(ConnectionType.FTP)) {
          new LocalFileSystemConnectorService
        } else if (sourceType.equals(ConnectionType.FILE_UPLOAD)) {
          new LocalFileSystemConnectorService {
            override def readInputStreamInternal(c: FileConnectorAuth, key: TaskId): InputStream =
              if (key.endsWith("/file1.csv")) {
                throw new Exception("Some problem with reading file")
              } else {
                super.readInputStreamInternal(c, key)
              }
          }
        } else {
          throw new Exception(s"Unsupported type $sourceType")
        }
      }
    }

    val state = new State
    val runId = Random.nextLong()
    val flowId = Random.nextInt()
    val dataSource = createDataSource(Random.nextInt(), sourceRootPath)
    val dataSink = createDataSink(Random.nextInt(), destinationRootPath)
    val ctx = Context(runId, s"task-$flowId", FlowType.REPLICATION, flowId, dataSource, dataSink.getDataSet, dataSink, RetryConfig(1, 1))

    val srcFile1Path = Paths.get(sourceRootPath, "/file1.csv").toString
    val srcFile2Path = Paths.get(sourceRootPath, "/file2.csv").toString

    val listingAppClient = Fixtures.mockListing(dataSource.getId, List(srcFile1Path, srcFile2Path))
    val adminApiClient = Fixtures.mockAdminApiClient(flowId, dataSource, dataSink)
    val listingStreamSource = new ListingStreamSource(listingAppClient, listingCooldownPeriod = 1.second)

    implicit val embeddedKafkaConfig: EmbeddedKafkaConfig = kafkaConfig.config
      withConsumer[String, String, Assertion] { consumer =>
        consumer.subscribe(util.Arrays.asList(TOPIC_MONITORING_LOGS, TOPIC_METRICS))

        val messageProducer = new ReplicationMessageProducer(
          new ControlMessageProducer(
            new KafkaMessageTransport(new NexlaKafkaConfig(s"localhost:${kafkaConfig.config.kafkaPort}"))
          )
        )
        val flowLogsSender = new FlowLogsSender(messageProducer)(ec)
        val metricsSender = new MetricsSender(messageProducer)(ec)
        val pip = new ReplicationPipeline(ctx, state, adminApiClient, listingAppClient, listingStreamSource, javaListingClient, mockFileVault, messageProducer,
          props, probeFactory, flowLogsSender, metricsSender)
        pip.startPipeline(new PipelineKiller).futureValue(Timeout(70.seconds))

        eventually {
          val records = consumer.poll(java.time.Duration.ofMillis(10.seconds.toMillis)).asScala
            .groupBy(_.topic()).mapValues(_.map(_.value()))

          val metrics = records(TOPIC_METRICS).map(r => jsonObjectMapper.readValue(r, classOf[NexlaRawMetric]))
          val flowLogs = records(TOPIC_MONITORING_LOGS).map(r => jsonObjectMapper.readValue(r, classOf[NexlaMonitoringLogEvent]))

          val errorMetrics = metrics.filter(m => m.hasErrors).toList
          errorMetrics.length shouldEqual 1
          errorMetrics.head.getResourceType shouldEqual ResourceType.SOURCE
          errorMetrics.head.getResourceId shouldEqual dataSource.getId
          errorMetrics.head.getTags.get(NexlaRawMetric.NAME) shouldEqual Paths.get(sourceRootPath, "file1.csv").toString
          errorMetrics.head.getFields.asScala(NexlaRawMetric.ERROR_COUNT) shouldEqual 1
          errorMetrics.head.getFields.asScala("error_message") shouldEqual "Some problem with reading file"

          val errorFlowLogs = flowLogs.filter(m => m.getSeverity == NexlaMonitoringLogSeverity.ERROR).toList
          errorFlowLogs.length shouldEqual 1
          errorFlowLogs.head.getResourceId shouldEqual dataSource.getId
          errorFlowLogs.head.getResourceType shouldEqual ResourceType.SOURCE
          errorFlowLogs.head.getLog shouldEqual s"Error while processing record ${Paths.get(sourceRootPath, "file1.csv").toString} in the source connector. Error: Some problem with reading file"

        }
        succeed
    }

  }

  test("should handle problems with uploading file") {
    val sourceRootPath = getClass.getResource("/test_data/input1").getPath
    val destinationRootPath = Files.createTempDirectory(getClass.getSimpleName).toAbsolutePath.toString

    val probeFactory = new ProbeFactory {
     override def getProbeService(adminApiClient: AdminApiClient, listingClient: JavaListingClient, decryptKey: String, sourceType: ConnectionType, fileVaultClient: FileVaultClient): FileConnectorService[_] = {
        if (sourceType.equals(ConnectionType.FTP)) {
          new LocalFileSystemConnectorService {
            override def writeInternal(config: FileConnectorAuth, key: TaskId, file: File): FileDetails =
              if (key.endsWith("/file1.csv")) {
                throw new Exception("Some problem with writing file")
              } else {
                super.writeInternal(config, key, file)
              }
          }
        } else if (sourceType.equals(ConnectionType.FILE_UPLOAD)) {
          new LocalFileSystemConnectorService
        } else {
          throw new Exception(s"Unsupported type $sourceType")
        }
      }
    }

    val state = new State
    val runId = Random.nextLong()
    val flowId = Random.nextInt()
    val dataSource = createDataSource(Random.nextInt(), sourceRootPath)
    val dataSink = createDataSink(Random.nextInt(), destinationRootPath)
    val ctx = Context(runId, s"task-$flowId", FlowType.REPLICATION, flowId, dataSource, dataSink.getDataSet, dataSink, RetryConfig(1, 1))

    val srcFile1Path = Paths.get(sourceRootPath, "/file1.csv").toString
    val srcFile2Path = Paths.get(sourceRootPath, "/file2.csv").toString
    val expectedDstFile2Path = Paths.get(destinationRootPath, "/file2.csv").toString

    val listingAppClient = Fixtures.mockListing(dataSource.getId, List(srcFile1Path, srcFile2Path))
    val adminApiClient = Fixtures.mockAdminApiClient(flowId, dataSource, dataSink)

    val flowLogsSender = new FlowLogsSender(mockMsgProducer)(ec)
    val metricsSender = new MetricsSender(mockMsgProducer)(ec)
    val listingStreamSource = new ListingStreamSource(listingAppClient, listingCooldownPeriod = 1.second)
    val pip = new ReplicationPipeline(ctx, state, adminApiClient, listingAppClient, listingStreamSource, javaListingClient, mockFileVault, mockMsgProducer,
      props, probeFactory, flowLogsSender, metricsSender)
    pip.startPipeline(new PipelineKiller).futureValue(Timeout(70.seconds))

    val areFiles21Equal = FileUtils.contentEquals(new File(expectedDstFile2Path), new File(srcFile2Path))
    Files.exists(Paths.get(expectedDstFile2Path)) shouldBe true
    areFiles21Equal shouldBe true
  }

  test("should dispatch proper flow logs and metrics when there is problem with sink") {
    val sourceRootPath = getClass.getResource("/test_data/input1").getPath
    val destinationRootPath = Files.createTempDirectory(getClass.getSimpleName).toAbsolutePath.toString
    val probeFactory = new ProbeFactory {
     override def getProbeService(adminApiClient: AdminApiClient, listingClient: JavaListingClient, decryptKey: String, sourceType: ConnectionType, fileVaultClient: FileVaultClient): FileConnectorService[_] = {
        if (sourceType.equals(ConnectionType.FTP)) {
          new LocalFileSystemConnectorService {
            override def writeInternal(config: FileConnectorAuth, key: TaskId, file: File): FileDetails =
              if (key.endsWith("/file1.csv")) {
                throw new Exception("Some problem with writing file")
              } else {
                super.writeInternal(config, key, file)
              }
          }
        } else if (sourceType.equals(ConnectionType.FILE_UPLOAD)) {
          new LocalFileSystemConnectorService
        } else {
          throw new Exception(s"Unsupported type $sourceType")
        }
      }
    }

    val state = new State
    val runId = Random.nextLong()
    val flowId = Random.nextInt()
    val dataSource = createDataSource(Random.nextInt(), sourceRootPath)
    val dataSink = createDataSink(Random.nextInt(), destinationRootPath)
    val ctx = Context(runId, s"task-$flowId", FlowType.REPLICATION, flowId, dataSource, dataSink.getDataSet, dataSink, RetryConfig(1, 1))

    val srcFile1Path = Paths.get(sourceRootPath, "/file1.csv").toString
    val srcFile2Path = Paths.get(sourceRootPath, "/file2.csv").toString

    val listingAppClient = Fixtures.mockListing(dataSource.getId, List(srcFile1Path, srcFile2Path))
    val adminApiClient = Fixtures.mockAdminApiClient(flowId, dataSource, dataSink)
    val listingStreamSource = new ListingStreamSource(listingAppClient, listingCooldownPeriod = 1.second)

    implicit val embeddedKafkaConfig: EmbeddedKafkaConfig = kafkaConfig.config
      withConsumer[String, String, Assertion] { consumer =>
        consumer.subscribe(util.Arrays.asList(TOPIC_MONITORING_LOGS, TOPIC_METRICS))

        val messageProducer = new ReplicationMessageProducer(
          new ControlMessageProducer(
            new KafkaMessageTransport(new NexlaKafkaConfig(s"localhost:${kafkaConfig.config.kafkaPort}"))
          ),
        )
        val flowLogsSender = new FlowLogsSender(messageProducer)(ec)
        val metricsSender = new MetricsSender(messageProducer)(ec)
        val pip = new ReplicationPipeline(ctx, state, adminApiClient, listingAppClient, listingStreamSource, javaListingClient, mockFileVault, messageProducer,
          props, probeFactory, flowLogsSender, metricsSender)
        pip.startPipeline(new PipelineKiller).futureValue(Timeout(70.seconds))

        eventually {
          val records = consumer.poll(java.time.Duration.ofMillis(10.seconds.toMillis)).asScala
            .groupBy(_.topic()).mapValues(_.map(_.value()))

          val metrics = records(TOPIC_METRICS).map(r => jsonObjectMapper.readValue(r, classOf[NexlaRawMetric]))
          val flowLogs = records(TOPIC_MONITORING_LOGS).map(r => jsonObjectMapper.readValue(r, classOf[NexlaMonitoringLogEvent]))


          val errorMetrics = metrics.filter(m => m.hasErrors).toList
          errorMetrics.length shouldEqual 1
          errorMetrics.head.getResourceType shouldEqual ResourceType.SINK
          errorMetrics.head.getResourceId shouldEqual dataSink.getId
          errorMetrics.head.getTags.get(NexlaRawMetric.NAME) shouldEqual Paths.get(destinationRootPath, "file1.csv").toString
          errorMetrics.head.getFields.asScala(NexlaRawMetric.ERROR_COUNT) shouldEqual 1
          errorMetrics.head.getFields.asScala("error_message") shouldEqual "Some problem with writing file"

          val errorFlowLogs = flowLogs.filter(m => m.getSeverity == NexlaMonitoringLogSeverity.ERROR).toList
          errorFlowLogs.length shouldEqual 1
          errorFlowLogs.head.getResourceId shouldEqual dataSink.getId
          errorFlowLogs.head.getResourceType shouldEqual ResourceType.SINK
          errorFlowLogs.head.getLog shouldEqual s"Error while processing record ${Paths.get(destinationRootPath, "file1.csv").toString} in the sink connector. Error: Some problem with writing file"

        }

        succeed
    }

  }
}
