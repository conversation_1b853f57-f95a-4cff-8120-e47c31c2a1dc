package com.nexla.replication_connector

import akka.actor.{ActorSystem, Scheduler}
import com.nexla.admin.client._
import com.nexla.common.NexlaConstants.{TOPIC_HEARTBEATS, TOPIC_METRICS, TOPIC_NOTIFY}
import com.nexla.common.notify.transport.ControlMessageProducer
import com.nexla.common.{ConnectionType, NexlaKafkaConfig, NexlaSslContext, ResourceType, StreamUtils}
import com.nexla.connector.config.FlowType
import com.nexla.control.coordination.{CoordinationEventType, CoordinationMessage, HeartbeatConnectorCoordination}
import com.nexla.file.service.FileConnectorService
import com.nexla.inmemory_connector_common.PipelineKiller
import com.nexla.inmemory_connector_common.listing.ListingStreamSource
import com.nexla.inmemory_connector_common.probe.ProbeFactory
import com.nexla.kafka.KafkaMessageTransport
import com.nexla.listing.client.{FileVaultClient, ListingClient => JavaListingClient}
import com.nexla.replication_connector.messaging.ReplicationMessageProducer
import com.nexla.replication_connector.utils.{Fixtures, LocalFileSystemConnectorService, ShutdownActorSystem}
import com.nexla.sc.client.listing.CoordinationAppClient
import io.github.embeddedkafka.Codecs.stringDeserializer
import io.github.embeddedkafka.EmbeddedKafka.withConsumer
import io.github.embeddedkafka.{EmbeddedK, EmbeddedKafka, EmbeddedKafkaConfig}
import org.mockito.Mockito.mock
import org.scalatest.{Assertion, BeforeAndAfterEach}
import org.scalatest.concurrent.Eventually.eventually
import org.scalatest.concurrent.PatienceConfiguration.Timeout
import org.scalatest.concurrent.ScalaFutures.convertScalaFuture
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

import java.nio.file.{Files, Paths}
import java.util
import scala.concurrent.ExecutionContextExecutor
import scala.concurrent.duration.DurationInt
import scala.jdk.CollectionConverters.iterableAsScalaIterableConverter
import scala.util.Random

class HeartbeatsSpec extends AnyFunSuite with Matchers with ShutdownActorSystem with BeforeAndAfterEach {
  implicit val system: ActorSystem = ActorSystem()
  private implicit val ec: ExecutionContextExecutor = system.dispatcher
  private implicit val sch: Scheduler = system.scheduler

  private val SourceRootPath = getClass.getResource("/test_data/input1").getPath
  private val DestinationRootPath = Files.createTempDirectory(getClass.getSimpleName).toAbsolutePath.toString

  private var kafkaConfig: EmbeddedK = _

  override protected def beforeEach(): Unit = {
    super.beforeEach()
    kafkaConfig = EmbeddedKafka.start()(EmbeddedKafkaConfig(0, 0))
  }

  override protected def afterEach(): Unit = {
    super.afterEach()
    EmbeddedKafka.stop(kafkaConfig)
  }

  private def createDataSource(sourceId: Int, org: Org) = {
    val dataSource = new DataSource()
    dataSource.setId(sourceId)
    dataSource.setOrg(org)
    dataSource.setConnectionType(ConnectionType.FILE_UPLOAD)
    dataSource.setSourceConfig(new util.HashMap[String, AnyRef] {
      {
        put("path", SourceRootPath)
      }
    })
    dataSource
  }

  private def createDataSink(sinkId: Int, org: Org) = {
    val dataSink = new DataSink()
    dataSink.setId(sinkId)
    dataSink.setOrg(org)
    dataSink.setConnectionType(ConnectionType.FTP)
    dataSink.setSinkConfig(new util.HashMap[String, AnyRef] {
      {
        put("path", DestinationRootPath)
      }
    })
    val ds = new DataSet
    ds.setId(1)
    dataSink.setDataSet(ds)
    dataSink
  }


  test("successful pipeline should emit heartbeat messages with DONE status") {
    val flowId = Random.nextInt()
    val runId = Random.nextInt()
    val org = Fixtures.mockOrg(Random.nextInt())
    val dataSource = createDataSource(Random.nextInt(), org)
    val dataSink = createDataSink(Random.nextInt(), org)

    val srcFile1Path = Paths.get(SourceRootPath, "/file1.csv").toString
    val srcFile2Path = Paths.get(SourceRootPath, "/file2.csv").toString

    val listingAppClient = Fixtures.mockListing(dataSource.getId, List(srcFile1Path, srcFile2Path))
    val adminApiClient = Fixtures.mockAdminApiClient(flowId, dataSource, dataSink)
    val javaListingClient = mock(classOf[JavaListingClient])
    val coordinationClient = mock(classOf[CoordinationAppClient])
    val mockFileVault: FileVaultClient = mock(classOf[FileVaultClient])

    val props = Fixtures.stubProps
    val probeFactory = new ProbeFactory {
     override def getProbeService(adminApiClient: AdminApiClient, listingClient: JavaListingClient, decryptKey: String, sourceType: ConnectionType, fileVaultClient: FileVaultClient): FileConnectorService[_] = {
        if (sourceType.equals(ConnectionType.FTP)) {
          new LocalFileSystemConnectorService
        } else if (sourceType.equals(ConnectionType.FILE_UPLOAD)) {
          new LocalFileSystemConnectorService
        } else {
          throw new Exception(s"Unsupported type $sourceType")
        }
      }
    }

    implicit val embeddedKafkaConfig: EmbeddedKafkaConfig = kafkaConfig.config
      withConsumer[String, String, Assertion] { heartbeatsConsumer =>
        heartbeatsConsumer.subscribe(util.Arrays.asList(TOPIC_HEARTBEATS))

        val messageProducer = new ReplicationMessageProducer(new ControlMessageProducer(new KafkaMessageTransport(new NexlaKafkaConfig(s"localhost:${kafkaConfig.config.kafkaPort}"))))

        val listingStreamSource = new ListingStreamSource(listingAppClient, listingCooldownPeriod = 1.second)

        val runner = new PipelineRunner(flowId, runId, adminApiClient, listingAppClient, listingStreamSource, javaListingClient, coordinationClient, mockFileVault, probeFactory, props, messageProducer)
        runner.run(new PipelineKiller).futureValue(Timeout(30.seconds))
        messageProducer.flush()

        eventually {
          val records = heartbeatsConsumer.poll(java.time.Duration.ofMillis(10.seconds.toMillis)).asScala.toList

          records.nonEmpty shouldBe true

          val receivedEvents = records.map(_.value()).map(ctrlMessage => StreamUtils.jsonUtil().stringToType(ctrlMessage, classOf[CoordinationMessage]))
          val heartbeats = receivedEvents.collect { case el: HeartbeatConnectorCoordination => el }

          val expectedSourceHeartbeat = heartbeats.find { el =>
            el.getCoordinationEventType == CoordinationEventType.HEARTBEAT_CONNECTOR &&
              el.getResourceType == ResourceType.SOURCE &&
              el.getResourceId.toInt == dataSource.getId &&
              el.getRunId == runId &&
              el.getContext.get("sourceId").toInt == dataSource.getId &&
              el.getContext.get("state") == PipelineRunState.DONE.toString &&
              el.getContext.get("flowId").toInt == flowId &&
              el.getContext.get("flowType") == FlowType.REPLICATION.toString
          }
          val expectedSinkHeartbeat = heartbeats.find { el =>
            el.getCoordinationEventType == CoordinationEventType.HEARTBEAT_CONNECTOR &&
              el.getResourceType == ResourceType.SINK &&
              el.getResourceId.toInt == dataSink.getId &&
              el.getRunId == runId &&
              el.getContext.get("sourceId").toInt == dataSource.getId &&
              el.getContext.get("state") == PipelineRunState.DONE.toString &&
              el.getContext.get("flowId").toInt == flowId &&
              el.getContext.get("flowType") == FlowType.REPLICATION.toString
          }

          expectedSourceHeartbeat.isDefined shouldBe true
          expectedSinkHeartbeat.isDefined shouldBe true
        }
        succeed

    }

  }
}
