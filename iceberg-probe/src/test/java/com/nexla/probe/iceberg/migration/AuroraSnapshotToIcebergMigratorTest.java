package com.nexla.probe.iceberg.migration;

import com.nexla.test.UnitTests;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.spark.SparkContext;
import org.apache.spark.sql.SparkSession;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.mockito.Mockito;

import java.io.IOException;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Category(UnitTests.class)
@Slf4j
public class AuroraSnapshotToIcebergMigratorTest {

  private final SparkSession spark = mock(SparkSession.class);
  private final String sourceSnapshotPath = "source-path";
  private final String icebergWarehousePath = "destination-path";
  private final String catalogName = "spark_catalog";
  private final SparkContext sparkContext = mock(SparkContext.class);
  private final Configuration hadoopConf = mock(Configuration.class);
  private AuroraSnapshotToIcebergMigrator migrator;
  private final String runId = "test-run-id";
  private final RunIdLogger logger = new RunIdLogger(runId, log);

  @Before
  @SneakyThrows
  public void setUp() {
    when(spark.sparkContext()).thenReturn(sparkContext);
    when(sparkContext.hadoopConfiguration()).thenReturn(hadoopConf);
    migrator = Mockito.spy(new AuroraSnapshotToIcebergMigrator(
        logger, spark, sourceSnapshotPath, icebergWarehousePath, catalogName));
    final Path rootPath = mock(Path.class);
    final FileSystem fs = mock(FileSystem.class);
    when(rootPath.getFileSystem(hadoopConf)).thenReturn(fs);
    when(migrator.getSourcePath()).thenReturn(rootPath);
    final FileStatus dbFS1 = mockDB(fs, "db1");
    final FileStatus dbFS2 = mockDB(fs, "db2");
    when(fs.listStatus(rootPath)).thenReturn(new FileStatus[]{dbFS1, dbFS2});
  }

  private @NotNull FileStatus mockDB(FileSystem fs, String dbName) throws IOException {
    final FileStatus dbFS = mock(FileStatus.class);
    final FileStatus tableFS11 = mock(FileStatus.class);
    final FileStatus tableFS12 = mock(FileStatus.class);
    final Path dbPath = new Path(sourceSnapshotPath + "/" + dbName);
    final Path tablePath1 = new Path(String.format("%s/%s.table1", sourceSnapshotPath, dbName));
    final Path tablePath2 = new Path(String.format("%s/%s.table2", sourceSnapshotPath, dbName));


    when(fs.listStatus(dbPath)).thenReturn(new FileStatus[]{tableFS11, tableFS12});
    when(dbFS.getPath()).thenReturn(dbPath);
    when(tableFS11.getPath()).thenReturn(tablePath1);
    when(tableFS12.getPath()).thenReturn(tablePath2);
    when(dbFS.isDirectory()).thenReturn(true);
    when(tableFS11.isDirectory()).thenReturn(true);
    when(tableFS12.isDirectory()).thenReturn(true);
    return dbFS;
  }

  @Test
  @SneakyThrows
  public void testMigrate() {
    migrator.migrate();
    verify(spark, times(1)).sql(String.format("CREATE DATABASE IF NOT EXISTS %s.db1", catalogName));
    verify(spark, times(1)).sql(String.format("CREATE DATABASE IF NOT EXISTS %s.db2", catalogName));
  }

  @Test
  @Ignore("Only for manual testing")
  public void testMigrateManual() {
    // Source configuration
    String sourceSnapshotPath = "s3a://qa.nexla.com/beta_snapshot/beta-admin-metrics-snapshot/";
    String icebergWarehousePath = "s3a://qa.nexla.com/iceberg_test/avinashnigam/11June/beta_admin_metrics_snapshot11/";
    String catalogName = "spark_catalog";
    String region = "us-east-1";
    String profileName = "nexla-saml";
    String sessionToken = "<SESSION_TOKEN>"; // Replace with actual session token if needed

    // Configure AWS credentials
    Credentials credentials = CredentialsUtils.getCredentials("s3", profileName, region);

    // Configure Spark session
    SparkSession spark = SparkSession.builder()
        .appName("Aurora Snapshot to Iceberg Migration")
        .master("local[*]")
        .config("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
        .config("spark.hadoop.fs.s3a.path.style.access", "true")
        .config("spark.hadoop.fs.s3a.connection.maximum", "200")
        .config("spark.hadoop.fs.s3a.endpoint.region", region)
        .config("spark.hadoop.fs.s3a.endpoint", "s3.amazonaws.com")
        .config("spark.sql.extensions", "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions")
        .config("spark.sql.catalog.spark_catalog", "org.apache.iceberg.spark.SparkCatalog")
        .config("spark.sql.catalog.spark_catalog.type", "hadoop")
        .config("spark.sql.catalog.spark_catalog.warehouse", icebergWarehousePath)
        .config("spark.sql.catalog.spark_catalog.handle-timestamp-without-timezone", "true")
        .config("spark.sql.parquet.datetimeRebaseModeInWrite", "CORRECTED")
        .config("spark.hadoop.fs.s3a.access.key", credentials.getAccessKeyId())
        .config("spark.hadoop.fs.s3a.secret.key", credentials.getSecretKey())
        .config("spark.hadoop.fs.s3a.session.token", sessionToken)
        .getOrCreate();

    // Additional S3 configurations
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.connection.timeout", "600000");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.socket.timeout", "600000");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.connection.establish.timeout", "120000");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.connection.acquisition.timeout", "600000");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.connection.request.timeout", "600000");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.attempts.maximum", "20");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.retry.interval", "5000");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.retry.limit", "20");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.timeout", "1800000");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.connection.ttl", "900000");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.connection.idle.time", "600000");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.experimental.input.fadvise", "sequential");

    try {
      final var migrator = new AuroraSnapshotToIcebergMigrator(
          logger, spark, sourceSnapshotPath, icebergWarehousePath, catalogName);
      migrator.migrate();
    } catch (final Exception e) {
      logger.error("Migration failed", e);
    } finally {
      spark.stop();
    }
  }
}