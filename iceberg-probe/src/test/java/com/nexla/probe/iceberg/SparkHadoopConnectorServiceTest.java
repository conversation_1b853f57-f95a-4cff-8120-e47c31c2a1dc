package com.nexla.probe.iceberg;

import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaBucket;
import com.nexla.common.NexlaFile;
import com.nexla.common.probe.ColumnInfo;
import com.nexla.common.probe.ProbeSampleResult;
import com.nexla.common.probe.ProbeSampleResultEntry;
import com.nexla.connect.common.spark.SparkSingleContextEnforcer;
import com.nexla.connector.ConnectorService;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.iceberg.IcebergSinkConnectorConfig;
import com.nexla.connector.config.iceberg.IcebergSourceConnectorConfig;
import com.nexla.test.IntegrationTests;
import lombok.SneakyThrows;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.junit.*;
import org.junit.experimental.categories.Category;
import org.testcontainers.utility.DockerImageName;

import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static com.nexla.common.NexlaConstants.*;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.BaseConnectorConfig.LOG_VERBOSE;
import static com.nexla.connector.config.file.AWSAuthConfig.SERVICE_ENDPOINT;
import static com.nexla.connector.config.file.FileSinkConnectorConfig.SPARK_SESSION_CONFIGS;
import static com.nexla.connector.config.file.S3Constants.*;
import static com.nexla.connector.config.iceberg.IcebergSourceConnectorConfig.TABLE_NAME;
import static com.nexla.connector.config.iceberg.IcebergSourceConnectorConfig.WAREHOUSE_DIR;
import static com.nexla.connector.properties.FileConfigAccessor.FILE_NAME_PREFIX;
import static com.nexla.connector.properties.FileConfigAccessor.MAX_FILE_SIZE_MB;
import static com.nexla.probe.iceberg.LocalStackContainerAWSSDK2.Service.S3;

@Category(IntegrationTests.class)
public class SparkHadoopConnectorServiceTest {

    private static final String TEST_TABLE_NAME = "classic.classes";
    static DockerImageName localstackImage = DockerImageName.parse("localstack/localstack:4.1.1");

    @ClassRule
    public static LocalStackContainerAWSSDK2 localstack = new LocalStackContainerAWSSDK2(localstackImage)
            .withServices(S3);

    private SparkHadoopConnectorService connectorService;
    private IcebergSourceConnectorConfig config;

    @Before
    public void setup() {
        connectorService = new SparkHadoopConnectorService();
        config = getSinkConnectorConfig();
        createTable(config, "classic.classes");
    }

    @SneakyThrows
    @BeforeClass
    public static void globalSetup() {
        localstack.execInContainer("awslocal", "s3", "mb", "s3://nexla-data-lake");
    }

    @AfterClass
    public static void teardown() {
        try {
            SparkSession.active().close();
        } catch (IllegalStateException ignored) {
        }
    }

    @Test
    public void shouldBeAbleToAuthenticateWithS3() {
        var configMap = Map.of(
                ACCESS_KEY_ID, localstack.getAccessKey(),
                SECRET_KEY, localstack.getSecretKey(),
                REGION, localstack.getRegion(),
                SERVICE_ENDPOINT, localstack.getEndpointOverride(S3).toString());

        ConnectorService.AuthResponse response = connectorService.authenticate(new AWSAuthConfig(configMap, 1));
        Assert.assertEquals(response.success, true);
    }

    @Test
    public void listBucketsListsAllNamespaces() {
        createTable(config, "wotlk.classes");

        List<NexlaBucket> listed = connectorService.listBuckets(config).sortedBy(NexlaBucket::getName).collect(Collectors.toList());
        List<NexlaBucket> expected = Stream.of("classic", "wotlk").map(NexlaBucket::new).collect(Collectors.toList());
        Assert.assertEquals(expected, listed);
    }

    @Test
    public void listDatabasesListsAllNamespaces() {
        createTable(config, "wotlk.classes");

        List<String> listed = connectorService.listDatabases(config).sorted().collect(Collectors.toList());
        Assert.assertEquals(List.of("classic", "wotlk"), listed);
    }

    @Test
    public void listBucketContentsListAllBucketsForAllNamespaces() {
        createTable(config, "wotlk.other");

        List<String> listed = connectorService.listBucketContents(config).map(NexlaFile::getFullPath).sorted().collect(Collectors.toList());
        Assert.assertEquals(List.of("classes", "other"), listed);
    }

    @Test
    public void shouldBeAbleToVerifyWriteAccess() {
        var connectorConfig = getSourceConnectorConfigProps();
        var success = connectorService.checkWriteAccess(new IcebergSinkConnectorConfig(connectorConfig));
        Assert.assertTrue(success);
    }

    @Test
    public void listTablesOffsetListsAllTables() {
        createTable(config, "classic.moreClasses");
        createTable(config, "wotlk.other");

        Iterable<String> listed = connectorService.listTables(config, "classic", null, 10, "0").getResult();
        List<String> actual = StreamSupport.stream(listed.spliterator(), false).collect(Collectors.toList());
        Assert.assertEquals(List.of("classes", "moreClasses"), actual);
    }

    @Test
    public void listTablesListsAllTables() {
        createTable(config, "classic.moreClasses");
        createTable(config, "wotlk.other");

        List<String> listed = connectorService.listTables("classic", null, config).collect(Collectors.toList());
        Assert.assertEquals(List.of("classes", "moreClasses"), listed);
    }

    @Test
    public void readSampleReadsSamples() {
        ProbeSampleResult samples = connectorService.readSample(config, false);
        Assert.assertEquals(9, samples.getData().size());
        HashMap<String, Object> expectedWarrior = new HashMap<>() {{
            put("class", "Warrior");
            put("expansion", "Classic");
            put("release_date", "2004-11-23");
            put("races", List.of("Human", "Dwarf", "Night Elf", "Gnome", "Orc", "Undead", "Tauren", "Troll"));
        }};
        Assert.assertEquals(expectedWarrior, ((ProbeSampleResultEntry) samples.getData().get(0)).data);
    }

    @Test
    public void listColumnInfosListsColumnNamesAndTypes() {
        Comparator<ColumnInfo> columnInfoComparator = Comparator.comparing(ColumnInfo::getName);
        List<ColumnInfo> columnInfos = connectorService.listColumnInfos("classic", Optional.empty(), "classes", config)
                .stream().sorted(columnInfoComparator).collect(Collectors.toList());
        List<ColumnInfo> expectedColumnInfos = Stream.of(
                new ColumnInfo("class", false, "string", null),
                new ColumnInfo("expansion", false, "string", null),
                new ColumnInfo("release_date", false, "string", null),
                new ColumnInfo("races", false, "array", null)
        ).sorted(columnInfoComparator).collect(Collectors.toList());
        Assert.assertEquals(expectedColumnInfos, columnInfos);
    }

    private void createTable(IcebergSourceConnectorConfig config, String table) {
        SparkSingleContextEnforcer.withSparkSession(config,
                () -> connectorService.getSparkSession(config),
                session -> {
                    URL baseDataframeUrl = Thread.currentThread().getContextClassLoader().getResource("classic_classes.json");
                    Dataset<Row> df = session.read().json(baseDataframeUrl.getPath());
                    df.writeTo(table).using("iceberg").createOrReplace();
                    return null;
                }
        );
    }

    private IcebergSourceConnectorConfig getSinkConnectorConfig() {
        var props = getSourceConnectorConfigProps();
        return new IcebergSourceConnectorConfig(props);
    }

    private Map<String, String> getSourceConnectorConfigProps() {
        return new HashMap<>() {{
            put(UNIT_TEST, "true");
            put(SINK_ID, "1");
            put(SOURCE_ID, "1");
            put(SOURCE_TYPE, ConnectionType.S3_ICEBERG.name());
            put(CREDS_ENC, "1");
            put(CREDS_ENC_IV, "1");
            put(CREDENTIALS_DECRYPT_KEY, "1");
            put(LISTING_ENABLED, "false");
            put(SINK_TYPE, ConnectionType.S3.name());
            put(ACCESS_KEY_ID, localstack.getAccessKey());
            put(SECRET_KEY, localstack.getSecretKey());
            put(REGION, localstack.getRegion());
            put(SERVICE_ENDPOINT, localstack.getEndpointOverride(S3).toString());
            put(TABLE_NAME, TEST_TABLE_NAME);
            put(DATA_FORMAT, "parquet");
            put(WAREHOUSE_DIR, "nexla-data-lake/warehouse");
            // TODO fix support root level paths
            // put(WAREHOUSE_DIR, "nexla-data-lake");
            put(SPARK_SESSION_CONFIGS, "spark.driver.bindAddress=127.0.0.1");
            put(MAX_FILE_SIZE_MB, "0");
            put(FILE_NAME_PREFIX, "my-dataset");
            put(MONITOR_POLL_MS, "1");
            put(CLIENT_SSL_ENABLED, "false");
            put(CONTROL_KAFKA_SECURITY_PROTOCOL, "PLAINTEXT");
            put(DATA_KAFKA_SECURITY_PROTOCOL, "PLAINTEXT");
            put(LOG_VERBOSE, "true");
        }};
    }
}
