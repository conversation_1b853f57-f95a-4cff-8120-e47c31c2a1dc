package com.nexla.probe.iceberg.migration;

import com.nexla.test.UnitTests;
import lombok.SneakyThrows;
import org.apache.spark.sql.SparkSession;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.mockito.MockedStatic;

import java.util.UUID;

import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Category(UnitTests.class)
public class AWSIcebergMigratorTest {

  private CredentialsHelper credentialsHelper;
  private SparkSession sparkSession;
  private AuroraSnapshotToIcebergMigrator migrator;

  @Before
  public void setUp() {
    credentialsHelper = mock(CredentialsHelper.class);
    Credentials credentials = mock(Credentials.class);
    sparkSession = mock(SparkSession.class, RETURNS_DEEP_STUBS);
    migrator = mock(AuroraSnapshotToIcebergMigrator.class);

    // Mock credentials
    when(credentialsHelper.enrichWithDataCredentials(anyInt())).thenReturn(credentials);
    when(credentials.getRegion()).thenReturn("us-east-1");
    when(credentials.getAccessKeyId()).thenReturn("accessKey");
    when(credentials.getSecretKey()).thenReturn("secretKey");
  }

  @Test
  @SneakyThrows
  @Ignore
  public void testMigrateSuccess() {
    try (MockedStatic<SparkSession> sparkSessionStatic = mockStatic(SparkSession.class)) {
      SparkSession.Builder builder = mock(SparkSession.Builder.class, RETURNS_DEEP_STUBS);
      when(builder.appName(anyString())).thenReturn(builder);
      when(builder.master(anyString())).thenReturn(builder);
      when(builder.config(anyString(), anyString())).thenReturn(builder);
      when(builder.getOrCreate()).thenReturn(sparkSession);
      sparkSessionStatic.when(SparkSession::builder).thenReturn(builder);

      AWSIcebergMigrator migratorSpy = spy(new AWSIcebergMigrator(UUID.randomUUID().toString(), credentialsHelper));
      doReturn(migrator).when(migratorSpy).createMigrator(any(SparkSession.class), anyString(), anyString(), anyString());

      migratorSpy.migrate(1, "s3://src", "s3://dest");

      verify(migrator, times(1)).migrate();
    }
  }
}