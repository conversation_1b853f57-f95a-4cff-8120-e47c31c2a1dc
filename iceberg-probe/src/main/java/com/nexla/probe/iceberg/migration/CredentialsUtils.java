package com.nexla.probe.iceberg.migration;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.profile.ProfileCredentialsProvider;
import lombok.experimental.UtilityClass;

@UtilityClass
public class CredentialsUtils {

  public Credentials getCredentials(final String profileName, final String region, final String credentialsType) {
    final ProfileCredentialsProvider credentialsProvider = new ProfileCredentialsProvider(profileName);
    final AWSCredentials credentials = credentialsProvider.getCredentials();
    return new Credentials(credentialsType, credentials.getAWSAccessKeyId(), credentials.getAWSSecretKey(), region);
  }
}
