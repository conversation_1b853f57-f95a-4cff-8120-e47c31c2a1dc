package com.nexla.probe.iceberg.migration;

import com.nexla.telemetry.utils.NexlaStopwatch;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.iceberg.AppendFiles;
import org.apache.iceberg.DataFile;
import org.apache.iceberg.DataFiles;
import org.apache.iceberg.Metrics;
import org.apache.iceberg.PartitionSpec;
import org.apache.iceberg.Schema;
import org.apache.iceberg.Table;
import org.apache.iceberg.hadoop.HadoopInputFile;
import org.apache.iceberg.hadoop.HadoopTables;
import org.apache.iceberg.io.InputFile;
import org.apache.iceberg.spark.SparkSchemaUtil;
import org.apache.iceberg.types.Types;
import org.apache.parquet.hadoop.ParquetFileReader;
import org.apache.parquet.hadoop.metadata.BlockMetaData;
import org.apache.parquet.schema.MessageType;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.execution.datasources.parquet.ParquetToSparkSchemaConverter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Creates a new Iceberg table referencing an existing parquet file without copying it.
 */
public class ParquetToIcebergReferencer {


  private final RunIdLogger logger;
  private final SparkSession spark;
  private final String tableLocation;
  private final String tableName;
  private final Configuration hadoopConf;
  private final HadoopTables tables;
  private final ParquetToSparkSchemaConverter schemaConverter;

  public ParquetToIcebergReferencer(
      final RunIdLogger logger,
      final SparkSession spark,
      final String tableLocation,
      final String tableName) {
    this.logger = logger;
    this.spark = spark;
    this.tableLocation = tableLocation;
    this.tableName = tableName;
    this.hadoopConf = spark.sparkContext().hadoopConfiguration();
    this.tables = new HadoopTables(hadoopConf);
    this.schemaConverter = new ParquetToSparkSchemaConverter(spark.sessionState().conf()); // Initialize once
  }

  /**
   * Creates or updates an Iceberg table with references to existing parquet files.
   * If the table already exists, it will evolve the schema if necessary.
   *
   * @param parquetFiles List of parquet file paths to reference in the Iceberg table
   */
  public void createTableWithReference(final List<String> parquetFiles) {
    if (CollectionUtils.isEmpty(parquetFiles)) {
      logger.warn("No parquet files provided to add to Iceberg table");
      return;
    }

    try {
      final NexlaStopwatch stopwatch = new NexlaStopwatch("createTableWithReference");
      stopwatch.start("prepareTableLocation");
      String fullTableLocation = prepareTableLocation();
      stopwatch.stop();

      stopwatch.start("tableExistsCheck");
      boolean tableExists = tables.exists(fullTableLocation);
      stopwatch.stop();

      // Extract all file info in parallel
      stopwatch.start("extractFileInfo");
      final Map<String, ParquetFileInfo> fileInfoMap = extractFileInfoParallel(parquetFiles);
      stopwatch.stop();

      // Filter out files that failed to extract metadata
      final List<String> validParquetFiles = parquetFiles.stream()
          .filter(fileInfoMap::containsKey)
          .collect(java.util.stream.Collectors.toList());

      if (validParquetFiles.isEmpty()) {
        String errorMsg = "No valid parquet files found after metadata extraction";
        logger.warn(errorMsg);

        // If table already exists, we can skip this error and just return
        if (tableExists) {
          logger.info("Table already exists at {}. Skipping update since no valid files were found.", fullTableLocation);
          return;
        }

        // For new tables, log detailed information about failed files
        if (!parquetFiles.isEmpty()) {
          logger.warn("Failed to extract metadata from {} parquet files. This may indicate corrupted files or version compatibility issues.", parquetFiles.size());
          // Log first few file paths for debugging
          int logCount = Math.min(3, parquetFiles.size());
          for (int i = 0; i < logCount; i++) {
            logger.warn("Failed file example {}: {}", i + 1, parquetFiles.get(i));
          }
        }

        // Don't throw exception, just skip this table
        logger.warn("Skipping table creation due to no valid parquet files");
        return;
      }

      if (validParquetFiles.size() < parquetFiles.size()) {
        logger.warn("Some files failed metadata extraction. Processing {}/{} files",
            validParquetFiles.size(), parquetFiles.size());
      }

      Table table;
      stopwatch.start("getOrCreateTable");
      if (!tableExists) {
        // Get schema from first file
        String firstFile = validParquetFiles.get(0);
        Schema initialSchema = fileInfoMap.get(firstFile).schema;
        logger.info("Creating new Iceberg table with initial schema");
        table = tables.create(initialSchema, PartitionSpec.unpartitioned(), fullTableLocation);

        // Register the table with Spark catalog using a safer approach
        try {
          String createTableSQL = String.format("CREATE TABLE IF NOT EXISTS %s USING iceberg LOCATION '%s'",
              tableName, fullTableLocation);
          spark.sql(createTableSQL);
          logger.info("Successfully registered Iceberg table with Spark catalog");
        } catch (Exception e) {
          // Log the error but don't fail the migration since the Iceberg table was created successfully
          logger.warn("Failed to register table with Spark catalog (table still created successfully): {}",
              e.getMessage());
        }

        logger.info("Created new Iceberg table");
      } else {
        table = tables.load(fullTableLocation);
        logger.info("Using existing table at {}", fullTableLocation);
      }
      stopwatch.stop();

      stopwatch.start("updateSchema");
      table = mayEvolveSchemaWithFileInfo(validParquetFiles, fileInfoMap, table, fullTableLocation);
      stopwatch.stop();

      stopwatch.start("batchAddFiles");
      AppendFiles append = table.newAppend();
      for (String parquetFile : validParquetFiles) {
        try {
          final NexlaStopwatch batchStopwatch = new NexlaStopwatch("batchAddFiles");
          batchStopwatch.start("createInputFile");
          Path path = new Path(parquetFile);
          InputFile inputFile = HadoopInputFile.fromPath(path, hadoopConf);
          batchStopwatch.stop();

          batchStopwatch.start("getMetrics");
          // Use pre-extracted metrics
          Metrics metrics = fileInfoMap.get(parquetFile).metrics;
          batchStopwatch.stop();

          batchStopwatch.start("createDataFile");
          DataFile dataFile = DataFiles.builder(table.spec())
              .withInputFile(inputFile)
              .withMetrics(metrics)
              .build();
          batchStopwatch.stop();

          batchStopwatch.start("appendFile");
          append.appendFile(dataFile);
          batchStopwatch.stop();
          logger.info("BatchUpdate stop watch for file {}: {}", parquetFile, batchStopwatch);
        } catch (Exception e) {
          logger.warn("Error processing file {}: {}", parquetFile, e.getMessage(), e);
        }
      }
      stopwatch.stop();

      stopwatch.start("batchCommit");
      append.set("commit.manifest.min-count-to-merge", "5")
          .set("write.parquet.compression-codec", "zstd")
          .commit();
      stopwatch.stop();

      logger.info("Successfully updated Iceberg table {} with {} parquet file references. Time breakdown: {}",
          tableName, validParquetFiles.size(), stopwatch);
    } catch (final Exception e) {
      logger.error("Failed to update Iceberg table: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to update Iceberg table", e);
    }
  }


  /**
   * Checks if two schemas are structurally similar (same fields, types, and nullability)
   * while ignoring differences in internal field IDs
   */
  private boolean areSchemaStructurallySimilar(final Schema schema1, final Schema schema2) {
    if (schema1.columns().size() != schema2.columns().size()) {
      return false;
    }

    List<Types.NestedField> fields1 = schema1.columns();
    List<Types.NestedField> fields2 = schema2.columns();

    // Compare each field by name rather than position
    for (Types.NestedField field1 : fields1) {
      String fieldName = field1.name();

      // Find matching field in schema2
      Types.NestedField field2 = null;
      for (Types.NestedField f : fields2) {
        if (f.name().equals(fieldName)) {
          field2 = f;
          break;
        }
      }

      // If field not found or types don't match, schemas differ
      if (field2 == null || !field1.type().equals(field2.type()) || field1.isOptional() != field2.isOptional()) {
        logger.debug("Field difference: {} - {}", field1, field2 == null ? "missing" : field2);
        return false;
      }
    }
    return true;
  }


  /**
   * Prepares the table location based on the configured table name.
   */
  private String prepareTableLocation() {
    // Parse table name to extract the namespace and table name
    String[] parts = tableName.split("\\.");
    String namespace = parts.length > 2 ? parts[1] : "default";
    String simpleTableName = parts.length > 2 ? parts[2] : parts[1];

    // Ensure table location includes namespace and table name
    String fullTableLocation = tableLocation;
    if (!fullTableLocation.endsWith("/" + namespace + "/" + simpleTableName)) {
      if (!fullTableLocation.endsWith("/")) {
        fullTableLocation += "/";
      }
      fullTableLocation += namespace + "/" + simpleTableName;
    }

    logger.info("Using table location: {}", fullTableLocation);
    return fullTableLocation;
  }


  /**
   * Container for parquet file information extracted in one pass
   */
  private static class ParquetFileInfo {
    private final Schema schema;
    private final Metrics metrics;

    public ParquetFileInfo(Schema schema, Metrics metrics) {
      this.schema = schema;
      this.metrics = metrics;
    }
  }

  /**
   * Extract both schema and metrics in a single file read operation
   */
  private ParquetFileInfo extractFileInfo(String parquetFilePath) throws IOException {
    Path path = new Path(parquetFilePath);

    try {
      // First check if file exists and is readable
      org.apache.hadoop.fs.FileSystem fs = path.getFileSystem(hadoopConf);
      if (!fs.exists(path)) {
        throw new IOException("Parquet file does not exist: " + parquetFilePath);
      }

      org.apache.hadoop.fs.FileStatus fileStatus = fs.getFileStatus(path);
      if (fileStatus.getLen() == 0) {
        throw new IOException("Parquet file is empty: " + parquetFilePath);
      }

      // Use existing Hadoop configuration
      org.apache.parquet.hadoop.util.HadoopInputFile parquetInputFile =
          org.apache.parquet.hadoop.util.HadoopInputFile.fromPath(path, hadoopConf);

      try (ParquetFileReader reader = ParquetFileReader.open(parquetInputFile)) {
        // Extract schema
        final MessageType parquetSchema = reader.getFooter().getFileMetaData().getSchema();
        if (parquetSchema.getFieldCount() == 0) {
          throw new IOException("Parquet file has no schema fields: " + parquetFilePath);
        }

        org.apache.spark.sql.types.StructType sparkSchema = schemaConverter.convert(parquetSchema);
        Schema icebergSchema = SparkSchemaUtil.convert(sparkSchema);

        // Extract metrics
        final List<BlockMetaData> blocks = reader.getFooter().getBlocks();
        long rowCount = blocks.stream().mapToLong(BlockMetaData::getRowCount).sum();

        Metrics metrics = new Metrics(
            rowCount,
            Collections.emptyMap(),
            Collections.emptyMap(),
            Collections.emptyMap(),
            Collections.emptyMap()
        );

        return new ParquetFileInfo(icebergSchema, metrics);
      } catch (NoSuchMethodError e) {
        // This is likely a Parquet version compatibility issue
        logger.warn("Parquet version compatibility issue for file {}: {}", parquetFilePath, e.getMessage());
        throw new IOException("Parquet version compatibility issue: " + e.getMessage(), e);
      } catch (Exception e) {
        logger.warn("Error reading Parquet file {}: {}", parquetFilePath, e.getMessage());
        throw new IOException("Error reading Parquet file: " + e.getMessage(), e);
      }
    } catch (IOException e) {
      // Re-throw IOException as-is
      throw e;
    } catch (Exception e) {
      logger.warn("Unexpected error processing Parquet file {}: {}", parquetFilePath, e.getMessage());
      throw new IOException("Failed to process Parquet file: " + e.getMessage(), e);
    }
  }

  /**
   * Extract file info for multiple files in parallel
   */
  private Map<String, ParquetFileInfo> extractFileInfoParallel(List<String> parquetFiles) {
    // Create a smaller thread pool to prevent S3 connection pool exhaustion
    final ExecutorService executor = Executors.newFixedThreadPool(3);
    final Map<String, ParquetFileInfo> fileInfoMap = new ConcurrentHashMap<>();
    final List<CompletableFuture<Void>> futures = new ArrayList<>();
    final Map<String, Throwable> failedFiles = new ConcurrentHashMap<>();

    for (String parquetFile : parquetFiles) {
      CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
        try {
          ParquetFileInfo fileInfo = extractFileInfo(parquetFile);
          fileInfoMap.put(parquetFile, fileInfo);
        } catch (Exception e) {
          logger.warn("Failed to extract file info for {}: {}", parquetFile, e.getMessage());
          failedFiles.put(parquetFile, e);
          // Don't throw CompletionException to prevent entire batch from failing
          // Just skip this file and continue with others
        }
      }, executor);
      futures.add(future);
    }

    try {
      CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    } catch (Exception e) {
      logger.warn("Some files failed during parallel extraction: {}", e.getMessage());
    } finally {
      executor.shutdown();
    }

    if (fileInfoMap.isEmpty() && !failedFiles.isEmpty()) {
      // Log detailed error information for the first few failed files
      int count = 0;
      for (Map.Entry<String, Throwable> entry : failedFiles.entrySet()) {
        if (count++ < 3) { // Limit to first 3 errors to avoid log spam
          logger.warn("Error details for file {}: {}", entry.getKey(), entry.getValue().getMessage());
          logger.debug("Full stack trace for file {}: ", entry.getKey(), entry.getValue());
        }
      }
    } else if (!failedFiles.isEmpty()) {
      logger.info("Successfully extracted file info for {}/{} files ({} files failed metadata extraction)",
          fileInfoMap.size(), parquetFiles.size(), failedFiles.size());
    } else {
      logger.info("Successfully extracted file info for {}/{} files", fileInfoMap.size(), parquetFiles.size());
    }
    return fileInfoMap;
  }

  private Table mayEvolveSchemaWithFileInfo(List<String> parquetFiles, Map<String, ParquetFileInfo> fileInfoMap, Table table, String fullTableLocation) {
    boolean schemaEvolved = false;
    Schema currentSchema = table.schema();

    logger.info("Current table schema: {}", currentSchema);

    // Process schema evolution using pre-extracted schemas
    for (final String parquetFile : parquetFiles) {
      final Schema fileSchema = fileInfoMap.get(parquetFile).schema;

      if (!areSchemaStructurallySimilar(currentSchema, fileSchema)) {
        logger.info("Schema evolution needed. File: {}, Existing: {}, New: {}", parquetFile, currentSchema, fileSchema);
        table.updateSchema().unionByNameWith(fileSchema).commit();
        schemaEvolved = true;
        currentSchema = table.schema();
      }
    }

    // If schema evolved, reload table to ensure we have latest metadata
    if (schemaEvolved) {
      return tables.load(fullTableLocation);
    }
    return table;
  }
}