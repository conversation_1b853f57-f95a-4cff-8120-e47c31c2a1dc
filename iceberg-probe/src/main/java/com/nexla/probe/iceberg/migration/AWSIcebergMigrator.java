package com.nexla.probe.iceberg.migration;

import com.google.common.annotations.VisibleForTesting;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.spark.sql.SparkSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.Option;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.InstanceProfileCredentialsProvider;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.GetCallerIdentityResponse;
import org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider;
import org.apache.hadoop.fs.s3a.auth.AssumedRoleCredentialProvider;

import java.util.concurrent.CompletableFuture;

/**
 * Manages AWS credential handling and delegates to AuroraSnapshotToIcebergMigrator
 * for the actual migration process.
 */
public class AWSIcebergMigrator {
  private static final Logger log = LoggerFactory.getLogger(AWSIcebergMigrator.class);

  // =================================================================================
  // === DEFINITIVE FIX: Static initializer block.                                 ===
  // === This code runs once when the class is loaded by the JVM, ensuring the     ===
  // === properties are set before any Spark/Hadoop code is ever executed.         ===
  // =================================================================================
  static {
    try {
      // Set system properties first to ensure they're available
      String userName = System.getProperty("user.name");
      if (userName == null || userName.trim().isEmpty()) {
        userName = "nexla";
        System.setProperty("user.name", userName);
      }

      System.setProperty("hadoop.security.authentication", "simple");
      System.setProperty("java.security.krb5.conf", "/dev/null");

      // Configure Hadoop with the user name
      Configuration conf = new Configuration();
      conf.set("hadoop.security.authentication", "simple");
      conf.set("hadoop.security.authorization", "false");
      UserGroupInformation.setConfiguration(conf);

      // Create user with validated name
      UserGroupInformation.setLoginUser(UserGroupInformation.createRemoteUser(userName));

      log.info("Static Initializer: Setting Hadoop user context for containerized environment with user: {}", userName);
    } catch (Exception e) {
      log.error("Error setting up Hadoop user context.", e);
    }
  }

  private final CredentialsHelper credentialsHelper;
  private final RunIdLogger logger;

  public AWSIcebergMigrator(final String runId, final CredentialsHelper credentialsHelper) {
    this.logger = new RunIdLogger(runId, log);
    this.credentialsHelper = credentialsHelper;
  }

  /**
   * Migrates data from AWS S3 snapshot to Iceberg format.
   *
   * @param awsCredentialId             AWS credentials ID
   * @param sourceSnapshotPath          Source snapshot path
   * @param destinationIcebergWarehouse Destination Iceberg warehouse path
   */
  public void migrate(final int awsCredentialId,
                      final String sourceSnapshotPath,
                      final String destinationIcebergWarehouse) {
    logger.info("Starting AWS Iceberg migration with credential ID: {}, sourceSnapshotPath: {}, destinationIcebergWarehouse: {}",
        awsCredentialId, sourceSnapshotPath, destinationIcebergWarehouse);

    // Fetch AWS credentials using the specified ID
    final var credentials = credentialsHelper.enrichWithDataCredentials(awsCredentialId);
    final var catalogName = "spark_catalog";

    // Correctly handle scala.Option in Java to stop any existing SparkContext.
    Option<SparkSession> activeSessionOption = SparkSession.getActiveSession();
    if (activeSessionOption.isDefined()) {
        logger.warn("An active Spark session was found. Stopping it before creating a new one.");
        activeSessionOption.get().stop();
    }

    // Configure and create Spark session with AWS credentials
    final SparkSession spark = createSparkSessionWithCredentials(credentials, destinationIcebergWarehouse);

    // Additional S3 configurations
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.connection.timeout", "5000");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.attempts.maximum", "3");
    spark.sparkContext().hadoopConfiguration().set("fs.s3a.experimental.input.fadvise", "sequential");

    // Log Spark configuration for debugging
    logSparkConfiguration(spark);

    CompletableFuture.runAsync(() -> {
      try {
        final var migrator = createMigrator(
            spark, sourceSnapshotPath, destinationIcebergWarehouse, catalogName);
        migrator.migrate();
        logger.info("Migration completed successfully");
      } catch (final Exception e) {
        logger.error("Migration failed: awsCredentialId: {}, sourceSnapshotPath: {}, destinationIcebergWarehouse: {}",
            awsCredentialId, sourceSnapshotPath, destinationIcebergWarehouse, e);
      } finally {
        spark.stop();
      }
    });
  }

  private SparkSession createSparkSessionWithCredentials(final Credentials credentials, final String destinationIcebergWarehouse) {
    final var sparkBuilder = SparkSession.builder()
        .appName("Aurora Snapshot to Iceberg Migration")
        .master("local[*]")
        .config("spark.hadoop.user.name", "nexla")
        .config("spark.executorEnv.USER", "nexla")
        .config("spark.executorEnv.HADOOP_USER_NAME", "nexla")
        .config("spark.hadoop.hadoop.security.authentication", "simple")
        .config("spark.hadoop.hadoop.security.authorization", "false")
        .config("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
        .config("spark.hadoop.fs.s3a.path.style.access", "true")
        .config("spark.hadoop.fs.s3a.connection.maximum", "100")
        .config("spark.hadoop.fs.s3a.endpoint", "s3.amazonaws.com")
        .config("spark.sql.extensions", "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions")
        .config("spark.sql.catalog.spark_catalog", "org.apache.iceberg.spark.SparkCatalog")
        .config("spark.sql.catalog.spark_catalog.type", "hadoop")
        .config("spark.sql.catalog.spark_catalog.handle-timestamp-without-timezone", "true")
        .config("spark.sql.parquet.datetimeRebaseModeInWrite", "CORRECTED")
        .config("spark.sql.catalog.spark_catalog.warehouse", destinationIcebergWarehouse)
        .config("spark.hadoop.fs.s3a.endpoint.region", credentials.getRegion());

    logger.info("Configuring AWS credentials for Spark session, hasArn={}, region={}",
                StringUtils.isNotBlank(credentials.getArn()), credentials.getRegion());

    // Configure AWS credentials based on authentication type
    if (StringUtils.isNotBlank(credentials.getArn())) {
      logger.info("Configuring ARN-based authentication with role: {}", credentials.getArn());
      configureAssumedRoleCredentials(sparkBuilder, credentials);
    } else {
      logger.info("Configuring direct AWS credentials authentication");
      configureSimpleCredentials(sparkBuilder, credentials);
    }

    return sparkBuilder.getOrCreate();
  }

  private void configureAssumedRoleCredentials(SparkSession.Builder sparkBuilder, Credentials credentials) {
    logger.info("Configuring assumed role credentials with ARN: {}", credentials.getArn());

    // Check if explicit access keys are provided along with ARN
    boolean hasExplicitCredentials = StringUtils.isNotBlank(credentials.getAccessKeyId()) &&
                                   StringUtils.isNotBlank(credentials.getSecretKey());

    if (hasExplicitCredentials) {
      // When explicit credentials are provided, use them to assume the role
      logger.info("Using explicit credentials to assume role: {}", credentials.getArn());
      sparkBuilder.config("spark.hadoop.fs.s3a.assumed.role.arn", credentials.getArn())
                  .config("spark.hadoop.fs.s3a.aws.credentials.provider", AssumedRoleCredentialProvider.NAME)
                  .config("spark.hadoop.fs.s3a.assumed.role.credentials.provider", SimpleAWSCredentialsProvider.NAME)
                  .config("spark.hadoop.fs.s3a.access.key", credentials.getAccessKeyId())
                  .config("spark.hadoop.fs.s3a.secret.key", credentials.getSecretKey());

      // Add session token if available
      if (StringUtils.isNotBlank(credentials.getSessionToken())) {
        sparkBuilder.config("spark.hadoop.fs.s3a.session.token", credentials.getSessionToken());
        logger.info("Added session token for role assumption");
      }
    } else {
      // No explicit credentials provided, check for EKS IRSA environment
      String currentIdentity = getCurrentAwsIdentity();
      logger.info("Current AWS identity type: {}",
               currentIdentity != null ? (currentIdentity.contains("assumed-role") ? "assumed-role" : "other") : "null");

      // If current identity is already an assumed role and matches the configured ARN,
      // we're in EKS with IRSA - use DefaultCredentialsProvider directly
      if (currentIdentity != null && currentIdentity.contains("assumed-role") &&
          isCurrentIdentityMatchingConfiguredRole(currentIdentity, credentials.getArn())) {
        logger.info("Detected EKS IRSA environment, using DefaultCredentialsProvider");
        sparkBuilder.config("spark.hadoop.fs.s3a.aws.credentials.provider",
                           DefaultCredentialsProvider.class.getName());
      } else {
        // Traditional role assumption using instance profile or default credentials
        logger.info("Using traditional role assumption with AssumedRoleCredentialProvider");
        sparkBuilder.config("spark.hadoop.fs.s3a.assumed.role.arn", credentials.getArn())
                    .config("spark.hadoop.fs.s3a.aws.credentials.provider", AssumedRoleCredentialProvider.NAME)
                    .config("spark.hadoop.fs.s3a.assumed.role.credentials.provider",
                            String.join(",",
                                    InstanceProfileCredentialsProvider.class.getName(),
                                    DefaultCredentialsProvider.class.getName()));
      }
    }
  }

  private void configureSimpleCredentials(SparkSession.Builder sparkBuilder, Credentials credentials) {
    if (StringUtils.isNotBlank(credentials.getAccessKeyId()) && StringUtils.isNotBlank(credentials.getSecretKey())) {
      sparkBuilder.config("spark.hadoop.fs.s3a.access.key", credentials.getAccessKeyId())
                  .config("spark.hadoop.fs.s3a.secret.key", credentials.getSecretKey())
                  .config("spark.hadoop.fs.s3a.aws.credentials.provider", SimpleAWSCredentialsProvider.NAME);

      // Enable session token if available
      if (StringUtils.isNotBlank(credentials.getSessionToken())) {
        sparkBuilder.config("spark.hadoop.fs.s3a.session.token", credentials.getSessionToken());
        logger.info("Configured direct credentials with session token");
      } else {
        logger.info("Configured direct credentials without session token");
      }
    } else {
      logger.warn("No valid access key/secret key found, using DefaultCredentialsProvider");
      sparkBuilder.config("spark.hadoop.fs.s3a.aws.credentials.provider",
                         DefaultCredentialsProvider.class.getName());
    }
  }

  private String getCurrentAwsIdentity() {
    try (StsClient stsClient = StsClient.builder()
        .credentialsProvider(DefaultCredentialsProvider.create())
        .build()) {

      GetCallerIdentityResponse response = stsClient.getCallerIdentity();
      String arn = response.arn();
      logger.debug("Retrieved AWS identity type: {}",
                arn != null ? (arn.contains("assumed-role") ? "assumed-role" : "other") : "null");
      return arn;
    } catch (Exception e) {
      logger.warn("Failed to get current AWS identity: {}", e.getMessage());
      return null;
    }
  }

  private boolean isCurrentIdentityMatchingConfiguredRole(String currentIdentity, String configuredArn) {
    if (currentIdentity == null || configuredArn == null) {
      return false;
    }

    // Extract role name from current identity (assumed-role format)
    // Current identity: arn:aws:sts::account:assumed-role/role-name/session-name
    // Configured ARN: arn:aws:iam::account:role/role-name
    try {
      String[] currentParts = currentIdentity.split("/");
      String[] configuredParts = configuredArn.split("/");

      if (currentParts.length >= 2 && configuredParts.length >= 2) {
        String currentRoleName = currentParts[1]; // role-name from assumed-role
        String configuredRoleName = configuredParts[1]; // role-name from iam role

        boolean matches = currentRoleName.equals(configuredRoleName);
        logger.debug("Role name comparison: currentRole={}, configuredRole={}, matches={}",
                      currentRoleName, configuredRoleName, matches);
        return matches;
      }
    } catch (Exception e) {
      logger.warn("Failed to parse role names from ARNs: current={}, configured={}",
                  currentIdentity, configuredArn, e);
    }

    return false;
  }

  private void logSparkConfiguration(SparkSession spark) {
    logger.info("=== Spark Configuration Debug ===");

    // Log key Spark configurations
    String[] configKeys = {
        "spark.sql.catalog.spark_catalog",
        "spark.sql.catalog.spark_catalog.type",
        "spark.sql.catalog.spark_catalog.warehouse",
        "spark.hadoop.fs.s3a.access.key",
        "spark.hadoop.fs.s3a.secret.key",
        "spark.hadoop.fs.s3a.session.token",
        "spark.hadoop.fs.s3a.aws.credentials.provider",
        "spark.hadoop.fs.s3a.assumed.role.arn",
        "spark.hadoop.fs.s3a.endpoint.region"
    };

    for (String key : configKeys) {
      String value = spark.conf().getOption(key).getOrElse(() -> "NOT_SET");
      // Mask sensitive values
      if (key.contains("secret") || key.contains("token")) {
        value = value.equals("NOT_SET") ? "NOT_SET" : "***MASKED***";
      }
      logger.info("Config: {} = {}", key, value);
    }

    logger.info("=== End Spark Configuration Debug ===");
  }



  @VisibleForTesting
  AuroraSnapshotToIcebergMigrator createMigrator(
      final SparkSession spark,
      final String sourceSnapshotPath,
      final String destinationIcebergWarehouse,
      final String catalogName) {
    return new AuroraSnapshotToIcebergMigrator(logger, spark, sourceSnapshotPath, destinationIcebergWarehouse, catalogName);
  }
}