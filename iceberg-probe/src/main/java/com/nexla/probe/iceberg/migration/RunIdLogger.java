package com.nexla.probe.iceberg.migration;

import org.slf4j.Logger;

public class RunIdLogger {
  private final String runId;
  private final Logger logger;

  public RunIdLogger(String runId, Logger logger) {
    this.runId = runId;
    this.logger = logger;
  }

  public void debug(final String message, final Object... args) {
    logger.debug("RunId: {}, {}", prependRunId(runId, args), message);
  }

  public void info(final String message, final Object... args) {
    logger.info("RunId: {}, {}", prependRunId(runId, args), message);
  }

  public void error(final String message, final Object... args) {
    logger.error("RunId: {}, {}", prependRunId(runId, args), message);
  }

  public void warn(final String message, final Object... args) {
    logger.warn("RunId: {}, {}", prependRunId(runId, args), message);
  }

  private static Object[] prependRunId(final String runId, final Object[] args) {
    Object[] newArgs = new Object[args.length + 1];
    newArgs[0] = runId;
    System.arraycopy(args, 0, newArgs, 1, args.length);
    return newArgs;
  }
}
