package com.nexla.connector.soap.sink;

import com.google.common.collect.Maps;
import com.nexla.admin.client.oauth2.RefreshingTokenProvider;
import com.nexla.client.ScriptEvalClient;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaSslContext;
import com.nexla.common.Resource;
import com.nexla.common.exception.NexlaError;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.pool.NexlaPool;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connect.common.DetailedFlowInsightsSender;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.soap.SoapSinkConnectorConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.probe.http.SoapConnectorService;
import com.nexla.sinkcaller.HttpResponseState;
import com.nexla.sinkcaller.SoapSinkCaller;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.function.Supplier;

import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.exception.NexlaError.getErrorDetails;
import static com.nexla.connect.common.NexlaConnectorUtils.publishException;
import static com.nexla.probe.http.BaseRequestSender.createSender;
import static com.nexla.probe.http.BaseRequestSender.createSenderPool;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;

public class SoapSinkTask extends BaseSinkTask<SoapSinkConnectorConfig> {

	private SoapConnectorService probeService;
	private NexlaPool<RequestSender> senderPool;
	private ForkJoinPool executorService;
	private Optional<DetailedFlowInsightsSender> flowInsightsSender;

	@Override
	public void doStart() {
		this.sinkTelemetryReporter =
				Optional.of(new ConnectorTelemetryReporter(config.sinkId, ConnectionType.SOAP.name(), SINK, isDedicatedNode));
		this.flowInsightsSender = Optional.ofNullable(DetailedFlowInsightsSender.from(
				config.logVerbose, getSuccessFlowInsightsSender(), getErrorFlowInsightsSender(), config.detailedFlowInsightsAbbreviatedLength));

		ScriptEvalClient scriptEvalClient = new ScriptEvalClient(config.probeAppUrl, config.nexlaUsername, config.nexlaPassword, restTemplate);

		this.probeService = new SoapConnectorService(scriptEvalClient);
		this.probeService.initLogger(SINK, config.sinkId, taskId);

		RefreshingTokenProvider tokenProvider = new RefreshingTokenProvider(adminApiClient, config.decryptKey);
		Supplier<RequestSender> senderSupplier = () ->
				// XXX: always does URL encoding
			createSender(config.authConfig, tokenProvider, scriptEvalClient, config.logVerbose, false, flowInsightsSender)
				.withLoggerPrefix(logger.getPrefix(), "[request]");

		this.senderPool = createSenderPool(config.requestParallelism, senderSupplier);
		this.executorService = (ForkJoinPool) Executors.newWorkStealingPool(config.requestParallelism);
	}

	@Override
	protected ConfigDef configDef() {
		return SoapSinkConnectorConfig.configDef();
	}

	@Override
	protected SoapSinkConnectorConfig parseConfig(Map<String, String> props) {
		return new SoapSinkConnectorConfig(props);
	}

	@SneakyThrows
	@Override
	protected void doPut(StreamEx<NexlaMessageContext> messages, int streamSize) {
		SoapSinkCaller caller = createSinkCaller();
		doPutBatch(streamSize, caller, messages);
	}

	public List<String> doPutBatch(int streamSize, SoapSinkCaller caller, StreamEx<NexlaMessageContext> messages) {
		Map<TopicPartition, Long> offsets = Maps.newHashMap();

		StreamEx<NexlaMessageContext> streamUpdatingOffsets = config.fastMode
			? messages
			: messages.peek(m -> offsets.put(m.topicPartition, m.kafkaOffset));

		List<HttpResponseState> results = executeByOneParallel(caller, streamUpdatingOffsets);

		offsetsSender.ifPresent(os -> {
			os.updateSinkOffsets(config.sinkId, offsets);
			logger.info("Updated offsets: {}", offsets);
		});

		AtomicLong sentRecordsTotal = new AtomicLong();
		AtomicLong sentBytesTotal = new AtomicLong();

		results.stream().filter(r -> r.getThrowable() == null).forEach(state -> {
			sentRecordsTotal.addAndGet(state.getSentRecords());
			sentBytesTotal.addAndGet(state.getSentBytes());
		});

		List<HttpResponseState> callsWithErrors = results.stream()
			.filter(restResponseState -> restResponseState.getThrowable() != null)
			.collect(toList());

		callsWithErrors.forEach(state -> {
			logException(state.getThrowable(), state.getUrl(), state.getBody());
			sendMetric(state.getUrl(), Optional.empty(), 0L, 0L, state.getErrorRecords());
			publishExceptionMessage(state.getThrowable(), state.getUrl(), state.getErrorRecords());
			publishMonitoringLog(state.getThrowable().getMessage(), NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
		});

		if (!callsWithErrors.isEmpty()) {
			if (config.stopOnError) {
				throw new ConnectException("Stopping connection as stop.on.error=true", callsWithErrors.get(0).getThrowable());
			} else {
				return callsWithErrors.stream().map(soapResponseState -> {
							Optional<String> responseOpt = Optional.ofNullable(soapResponseState.getResponse()).orElseGet(Optional::empty);
							return responseOpt.orElse(soapResponseState.getThrowable().getMessage());
						}
				).collect(toList());	
			} 
			
		} else if (sentRecordsTotal.get() > 0) {
			HttpResponseState firstResult = results.get(0);
			sendMetric(config.soapConf.getWsdlUrl(), Optional.ofNullable(firstResult.getUrl()),
				sentRecordsTotal.get(), sentBytesTotal.get(), 0L);
		}
		
		return results.stream()
				.map(soapResponseState -> Optional.ofNullable(soapResponseState.getResponse()).flatMap(Function.identity()).orElse(""))
				.collect(toList());
	}

	public SoapSinkCaller createSinkCaller() {
		return new SoapSinkCaller(
			senderPool,
			config.soapConf,
			logger,
			config.logVerbose
		);
	}

	@SneakyThrows
	private List<HttpResponseState> executeByOneParallel(SoapSinkCaller caller, StreamEx<NexlaMessageContext> messages) {
		return messages
			.parallel(executorService)
			.map(m -> {
				HttpResponseState state = new HttpResponseState();
				try {
					caller.putSingleMessage(m.mapped, state);
				} catch (Exception e) {
					dataMessageProducer.sendQuarantineMessage(new Resource(config.sinkId, SINK), m.mapped, e);
					state.setThrowable(e);
					state.setErrorRecords(1);
				}
				return state;
			})
			.toList();
	}

	private void logException(Throwable e, String url, Optional<String> body) {
		NexlaError error = getErrorDetails(e, empty());
		logger.error("Failed to send message for url={} body={} error={} responseBody={}", url, body, error.getMessage(),
			error.getResponseBody(), e);
	}

	private void publishExceptionMessage(Throwable e, String url, long errorRecords) {
		publishException(controlMessageProducer, 0L, SINK, config.sinkId, 0L, url, getErrorDetails(e, ofNullable(errorRecords)));
	}

	@SneakyThrows
	@Override
	public void stop() {
		probeService.close();
		super.stop();
	}

}
