package com.nexla.connector.push.sink;

import com.google.common.collect.Maps;
import com.nexla.connect.common.connector.BaseSinkConnector;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.connector.Task;

import java.util.Map;
import java.util.Optional;

import static com.nexla.connector.config.FileEncryptConfig.ENCRYPT_PRIVATE_KEY;
import static com.nexla.connector.config.FileEncryptConfig.ENCRYPT_PRIVATE_PASSWORD;
import static com.nexla.connector.config.vault.CredentialsStore.CONNECTOR_POLICY;
import static com.nexla.connector.config.vault.CredentialsStore.SECRET;

/**
 * FileSinkConnector is a Kafka Connect Connector implementation that exports data
 * from Kafka to a file storage (S3, FTP).
 */
public class FileSinkConnector extends BaseSinkConnector {

	public static final String FILE_SINK_TELEMETRY_NAME = "file-sink";

	@Override
	protected String telemetryAppName() {
		return FILE_SINK_TELEMETRY_NAME;
	}

	@Override
	public Class<? extends Task> taskClass() {
		return FileSinkTask.class;
	}

	public Map<String, String> customConnectorProperties(int sinkId) {
		Map<String, String> result = Maps.newHashMap();

		try {
			String vaultConnectorPrefix = SECRET + "/" + CONNECTOR_POLICY + "/" + ("sink_" + sinkId);
			Optional<Map<String, String>> vaultProps = nexlaAppConfigProperties
				.getStore()
				.getValuesMap(vaultConnectorPrefix);

			vaultProps
				.map(x -> x.get(ENCRYPT_PRIVATE_KEY))
				.ifPresent(x -> result.put(ENCRYPT_PRIVATE_KEY, x));

			vaultProps
				.map(x -> x.get(ENCRYPT_PRIVATE_PASSWORD))
				.ifPresent(x -> result.put(ENCRYPT_PRIVATE_PASSWORD, x));
		} catch (Exception e) {
			// do we use it really?
		}
		return result;
	}

	@Override
	public ConfigDef config() {
		return FileSinkConnectorConfig.configDef();
	}

}
