package com.nexla.connector.push.sink;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableSet;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.admin.client.pipeline.PDataSource;
import com.nexla.admin.client.pipeline.Pipeline;
import com.nexla.client.JavaJobSchedulerClient;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaFile;
import com.nexla.common.metrics.NexlaRawMetric;
import com.nexla.connect.common.*;
import com.nexla.connect.common.postponedFlush.KafkaProgressTracker;
import com.nexla.connector.config.file.FileSinkConnectorConfig;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.file.FtpAuthConfig;
import com.nexla.connector.config.file.FtpConstants;
import com.nexla.connector.file.sink.FileSinkWriter;
import com.nexla.kafka.service.KafkaLagCalculatorImpl;
import com.nexla.probe.ftp.FtpConnectorService;
import com.nexla.probe.ftp.impl.NexlaFtpClient;
import com.nexla.test.IntegrationTests;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ftpserver.ftplet.Authority;
import org.apache.ftpserver.usermanager.impl.WritePermission;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.javatuples.Pair;
import org.junit.*;
import org.junit.experimental.categories.Category;
import org.testcontainers.containers.KafkaContainer;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.zip.GZIPInputStream;

import static com.nexla.common.ConverterUtils.toLong;
import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.common.metrics.NexlaRawMetric.*;
import static com.nexla.connect.common.BaseSinkTask.ADMIN_API_PREFIX;
import static com.nexla.connect.common.PostponedFlush.FORCE_FLUSH_FOLDER_PATH;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.BaseConnectorConfig.METRICS_TOPIC;
import static com.nexla.connector.config.SinkConnectorConfig.*;
import static com.nexla.connector.config.file.DirScanningMode.BOTH;
import static com.nexla.connector.config.file.FileSinkConnectorConfig.FILENAME_OFFSET;
import static com.nexla.connector.config.file.FtpConstants.PASSIVE_LOCAL;
import static com.nexla.connector.properties.FileConfigAccessor.MONITOR_POLL_MS;
import static com.nexla.connector.properties.FileConfigAccessor.*;
import static java.util.Arrays.asList;
import static java.util.Collections.emptySet;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.io.FileUtils.deleteDirectory;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.never;

@Category(IntegrationTests.class)
public class FileSinkTaskTest extends BaseKafkaTest {

	public static KafkaContainer kafka = new KafkaContainer("7.2.11");

	@BeforeClass
	public static void startUp() {
		kafka.withReuse(true);
		kafka.start();
		init(kafka);
	}

	@AfterClass
	public static void tearDown() {
		kafka.stop();
	}

	private static final String FTP_USERNAME = "username";
	private static final String FTP_PASSWORD = "password";

	private FileSinkTask task;
	private TestFtpServer ftpServer;
	private Path rootDir;
	private FileSourceConnectorConfig config = mock(FileSourceConnectorConfig.class);
	private ObjectMapper mapper = new ObjectMapper();
	private AdminApiClient adminApiClient = mock(AdminApiClient.class);
	private JavaJobSchedulerClient ctrlClient = mock(JavaJobSchedulerClient.class);
	private Pipeline pipeline;

	@SneakyThrows
	@Before
	public void onBefore() {
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

		String pipelineStr = getPipelineStr();
		pipeline = mapper.readValue(pipelineStr, Pipeline.class);

		AdminApiClientBuilder.INSTANCE = adminApiClient;
		when(adminApiClient.getPipeline(anyInt())).thenReturn(Optional.of(pipeline));
		DataSink dataSink = new DataSink();
		dataSink.setId(1);
		dataSink.setConnectionType(ConnectionType.MYSQL);
		dataSink.setSinkConfig(Map.of("parallelism", "1"));
		dataSink.setStatus(ResourceStatus.ACTIVE);
		when(adminApiClient.getDataSink(any(Integer.class))).thenReturn(Optional.of(dataSink));
		when(adminApiClient.getPipeline(anyInt())).thenReturn(Optional.of(pipeline));
		KafkaLagCalculatorImpl mockLagCalc = mock(KafkaLagCalculatorImpl.class);

		this.task = new FileSinkTask() {
			@Override
			@SneakyThrows
			protected Optional<JavaJobSchedulerClient> createCtrlClient() {
				when(FileSinkTaskTest.this.ctrlClient.isServiceRunning(any())).thenReturn(false);
				return Optional.of(FileSinkTaskTest.this.ctrlClient);
			}

			KafkaProgressTracker tracker = new KafkaProgressTracker(mockLagCalc, adminApiClient, ctrlClient, logger);
			protected PostponedFlush postponedFlush(BaseSinkTask task) {
				return new PostponedFlush(task, tracker, logger) {
					@Override
					protected boolean dataMatureMs(long delayMs) {
						return true;
					}

					@Override
					protected boolean noDataMatureMs(long delayMs) {
						return false;
					}

					@Override
					protected boolean hasAccumulatedRecords() { return false; }
				};
			}
		};

		task.initialize(mock(SinkTaskContext.class));

		SinkTaskContext taskContext = mock(SinkTaskContext.class);
		when(taskContext.assignment()).thenReturn(emptySet());

		task.initialize(taskContext);

		this.rootDir = Files.createTempDirectory("test-file-sink-ftp");
		this.runBeforeTopicReading = Optional.of(() -> task.getControlMessageProducer().flush());
	}

	@After
	@SneakyThrows
	public void onAfter() {
		closeSilently(
			() -> ftpServer.after(),
			() -> deleteDirectory(rootDir.toFile()));
		if (task != null
				&& task.getControlMessageProducer() != null) {
			task.getControlMessageProducer().close();
		}
	}

	@SneakyThrows
	//@Test FIXME Deactivating failed tests in release/v3.2.0
	public void putFtp_groupped_old_mapping_manual_with_tracker() {

		withConsumer((metricsTopic, metricsConsumer) -> {
			try {

				this.ftpServer = new TestFtpServer(rootDir, writePermissions());
				ftpServer.before();

				FileSinkConnector connector = new FileSinkConnector();

				connector.start(
					new HashMap<String, String>() {{

						put(UNIT_TEST, "true");

						put(SINK_ID, "1");

						put(FtpConstants.FTP_MODE, PASSIVE_LOCAL);
						put(FtpConstants.USERNAME, FTP_USERNAME);
						put(FtpConstants.PASSWORD, FTP_PASSWORD);
						put(FtpConstants.PORT, ftpServer.port + "");

						put(CREDS_ENC, "1");
						put(CREDS_ENC_IV, "1");
						put(CREDENTIALS_DECRYPT_KEY, "1");
						put(LISTING_ENABLED, "false");

						put(SINK_TYPE, ConnectionType.FTP.name());

						put(MAPPING,
							"{ " +
								"\"mode\": \"manual\", " +
								"\"tracker_name\": \"mytracker\", " +
								"\"tracker_mode\": \"record\", " +
								"\"mapping\": { \"city\": \"city\", \"country.name\": \"country_name\", \"key3\": \"vvv3\", \"key1\": \"vvv1\" } " +
								"}");

						put(DATA_FORMAT, "csv");
						put(PATH, "/upload");
						put(OUTPUT_DIR_NAME_PATTERN, "{yyyy}/{MM}/{dd}/{HH}/{mm}/{ss}/city={record.city}");
						put(MAX_FILE_SIZE_MB, "0");
						put(FILE_NAME_PREFIX, "my-dataset_{yyyy}");
						put(MONITOR_POLL_MS, "1");
						put(TRACKER_ENCRYPTION_ENABLED, "true");
						put(TRACKER_ENCRYPTION_KEY, "9Rz+VE1VzxS7wvUpg8hhsbrt8vrPLUKBtDNd8N/wKbg=");
						put(METRICS_TOPIC, metricsTopic);
					}});

				Map<String, String> taskParams = connector.taskConfigs(1).get(0);

				task.start(taskParams, "sink");

				TopicPartition tp = new TopicPartition("ds-12", 1);
				task.open(ImmutableSet.of(tp));

				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 1, \"city\": \"New York\", \"key1\": \"val1\", \"key2\": \"val2\", \"key3\": \"val3\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 1L)));
				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 2, \"city\": \"Moscow\", \"key1\": \"val1\", \"key2\": \"val2\", \"key3\": \"val3\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 2L)));

				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 3, \"city\": \"New York\", \"key1\": \"val1\", \"key2\": \"val2\", \"key3\": \"val3\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 3L)));
				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 4, \"city\": \"Moscow\", \"key1\": \"val1\", \"key2\": \"val2\", \"key3\": \"val3\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 4L)));

				Thread.sleep(2000);

				task.flush(null);

				FtpConnectorService ftpProbeService = (FtpConnectorService) task.probeService;
				try (NexlaFtpClient ftpClient = ftpProbeService.getFtpClientProvider().getNexlaFtpClient((FtpAuthConfig) task.config.getAuthConfig())) {

					List<NexlaFile> uploadedFiles = FtpConnectorService.getFiles(ftpClient, "/", BOTH, 10, config)
																	   .filter(file -> file.getSize() > 0)
																	   .collect(toList());

					Map<String, String> fileContent = getFilesContent(ftpClient, uploadedFiles);

					assertEquals(6, uploadedFiles.size());

					int year = nowUTC().getYear();

					verifyContentByLine(uploadedFiles, fileContent, "/city=Moscow/my-dataset_" + year + "-" + tp.topic() + "-000000000002.csv",
							new String[]{
									"city,country_name,vvv3,vvv1,mytracker",
									"Moscow,,val3,val1"
							});
					verifyContentByLine(uploadedFiles, fileContent, "/city=Moscow/my-dataset_" + year + "-" + tp.topic() + "-000000000004.csv",
							new String[]{
									"city,country_name,vvv3,vvv1,mytracker",
									"Moscow,,val3,val1"
							});
					verifyContentByLine(uploadedFiles, fileContent, "/city=New York/my-dataset_" + year + "-" + tp.topic() + "-000000000001.csv",
							new String[]{
									"city,country_name,vvv3,vvv1,mytracker",
									"\"New York\",,val3,val1"
							});
					verifyContentByLine(uploadedFiles, fileContent, "/city=New York/my-dataset_" + year + "-" + tp.topic() + "-000000000003.csv",
							new String[]{
									"city,country_name,vvv3,vvv1,mytracker",
									"\"New York\",,val3,val1"
							});
					verifyContent(uploadedFiles, fileContent, "/city=Moscow/my-dataset_" + year + "-" + tp.topic() + "-00001-000000000001.index.json",
							"[{\"rawBytes\":520,\"firstOffset\":1,\"lastOffset\":4,\"numRecords\":4}]");

					verifyContent(uploadedFiles, fileContent, "/upload/meta/last_chunk_index." + tp.topic() + "-00001.txt",
							"/city=Moscow/my-dataset_" + year + "-" + tp.topic() + "-00001-000000000001.index.json");

					List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);

					assertEquals(4, metrics.size());
					metrics.sort(Comparator.comparing(metric -> StringUtils.right(metric.getTags().get(NAME), 5)));

					assertMetric(metrics.get(0), "/city=New York/my-dataset_" + year + "-" + tp.topic() + "-000000000001.csv", 1L, 731L);
					assertMetric(metrics.get(1), "/city=Moscow/my-dataset_" + year + "-" + tp.topic() + "-000000000002.csv", 1L, 729L);
					assertMetric(metrics.get(2), "/city=New York/my-dataset_" + year + "-" + tp.topic() + "-000000000003.csv", 1L, 731L);
					assertMetric(metrics.get(3), "/city=Moscow/my-dataset_" + year + "-" + tp.topic() + "-000000000004.csv", 1L, 729L);

					task.stop();
					connector.stop();
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		});
	}

	@SneakyThrows
	//@Test FIXME Deactivating failed tests in release/v3.2.0
	public void putFtp_exclusive_writer_runid() {

		withConsumer((metricsTopic, metricsConsumer) -> {

			try {

				this.ftpServer = new TestFtpServer(rootDir, writePermissions());
				ftpServer.before();

				FileSinkConnector connector = new FileSinkConnector();

				connector.start(
					new HashMap<String, String>() {{

						put(UNIT_TEST, "true");

						put(SINK_ID, "1");

						put(FtpConstants.FTP_MODE, PASSIVE_LOCAL);
						put(FtpConstants.USERNAME, FTP_USERNAME);
						put(FtpConstants.PASSWORD, FTP_PASSWORD);
						put(FtpConstants.PORT, ftpServer.port + "");

						put(CREDS_ENC, "1");
						put(CREDS_ENC_IV, "1");
						put(CREDENTIALS_DECRYPT_KEY, "1");
						put(LISTING_ENABLED, "false");

						put(SINK_TYPE, ConnectionType.FTP.name());

						put(MAPPING,
							"{ \"mode\": \"auto\", \"mapping\": { \"country.name\": \"country_name\", \"key3\": \"vvv3\", \"key1\": \"vvv1\" } }");

						put(DATA_FORMAT, "csv");
						put(PATH, "/upload");
						put(OUTPUT_DIR_NAME_PATTERN, "{yyyy}/{MM}/{dd}/{HH}/{mm}/{ss}/city={record.city}-{record.meta.run_id}");
						put(MAX_FILE_SIZE_MB, "1");
						put(FILE_NAME_PREFIX, "my-dataset");
						put(MONITOR_POLL_MS, "1");
						put(TRACKER_ENCRYPTION_ENABLED, "true");
						put(TRACKER_ENCRYPTION_KEY, "9Rz+VE1VzxS7wvUpg8hhsbrt8vrPLUKBtDNd8N/wKbg=");
						put(METRICS_TOPIC, metricsTopic);
						put(FILENAME_OFFSET, "true");
					}});

				Map<String, String> taskParams = connector.taskConfigs(1).get(0);
				task.start(taskParams, "sink");

				TopicPartition tp = new TopicPartition("ds-12", 1);
				task.open(ImmutableSet.of(tp));

				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 1, \"city\": \"Moscow\", \"key1\": \"val1\", \"key2\": \"val2\", \"key3\": \"val3\"}, \"nexlaMetaData\": {\"runId\": 123,  \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 1L)));
				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 2, \"city\": \"Moscow\", \"key1\": \"val1\", \"key2\": \"val2\", \"key3\": \"val3\"}, \"nexlaMetaData\": {\"runId\": 234,  \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 2L)));

				task.flush(null);

				FtpConnectorService ftpProbeService = (FtpConnectorService) task.probeService;
				try (NexlaFtpClient ftpClient = ftpProbeService.getFtpClientProvider().getNexlaFtpClient((FtpAuthConfig) task.config.getAuthConfig())) {

					List<NexlaFile> uploadedFiles = FtpConnectorService.getFiles(ftpClient, "/", BOTH, 10, config)
																	   .filter(file -> file.getSize() > 0)
																	   .collect(toList());

					Map<String, String> fileContent = getFilesContent(ftpClient, uploadedFiles);

					assertEquals(4, uploadedFiles.size());

					verifyContent(uploadedFiles, fileContent, "/city=Moscow-123/my-dataset-ds-12-000000000001.csv",
							"vvv3,vvv1,id,city,key2\n" +
									"val3,val1,1,Moscow,val2");
					verifyContent(uploadedFiles, fileContent, "/city=Moscow-234/my-dataset-ds-12-000000000002.csv",
							"vvv3,vvv1,id,city,key2\n" +
									"val3,val1,2,Moscow,val2");
					verifyContent(uploadedFiles, fileContent, "/city=Moscow-234/my-dataset-ds-12-00001-000000000001.index.json",
							"[{\"rawBytes\":48,\"firstOffset\":1,\"lastOffset\":2,\"numRecords\":2}]");
					verifyContent(uploadedFiles, fileContent, "/upload/meta/last_chunk_index.ds-12-00001.txt",
							"/city=Moscow-234/my-dataset-ds-12-00001-000000000001.index.json");

					List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);

					assertEquals(2, metrics.size());
					metrics.sort(Comparator.comparing(metric -> StringUtils.right(metric.getTags().get(NAME), 5)));

					assertMetric(metrics.get(0), "/city=Moscow-123/my-dataset-ds-12-000000000001.csv", 1L, 605L);
					assertMetric(metrics.get(1), "/city=Moscow-234/my-dataset-ds-12-000000000002.csv", 1L, 605L);

					task.stop();
					connector.stop();
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		});
	}

	@SneakyThrows
	//@Test FIXME Deactivating failed tests in release/v3.2.0
	public void putFtp_groupped_old_mapping() {

		withConsumer((metricsTopic, metricsConsumer) -> {

			try {

				this.ftpServer = new TestFtpServer(rootDir, writePermissions());
				ftpServer.before();

				FileSinkConnector connector = new FileSinkConnector();

				connector.start(
					new HashMap<String, String>() {{

						put(UNIT_TEST, "true");

						put(SINK_ID, "1");

						put(FtpConstants.FTP_MODE, PASSIVE_LOCAL);
						put(FtpConstants.USERNAME, FTP_USERNAME);
						put(FtpConstants.PASSWORD, FTP_PASSWORD);
						put(FtpConstants.PORT, ftpServer.port + "");

						put(CREDS_ENC, "1");
						put(CREDS_ENC_IV, "1");
						put(CREDENTIALS_DECRYPT_KEY, "1");
						put(LISTING_ENABLED, "false");

						put(SINK_TYPE, ConnectionType.FTP.name());

						put(MAPPING,
							"{ \"mode\": \"auto\", \"mapping\": { \"country.name\": \"country_name\", \"key3\": \"vvv3\", \"key1\": \"vvv1\" } }");

						put(DATA_FORMAT, "csv");
						put(PATH, "/upload");
						put(OUTPUT_DIR_NAME_PATTERN, "{yyyy}/{MM}/{dd}/{HH}/{mm}/{ss}/city={record.city}");
						put(MAX_FILE_SIZE_MB, "0");
						put(FILE_NAME_PREFIX, "my-dataset");
						put(MONITOR_POLL_MS, "1");
						put(TRACKER_ENCRYPTION_ENABLED, "true");
						put(TRACKER_ENCRYPTION_KEY, "9Rz+VE1VzxS7wvUpg8hhsbrt8vrPLUKBtDNd8N/wKbg=");
						put(METRICS_TOPIC, metricsTopic);
					}});

				Map<String, String> taskParams = connector.taskConfigs(1).get(0);
				task.start(taskParams, "sink");

				TopicPartition tp = new TopicPartition("ds-12", 1);
				task.open(ImmutableSet.of(tp));

				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 1, \"city\": \"New York\", \"key1\": \"val1\", \"key2\": \"val2\", \"key3\": \"val3\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 1L)));
				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 2, \"city\": \"Moscow\", \"key1\": \"val1\", \"key2\": \"val2\", \"key3\": \"val3\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 2L)));

				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 3, \"city\": \"New York\", \"key1\": \"val1\", \"key2\": \"val2\", \"key3\": \"val3\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 3L)));
				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 4, \"city\": \"Moscow\", \"key1\": \"val1\", \"key2\": \"val2\", \"key3\": \"val3\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 4L)));

				Thread.sleep(2000);

				task.flush(null);

				FtpConnectorService ftpProbeService = (FtpConnectorService) task.probeService;
				try (NexlaFtpClient ftpClient = ftpProbeService.getFtpClientProvider().getNexlaFtpClient((FtpAuthConfig) task.config.getAuthConfig())) {

					List<NexlaFile> uploadedFiles = FtpConnectorService.getFiles(ftpClient, "/", BOTH, 10, config)
																	   .filter(file -> file.getSize() > 0)
																	   .collect(toList());

					Map<String, String> fileContent = getFilesContent(ftpClient, uploadedFiles);

					assertEquals(6, uploadedFiles.size());

					verifyContent(uploadedFiles, fileContent, "/city=Moscow/my-dataset-ds-12-000000000002.csv",
							"vvv3,vvv1,id,city,key2\n" +
									"val3,val1,2,Moscow,val2");
					verifyContent(uploadedFiles, fileContent, "/city=Moscow/my-dataset-ds-12-000000000004.csv",
							"vvv3,vvv1,id,city,key2\n" +
									"val3,val1,4,Moscow,val2");
					verifyContent(uploadedFiles, fileContent, "/city=New York/my-dataset-ds-12-000000000001.csv",
							"vvv3,vvv1,id,city,key2\n" +
									"val3,val1,1,\"New York\",val2");
					verifyContent(uploadedFiles, fileContent, "/city=New York/my-dataset-ds-12-000000000003.csv",
							"vvv3,vvv1,id,city,key2\n" +
									"val3,val1,3,\"New York\",val2");
					verifyContent(uploadedFiles, fileContent, "/city=Moscow/my-dataset-ds-12-00001-000000000001.index.json",
							"[{\"rawBytes\":104,\"firstOffset\":1,\"lastOffset\":4,\"numRecords\":4}]");

					verifyContent(uploadedFiles, fileContent, "/upload/meta/last_chunk_index.ds-12-00001.txt",
							"/city=Moscow/my-dataset-ds-12-00001-000000000001.index.json");

					List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);

					assertEquals(4, metrics.size());
					metrics.sort(Comparator.comparing(metric -> StringUtils.right(metric.getTags().get(NAME), 5)));

					assertMetric(metrics.get(0), "/city=New York/my-dataset-ds-12-000000000001.csv", 1L, 605L);
					assertMetric(metrics.get(1), "/city=Moscow/my-dataset-ds-12-000000000002.csv", 1L, 603L);
					assertMetric(metrics.get(2), "/city=New York/my-dataset-ds-12-000000000003.csv", 1L, 605L);
					assertMetric(metrics.get(3), "/city=Moscow/my-dataset-ds-12-000000000004.csv", 1L, 603L);

					task.stop();
					connector.stop();
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		});
	}

	//@Test FIXME Deactivating failed tests in release/v3.2.0
	public void putFtp_groupped_reopen_LRU_gzip() throws Exception {
		FileSinkConnector connector = new FileSinkConnector();
		this.ftpServer = new TestFtpServer(rootDir, writePermissions());
		ftpServer.before();

		connector.start(
			new HashMap<String, String>() {{

				put(UNIT_TEST, "true");

				put(SINK_ID, "1");

				put(FtpConstants.FTP_MODE, PASSIVE_LOCAL);
				put(FtpConstants.USERNAME, FTP_USERNAME);
				put(FtpConstants.PASSWORD, FTP_PASSWORD);
				put(FtpConstants.PORT, ftpServer.port + "");

				put(CREDS_ENC, "1");
				put(CREDS_ENC_IV, "1");
				put(CREDENTIALS_DECRYPT_KEY, "1");
				put(LISTING_ENABLED, "false");

				put(SINK_TYPE, ConnectionType.FTP.name());

				put(MAPPING, "{ \"mode\": \"auto\", \"mapping\": { \"country.name\": [\"country_name\"] } }");

				put(DATA_FORMAT, "csv");
				put(PATH, "/upload");
				put(OUTPUT_DIR_NAME_PATTERN, "{yyyy}/{MM}/{dd}/{HH}/{mm}/{ss}/city={record.city}");
				put(MAX_FILE_SIZE_MB, "1");
				put(FILE_NAME_PREFIX, "my-dataset-{record.vehicleType=no_data}");

				put(OPEN_FILES_CACHE_SIZE, "1");
				put(GZIP, "true");

				put(MONITOR_POLL_MS, "1");
			}});

		Map<String, String> taskParams = connector.taskConfigs(1).get(0);
		task.start(taskParams, "sink");

		TopicPartition tp = new TopicPartition("ds-12", 1);
		task.open(ImmutableSet.of(tp));

		task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
			"{ \"rawMessage\": {\"id\": 1, \"city\": \"New York\", \"vehicleType\": \"car\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 1L)));
		task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
			"{ \"rawMessage\": {\"id\": 2, \"city\": \"Moscow\", \"vehicleType\": \"truck\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 2L)));

		task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
			"{ \"rawMessage\": {\"id\": 3, \"city\": \"New York\", \"vehicleType\": \"car\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 3L)));
		task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
			"{ \"rawMessage\": {\"id\": 4, \"city\": \"Moscow\", \"vehicleType\": \"truck\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 4L)));

		// no value for key=vehicleType => "no_data" should be used
		task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
			"{ \"rawMessage\": {\"id\": 5, \"city\": \"Moscow\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 5L)));

		Thread.sleep(2000);

		task.flush(null);

		FtpConnectorService ftpProbeService = (FtpConnectorService) task.probeService;
		try (NexlaFtpClient ftpClient = ftpProbeService.getFtpClientProvider().getNexlaFtpClient((FtpAuthConfig) task.config.getAuthConfig())) {

			List<NexlaFile> uploadedFiles = FtpConnectorService.getFiles(ftpClient, "/", BOTH, 10, config)
															   .filter(file -> file.getSize() > 0)
															   .collect(toList());

			Map<String, String> fileContent = getFilesContent(ftpClient, uploadedFiles);

			assertEquals(5, uploadedFiles.size());

			verifyContentLines(uploadedFiles, fileContent,
					"/city=Moscow/my-dataset-no_data-ds-12-000000000005.csv.gz",
					"id,city",
					"5,Moscow");

			verifyContentLines(uploadedFiles, fileContent,
					"/city=Moscow/my-dataset-truck-ds-12-000000000002.csv.gz",
					"id,city,vehicleType",
					"2,Moscow,truck",
					"4,Moscow,truck");

			verifyContentLines(uploadedFiles, fileContent,
					"/city=New York/my-dataset-car-ds-12-000000000001.csv.gz",
					"id,city,vehicleType",
					"1,\"New York\",car",
					"3,\"New York\",car");

			verifyContent(uploadedFiles, fileContent,
					"/city=Moscow/my-dataset-no_data-ds-12-00001-000000000001.index.json",
					"[{\"rawBytes\":73,\"firstOffset\":1,\"lastOffset\":5,\"numRecords\":5}]");

			verifyContent(uploadedFiles, fileContent,
					"/upload/meta/last_chunk_index.ds-12-00001.txt",
					"/city=Moscow/my-dataset-no_data-ds-12-00001-000000000001.index.json");

			task.stop();
			connector.stop();
		}
	}

	private Map<String, String> getFilesContent(NexlaFtpClient ftpClient, List<NexlaFile> uploadedFiles) {
		return uploadedFiles
			.stream()
			.map(nexlaFile -> {
				String content = logFileContent(nexlaFile, ftpClient);
				return Pair.with(nexlaFile.getFullPath(), content);
			})
			.collect(toMap(Pair::getValue0, Pair::getValue1));
	}

	private void verifyContent(List<NexlaFile> uploadedFiles, Map<String, String> uploadedContent, String suffix, String expectedContent) {
		NexlaFile found = uploadedFiles.stream().filter(file -> file.getFullPath().endsWith(suffix)).findFirst().get();
		assertTrue(
			"'" + uploadedContent.get(found.getFullPath()) + "' does not start with '" + expectedContent + "'",
			uploadedContent.get(found.getFullPath()).contains(expectedContent));
	}

	private void verifyContentByLine(List<NexlaFile> uploadedFiles, Map<String, String> uploadedContent, String suffix, String[] expectedContent) {
		NexlaFile found = uploadedFiles.stream().filter(file -> file.getFullPath().endsWith(suffix)).findFirst().get();
		String[] contentArr = uploadedContent.get(found.getFullPath()).split("\n");
		for (int i = 0; i < contentArr.length; i++) {
			String content = contentArr[i];
			assertTrue("'" + content + "' does not start with '" + expectedContent[i] + "'", content.contains(expectedContent[i]));
		}
	}

	private void verifyContentLines(List<NexlaFile> uploadedFiles, Map<String, String> uploadedContent, String suffix, String... expectedLines) {
		NexlaFile found = uploadedFiles.stream().filter(file -> file.getFullPath().endsWith(suffix)).findFirst().get();
		String[] lines = uploadedContent.get(found.getFullPath()).split("\n");
		for (int i = 0; i < lines.length; i++) {
			String line = lines[i];
			assertTrue("'" + line + "' does not start with '" + expectedLines[i] + "'", line.startsWith(expectedLines[i]));
		}
	}

	//@Test FIXME Deactivating failed tests in release/v3.2.0
	public void putFtp() {
		withConsumer((metricsTopic, metricsConsumer) -> {

			try {

				this.ftpServer = new TestFtpServer(rootDir, writePermissions());
				ftpServer.before();

				FileSinkConnector connector = new FileSinkConnector();

				connector.start(
					new HashMap<String, String>() {{

						put(UNIT_TEST, "true");

						put(SINK_ID, "1");

						put(FtpConstants.FTP_MODE, PASSIVE_LOCAL);
						put(FtpConstants.USERNAME, FTP_USERNAME);
						put(FtpConstants.PASSWORD, FTP_PASSWORD);
						put(FtpConstants.PORT, ftpServer.port + "");

						put(CREDS_ENC, "1");
						put(CREDS_ENC_IV, "1");
						put(CREDENTIALS_DECRYPT_KEY, "1");
						put(LISTING_ENABLED, "false");

						put(SINK_TYPE, ConnectionType.FTP.name());

						put(MAPPING, "{ \"mode\": \"auto\", \"tracker_mode\":\"record\",\"mapping\": { \"country.name\": [\"country_name\"] }, \"excludes\" : [\"city\"] }");
						put(DATA_FORMAT, "csv");
						put(PATH, "/upload");
						put(OUTPUT_DIR_NAME_PATTERN, "{yyyy}/{MM}/{dd}/{HH}/{mm}/{ss}");
						put(MAX_FILE_SIZE_MB, "0");
						put(FILE_NAME_PREFIX, "my-dataset");

						put(MONITOR_POLL_MS, "1");
						put(METRICS_TOPIC, metricsTopic);
					}});

				Map<String, String> taskParams = connector.taskConfigs(1).get(0);
				task.start(taskParams, "sink");

				TopicPartition tp = new TopicPartition("ds-12", 1);
				task.open(ImmutableSet.of(tp));

				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 1, \"city\": \"New York\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 1L)));

				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 2, \"city\": \"Moscow\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 2L)));

				Thread.sleep(2000);

				task.flush(null);

				List<NexlaRawMetric> nexlaRawMetrics = StreamEx.of(readMetrics(metricsConsumer))
					.sortedBy(x -> x.getTags().get(NAME))
					.toList();

				assertMetric(nexlaRawMetrics.get(0), "/my-dataset-ds-12-000000000001.csv", 1L, 664L);
				assertMetric(nexlaRawMetrics.get(1), "/my-dataset-ds-12-000000000002.csv", 1L, 664L);

				FtpConnectorService ftpProbeService = (FtpConnectorService) task.probeService;
				try (NexlaFtpClient ftpClient = ftpProbeService.getFtpClientProvider().getNexlaFtpClient((FtpAuthConfig) task.config.getAuthConfig())) {

					List<NexlaFile> uploadedFiles = FtpConnectorService.getFiles(ftpClient, "/", BOTH, 10, config)
																	   .filter(file -> file.getSize() > 0)
																	   .collect(toList());

					assertEquals(5, uploadedFiles.size());

					assertEquals(2, uploadedFiles.stream().filter(file -> file.getFullPath().endsWith(".csv")).count());
					assertEquals(2, uploadedFiles.stream().filter(file -> file.getFullPath().endsWith(".index.json")).count());
					assertEquals(1, uploadedFiles.stream().filter(file -> file.getFullPath().endsWith(".txt")).count());

					for (NexlaFile uploadedFile : uploadedFiles) {
						logFileContent(uploadedFile, ftpClient);
					}

					Thread.sleep(2000);

					task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
							"{ \"rawMessage\": {\"id\": 3, \"city\": \"Washington\", \"country\": { \"id\": 2, \"name\":\"USA\" } } , \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 3L)));

					task.flush(null);

					assertTrue(task.fileSinkWriter.getFilesCache().isEmpty());
					assertTrue(task.fileSinkWriter.getWriters().isEmpty());

					uploadedFiles = FtpConnectorService.getFiles(ftpClient, "/", BOTH, 9, config)
													   .filter(file -> file.getSize() > 0)
													   .collect(toList());

					for (NexlaFile uploadedFile : uploadedFiles) {
						logFileContent(uploadedFile, ftpClient);
					}

					NexlaFile outputFile = uploadedFiles.stream().filter(file -> file.getFullPath().endsWith("3.csv")).findAny().get();

					InputStream inputStream = ftpClient.retrieveFileStream(outputFile.getFullPath());
					String csvData = IOUtils.toString(inputStream, "UTF-8");
					inputStream.close();
					ftpClient.completePendingCommand();

					assertTrue(csvData.startsWith(
							"id,country.id,country.name,nexla_tracker_id\n" +
									"3,2,USA,\"cjUwMTA6MTIzOmZpbGUuY3N2O"));

					List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);
					assertMetric(metrics.get(0), "/my-dataset-ds-12-000000000003.csv", 1L, 696L);

					connector.stop();
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		});
	}

	//@Test FIXME Deactivating failed tests in release/v3.2.0
	public void testParquetWriter() {
		withConsumer((metricsTopic, metricsConsumer) -> {
			try {
				this.ftpServer = new TestFtpServer(rootDir, writePermissions());
				ftpServer.before();

				FileSinkConnector connector = new FileSinkConnector();

				connector.start(
					new HashMap<String, String>() {{

						put(UNIT_TEST, "true");

						put(SINK_ID, "1");

						put(FtpConstants.FTP_MODE, PASSIVE_LOCAL);
						put(FtpConstants.USERNAME, FTP_USERNAME);
						put(FtpConstants.PASSWORD, FTP_PASSWORD);
						put(FtpConstants.PORT, ftpServer.port + "");

						put(CREDS_ENC, "1");
						put(CREDS_ENC_IV, "1");
						put(CREDENTIALS_DECRYPT_KEY, "1");
						put(LISTING_ENABLED, "false");

						put(SINK_TYPE, ConnectionType.FTP.name());

						put(DATA_FORMAT, "parquet");
						put(PATH, "/upload");
						put(OUTPUT_DIR_NAME_PATTERN, "{yyyy}/{MM}/{dd}/{HH}/{mm}/{ss}");
						put(MAX_FILE_SIZE_MB, "0");
						put(FILE_NAME_PREFIX, "my-dataset");

						put(MONITOR_POLL_MS, "1");
						put(METRICS_TOPIC, metricsTopic);
					}});

				Map<String, String> taskParams = connector.taskConfigs(1).get(0);
				task.start(taskParams, ADMIN_API_PREFIX);

				TopicPartition tp = new TopicPartition("ds-12", 1);
				task.open(ImmutableSet.of(tp));

				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 1, \"city\": \"New York\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 1L)));

				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 2, \"city\": \"Moscow\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 2L)));

				Thread.sleep(2000);

				task.flush(null);

				List<NexlaRawMetric> nexlaRawMetrics = StreamEx.of(readMetrics(metricsConsumer))
					.sortedBy(x -> x.getTags().get(NAME))
					.toList();

				assertMetric(nexlaRawMetrics.get(0), "/my-dataset-ds-12-000000000001.parquet", 1L, 563L);
				assertMetric(nexlaRawMetrics.get(1), "/my-dataset-ds-12-000000000002.parquet", 1L, 561L);

				FtpConnectorService ftpProbeService = (FtpConnectorService) task.probeService;
				try (NexlaFtpClient ftpClient = ftpProbeService.getFtpClientProvider().getNexlaFtpClient((FtpAuthConfig) task.config.getAuthConfig())) {

					List<NexlaFile> uploadedFiles = FtpConnectorService.getFiles(ftpClient, "/", BOTH, 10, config)
																	   .filter(file -> file.getSize() > 0)
																	   .collect(toList());

					assertEquals(5, uploadedFiles.size());

					assertEquals(2, uploadedFiles.stream().filter(file -> file.getFullPath().endsWith(".parquet")).count());
					assertEquals(2, uploadedFiles.stream().filter(file -> file.getFullPath().endsWith(".index.json")).count());
					assertEquals(1, uploadedFiles.stream().filter(file -> file.getFullPath().endsWith(".txt")).count());

					Thread.sleep(2000);

					task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
							"{ \"rawMessage\": {\"id\": 3, \"city\": \"Washington\", \"country\": { \"id\": 2, \"name\":\"USA\" } } , \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 3L)));

					task.flush(null);

					assertTrue(task.fileSinkWriter.getFilesCache().isEmpty());
					assertTrue(task.fileSinkWriter.getWriters().isEmpty());

					List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);
					assertMetric(metrics.get(0), "/my-dataset-ds-12-000000000003.parquet", 1L, 597L);

					connector.stop();
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		});
	}

	//@Test FIXME Deactivating failed tests in release/v3.2.0
	public void testParquetWriterWithSameOffset() {
		withConsumer((metricsTopic, metricsConsumer) -> {
			try {
				this.ftpServer = new TestFtpServer(rootDir, writePermissions());
				ftpServer.before();

				FileSinkConnector connector = new FileSinkConnector();

				connector.start(
						new HashMap<String, String>() {{

							put(UNIT_TEST, "true");

							put(SINK_ID, "1");

							put(FtpConstants.FTP_MODE, PASSIVE_LOCAL);
							put(FtpConstants.USERNAME, FTP_USERNAME);
							put(FtpConstants.PASSWORD, FTP_PASSWORD);
							put(FtpConstants.PORT, ftpServer.port + "");

							put(CREDS_ENC, "1");
							put(CREDS_ENC_IV, "1");
							put(CREDENTIALS_DECRYPT_KEY, "1");
							put(LISTING_ENABLED, "false");

							put(SINK_TYPE, ConnectionType.FTP.name());

							put(DATA_FORMAT, "parquet");
							put(PATH, "/upload");
							put(OUTPUT_DIR_NAME_PATTERN, "{yyyy}/{MM}/{dd}");
							put(MAX_FILE_SIZE_MB, "0");
							put(FILE_NAME_PREFIX, "my-dataset");

							put(MONITOR_POLL_MS, "1");
							put(METRICS_TOPIC, metricsTopic);
						}});

				Map<String, String> taskParams = connector.taskConfigs(1).get(0);
				task.start(taskParams, ADMIN_API_PREFIX);

				TopicPartition tp = new TopicPartition("ds-12", 1);
				task.open(ImmutableSet.of(tp));

				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
						"{ \"rawMessage\": {\"id\": 1, \"city\": \"New York\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 1L)));

				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
						"{ \"rawMessage\": {\"id\": 2, \"city\": \"Moscow\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 1L)));

				Thread.sleep(2000);

				task.flush(null);

				List<NexlaRawMetric> nexlaRawMetrics = StreamEx.of(readMetrics(metricsConsumer))
						.sortedBy(x -> x.getTags().get(NAME))
						.toList();

				assertMetric(nexlaRawMetrics.get(0), "/my-dataset-ds-12-000000000001-1.parquet", 1L, 561L);
				assertMetric(nexlaRawMetrics.get(1), "/my-dataset-ds-12-000000000001.parquet", 1L, 563L);

				FtpConnectorService ftpProbeService = (FtpConnectorService) task.probeService;
				try (NexlaFtpClient ftpClient = ftpProbeService.getFtpClientProvider().getNexlaFtpClient((FtpAuthConfig) task.config.getAuthConfig())) {

					List<NexlaFile> uploadedFiles = FtpConnectorService.getFiles(ftpClient, "/", BOTH, 10, config)
																	   .filter(file -> file.getSize() > 0)
																	   .collect(toList());

					assertEquals(4, uploadedFiles.size());

					assertEquals(2, uploadedFiles.stream().filter(file -> file.getFullPath().endsWith(".parquet")).count());
					assertEquals(1, uploadedFiles.stream().filter(file -> file.getFullPath().endsWith(".index.json")).count());
					assertEquals(1, uploadedFiles.stream().filter(file -> file.getFullPath().endsWith(".txt")).count());

					Thread.sleep(2000);

					task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
							"{ \"rawMessage\": {\"id\": 3, \"city\": \"Washington\", \"country\": { \"id\": 2, \"name\":\"USA\" } } , \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 3L)));

					task.flush(null);

					assertTrue(task.fileSinkWriter.getFilesCache().isEmpty());
					assertTrue(task.fileSinkWriter.getWriters().isEmpty());

					List<NexlaRawMetric> metrics = readMetrics(metricsConsumer);
					assertMetric(metrics.get(0), "/my-dataset-ds-12-000000000003.parquet", 1L, 597L);

					connector.stop();
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		});
	}


	@SneakyThrows
	//@Test FIXME Deactivating failed tests in release/v3.2.0
	public void putFtpErrorCountTest() {

		withConsumer((metricsTopic, metricsConsumer) -> {

			try {

				this.ftpServer = new TestFtpServer(rootDir);
				ftpServer.before();

				FileSinkConnector connector = new FileSinkConnector();

				connector.start(
					new HashMap<String, String>() {{

						put(UNIT_TEST, "true");

						put(SINK_ID, "1");

						put(FtpConstants.FTP_MODE, PASSIVE_LOCAL);
						put(FtpConstants.USERNAME, FTP_USERNAME);
						put(FtpConstants.PASSWORD, FTP_PASSWORD);
						put(FtpConstants.PORT, ftpServer.port + "");

						put(CREDS_ENC, "1");
						put(CREDS_ENC_IV, "1");
						put(CREDENTIALS_DECRYPT_KEY, "1");
						put(LISTING_ENABLED, "false");

						put(SINK_TYPE, ConnectionType.FTP.name());

						put(MAPPING, "{ \"mode\": \"auto\", \"mapping\": { \"country.name\": [\"country_name\"] }, \"excludes\" : [\"city\"] }");
						put(DATA_FORMAT, "csv");
						put(PATH, "/upload");
						put(OUTPUT_DIR_NAME_PATTERN, "{yyyy}/{MM}/{dd}/{HH}/{mm}/{ss}");
						put(MAX_FILE_SIZE_MB, "0");
						put(FILE_NAME_PREFIX, "my-dataset");

						put(MONITOR_POLL_MS, "1");
						put(METRICS_TOPIC, metricsTopic);
					}});

				Map<String, String> taskParams = connector.taskConfigs(1).get(0);
				task.start(taskParams, "sink");

				TopicPartition tp = new TopicPartition("ds-12", 1);
				task.open(ImmutableSet.of(tp));

				task.put(asList(new SinkRecord(tp.topic(), 1, null, null, null,
					"{ \"rawMessage\": {\"id\": 1, \"city\": \"New York\"}, \"nexlaMetaData\": {\"runId\": 1, \"trackerId\": \"u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010\" } }", 1L)));

				Thread.sleep(2000);

				try {
					task.flush(null);
					fail("Should throw exception for write permission");
				} catch (Exception e) {
					List<NexlaRawMetric> nexlaRawMetrics = readMetrics(metricsConsumer);
					assertEquals(1, nexlaRawMetrics.size());
					assertEquals(1, nexlaRawMetrics.get(0).getFields().get(ERROR_COUNT));
				} finally {
					connector.stop();
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		});
	}

	//@Test FIXME Deactivating failed tests in release/v3.2.0
	@Ignore
	public void testDoFlushWhenShouldForceFlushAndFlushSuccessful() throws IOException {
		Pair<FileSinkWriter, File> pair = initForceFlushMocks(1, true);
		FileSinkWriter fileSinkWriterMock = pair.getValue0();
		File forceFlushFile = pair.getValue1();

		assertTrue(task.doFlush(ReadyToFlush.FLUSH));
		assertFalse(forceFlushFile.exists());

		verify(fileSinkWriterMock).getWriters();
		verify(fileSinkWriterMock).flush();
	}

	//@Test FIXME Deactivating failed tests in release/v3.2.0
	@Ignore
	public void testDoFlushWhenShouldForceFlushAndFlushFailed() throws IOException {
		Pair<FileSinkWriter, File> pair = initForceFlushMocks(1, false);
		FileSinkWriter fileSinkWriterMock = pair.getValue0();
		File forceFlushFile = pair.getValue1();

		assertFalse(task.doFlush(ReadyToFlush.FLUSH));
		assertTrue(forceFlushFile.exists());

		verify(fileSinkWriterMock).getWriters();
		verify(fileSinkWriterMock).flush();

		forceFlushFile.delete();
	}

	//@Test FIXME Deactivating failed tests in release/v3.2.0
	@Ignore
	public void testDoFlushWhenShouldNotForceFlush() throws IOException {
		Pair<FileSinkWriter, File> pair = initForceFlushMocks(2, true);
		FileSinkWriter fileSinkWriterMock = pair.getValue0();
		File forceFlushFile = pair.getValue1();

		assertFalse(task.doFlush(ReadyToFlush.FLUSH));
		assertTrue(forceFlushFile.exists());

		verify(fileSinkWriterMock, times(2)).getWriters();
		verify(fileSinkWriterMock, never()).flush();

		forceFlushFile.delete();
	}

	private Pair<FileSinkWriter, File> initForceFlushMocks(Integer forceFlushFileSinkId, boolean doFlushResult) throws IOException {
		var dataSource = new PDataSource();
		dataSource.setId(1);
		dataSource.setConnectionType(ConnectionType.API_PULL);
		var connector = new FileSinkConnector();
		connector.start(getSinkConnectorConfigProps());
		Map<String, String> taskParams = connector.taskConfigs(1).get(0);

		task.start(taskParams, "sink");

		var fileSinkWriterMock = mock(FileSinkWriter.class);
		task.fileSinkWriter = fileSinkWriterMock;
		task.config = new FileSinkConnectorConfig(getSinkConnectorConfigProps());

		ftpServer = mock(TestFtpServer.class);

		when(fileSinkWriterMock.getWriters()).thenReturn(new HashMap<>());
		when(fileSinkWriterMock.flush()).thenReturn(doFlushResult);

		var forceFlushFile = new File(FORCE_FLUSH_FOLDER_PATH.concat(String.valueOf(forceFlushFileSinkId)));
		forceFlushFile.getParentFile().mkdirs();
		forceFlushFile.createNewFile();

		return new Pair<>(fileSinkWriterMock, forceFlushFile);
	}

	private Map<String, String> getSinkConnectorConfigProps() {
		return new HashMap<>() {{
			put(UNIT_TEST, "true");
			put(SINK_ID, "1");
			put(FtpConstants.FTP_MODE, PASSIVE_LOCAL);
			put(FtpConstants.USERNAME, FTP_USERNAME);
			put(FtpConstants.PASSWORD, FTP_PASSWORD);
			put(FtpConstants.PORT, "");
			put(CREDS_ENC, "1");
			put(CREDS_ENC_IV, "1");
			put(CREDENTIALS_DECRYPT_KEY, "1");
			put(LISTING_ENABLED, "false");
			put(SINK_TYPE, ConnectionType.FTP.name());
			put(MAPPING, "{ \"mode\": \"auto\", \"mapping\": { \"country.name\": \"country_name\", \"key3\": \"vvv3\", \"key1\": \"vvv1\" } }");
			put(DATA_FORMAT, "csv");
			put(PATH, "/upload");
			put(OUTPUT_DIR_NAME_PATTERN, "{yyyy}/{MM}/{dd}/{HH}/{mm}/{ss}/city={record.city}");
			put(MAX_FILE_SIZE_MB, "0");
			put(FILE_NAME_PREFIX, "my-dataset");
			put(MONITOR_POLL_MS, "1");
			put(TRACKER_ENCRYPTION_ENABLED, "true");
			put(TRACKER_ENCRYPTION_KEY, "9Rz+VE1VzxS7wvUpg8hhsbrt8vrPLUKBtDNd8N/wKbg=");
			put(METRICS_TOPIC, "");
		}};
	}

	private void assertMetric(NexlaRawMetric metric, String fileSuffix, Long numRecords, Long rawBytes) {
		assertTrue(metric.getTags().get(NAME).endsWith(fileSuffix));
		assertEquals(rawBytes, toLong(metric.getFields().get(SIZE)));
		assertEquals(numRecords, toLong(metric.getFields().get(RECORDS)));
	}

	private String logFileContent(NexlaFile nexlaFile, NexlaFtpClient ftpClient) {

		try {
			System.out.println(nexlaFile.getFullPath());

			InputStream inputStream = ftpClient.retrieveFileStream(nexlaFile.getFullPath());
			if (nexlaFile.getFullPath().endsWith(".gz")) {
				inputStream = new GZIPInputStream(inputStream);
			}
			String content = IOUtils.toString(inputStream, "UTF-8");
			System.out.println(content);
			inputStream.close();
			ftpClient.completePendingCommand();
			System.out.println();

			return content;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	private static List<Authority> writePermissions() {
		return asList(new WritePermission("/"));
	}

	private String getPipelineStr() {
		return  "\n" +
				"    {\n" +
				"        \"code_containers\": [],\n" +
				"        \"data_sets\": [\n" +
				"            {\n" +
				"                \"id\": 12438,\n" +
				"                \"status\": \"ACTIVE\",\n" +
				"                \"data_source_id\": 8406,\n" +
				"                \"parent_data_set_id\": null,\n" +
				"                \"transform\": {\n" +
				"                    \"version\": 1,\n" +
				"                    \"data_maps\": [],\n" +
				"                    \"transforms\": [],\n" +
				"                    \"custom_config\": {}\n" +
				"                }\n" +
				"            }\n" +
				"        ],\n" +
				"        \"data_source\": {\n" +
				"            \"id\": 8406,\n" +
				"            \"status\": \"ACTIVE\",\n" +
				"            \"source_config\": {\n" +
				"                \"advanced_settings\": \"Auto Detect\",\n" +
				"                \"path_exclusions\": false,\n" +
				"                \"schema.detection.once\": false,\n" +
				"                \"allowGrouping\": false,\n" +
				"                \"ignore.files.older.than.ms\": null,\n" +
				"                \"start.cron\": \"0 10,25,40,55 * * * ? *\",\n" +
				"                \"path\": \"/Sample data/2021/07/07\",\n" +
				"                \"ui.display_path\": \"/Sample data/2021/07/07\",\n" +
				"                \"pipeline.type\": \"replication\"\n" +
				"            },\n" +
				"            \"connection_type\": \"ftp\",\n" +
				"            \"source_type\": \"ftp\",\n" +
				"            \"data_credentials\": {\n" +
				"                \"id\": 6077,\n" +
				"                \"credentials_enc\": \"feteq3evScW6grM7Z2IhCcL4ZISqFV9P5DfzK+GP7GUH6Vlm84bf37NjAMpTQ3SEZt1WTy3Iiw2oAd2ADMfekUPABv/uQUIPNzR56O6JDGjbDiNOxryvSpSvHw9OzT2/T6svDjNDSvSryZX+jfeDQ4JVQRoVoEpsfczlaTVZIgDPys5GecsbErPL1PiPJv+f4Q8sOvZTMhQ289kZgmcpzrG+SbY7OLQYEoIzyo9LribtKTjD09O6YtfGzI/vbGiDqpHQb8llaQ1Nr1hWzg==\",\n" +
				"                \"credentials_enc_iv\": \"CqP6KMjsgGOUA96s\"\n" +
				"            }\n" +
				"        },\n" +
				"        \"data_sink\": {\n" +
				"            \"id\": 7380,\n" +
				"            \"status\": \"ACTIVE\",\n" +
				"            \"data_credentials_id\": 6807,\n" +
				"            \"sink_config\": {\n" +
				"                \"mapping\": {\n" +
				"                    \"mapping\": {\n" +
				"                        \"first_name\": {\n" +
				"                            \"FIRST_NAME\": \"TEXT\"\n" +
				"                        },\n" +
				"                        \"gender\": {\n" +
				"                            \"GENDER\": \"TEXT\"\n" +
				"                        },\n" +
				"                        \"id\": {\n" +
				"                            \"ID\": \"INT\"\n" +
				"                        },\n" +
				"                        \"last_name\": {\n" +
				"                            \"LAST_NAME\": \"TEXT\"\n" +
				"                        }\n" +
				"                    },\n" +
				"                    \"mode\": \"manual\",\n" +
				"                    \"tracker_mode\": \"NONE\"\n" +
				"                },\n" +
				"                \"insert.mode\": \"INSERT\",\n" +
				"                \"upsert.nulls\": true,\n" +
				"                \"sink_type\": \"snowflake\",\n" +
				"                \"table\": \"NEX_6211\",\n" +
				"                \"database\": \"TEST\",\n" +
				"                \"primary.key\": \"ID\",\n" +
				"                \"direct.upload\": true,\n" +
				"                \"log.verbose\": true\n" +
				"            },\n" +
				"            \"connection_type\": \"snowflake\",\n" +
				"            \"sink_type\": \"snowflake\",\n" +
				"            \"data_credentials\": {\n" +
				"                \"id\": 6807,\n" +
				"                \"credentials_enc\": \"q+BHPAj6oZ6jdACZhoSyKIBXoWo6kH7SJ8umjWnV6I99Xgcct7Ftx1Ko2II2/NRlMKfIqRs39Rdi7/XHeq/kXwCVwPGhVsUyjItx9cNY4U0zCXx0gN+HrBNYZD8LWt9F0RA42HJuwYuXQBg6Te1cyIesB1+CVhG46kCkuETr7M9J2IrwruvpjFAcGrkzfLLo9oNTGyIvHSCcs6EQ8PeY9XBD8eElIwPprUg4lpryh5JbuWaAxNs2/wHVrt5lrjZ2bLeC0GadNT0gdO15CWcoo1PzLzlksqEfQI8gQgszf7Yf+yK4dNykgRpf5f4o2hRNPxIQ5dcGJEgiysyPPnJ68WKwP23b7tQOevk/oA==\",\n" +
				"                \"credentials_enc_iv\": \"Tb3vyrWpSFnsMxVG\"\n" +
				"            }\n" +
				"        }\n" +
				"    }";
	}
}
