package com.nexla.rest.iterations;

import com.nexla.admin.client.DataMap;
import com.nexla.common.NexlaConstants;
import com.nexla.common.io.RedisConnect;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.redis.Lookup;
import com.nexla.redis.LookupUtils;
import com.nexla.redis.RedisCreds;
import com.nexla.redis.dto.MasterEntry;
import com.nexla.rest.pojo.ResultEntry;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.sync.RedisCommands;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.google.common.collect.Sets.newHashSet;
import static com.nexla.common.NexlaConstants.*;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.redis.RedisAuthConfig.ID_FIELD_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Testcontainers
@Tag("com.nexla.test.IntegrationTests")
@Slf4j
class DataMapQueueTest {

	private static final int REDIS_PORT = 6379;
	private static final int REDIS_INDEX = 0;
	private static RedisCommands<String, String> JEDIS;
	private static RedisCreds REDIS_CREDS;

	@SuppressWarnings("rawtypes")
	public static final GenericContainer REDIS = new GenericContainer(DockerImageName.parse("redis:7.0.12"))
			.withExposedPorts(REDIS_PORT);

	@BeforeAll
	public static void startUp() {
		REDIS.withReuse(true);
		REDIS.start();
		REDIS_CREDS = new RedisCreds(newHashSet(redisHostPort()), false, Optional.empty(), Optional.empty());
		JEDIS = getRedis();
	}

	@AfterAll
	public static void tearDown() {
		REDIS.stop();
	}

	private static RedisConnect redisHostPort() {
		return new RedisConnect(REDIS.getHost(), REDIS.getMappedPort(REDIS_PORT), REDIS_INDEX);
	}

	public static RedisCommands<String, String> getRedis() {
		RedisClient jedis = RedisClient.create(RedisURI.create(REDIS.getHost(), REDIS.getMappedPort(REDIS_PORT)));
		RedisCommands<String, String> commands = jedis.connect().sync();
		commands.select(REDIS_INDEX);
		return commands;
	}

	private final Map<String, String> baseProperties = new HashMap<>() {{

//		put(NexlaConstants.BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);

		put(UNIT_TEST, "true");

		put(REDIS_HOSTS, redisHostPort().toUrl());
		put(REDIS_CLUSTER_ENABLED, "false");
		put(REDIS_TLS_ENABLED, "false");

		put(CREDS_ENC, "1");
		put(CREDS_ENC_IV, "1");
		put(CREDENTIALS_DECRYPT_KEY, "1");

		put(ID_FIELD_NAME, "id");
		put(SINK_ID, "1");
		put(LISTING_APP_SERVER_URL, "123");
		put(LISTING_ENABLED, "false");
	}};

	private static DataMap dataMap(int id, MasterEntry.DataModelVersion dataModelVersion, List<Map<String, String>> data) {
		DataMap dm = new DataMap();
		dm.setId(id);
		dm.setMapPrimaryKey("id");
		dm.setUseVersioning(false);
		dm.setDataModelVersion(dataModelVersion.toString());
		dm.setDataMap(data);
		return dm;
	}

	public static Stream<Arguments> testCases() {
		return Stream.of(
				Arguments.of(MasterEntry.DataModelVersion.V1),
				Arguments.of(MasterEntry.DataModelVersion.V2)
		);
	}

	@ParameterizedTest
	@MethodSource("testCases")
	@SneakyThrows
	void test(MasterEntry.DataModelVersion dataModelVersion) {
		int mapId = 1021;
		Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, mapId);
		try {
			DataMap dm = dataMap(mapId, dataModelVersion, List.of(
					Map.of("id", "1", "val", "A"),
					Map.of("id", "2", "val", "B")
			));
			lookup.save(dm);

			var props = new HashMap<>(baseProperties) {{
				put(NexlaConstants.MAP_ID, String.valueOf(mapId));
			}};
			var config = new RestIterationConfig(props, "1", true, 1, 0, null);
			DataMapQueue dataMapQueue = new DataMapQueue(config, null, new NexlaLogger(log));
			var dataMapOffset = dataMapQueue.createStartOffset(0, DateTime.now(DateTimeZone.UTC));
			assertEquals(2L, JEDIS.llen(dataMapOffset.getQueueName()));

			Set<Map<String, String>> expectedData = new HashSet<>(dm.getDataMap());

			var firstReadResult = dataMapQueue.readRecords(Map.of(), dataMapOffset);

			// verify we read one of the entries (order is random)
			var firstReadData = firstReadResult.getEntries().stream()
					.map(ResultEntry::getDataMap)
					.collect(Collectors.toList());
			assertEquals(1, firstReadData.size());
			assertTrue(expectedData.contains(firstReadData.get(0)));
			expectedData.remove(firstReadData.get(0));
			assertTrue(firstReadResult.getNextOffset().isPresent());
			assertEquals(1L, JEDIS.llen(dataMapOffset.getQueueName()));

			// verify we read another one of the entries (last one)
			var secondReadResult = dataMapQueue.readRecords(Map.of(), firstReadResult.getNextOffset().get());
			var secondReadData = secondReadResult.getEntries().stream()
					.map(ResultEntry::getDataMap)
					.collect(Collectors.toList());
			assertEquals(1, secondReadData.size());
			assertTrue(expectedData.contains(secondReadData.get(0)));
			assertTrue(secondReadResult.getNextOffset().isEmpty());
			assertEquals(0L, JEDIS.llen(dataMapOffset.getQueueName()));
		} finally {
			lookup.deleteAsync().get();
		}
	}
}