package com.nexla.rest.iterations;

import com.nexla.admin.client.*;
import com.nexla.common.NexlaConstants;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.connector.config.rest.IterationType;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.rest.iterations.offsets.CodeContainerOffset;
import com.nexla.rest.pojo.*;
import com.nexla.test.UnitTests;
import com.nexla.transform.exceptions.ScriptExecutionException;
import com.nexla.transform.schema.FormatDetector;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.EntryStream;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.mockito.Mockito;
import org.springframework.http.HttpHeaders;

import java.util.*;

import static com.nexla.common.NexlaConstants.*;
import static com.nexla.connector.ConnectorService.UNIT_TEST;

@Category(UnitTests.class)
@Slf4j
public class CodeContainerIterationTest {
    @BeforeClass
    public static void beforeClass() {
        FormatDetector.initDefault();
    }

    private final Map<String, String> baseProperties = new HashMap<>() {{
//        put(NexlaConstants.BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
        put(UNIT_TEST, "true");
        put(SOURCE_ID, "1");
    }};

    private CodeContainerIteration createCodeContainerIteration(String transformCode) {
        var props = new HashMap<>(baseProperties) {{
            put(RestIterationConfig.ITERATION_TYPE, IterationType.CODE_CONTAINER_ITERATION.property);
            put(RestIterationConfig.CODE_CONTAINER_ID, "1");
        }};
        var config = new RestIterationConfig(props, "1", true, 1, 0, null);

        var mockApiClient = Mockito.mock(AdminApiClient.class);
        String encodedTransformCode = new String(Base64.getEncoder().encode(transformCode.getBytes()));

        var transform = new Transform(
                1,
                "test transform",
                true,
                true,
                null,
                null,
                "test",
                "jolt_custom",
                new ArrayList<>(){{
                    add(new TransformCode(
                        "nexla.custom",
                        new TransformCodeSpec(null, "python", "base64", encodedTransformCode)
                    ));
                }},
                "record"
        );
        Mockito.when(mockApiClient.getTransform(1)).thenReturn(transform);

        NexlaLogger logger = new NexlaLogger(log);
        return new CodeContainerIteration(config, null, logger, Optional.of(mockApiClient));
    }

    @Test
    public void flatTransform_test() {
        var transformCode = "def transform(input, metadata, args):\n" +
                            "    input['baz'] = input['foo'] + input['bar']\n" +
                            "    return input";
        EntryStream<Integer, NexlaMessage> inputStream = EntryStream.of(new HashMap<>() {{
            put(0, new NexlaMessage(new LinkedHashMap<>() {{
                put("foo", 1);
                put("bar", 2);
            }}));
        }});

        var expected = new ParsedData<>(
                List.of(
                        new ResultEntry(
                                new LinkedHashMap<>() {{
                                    put("foo", 1);
                                    put("bar", 2);
                                    put("baz", 3);
                                }},
                                0,
                                null,
                                null,
                                HttpHeaders.EMPTY.toSingleValueMap(),
                                null
                        )
                ),
                null,
                Optional.empty()
        );

        var codeContainerIteration = createCodeContainerIteration(transformCode);
        var actual = codeContainerIteration.createCallResult(
                Optional.empty(),
                new HttpHeaders(),
                inputStream,
                null,
                ""
        );

        Assert.assertEquals(expected, actual);
    }

    @Test
    public void flatTransformNoUrl_test() {
        var transformCode = "def transform(input, metadata, args):\n" +
                            "    input['baz'] = input['foo'] + input['bar']\n" +
                            "    return input";
        RestIterationResult previousResult = new RestIterationResult(List.of(
                new ResultEntry(
                        new LinkedHashMap<String,Object>() {{
                            put("foo", 1);
                            put("bar", 2);
                        }},
                        1,
                        null,
                        null,
                        null,
                        null
                )
        ), null, Optional.empty(), "");

        var expectedEntries = List.of(
                new ResultEntry(
                        new LinkedHashMap<>() {{
                            put("foo", 1);
                            put("bar", 2);
                            put("baz", 3);
                        }},
                        0,
                        null,
                        null,
                        Collections.emptyMap(),
                        null
                )
        );
        var expected = new RestIterationResult<CodeContainerOffset>(expectedEntries, null, Optional.empty(), "");

        CodeContainerIteration codeContainerIteration = createCodeContainerIteration(transformCode);
        var actual = codeContainerIteration.readRecords(Collections.emptyMap(), Optional.of(previousResult), null);

        Assert.assertEquals(expected.getEntries(), actual.getEntries());
    }

    @Test
    public void explodeTransform_test() {
        var transformCode = "import java.util\n" +
                            "def transform(input, metadata, args):\n" +
                            "    return [java.util.LinkedHashMap({'foo': input['foo'], 'baz': b}) for b in input['bar']]";
        EntryStream<Integer, NexlaMessage> inputStream = EntryStream.of(new HashMap<>() {{
            put(0, new NexlaMessage(new LinkedHashMap<>() {{
                put("foo", 1);
                put("bar", List.of("first", "second"));
            }}));
        }});

        var expected = new ParsedData<>(
                List.of(
                        new ResultEntry(
                                new LinkedHashMap<>() {{
                                    put("foo", 1);
                                    put("baz", "first");
                                }},
                                0,
                                null,
                                null,
                                HttpHeaders.EMPTY.toSingleValueMap(),
                                null
                        ),
                        new ResultEntry(
                                new LinkedHashMap<>() {{
                                    put("foo", 1);
                                    put("baz", "second");
                                }},
                                1,
                                null,
                                null,
                                HttpHeaders.EMPTY.toSingleValueMap(),
                                null
                        )
                ),
                null,
                Optional.empty()
        );

        var codeContainerIteration = createCodeContainerIteration(transformCode);
        var actual = codeContainerIteration.createCallResult(
                Optional.empty(),
                new HttpHeaders(),
                inputStream,
                null,
                ""
        );

        Assert.assertEquals(expected, actual);
    }

    @Test
    public void explodeTransformNoUrl_test() {
        var transformCode = "import java.util\n" +
                            "def transform(input, metadata, args):\n" +
                            "    return [java.util.LinkedHashMap({'foo': input['foo'], 'baz': b}) for b in input['bar']]";
        RestIterationResult previousResult = new RestIterationResult(List.of(
                new ResultEntry(
                        new LinkedHashMap<String,Object>() {{
                            put("foo", 1);
                            put("bar", List.of("first", "second"));
                        }},
                        1,
                        null,
                        null,
                        null,
                        null
                )
        ), null, Optional.empty(), "");

        var expectedEntries = List.of(
                new ResultEntry(
                        new LinkedHashMap<>() {{
                            put("foo", 1);
                            put("baz", "first");
                        }},
                        0,
                        null,
                        null,
                        Collections.emptyMap(),
                        null
                ),
                new ResultEntry(
                        new LinkedHashMap<>() {{
                            put("foo", 1);
                            put("baz", "second");
                        }},
                        1,
                        null,
                        null,
                        Collections.emptyMap(),
                        null
                )
        );
        var expected = new RestIterationResult<CodeContainerOffset>(expectedEntries, null, Optional.empty(), "");

        CodeContainerIteration codeContainerIteration = createCodeContainerIteration(transformCode);
        var actual = codeContainerIteration.readRecords(Collections.emptyMap(), Optional.of(previousResult), null);

        Assert.assertEquals(expected.getEntries(), actual.getEntries());
    }

    @Test
    public void multipleTransform_test() {
        var transformCode = "def transform(input, metadata, args):\n" +
                            "    input['baz'] = input['foo'] + input['bar']\n" +
                            "    return input";
        EntryStream<Integer, NexlaMessage> inputStream = EntryStream.of(new HashMap<>() {{
            put(0, new NexlaMessage(new LinkedHashMap<>() {{
                put("foo", 1);
                put("bar", 2);
            }}));
            put(1, new NexlaMessage(new LinkedHashMap<>() {{
                put("foo", 3);
                put("bar", 4);
            }}));
        }});

        var expected = new ParsedData<>(
                List.of(
                        new ResultEntry(
                                new LinkedHashMap<>() {{
                                    put("foo", 1);
                                    put("bar", 2);
                                    put("baz", 3);
                                }},
                                0,
                                null,
                                null,
                                HttpHeaders.EMPTY.toSingleValueMap(),
                                null
                        ),
                        new ResultEntry(
                                new LinkedHashMap<>() {{
                                    put("foo", 3);
                                    put("bar", 4);
                                    put("baz", 7);
                                }},
                                1,
                                null,
                                null,
                                HttpHeaders.EMPTY.toSingleValueMap(),
                                null
                        )
                ),
                null,
                Optional.empty()
        );

        var codeContainerIteration = createCodeContainerIteration(transformCode);
        var actual = codeContainerIteration.createCallResult(
                Optional.empty(),
                new HttpHeaders(),
                inputStream,
                null,
                ""
        );

        Assert.assertEquals(expected, actual);
    }

    @Test
    public void multipleTransformNoUrl_test() {
        var transformCode = "def transform(input, metadata, args):\n" +
                            "    input['baz'] = input['foo'] + input['bar']\n" +
                            "    return input";
        RestIterationResult previousResult = new RestIterationResult(List.of(
                new ResultEntry(
                        new LinkedHashMap<String,Object>() {{
                            put("foo", 1);
                            put("bar", 2);
                        }},
                        0,
                        null,
                        null,
                        null,
                        null
                ),
                new ResultEntry(
                        new LinkedHashMap<String,Object>() {{
                            put("foo", 3);
                            put("bar", 4);
                        }},
                        0,
                        null,
                        null,
                        null,
                        null
                )
        ), null, Optional.empty(), "");

        var expectedEntries = List.of(
                new ResultEntry(
                        new LinkedHashMap<>() {{
                            put("foo", 1);
                            put("bar", 2);
                            put("baz", 3);
                        }},
                        0,
                        null,
                        null,
                        Collections.emptyMap(),
                        null
                ),
                new ResultEntry(
                        new LinkedHashMap<>() {{
                            put("foo", 3);
                            put("bar", 4);
                            put("baz", 7);
                        }},
                        1,
                        null,
                        null,
                        Collections.emptyMap(),
                        null
                )
        );
        var expected = new RestIterationResult<CodeContainerOffset>(expectedEntries, null, Optional.empty(), "");

        CodeContainerIteration codeContainerIteration = createCodeContainerIteration(transformCode);
        var actual = codeContainerIteration.readRecords(Collections.emptyMap(), Optional.of(previousResult), null);

        Assert.assertEquals(expected.getEntries(), actual.getEntries());
    }

    @Test
    public void badCode_test() {
        var transformCode = "def transform(input, metadata, args):\n" +
                            "    return this_variable_does_not_exist_and_should_cause_an_error";
        EntryStream<Integer, NexlaMessage> inputStream = EntryStream.of(new HashMap<>() {{
            put(0, new NexlaMessage(new LinkedHashMap<>() {{
                put("foo", 1);
                put("bar", 2);
            }}));
        }});

        var codeContainerIteration = createCodeContainerIteration(transformCode);

        Assert.assertThrows(ScriptExecutionException.class, () -> codeContainerIteration.createCallResult(
                Optional.empty(),
                new HttpHeaders(),
                inputStream,
                null,
                ""
        ));
    }

    @Test
    public void badCodeNoUrl_test() {
        var transformCode = "def transform(input, metadata, args):\n" +
                            "    return this_variable_does_not_exist_and_should_cause_an_error";
        RestIterationResult previousResult = new RestIterationResult(List.of(
                new ResultEntry(
                        new LinkedHashMap<String,Object>() {{
                            put("foo", 1);
                            put("bar", 2);
                        }},
                        1,
                        null,
                        null,
                        null,
                        null
                )
        ), null, Optional.empty(), "");

        CodeContainerIteration codeContainerIteration = createCodeContainerIteration(transformCode);

        Assert.assertThrows(ScriptExecutionException.class, () -> codeContainerIteration.readRecords(Collections.emptyMap(), Optional.of(previousResult), null));
    }

    @Test
    public void badData_test() {
        var transformCode = "def transform(input, metadata, args):\n" +
                            "    input['baz'] = input['foo'] + input['bar']\n" +
                            "    return input";

        EntryStream<Integer, NexlaMessage> inputStream = EntryStream.of(new HashMap<>() {{
            put(0, new NexlaMessage(new LinkedHashMap<>() {{
                put("foo", 1);
                put("bar", "apple");
            }}));
        }});

        var codeContainerIteration = createCodeContainerIteration(transformCode);

        Assert.assertThrows(ScriptExecutionException.class, () -> codeContainerIteration.createCallResult(
                Optional.empty(),
                new HttpHeaders(),
                inputStream,
                null,
                ""
        ));
    }

    @Test
    public void badDataNoUrl_test() {
        var transformCode = "def transform(input, metadata, args):\n" +
                            "    input['baz'] = input['foo'] + input['bar']\n" +
                            "    return input";
        RestIterationResult previousResult = new RestIterationResult(List.of(
                new ResultEntry(
                        new LinkedHashMap<String,Object>() {{
                            put("foo", 1);
                            put("bar", "apple");
                        }},
                        1,
                        null,
                        null,
                        null,
                        null
                )
        ), null, Optional.empty(), "");

        CodeContainerIteration codeContainerIteration = createCodeContainerIteration(transformCode);

        Assert.assertThrows(ScriptExecutionException.class, () -> codeContainerIteration.readRecords(Collections.emptyMap(), Optional.of(previousResult), null));
    }

    @Test
    public void partialFailure_test() {
        var transformCode = "def transform(input, metadata, args):\n" +
                            "    input['baz'] = input['foo'] + input['bar']\n" +
                            "    return input";

        EntryStream<Integer, NexlaMessage> inputStream = EntryStream.of(new HashMap<>() {{
            put(0, new NexlaMessage(new LinkedHashMap<>() {{
                put("foo", 1);
                put("bar", 1);
            }}));
            put(1, new NexlaMessage(new LinkedHashMap<>() {{
                put("foo", 2);
                put("bar", "apple");
            }}));
            put(2, new NexlaMessage(new LinkedHashMap<>() {{
                put("foo", 3);
                put("bar", 3);
            }}));
        }});

        var codeContainerIteration = createCodeContainerIteration(transformCode);

        Assert.assertThrows(ScriptExecutionException.class, () -> codeContainerIteration.createCallResult(
                Optional.empty(),
                new HttpHeaders(),
                inputStream,
                null,
                ""
        ));
    }

    @Test
    public void partialFailureNoUrl_test() {
        var transformCode = "def transform(input, metadata, args):\n" +
                            "    input['baz'] = input['foo'] + input['bar']\n" +
                            "    return input";
        RestIterationResult previousResult = new RestIterationResult(List.of(
                new ResultEntry(
                        new LinkedHashMap<String,Object>() {{
                            put("foo", 1);
                            put("bar", 1);
                        }},
                        1,
                        null,
                        null,
                        null,
                        null
                ),
                new ResultEntry(
                        new LinkedHashMap<String,Object>() {{
                            put("foo", 2);
                            put("bar", "apple");
                        }},
                        1,
                        null,
                        null,
                        null,
                        null
                ),
                new ResultEntry(
                        new LinkedHashMap<String,Object>() {{
                            put("foo", 3);
                            put("bar", 3);
                        }},
                        1,
                        null,
                        null,
                        null,
                        null
                )
        ), null, Optional.empty(), "");

        CodeContainerIteration codeContainerIteration = createCodeContainerIteration(transformCode);

        Assert.assertThrows(ScriptExecutionException.class, () -> codeContainerIteration.readRecords(Collections.emptyMap(), Optional.of(previousResult), null));
    }
}
