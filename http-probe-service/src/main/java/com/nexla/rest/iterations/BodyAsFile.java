package com.nexla.rest.iterations;

import com.bazaarvoice.jolt.JsonUtils;
import com.google.common.collect.Maps;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.DataCredentials;
import com.nexla.admin.client.DataSource;
import com.nexla.common.*;
import com.nexla.common.exception.ParseError;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.common.parse.NexlaParser;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connect.common.connector.schema.NoopSchemaDetectionUtils;
import com.nexla.connect.common.connector.schema.SchemaDetectionUtils;
import com.nexla.connector.config.ConnectorAdminConfig;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.connector.config.rest.RestSourceConnectorConfig;
import com.nexla.connector.file.source.*;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.LocalConnectorService;
import com.nexla.parser.ParserUtils;
import com.nexla.parser.ParserUtilsExt;
import com.nexla.probe.http.RequestSender;
import com.nexla.probe.http.RestConnectorService;
import com.nexla.rest.RestIteration;
import com.nexla.rest.iterations.offsets.BodyAsFileOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.RestIterationResult;
import com.nexla.rest.pojo.RestSourceError;
import com.nexla.rest.pojo.ResultEntry;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.MimeType;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;

import static com.nexla.common.StreamUtils.zipWithIndices;
import static com.nexla.connector.config.file.FileSourceConnectorConfig.OVERRIDDEN_EXTENSIONS;
import static com.nexla.parser.ParserUtilsExt.detectParser;
import static java.util.Collections.emptyMap;
import static java.util.Optional.*;

public class BodyAsFile extends RestIteration<BodyAsFileOffset> {

	public static final int DEFAULT_BATCH_SIZE = 1000;

	private BodyContentCache cache;
	private final RestSourceConnectorConfig restSourceConfig;
	private final Optional<AdminApiClient> adminApiClient;
	private final boolean enableCustomParser;
	private final Optional<RestConnectorService> probeService;
	private final Optional<ControlMessageProducer> controlMessageProducer;
	private final Optional<Integer> dataSourceId;
	private final Optional<RestSourceError> restSourceError;

	public BodyAsFile(RestSourceConnectorConfig restSourceConfig, RestIterationConfig config,
					  NexlaPool<RequestSender> senderPool,
					  NexlaLogger logger,
					  Optional<AdminApiClient> adminApiClient,
					  boolean enableCustomParser,
					  Optional<RestConnectorService> probeService,
					  Optional<ControlMessageProducer> controlMessageProducer,
					  Optional<Integer> dataSourceId,
					  Optional<RestSourceError> restSourceError) {
		super(config, senderPool, logger);
		this.restSourceConfig = restSourceConfig;
		this.adminApiClient = adminApiClient;
		this.enableCustomParser = enableCustomParser;
		this.probeService = probeService;
		this.controlMessageProducer = controlMessageProducer;
		this.dataSourceId = dataSourceId;
		this.restSourceError = restSourceError;
	}

	@Override
	public Class<BodyAsFileOffset> getOffsetClass() {
		return BodyAsFileOffset.class;
	}

	@Override
	@SneakyThrows
	public Map<String, String> getParameters(BodyAsFileOffset prevCtx) {
		Map<String, String> params = Maps.newHashMap();
		prevCtx.getResponseToken().ifPresent(token -> params.put(config.paramId, token));
		prevCtx.getPage().ifPresent(p -> params.put(config.pageParamName, String.valueOf(p)));
		return params;
	}

	@SneakyThrows
	public RestIterationResult readRecords(Map<String, String> substituteMap, BodyAsFileOffset offset) {
		if (offset.getHasCachedData()) {
			var parsedData = readFromCachedFile(offset);
			return new RestIterationResult(parsedData.getEntries(), parsedData.getOffset(), parsedData.getNextOffset(), cache.url);
		} else {
			return super.readRecords(substituteMap, offset);
		}
	}

	private Map<String, String> inlineCredentialsReferences(DataSource dataSource, ConnectorAdminConfig adminConfig) {
		// TODO REVISIT: check ReferencedResourceIds
		// XXX
		Object runtimeDataCredentialsId = dataSource.getSourceConfig()
				.get(NexlaConstants.RUNTIME_DATA_CREDENTIALS_ID);

		if (runtimeDataCredentialsId == null) {
			return Map.of();
		}

		Optional<DataCredentials> optCredentials = adminApiClient.get().getDataCredentials(Integer.parseInt(runtimeDataCredentialsId.toString()));

		if (optCredentials.isEmpty()) {
			this.logger.warn("Referenced credentials not found: id: {}", runtimeDataCredentialsId);
			return Map.of();
		}

		DataCredentials cs = optCredentials.get();

		return EntryStream.of(NexlaDataCredentials.getCreds(adminConfig.decryptKey, cs.getCredentialsEnc(), cs.getCredentialsEncIv()))
				.filterKeys(x -> !x.equals(NexlaConstants.CREDENTIALS_TYPE))
				.toMap();
	}

	@SneakyThrows
	public StreamEx<NexlaMessage> readData(byte[] bytes, Optional<MediaType> contentType, String responseDataAdditional) {
		if (config.advancedFileMode) {
			return readDataAdvanced(bytes, contentType);
		} else {
			return readDataSimple(bytes, contentType);
		}
	}

	@SneakyThrows
	public StreamEx<NexlaMessage> readDataSimple(byte[] bytes, Optional<MediaType> contentType) {
		Optional<String> mimeType = contentType.map(MimeType::toString)
			.filter(x -> config.parseAsFileExtension.isEmpty());
		Optional<NexlaParser> parserOpt = getParser(mimeType, bytes);

		if (parserOpt.isEmpty()) {
			return StreamEx.empty();
		} else {
			return parserOpt.get().parseMessages(() -> new ByteArrayInputStream(bytes), true)
				.filter(Optional::isPresent)
				.map(Optional::get);
		}
	}

	protected Optional<NexlaParser> getParser(Optional<String> mimeType, byte[] bytes) {
		String fileName = config.parseAsFileExtension
			.map(x -> "fileName." + x)
			.orElse("fileName");

		Map<String, String> overridenExtensions = ofNullable(config.originalsStrings().get(OVERRIDDEN_EXTENSIONS))
			.map(ext -> StreamEx.of(ext)
				.mapToEntry(m -> m.split(":")[0].toLowerCase(), m -> m.split(":")[1].toLowerCase())
				.toMap())
			.orElse(emptyMap());

		BiConsumer<ParseError, Exception> exceptionHandler = restSourceError.isPresent() && probeService.isPresent() ?
				getExceptionHandler(probeService.get(), controlMessageProducer, dataSourceId, restSourceError.get()) : null;

		Map<String, String> config = new HashMap<>(this.restSourceConfig.originalsStrings());
		config.putAll(this.config.originalsStrings());

		try {
			adminApiClient.flatMap(c -> c.getDataSource(restSourceConfig.sourceId))
					.ifPresent(source -> {
						config.putAll(inlineCredentialsReferences(source, new ConnectorAdminConfig(restSourceConfig.originals())));
					});
		} catch (Throwable e) {
			logger.error("Error occurred while inline credentials references", e);
		}

		ParserUtilsExt.Result detection = detectParser(mimeType, new ByteArrayInputStream(bytes), overridenExtensions,
				config, fileName, exceptionHandler, logger);

		if (detection.parser().isDefined()) {
			return Optional.of(detection.parser().get());
		} else if (enableCustomParser && adminApiClient.isPresent()) {
			logger.info("Looking for custom parser for source {} file {}", this.config.sourceId, fileName);
			return ParserUtils.createCustomParser(this.adminApiClient.get(), this.config.sourceId, emptyMap(), this.config.credentialsDecryptKey);
		}

		return Optional.empty();
	}

	private BiConsumer<ParseError, Exception> getExceptionHandler(RestConnectorService probeService,
																  Optional<ControlMessageProducer> messageProducer,
																  Optional<Integer> dataSourceId,
																  RestSourceError restSourceError) {
		if ( messageProducer.isEmpty() || dataSourceId.isEmpty())
			return null;
		else {
			return (parseError, e) -> {
				switch (probeService.analyzeException(e)) {
					case RETRY:
					case SKIP_FILE:
						rethrow(e);
					case QUARANTINE:
							restSourceError.errorCounter.incrementAndGet();
							restSourceError.setErrorMessage(e.getMessage());
							logger.error("Error occurred while processing file for source {}. Error message:{}", dataSourceId.get(), e.getMessage());
						break;
					default:
						throw new IllegalArgumentException("Unsupported state");
				}
			};
		}
	}

	@SneakyThrows
	private void rethrow(Exception e) {
		throw e;
	}

	@SneakyThrows
	public StreamEx<NexlaMessage> readDataAdvanced(byte[] bytes, Optional<MediaType> contentType) {
		Map<String, Object> sourceConfig = restSourceConfig.originals();
		Map<String, Object> iterationConfig = this.config.originals();
		Map<String, Object> config = new HashMap<>(sourceConfig.size() + iterationConfig.size()); // may not be true, but close enough
		config.putAll(sourceConfig);
		config.putAll(iterationConfig);

		try {
			adminApiClient.flatMap(c -> c.getDataSource(restSourceConfig.sourceId))
					.ifPresent(source -> {
						config.putAll(inlineCredentialsReferences(source, new ConnectorAdminConfig(restSourceConfig.originals())));
					});
		} catch (Throwable e) {
			logger.error("Error occurred while inline credentials references", e);
		}

		return new AdvancedRestFileReader(this.logger, adminApiClient.get(), config)
				.stream(mkFile(bytes));
	}

	private File mkFile(byte[] bytes) throws IOException {
		Path path = Files.createTempFile("body.as.file.iter", "tmp");
		Files.write(path, bytes);

		File file = path.toFile();
		file.deleteOnExit();
		return file;
	}

	@SneakyThrows
	@Override
	public ParsedData createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> stream,
		BodyAsFileOffset offset,
		String url
	) {
		Map<String, List<NexlaMessage>> groupedBySource = stream.values()
				.groupingBy(x -> ofNullable(x.getNexlaMetaData().getSourceKey()).orElse(""));

		List<BodyContentCache.CachedData> data = EntryStream.of(groupedBySource)
				.mapKeyValue((key, rec) -> writeCachedFile(headers, rec))
				.toList();

		this.cache = new BodyContentCache(url, data);

		return readFromCachedFile(offset);
	}

	@SneakyThrows
	private BodyContentCache.CachedData writeCachedFile(HttpHeaders headers, List<NexlaMessage> rec) {
		var cachedFile = File.createTempFile("bodyasfile", ".tmp");
		cachedFile.deleteOnExit();

		AtomicInteger ai = new AtomicInteger();

		try (FileOutputStream fos = new FileOutputStream(cachedFile)) {
			rec
				.stream()
				.map(message -> {
					Map<String, Object> rawMessage = (Map<String, Object>) message.getRawMessage();
					Map<String, Object> tags = message.getNexlaMetaData().getTags();
					Map<String, Object> cacheEntry = new LinkedHashMap<>();
					cacheEntry.put("rawMessage", rawMessage);
					cacheEntry.put("tags", tags == null ? new LinkedHashMap<>() : tags);
					return cacheEntry;
				})
				.map(JsonUtils::toJsonString)
				.forEach(x -> {
					try {
						ai.incrementAndGet();
						IOUtils.write(x + "\n", fos, Charset.defaultCharset());
					} catch (Exception e) {
						throw new RuntimeException(e);
					}
				});

			fos.flush();
		}

		return new BodyContentCache.CachedData(headers, cachedFile, ai.get(), 0);
	}

	@SneakyThrows
	private ParsedData readFromCachedFile(BodyAsFileOffset offset) {
		if (cache.cachedDataList.isEmpty()) {
			return new ParsedData(Collections.emptyList(), offset, Optional.empty());
		}

		BodyContentCache.CachedData cached = cache.cachedDataList.get(0);
		HttpHeaders headers = cached.headers;

		Integer batchSize = config.batchSize.orElse(DEFAULT_BATCH_SIZE);
		var lastMessageNum = cached.lastMessageNum + batchSize;

		EntryStream<Integer, String> streamWindow = zipWithIndices(Files.lines(cached.file.toPath()))
			.skip(cached.lastMessageNum)
			.takeWhile(x -> x.getKey() < lastMessageNum);

		var records = streamWindow
			.mapValues(JsonUtils::jsonToMap)
			.mapKeyValue((offsetOnPage, cacheEntry) -> {
				Map<String, Object> rawMessage = (Map<String, Object>) cacheEntry.getOrDefault("rawMessage", Collections.emptyMap());
				Map<String, Object> tags = (Map<String, Object>) cacheEntry.getOrDefault("tags", Collections.emptyMap());
				return new ResultEntry(new LinkedHashMap<>(rawMessage), offsetOnPage, null, offset, headers.toSingleValueMap(), tags);
			})
			.toList();

		boolean moreData = cached.linesNum > lastMessageNum;
		if (moreData) {
			var mesNum = records.get(records.size() - 1).getOffsetOnPage();
			var nextTokenOffset = new BodyAsFileOffset(
				mesNum,
				of(mesNum + batchSize),
				offset.getParentMessageNumber(),
				offset.getDateTime(),
				true,
				empty(),
				offset.getPage()
			);
			cached.lastMessageNum = lastMessageNum;
			return new ParsedData(records, offset, of(nextTokenOffset));
		} else {
			cached.file.delete();
			cache.cachedDataList.remove(0);

			if (cache.cachedDataList.isEmpty()) {
				return new ParsedData(records, offset, createNextOffset(offset, headers));
			} else {
				var nextTokenOffset = new BodyAsFileOffset(
					0,
					empty(),
					offset.getParentMessageNumber(),
					offset.getDateTime(),
					true,
					empty(),
					offset.getPage()
				);

				return new ParsedData(records, offset, Optional.of(nextTokenOffset));
			}
		}
	}

	private Optional<BodyAsFileOffset> createNextOffset(BodyAsFileOffset offset, HttpHeaders headers) {
		switch (config.mode) {
			case RestIterationConfig.PAGING_MODE: {
				Optional<Long> optPage = offset.getPage()
						.map(p -> p + 1)
						.filter(p -> p <= config.endPageTo.orElse(Long.MAX_VALUE));

				return optPage.map(p -> new BodyAsFileOffset(
						0,
						empty(),
						offset.getParentMessageNumber(),
						offset.getDateTime(),
						false,
						empty(),
						of(p)
				));
			}
			case RestIterationConfig.LINK_HEADER_MODE: {
				Optional<String> optLinkHeader = config.responseNextTokenHeader
						.map(headers::get)
						.filter(linkHeaders -> !linkHeaders.isEmpty())
						.map(linkHeaders -> linkHeaders.get(0))
						.flatMap(linkValue ->
							(config.responseNextTokenStopValue.isPresent() &&
							 config.responseNextTokenStopValue.get().equals(linkValue)) ? empty(): of(linkValue)
						);

				return optLinkHeader.map(h -> new BodyAsFileOffset(
						0,
						empty(),
						offset.getParentMessageNumber(),
						offset.getDateTime(),
						false,
						of(h),
						empty()
				));
			}
		}

		return empty();
	}

	@Override
	public BodyAsFileOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		switch (config.mode) {
			case RestIterationConfig.PAGING_MODE: {
				return new BodyAsFileOffset(0, empty(), parentMessageNumber, dateTime, false, empty(), of(config.startPageFrom));
			}
			default:
			case RestIterationConfig.LINK_HEADER_MODE: {
				return new BodyAsFileOffset(0, empty(), parentMessageNumber, dateTime, false, empty(), empty());
			}
		}
	}

	@AllArgsConstructor
	private static class BodyContentCache {
		private String url;
		private List<CachedData> cachedDataList;

		@AllArgsConstructor
		private static class CachedData {
			HttpHeaders headers;
			File file;
			Integer linesNum;
			Integer lastMessageNum;
		}
	}

	/**
	 * This is just a helper to set up TransportFileReader, since it has quite heavy API.
	 */
	private class AdvancedRestFileReader {
		private final FileSourceNotificationSender NOOP_SOURCE_NOTIFICATION_SENDER = new NoopFileSourceNotificationSender();
		private final FileSourceOffsetWriter NOOP_OFFSET_WRITER = new NoopOffsetWriter();
		private final FileConnectorService<?> LOCAL_CONNECTOR_SERVICE = new LocalConnectorService();

		private final NexlaLogger logger;
		private final AdminApiClient adminApiClient;
		private final Map<String, ?> rawConfig;
		private final SchemaDetectionUtils noopSchemaDetection;

		public AdvancedRestFileReader(NexlaLogger logger, AdminApiClient adminApiClient, Map<String, ?> rawConfig) {
			this.logger = logger;
			this.adminApiClient = adminApiClient;
			this.rawConfig = rawConfig;

			this.noopSchemaDetection = new NoopSchemaDetectionUtils(adminApiClient);
		}

		public StreamEx<NexlaMessage> stream(File f) {
			long runId = System.currentTimeMillis();
			FileSourceContext context = new FileSourceContext(new FileSourceConnectorConfig(rawConfig), empty(), runId);
			MessageGrouper messageGrouper = new KafkaFileSourceMessageGrouper(context, logger, runId);

			List<TransportFile> tfs = List.of(new TransportFile(
							new NexlaFile(f.getAbsolutePath(), f.length(), null, null, null, null, ListingResourceType.FILE),
							0L,
							false
					));
			
			Map<String, String> overridenExtensionsGenerated = one.util.streamex.StreamEx.of(context.config.originalsStrings().get("extension"))
					.mapToEntry(m -> m.split(":")[0].toLowerCase(), m -> m.split(":")[1].toLowerCase())
					.toMap();
			context.config.overridenExtensions.putAll(overridenExtensionsGenerated);
			
			TransportFileReader tfr = new TransportFileReader(NOOP_SOURCE_NOTIFICATION_SENDER, LOCAL_CONNECTOR_SERVICE, noopSchemaDetection, NOOP_OFFSET_WRITER, messageGrouper, logger, empty(), empty(), empty(), context, empty(), false);
			tfr.addFiles(tfs);

			BaseKafkaFileMessageReader kafkaFileMessageReader = new BaseKafkaFileMessageReader(NOOP_OFFSET_WRITER, messageGrouper, context, logger, LOCAL_CONNECTOR_SERVICE, NOOP_SOURCE_NOTIFICATION_SENDER);

			return StreamEx.iterate(
							tfr.readNextBatch(kafkaFileMessageReader, adminApiClient),
							t -> !CollectionUtils.isEmpty(t.messages),
							t -> tfr.readNextBatch(kafkaFileMessageReader, adminApiClient)
					)
					.flatMap(t -> t.messages.stream())
					.map(m -> m.message);
		}
	}
}
