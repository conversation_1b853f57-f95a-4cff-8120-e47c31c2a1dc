package com.nexla.probe.http;

import com.nexla.admin.client.oauth2.NexlaTokenProvider;
import com.nexla.client.ScriptEvalClient;
import com.nexla.connect.common.DetailedFlowInsightsSender;
import com.nexla.common.interceptor.RestTemplateLoggerInterceptor;
import com.nexla.connector.config.Oauth2BasedAuthConfig;
import com.nexla.connector.config.rest.RestAuthConfig;
import org.apache.http.conn.DnsResolver;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.resource.OAuth2ProtectedResourceDetails;
import org.springframework.security.oauth2.client.resource.UserRedirectRequiredException;
import org.springframework.security.oauth2.client.token.AccessTokenProvider;
import org.springframework.security.oauth2.client.token.AccessTokenRequest;
import org.springframework.security.oauth2.client.token.DefaultAccessTokenRequest;
import org.springframework.security.oauth2.client.token.grant.client.ClientCredentialsResourceDetails;
import org.springframework.security.oauth2.common.AuthenticationScheme;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.util.ArrayList;
import java.util.List;

public class Oauth2RequestSender extends BaseRequestSender {

	private final NexlaTokenProvider tokenProvider;

	public Oauth2RequestSender(
		RestAuthConfig authConfig,
		NexlaTokenProvider tokenProvider,
		ScriptEvalClient scriptEvalClient,
		boolean logBody,
		boolean skipUrlEncoding
	) {
		super(authConfig, scriptEvalClient, logBody, skipUrlEncoding);
		this.tokenProvider = tokenProvider;
	}

	@Override
	protected RestTemplate buildTemplate(boolean logBody, SSLContext defaultSslContext, DnsResolver dnsResolver, DetailedFlowInsightsSender flowInsightsSender) {

		DefaultOAuth2ClientContext tokenContext = new DefaultOAuth2ClientContext(new DefaultAccessTokenRequest());
		OAuth2RestTemplate restTemplate = new OAuth2RestTemplate(oauth2Details(authConfig), tokenContext);

		ClientHttpRequestFactory requestFactory = new BufferingClientHttpRequestFactory(new HttpComponentsClientHttpRequestFactory());
		restTemplate.setRequestFactory(requestFactory);

		AccessTokenProvider accessTokenProvider = authConfig.isOauth2Refreshable()
			? refreshableTokenProvider()
			: new CustomAccessTokenProvider(authConfig);

		restTemplate.setAccessTokenProvider(accessTokenProvider);

        List<ClientHttpRequestInterceptor> interceptors = restTemplate.getInterceptors();
        if (interceptors == null) {
            restTemplate.setInterceptors(interceptors = new ArrayList<>(2));
        }

        interceptors.add(new GzipRequestInterceptor());

        if (logBody) {
			interceptors.add(new RestTemplateLoggerInterceptor(logBody));
		}

        return restTemplate;
	}

	private OAuth2ProtectedResourceDetails oauth2Details(RestAuthConfig authConfig) {

		ClientCredentialsResourceDetails resource = new ClientCredentialsResourceDetails();

		resource.setClientId(authConfig.oauth2ClientId);
		resource.setClientSecret(authConfig.oauth2ClientSecret);
		authConfig.oauth2ClientAuthScheme.ifPresent(sch ->
				resource.setClientAuthenticationScheme(AuthenticationScheme.valueOf(sch)));

		if (authConfig.isOauth2Refreshable()) {
			AuthenticationScheme authScheme = AuthenticationScheme.valueOf(authConfig.vendorAuthScheme);
			resource.setAuthenticationScheme(authScheme);
		} else {
			resource.setAccessTokenUri(authConfig.oauth2AccessTokenUrl);
		}

		resource.setScope(authConfig.vendorScopes);

		return resource;
	}

	private AccessTokenProvider refreshableTokenProvider() {

		return new AccessTokenProvider() {

			@Override
			public OAuth2AccessToken obtainAccessToken(
				OAuth2ProtectedResourceDetails details,
				AccessTokenRequest parameters
			) {
				Oauth2BasedAuthConfig freshConfig = tokenProvider.getToken(authConfig);
				DefaultOAuth2AccessToken t = new DefaultOAuth2AccessToken(freshConfig.getVendorAccessToken());

				freshConfig
					.getVendorLastRefreshedAt()
					.ifPresent(lastRefreshed ->
						freshConfig
							.getVendorTokenExpiresInSec()
							.map(expires -> lastRefreshed.plusSeconds(expires.intValue()))
							.ifPresent(expires -> t.setExpiration(expires.toDate())));

				String tokenType = authConfig.oauth2TokenTypeOverride.orElse(freshConfig.getVendorAccessTokenType());
				t.setTokenType(tokenType);
				return t;
			}

			@Override
			public boolean supportsResource(OAuth2ProtectedResourceDetails resource) {
				return true;
			}

			@Override
			public OAuth2AccessToken refreshAccessToken(OAuth2ProtectedResourceDetails resource, OAuth2RefreshToken refreshToken, AccessTokenRequest request) throws UserRedirectRequiredException {
				return null;
			}

			@Override
			public boolean supportsRefresh(OAuth2ProtectedResourceDetails resource) {
				return false;
			}
		};
	}
}
