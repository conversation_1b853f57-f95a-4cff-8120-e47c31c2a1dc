package com.nexla.probe.http;

import com.google.common.collect.Sets;
import com.nexla.admin.client.oauth2.NexlaTokenProvider;
import com.nexla.client.ScriptEvalClient;
import com.nexla.common.interceptor.RestTemplateAws4SignatureInterceptor;
import com.nexla.common.interceptor.RestTemplateGCPAccountInterceptor;
import com.nexla.common.interceptor.RestTemplateQueryParamsInterceptor;
import com.nexla.common.pool.NexlaPool;
import com.nexla.common.pool.ResizableNexlaPool;
import com.nexla.common.pool.SimplePool;
import com.nexla.common.request.HttpComponentsClientHttpRequestWithBodyFactory;
import com.nexla.connect.common.DetailedFlowInsightsSender;
import com.nexla.connector.config.rest.HttpCallParameters;
import com.nexla.connector.config.rest.RestAuthConfig;
import com.nexla.probe.http.wrapped.auth.JwtWrapperSender;
import com.nexla.probe.http.wrapped.auth.TokenizedSender;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.conn.DnsResolver;
import org.apache.http.conn.ssl.BrowserCompatHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.support.BasicAuthorizationInterceptor;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.*;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.IntStream;
import java.util.zip.GZIPOutputStream;

import static java.util.Optional.ofNullable;

public class BaseRequestSender extends RequestSender {
	private static final Logger LOGGER = LoggerFactory.getLogger(BaseRequestSender.class);

	public static final Set<String> BLOCKED_URLS = Sets.newHashSet(
			"***************",
			"checkip.amazonaws.com"
	);

	private final RestTemplate restTemplate;
	protected final RestAuthConfig authConfig;
	private final Optional<String> hmacFunc;
	private final ScriptEvalClient scriptEvalClient;
	private long lastRequestMs;
	private final boolean skipUrlEncoding;

	public BaseRequestSender(RestAuthConfig authConfig,
							 ScriptEvalClient scriptEvalClient,
							 boolean logBody,
							 boolean skipUrlEncoding) {
		this(authConfig, scriptEvalClient, logBody, skipUrlEncoding, null, null, null);
	}
	public BaseRequestSender(RestAuthConfig authConfig,
							 ScriptEvalClient scriptEvalClient,
							 boolean logBody,
							 boolean skipUrlEncoding,
							 DetailedFlowInsightsSender flowInsightsSender) {
		this(authConfig, scriptEvalClient, logBody, skipUrlEncoding, null, null, flowInsightsSender);
	}

	public BaseRequestSender(RestAuthConfig authConfig,
							 ScriptEvalClient scriptEvalClient,
							 boolean logBody) {
		this(authConfig, scriptEvalClient, logBody, false, null, null, null);
	}

	public BaseRequestSender(RestAuthConfig authConfig,
							 ScriptEvalClient scriptEvalClient,
							 boolean logBody,
							 DetailedFlowInsightsSender flowInsightsSender) {
		this(authConfig, scriptEvalClient, logBody, false, null, null, flowInsightsSender);
	}

	public BaseRequestSender(RestAuthConfig authConfig,
	                         ScriptEvalClient scriptEvalClient,
	                         boolean logBody,
							 boolean skipUrlEncoding,
							 SSLContext defaultSslContext,
							 DnsResolver defaultNnsResolver,
							 DetailedFlowInsightsSender flowInsightsSender) {
		super(logBody);
		this.authConfig = authConfig;
		this.restTemplate = buildTemplate(logBody, defaultSslContext, defaultNnsResolver, flowInsightsSender);
		if (authConfig.ignoreSslCertValidation) {
			restTemplate.setRequestFactory(getRequestFactoryIgnoringSsl());
		}

		this.hmacFunc = authConfig.hmacFunc;
		this.lastRequestMs = System.currentTimeMillis();
		this.scriptEvalClient = scriptEvalClient;
		this.skipUrlEncoding = skipUrlEncoding;
	}

	public RestTemplate getRestTemplate() {
		return restTemplate;
	}

	public HttpSenderResponse send(HttpCallParameters cp) {
		try {
			ResponseEntity<byte[]> responseEntity = sendRequest(cp);
			Optional<byte[]> bytes = ofNullable(responseEntity.getBody());
			return new HttpSenderResponse(bytes, responseEntity.getHeaders(), HttpStatus.OK.value());
		} catch (HttpClientErrorException e) {
			logError(e.getClass().getName(), e.getResponseBodyAsString(), cp);
			throw new SenderHttpException(cp.getUrl(), e);
		} catch (Exception e) {
			logError(e.getClass().getName(), e.getMessage(), cp);
			throw e;
		}
	}

	private void logError(String type, String msg, HttpCallParameters cp) {
		logger().error("Request '{}' failed with [{}]: {}",
				StringUtils.abbreviate(cp.getUrl(), 64), type, msg);
		if (logBody) {
			logger().error("Failed request details: '{} {}' \nbody={} \nheaders={}",
					cp.getMethod(), cp.getUrl(), cp.getBody(), cp.getRestHeaders());
		}
	}

	@Override
	public boolean auth(HttpCallParameters callParameters) {
		ResponseEntity<byte[]> response = sendRequest(callParameters);
		return response.getStatusCode().is2xxSuccessful();
	}

	@SneakyThrows
	public ResponseEntity<byte[]> sendRequest(
			HttpCallParameters callParameters
	) {
		HttpCallParameters restCall = callParameters.withDefaultHeaders(authConfig.restHeaders);

		HttpCallParameters signedRestCall = hmacFunc
				.map(func -> new HmacEval(func, restTemplate, scriptEvalClient)
						.hmacSign(authConfig.originalsStrings(), restCall))
				.orElse(restCall);
		maybeDelay();
		HttpEntity entity = signedRestCall.createEntity(signedRestCall.getMethod());
		try {
			return restTemplate.exchange(createUri(encode(signedRestCall.getUrl())), signedRestCall.getMethod(), entity, byte[].class);
		} catch (Exception e) {
			return restTemplate.exchange(createUri(signedRestCall.getUrl()), signedRestCall.getMethod(), entity, byte[].class);
		}
	}

	@SneakyThrows
	private URI createUri(String encodedUrl) {
		URI uri = URI.create(encodedUrl);
		if (BLOCKED_URLS.contains(uri.getHost()) || BLOCKED_URLS.contains(uri.getPath())) {
			throw new RuntimeException("URL is blocked: " + uri);
		}
		return uri;
	}

	@SneakyThrows
	private void maybeDelay() {
		Long delayMs = Math.max(0, this.lastRequestMs + authConfig.minimalRequestInterval - System.currentTimeMillis());
		Thread.sleep(delayMs);
		this.lastRequestMs = System.currentTimeMillis();
	}

	@SneakyThrows
	private String encode(String urlString) {
		if (this.skipUrlEncoding) {
			logger().warn("skip URL encoding is turned on - url is {}, returning it as is", urlString);
			return urlString;
		} else {
			String decodedURL = URLDecoder.decode(urlString, StandardCharsets.UTF_8.name());
			URL url = new URL(decodedURL);
			URI uri = new URI(url.getProtocol(), url.getUserInfo(), url.getHost(), url.getPort(), url.getPath(), url.getQuery(), url.getRef());
			return uri.toASCIIString();
		}
	}

	protected RestTemplate buildTemplate(boolean logBody, SSLContext defaultSslContext, DnsResolver defaultNnsResolver,
										 DetailedFlowInsightsSender flowInsightsSender) {

		switch (authConfig.authType) {

			case NONE:
			case TOKEN:
				return this.restTemplate(logBody, defaultSslContext, defaultNnsResolver, flowInsightsSender);

			case API_KEY:
				RestTemplate restTemplateMod = this.restTemplate(logBody, defaultSslContext, defaultNnsResolver, flowInsightsSender);

				restTemplateMod
						.getInterceptors()
						.add(new RestTemplateQueryParamsInterceptor(authConfig.apiKeyAuthKey, authConfig.apiKeyAuthValue, authConfig.apiKeyIncludeMode));

				return restTemplateMod;

			case AWS_SIGNATURE:
				RestTemplate restTemplateAws = this.restTemplate(logBody, defaultSslContext, defaultNnsResolver, flowInsightsSender);
				restTemplateAws
						.getInterceptors()
						.add(new RestTemplateAws4SignatureInterceptor(authConfig.getAwsAccessKey(),
								authConfig.getAwsSecretKey(), authConfig.getAwsService(), authConfig.getAwsRegion(),
								authConfig.awsSignatureIncludeMode, authConfig.amazonSessionToken));

				return restTemplateAws;

			case GCP_SERVICE_ACCOUNT:
				RestTemplate restTemplateGCP = this.restTemplate(logBody, defaultSslContext, defaultNnsResolver, flowInsightsSender);
				restTemplateGCP
						.getInterceptors()
						.add(new RestTemplateGCPAccountInterceptor(authConfig.getGcpCredentialsJson(), authConfig.gcpServiceAccountOAuthScopes));
				return restTemplateGCP;
				
			case BASIC:
				RestTemplate restTemplate = this.restTemplate(logBody, defaultSslContext, defaultNnsResolver, flowInsightsSender);

				restTemplate
						.getInterceptors()
						.add(new BasicAuthorizationInterceptor(authConfig.basicUsername, authConfig.basicPassword));

				return restTemplate;

			default:
				throw new IllegalArgumentException();
		}
	}

	private static HttpComponentsClientHttpRequestFactory getRequestFactoryIgnoringSsl() {
		CloseableHttpClient httpClient = HttpClients.custom()
				.setSSLSocketFactory(disableSslSocketConnectionFactory())
				.build();

		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setHttpClient(httpClient);
		return requestFactory;
	}

	public RestAuthConfig getAuthConfig() {
		return authConfig;
	}

	public static RequestSender createSender(RestAuthConfig authConfig,
																					NexlaTokenProvider tokenProvider,
																					ScriptEvalClient scriptEvalClient,
																					boolean logBody,
																					boolean skipUrlEncoding,
																					Optional<DetailedFlowInsightsSender> flowInsightsSenderOpt) {
		RequestSender sender;
		sender = flowInsightsSenderOpt
				.map(flowInsightsSender -> createBaseSender(authConfig, tokenProvider, scriptEvalClient, logBody,
						skipUrlEncoding, flowInsightsSender))
				.orElseGet(() -> createBaseSender(authConfig, tokenProvider, scriptEvalClient, logBody, skipUrlEncoding, null));
		return authConfig.jwtConfig
				.<RequestSender>map(jwtConfig -> new JwtWrapperSender(scriptEvalClient, sender, jwtConfig, logBody))
				.orElse(sender);
	}

	private static RequestSender createBaseSender(RestAuthConfig authConfig,
																								NexlaTokenProvider tokenProvider,
																								ScriptEvalClient scriptEvalClient,
																								boolean logBody,
												  boolean skipUrlEncoding,
												  DetailedFlowInsightsSender flowInsightsSender) {
		switch (authConfig.authType) {
			case OAUTH1:
				return new Oauth1RequestSender(authConfig, logBody);
			case OAUTH2:
				return new Oauth2RequestSender(authConfig, tokenProvider, scriptEvalClient, logBody, skipUrlEncoding);
			case TOKEN:
				return new TokenizedSender(scriptEvalClient, new BaseRequestSender(authConfig, scriptEvalClient, logBody, skipUrlEncoding, flowInsightsSender), authConfig.tokenAuthConfig.get(), logBody);
			default:
				return new BaseRequestSender(authConfig, scriptEvalClient, logBody, skipUrlEncoding, flowInsightsSender);
		}
	}

	public static NexlaPool<RequestSender> createSenderPool(int parallelism, Supplier<RequestSender> getSenderFn) {
		return new ResizableNexlaPool<RequestSender>(getSenderFn, parallelism, ResizableNexlaPool.DEFAULT_EVICT_DELAY);
	}

	public RestTemplate restTemplate(boolean logBody, SSLContext defaultSslContext, DnsResolver defaultDnsResolver,
									 DetailedFlowInsightsSender flowInsightsSender) {
		CloseableHttpClient client = authConfig.clientP12
				.flatMap(certificate -> {
					try {
						KeyStore ks = KeyStore.getInstance("PKCS12");
						ks.load(new FileInputStream(certificate.getFile()), certificate.getPassword().toCharArray());
						KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
						kmf.init(ks, certificate.getPassword().toCharArray());

						TrustManager trustEveryoneTM = new X509TrustManager() {
							public X509Certificate[] getAcceptedIssuers() {
								return null;
							}
							public void checkClientTrusted(X509Certificate[] certs, String authType) {}
							public void checkServerTrusted(X509Certificate[] certs, String authType) {}
						};

						SSLContext sslContext = SSLContext.getInstance("TLS");
						sslContext.init(kmf.getKeyManagers(), new TrustManager[] { trustEveryoneTM }, new SecureRandom());

						HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());

						return Optional.of(
								HttpClients
										.custom()
										.setSSLSocketFactory(new SSLConnectionSocketFactory(sslContext))
										.setSSLHostnameVerifier(new BrowserCompatHostnameVerifier())
										.setDnsResolver(defaultDnsResolver)
										.build()
						);

					} catch (KeyStoreException | IOException | NoSuchAlgorithmException | CertificateException |
					         UnrecoverableKeyException | KeyManagementException e) {
						LOGGER.info("Cannot load key file", e);
						return Optional.empty();
					}
				})
				.orElse(HttpClients.custom()
						.setSSLHostnameVerifier(new BrowserCompatHostnameVerifier())
						.setDnsResolver(defaultDnsResolver)
						.setSSLContext(defaultSslContext)
						.build());

		return constructTemplate(true, client, this.authConfig.socketTimeout, logBody, flowInsightsSender);
	}

	// TODO method duplicated from commons common/src/main/java/com/nexla/common/AppUtils.java: can we make the timeout configurable?
	public static RestTemplate constructTemplate(boolean bufferRequestBody, CloseableHttpClient client,int readTimeout,
												 boolean logVerbose, DetailedFlowInsightsSender flowInsightsSender) {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestWithBodyFactory(client);
		requestFactory.setConnectTimeout(5000);
		requestFactory.setReadTimeout(readTimeout);
		requestFactory.setBufferRequestBody(bufferRequestBody);
		ClientHttpRequestFactory factory = requestFactory;
		if (bufferRequestBody) {
			factory = new BufferingClientHttpRequestFactory(requestFactory);
		}

		RestTemplate restTemplate = new RestTemplate(factory);
		restTemplate.getMessageConverters().add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
		if (bufferRequestBody) {
			List<ClientHttpRequestInterceptor> interceptors = restTemplate.getInterceptors();
			if (CollectionUtils.isEmpty(interceptors)) {
				interceptors = new ArrayList<>();
			}

			if (flowInsightsSender != null) {
				interceptors.add(new FlowInsightsRestTemplateInterceptor(logVerbose, flowInsightsSender));
			}
			interceptors.add(new GzipRequestInterceptor());
			restTemplate.setInterceptors(interceptors);
		}

		return restTemplate;
	}

	public static class GzipRequestInterceptor implements ClientHttpRequestInterceptor {
		private static final Logger LOGGER = LoggerFactory.getLogger(GzipRequestInterceptor.class);
		@Override
		public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
			if (body != null && body.length > 0) {
				// We only want to compress the body if it is requested to be compressed via the Content-Encoding header
				if (!request.getHeaders().containsKey(HttpHeaders.CONTENT_ENCODING)) {
					return execution.execute(request, body);
				}

				List<String> contentEncoding = request.getHeaders().get(HttpHeaders.CONTENT_ENCODING);
				if (contentEncoding == null || contentEncoding.isEmpty()) {
					return execution.execute(request, body);
				}

				if (!contentEncoding.contains("gzip")) {
					return execution.execute(request, body);
				}

                return execution.execute(
						request,
						compress(body)
				);
			} else {
				return execution.execute(request, body);
			}
		}

		private byte[] compress(byte[] body) throws IOException {
			ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(body.length);
			try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream)) {
				gzipOutputStream.write(body);
			}

			return byteArrayOutputStream.toByteArray();
		}
	}

}
