<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.nexla</groupId>
        <artifactId>backend-connectors</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>

    <groupId>com.nexla.probe</groupId>
    <artifactId>s3-probe</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>file-service</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>connector-test</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>connector-properties</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>common</artifactId>
            <version>${nexla-backend-common.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nexla</groupId>
            <artifactId>parsers</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-library</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>bom</artifactId>
            <version>${aws.sdk.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        <dependency>
            <groupId>software.amazon.encryption.s3</groupId>
            <artifactId>amazon-s3-encryption-client-java</artifactId>
            <version>${aws.sdk.s3.encryption.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>ec2</artifactId>
            <version>${aws.sdk.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                    <artifactId>jackson-dataformat-cbor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-cbor</artifactId>
            <version>${jackson-dataformat-cbor.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <version>${aws.sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sts</artifactId>
            <version>${aws.sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>org.scalatest</groupId>
                        <artifactId>scalatest_${scala.short.version}</artifactId>
                        <version>${scalatest.version}</version>
                    </dependency>
                </dependencies>
                <version>${scalatest-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>test</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <configuration>
                            <tagsToInclude>${scalatestTagsInclude}</tagsToInclude>
                            <tagsToExclude>${scalatestTagsExclude}</tagsToExclude>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
