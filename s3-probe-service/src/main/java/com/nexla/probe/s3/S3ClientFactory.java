package com.nexla.probe.s3;

import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.file.NexlaAWSCredentialsProvider;
import java.net.URI;
import java.util.Optional;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.S3AsyncClientBuilder;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.S3CrtAsyncClientBuilder;
import software.amazon.awssdk.utils.builder.SdkBuilder;
import software.amazon.encryption.s3.S3AsyncEncryptionClient;
import software.amazon.encryption.s3.S3EncryptionClient;
import software.amazon.encryption.s3.materials.CryptographicMaterialsManager;
import software.amazon.encryption.s3.materials.DefaultCryptoMaterialsManager;

public class S3ClientFactory {

  public static S3Client createSyncClient(AWSAuthConfig authConfig) {
    AwsCredentialsProvider credentialsProvider = NexlaAWSCredentialsProvider.getCredentialsProvider(authConfig);

    if (authConfig.kmsKey.isPresent()) {
      CryptographicMaterialsManager materialsProvider = DefaultCryptoMaterialsManager.builder().build();

      S3EncryptionClient.Builder builder = S3EncryptionClient.builder()
          .region(regionOf(authConfig))
          .credentialsProvider(credentialsProvider)
          .cryptoMaterialsManager(materialsProvider)
          .kmsKeyId(authConfig.kmsKey.get());

      authConfig.serviceEndpoint.ifPresent(ep -> builder.endpointOverride(URI.create(ep)));

      return builder.build();

    } else {
      S3ClientBuilder builder = S3Client.builder()
          .region(regionOf(authConfig))
          .credentialsProvider(credentialsProvider);

      authConfig.serviceEndpoint.ifPresent(ep -> builder.endpointOverride(URI.create(ep)));

      return builder.build();
    }
  }

  public static S3AsyncClient createAsyncClient(
      AWSAuthConfig authConfig,
      long multipartThresholdInBytes,
      long multipartMinPartSizeInBytes) {
    AwsCredentialsProvider credentialsProvider = NexlaAWSCredentialsProvider.getCredentialsProvider(authConfig);

    S3AsyncClientBuilder builder = authConfig.kmsKey.isPresent() ?
        S3AsyncEncryptionClient.builder().kmsKeyId(authConfig.kmsKey.get()).cryptoMaterialsManager(DefaultCryptoMaterialsManager.builder()
            .build())
        : S3AsyncClient.builder();

    authConfig.serviceEndpoint.ifPresent(ep -> builder.endpointOverride(URI.create(ep)));

    return builder
        .region(regionOf(authConfig))
        .credentialsProvider(credentialsProvider)
        .multipartEnabled(true)
        .multipartConfiguration(conf -> conf
            .thresholdInBytes(multipartThresholdInBytes)
            .minimumPartSizeInBytes(multipartMinPartSizeInBytes))
        .build();
  }

  /**
   * CRT stands for common runtime, it's a high performance C/C++ based implementation of some AWS SDKs. Using CRT might
   * require system level considerations especially when running in containers.
   * <p>
   * No encryption support at the client level in CRT, if you use this and require a non-default KMS setup then you
   * must configure your request calls appropriately.
   */
  public static S3AsyncClient createCrtAsyncClient(
      AWSAuthConfig authConfig,
      long multipartThresholdInBytes,
      long multipartMinPartSizeInBytes) {
    AwsCredentialsProvider credentialsProvider = NexlaAWSCredentialsProvider.getCredentialsProvider(authConfig);

    S3CrtAsyncClientBuilder builder = S3AsyncClient.crtBuilder()
        .region(regionOf(authConfig))
        .credentialsProvider(credentialsProvider)
        .minimumPartSizeInBytes(multipartMinPartSizeInBytes)
        .thresholdInBytes(multipartThresholdInBytes);

    authConfig.serviceEndpoint.ifPresent(ep -> builder.endpointOverride(URI.create(ep)));

    return builder.build();
  }

  private static Region regionOf(AWSAuthConfig authConfig) {
    return Region.of(authConfig.region != null ? authConfig.region : "us-east-1");
  }
}
