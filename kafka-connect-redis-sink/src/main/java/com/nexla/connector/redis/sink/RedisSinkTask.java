package com.nexla.connector.redis.sink;

import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.exception.LimitReachedException;
import com.nexla.common.exception.NexlaException;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connect.common.ReadyToFlush;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connector.MessageMapper;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.redis.RedisConnectorConfig;
import com.nexla.control.message.ControlEventType;
import com.nexla.probe.redis.RedisConnectorService;
import com.nexla.redis.Lookup;
import com.nexla.redis.LookupUtils;
import com.nexla.redis.RedisCreds;
import com.nexla.redis.dto.MasterEntry;
import io.lettuce.core.RedisConnectionException;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.MapUtils;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.errors.RetriableException;
import org.joda.time.DateTime;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

import static com.nexla.common.Resource.sink;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.exception.NexlaError.getErrorDetails;
import static com.nexla.connect.common.NexlaConnectorUtils.publishException;

public class RedisSinkTask extends BaseSinkTask<RedisConnectorConfig> {

	private static final int DEFAULT_FLUSH_INTERVAL_SECONDS = 300;

	private RedisConnectorService probeService;
	private DateTime lastUnflushedTs;

	private Map<TopicPartition, Long> offsets = Maps.newConcurrentMap();

	private final int flushIntervalSeconds;
	private RedisCreds redisCreds;

	public RedisSinkTask() {
		this(DEFAULT_FLUSH_INTERVAL_SECONDS);
	}

	public RedisSinkTask(int flushIntervalSeconds) {
		this.flushIntervalSeconds = flushIntervalSeconds;

	}

	private RedisCreds getRedisCreds() {
		if (redisCreds == null) {
			redisCreds = new RedisCreds(config.authConfig.hosts, config.authConfig.redisClusterEnabled, config.authConfig.password, config.authConfig.tlsContext);
		}
		return redisCreds;
	}

	@Override
	public void doStart() throws ConnectException {
		this.sinkTelemetryReporter =
				Optional.of(new ConnectorTelemetryReporter(config.sinkId, "REDIS", SINK, isDedicatedNode));

		LookupUtils.withLookup(getRedisCreds(), config.mapId, (lookup) -> {
			String mapPrimaryKey = getPrimaryKey(lookup);
			checkAndSetDataMapSizeLimit(lookup, config.datamapSizeLimit);

			logger.info("init lookup: mapId={}, mapPrimaryKey={}", config.mapId, mapPrimaryKey);
			this.probeService = new RedisConnectorService();
			probeService.initLogger(SINK, config.sinkId, taskId);
		});
	}

    @Override
    protected ConfigDef configDef() {
        return RedisConnectorConfig.configDef();
    }

    @Override
	protected MessageMapper createMessageMapper() {
		// forcing flattening of nested json to store plain values in Redis
		return new MessageMapper(config.mappingConfig, config, true, encryptionUtils);
	}

	@Override
	protected RedisConnectorConfig parseConfig(Map<String, String> props) {
		return new RedisConnectorConfig(props);
	}

	protected synchronized void onRunIdChanged(Long prevRunId, Long currRunId) {
		LookupUtils.withLookup(getRedisCreds(), config.mapId, (lookup) -> {
			logger.info("New run id: {}", currRunId);
			if (prevRunId == null) {
				logger.info("First runId, opening update window for lookup {}", config.mapId);
				lookup.beginUpdateEntries();
			} else if (lastUnflushedTs != null) {
				flushTx();
			}
		});
	}

	@Override
	public synchronized boolean doFlush(ReadyToFlush readyToFlush) {
		if (config.fastMode && readyToFlush.flush) {
			logger.info("ReadyToFlush set to true {} for fast-connector mode, flushing", readyToFlush);
			flushTx();
		} else if (lastUnflushedTs != null && lastUnflushedTs.plusSeconds(flushIntervalSeconds).isBeforeNow()) {
			logger.info("{} seconds passed after the last flush, flushing", flushIntervalSeconds);
			flushTx();
		}
		updateAndResetOffsets();
		return false;
	}

	private void flushTx() {
		LookupUtils.withLookup(getRedisCreds(), config.mapId, (lookup) -> {
			logger.info("Closing previous update window for lookup {}", config.mapId);
			lookup.endUpdateEntries();
			logger.info("Opening new update window for lookup {}", config.mapId);
			lookup.beginUpdateEntries();
			lastUnflushedTs = null;
		});
	}

	@Override
	protected void doPut(StreamEx<NexlaMessageContext> messages, int streamSize) {
		Iterator<NexlaMessageContext> messageIter = messages.iterator();
		try {
			// FIXME: 'withRedis' method wraps code into a Retryer. Consider to reduce the scope of 'withRedis' code in order to exclude extra logic from the Retryer block
			LookupUtils.withLookup(getRedisCreds(), config.mapId, (lookup) -> {
				List<Map<String, String>> dataRecords = new ArrayList<>(streamSize);
				AtomicLong approxSizeBytes = new AtomicLong(0);

				messageIter.forEachRemaining(message -> {
					offsets.put(message.topicPartition, message.kafkaOffset);
					approxSizeBytes.addAndGet(getSizeBytes(message.mapped.getRawMessage()));
					dataRecords.add(stringifyMap(message.mapped.getRawMessage()));
				});

				int errorCount = 0;
				try {
					List<NexlaException> errorList = lookup.updateEntriesAndReturnErrors(dataRecords);
					for (NexlaException nexlaException : errorList) {
						Map<String, Object> dataMap = nexlaException.getDataMap();

						publishExceptionMessage(nexlaException);
						publishMonitoringLog(nexlaException.getMessage(), NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
						dataMessageProducer.sendQuarantineMessage(sink(config.sinkId), new NexlaMessage(new LinkedHashMap<>(dataMap)), nexlaException);
						errorCount = errorCount + 1;
						approxSizeBytes.addAndGet(-getSizeBytes(dataMap));
					}
				} catch (final NexlaException e) {
					if (e instanceof LimitReachedException) {
						logger.debug(String.format("Pausing Sink %d due to entries limit reached.", dataSink.getId()));
						adminApiClient.updateResourceStatus(dataSink.getId(), SINK, ControlEventType.PAUSE.name().toLowerCase());
					}
					logger.error("Failed to update Redis Entries for lookup {}", lookup.getId(), e);
					publishExceptionMessage(e);
					publishMonitoringLog(e.getMessage(), NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
				}

				sendMetric("dm:entry:" + config.mapId, Optional.empty(), streamSize - errorCount,
					approxSizeBytes.get(), errorCount);
				this.lastUnflushedTs = DateTime.now();
			});
		} catch (Exception e) {
			logger.error("Error while writing records to Redis", e);
			publishExceptionMessage(new ConnectException("Error writing record in Sink connector with ID=" + config.sinkId, e));
			publishMonitoringLog(
					"Error writing record: " + e.getMessage(),
					NexlaMonitoringLogType.LOG,
					NexlaMonitoringLogSeverity.ERROR);
			if (e instanceof RedisConnectionException) {
				throw new RetriableException("Retrying without quarantine", e);
			} else {
				messageIter.forEachRemaining(m -> dataMessageProducer.sendQuarantineMessage(sink(config.sinkId), m.mapped, e));
				throw new RuntimeException(e);
			}
		}
	}

	private Map<String, String> stringifyMap(LinkedHashMap<String, Object> dataMap) {
		Map<String, String> results = new LinkedHashMap<>();
		dataMap.forEach((key, value) -> {
			String data = value != null ? value.toString() : null;
			results.put(key, data);
		});
		return results;
	}

	private long getSizeBytes(Map<String, Object> dataMap) {
		long sizeBytes = 0;
		for (Map.Entry<String, Object> entry: dataMap.entrySet()) {
			sizeBytes = sizeBytes + (entry.getKey() != null ? entry.getKey().length() : 0);
			sizeBytes = sizeBytes + (entry.getValue() != null ? entry.getValue().toString().length() : 0);
		}
		return sizeBytes;
	}

	@SneakyThrows
	@Override
	public void stop() {
		if (probeService != null) {
			probeService.close();
		}
		updateAndResetOffsets();
		super.stop();
	}

	private void publishExceptionMessage(Throwable exp) {
		publishException(controlMessageProducer, 0L, SINK, config.sinkId, 0L, null, getErrorDetails(exp, Optional.empty()));
	}

	private String getPrimaryKey(Lookup lookup) {
		String mapPrimaryKey = lookup.getMasterEntryField(MasterEntry.MAP_PRIMARY_KEY);

		if (mapPrimaryKey == null) {
			throw new RuntimeException(String.format("[SINK-%s] Primary key not found, lookup '%s' is missing", config.sinkId, lookup.getId()));
		}

		return mapPrimaryKey;
	}

	private void checkAndSetDataMapSizeLimit(final Lookup lookup, final int mapSizeLimit) {
		final String mapSizeLimitStr = lookup.getMasterEntryField(MasterEntry.MAP_SIZE_LIMIT);
		if (mapSizeLimitStr == null || mapSizeLimitStr.isEmpty()){
			lookup.setMasterEntryField(MasterEntry.MAP_SIZE_LIMIT, String.valueOf(mapSizeLimit));
		}
	}

	private void updateAndResetOffsets() {
		if (MapUtils.isNotEmpty(offsets)) {
			offsetsSender.ifPresent(os -> {
				os.updateSinkOffsets(config.sinkId, offsets);
				logger.info("Updated offsets: {}", offsets);
				offsets = Maps.newConcurrentMap();
			});
		}
	}
}
