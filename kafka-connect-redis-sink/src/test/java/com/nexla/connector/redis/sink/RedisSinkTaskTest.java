package com.nexla.connector.redis.sink;

import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.AdminApiClientBuilder;
import com.nexla.admin.client.DataMap;
import com.nexla.admin.client.DataSink;
import com.nexla.admin.client.Org;
import com.nexla.admin.client.Owner;
import com.nexla.admin.client.ResourceStatus;
import com.nexla.admin.client.flownode.FlowNodeDatasource;
import com.nexla.admin.client.flownode.NexlaFlow;
import com.nexla.admin.client.pipeline.PDataSource;
import com.nexla.admin.client.pipeline.PDataset;
import com.nexla.admin.client.pipeline.Pipeline;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaConstants;
import com.nexla.common.NexlaMessage;
import com.nexla.common.NexlaMetaData;
import com.nexla.common.io.RedisConnect;
import com.nexla.common.tracker.Tracker;
import com.nexla.connect.common.BaseKafkaTest;
import com.nexla.connect.common.DbTestUtils.RedisData;
import com.nexla.connect.common.ReadyToFlush;
import com.nexla.redis.Lookup;
import com.nexla.redis.LookupUtils;
import com.nexla.redis.RedisCreds;
import com.nexla.redis.dto.MasterEntry;
import io.lettuce.core.api.sync.RedisCommands;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.connect.sink.SinkRecord;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.google.common.collect.Sets.newHashSet;
import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.common.NexlaConstants.CREDENTIALS_DECRYPT_KEY;
import static com.nexla.common.NexlaConstants.CREDS_ENC;
import static com.nexla.common.NexlaConstants.CREDS_ENC_IV;
import static com.nexla.common.NexlaConstants.MAP_ID;
import static com.nexla.common.NexlaConstants.REDIS_CLUSTER_ENABLED;
import static com.nexla.common.NexlaConstants.REDIS_HOSTS;
import static com.nexla.common.NexlaConstants.REDIS_TLS_ENABLED;
import static com.nexla.common.NexlaConstants.SINK_ID;
import static com.nexla.common.datetime.DateTimeUtils.nowUTC;
import static com.nexla.connect.common.DbTestUtils.getRedis;
import static com.nexla.connector.ConnectorService.UNIT_TEST;
import static com.nexla.connector.config.BaseConnectorConfig.FAST_MODE;
import static com.nexla.connector.config.SinkConnectorConfig.MAPPING;
import static com.nexla.connector.config.redis.RedisAuthConfig.ID_FIELD_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@Testcontainers
@Tag("com.nexla.test.IntegrationTests")
@Slf4j
public class RedisSinkTaskTest extends BaseKafkaTest {

	private static final int REDIS_PORT = 6379;
	private static final int REDIS_INDEX = 0;
	@SuppressWarnings("rawtypes")
	private final static GenericContainer REDIS = new GenericContainer(DockerImageName.parse("redis:7.0.12"))
			.withExposedPorts(REDIS_PORT);
	private final static KafkaContainer KAFKA = new KafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.2.11"));

	private static RedisCreds REDIS_CREDS;
	private static RedisCommands<String, String> JEDIS;

	private static final int FLUSH_INTERVAL_SECONDS = 1;

	@BeforeAll
	public static void startUp() {
		log.info("Starting redis");
		REDIS.withReuse(true);
		REDIS.start();

		log.info("Starting kafka");
		KAFKA.withReuse(true);
		KAFKA.start();
		init(KAFKA);

		REDIS_CREDS = new RedisCreds(newHashSet(redisHostPort()), false, Optional.empty(), Optional.empty());
		JEDIS = getRedis(redisData());
	}

	@AfterAll
	public static void tearDown() {
		log.info("Stopping kafka");
		KAFKA.stop();
		log.info("Stopping redis");
		REDIS.stop();
	}

	private final Map<String, String> baseProperties = new HashMap<>() {{

		put(NexlaConstants.CONTROL_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		put(NexlaConstants.DATA_KAFKA_BOOTSTRAP_SERVERS, BOOTSTRAP_SERVERS);
		put(NexlaConstants.CONTROL_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);
		put(NexlaConstants.DATA_KAFKA_SECURITY_PROTOCOL, BOOTSTRAP_SECURITY_PROTOCOL);

		put(UNIT_TEST, "true");

		put(REDIS_HOSTS, redisHostPort().toUrl());
		put(REDIS_CLUSTER_ENABLED, "false");
		put(REDIS_TLS_ENABLED, "false");

		put(CREDS_ENC, "1");
		put(CREDS_ENC_IV, "1");
		put(CREDENTIALS_DECRYPT_KEY, "1");

		put(ID_FIELD_NAME, "id");
		put(SINK_ID, "1");
		put(MAPPING, "{ \"mode\": \"auto\"}");
		put(FAST_MODE, "true"); // disable listing calls
	}};

	private RedisSinkTask task;
	private RedisSinkConnector connector;

	@SneakyThrows
	@BeforeEach
	public void before() {
		AdminApiClient adminApiClient = mock(AdminApiClient.class);
		AdminApiClientBuilder.INSTANCE = adminApiClient;
		List<PDataset> dataSets = List.of(new PDataset(24, 32, null));
		PDataSource ds = new PDataSource();
		DataSink sink = new DataSink();
		sink.setId(123);
		sink.setConnectionType(ConnectionType.FTP);
		sink.setSinkConfig(Map.of());
		sink.setOwner(Owner.getOwnerById(22));
		sink.setOrg(Org.getOrgById(22));
		when(adminApiClient.getPipeline(anyInt())).thenReturn(Optional.of(new Pipeline(dataSets, ds, sink)));
		when(adminApiClient.getDataSink(anyInt())).thenReturn(Optional.of(sink));
		when(adminApiClient.getFlowByResource(any())).thenReturn(Optional.of(new NexlaFlow(Collections.emptyList(),
			Collections.singletonList(new FlowNodeDatasource(1, 2, 3, 4, 5, "", "", ResourceStatus.ACTIVE, Collections.emptyList(),
				7, 8, 9, ConnectionType.MONGO, ConnectionType.MONGO, ConnectionType.MONGO)), Collections.emptyList(), Collections.emptyList())));

		this.task = new RedisSinkTask(FLUSH_INTERVAL_SECONDS);
		this.connector = new RedisSinkConnector();
		task.initialize(mock(SinkTaskContext.class));
		this.runBeforeTopicReading = Optional.of(() -> task.getControlMessageProducer().flush());
	}


	@AfterEach
	public void after() {
		task.getControlMessageProducer().flush();
		closeSilently(task::stop);
		connector.stop();
	}

	protected static RedisConnect redisHostPort() {
		return new RedisConnect(REDIS.getHost(), REDIS.getMappedPort(REDIS_PORT), REDIS_INDEX);
	}

	protected static RedisData redisData() {
		return new RedisData(REDIS.getHost(), REDIS.getMappedPort(REDIS_PORT), REDIS_INDEX);
	}

	private static DataMap emptyDataMap(int id, MasterEntry.DataModelVersion dataModelVersion, boolean useVersioning) {
		DataMap dm = new DataMap();
		dm.setId(id);
		dm.setMapPrimaryKey("id");
		dm.setUseVersioning(useVersioning);
		dm.setDataModelVersion(dataModelVersion.toString());
		return dm;
	}

	final List<SinkRecord> buildRecords(List<Map<String, Object>> data, long runId) {
		return data.stream()
				.map(rawMessage -> new NexlaMessage(new LinkedHashMap<>(rawMessage), getNexlaMetaData(runId)))
				.map(message -> new SinkRecord("test-topic", 1, null, null, null, toJsonString(message), 0))
				.collect(Collectors.toList());
	}

	public static Stream<Arguments> modelAndVersioningInvariants() {
		return Stream.of(
				Arguments.of(MasterEntry.DataModelVersion.V1, true),
				Arguments.of(MasterEntry.DataModelVersion.V1, false),
				Arguments.of(MasterEntry.DataModelVersion.V2, true),
				Arguments.of(MasterEntry.DataModelVersion.V2, false)
		);
	}

	public static Stream<Arguments> modelInvariants() {
		return Stream.of(
				Arguments.of(MasterEntry.DataModelVersion.V1),
				Arguments.of(MasterEntry.DataModelVersion.V2)
		);
	}

	@ParameterizedTest
	@MethodSource("modelInvariants")
	@SneakyThrows
	public void testSink_WithoutVersioning(MasterEntry.DataModelVersion dataModelVersion) {
		Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1001);
		try {
			lookup.save(emptyDataMap(lookup.getId(), dataModelVersion, false));
			connector.start(new HashMap<>(baseProperties) {{
				put(MAP_ID, String.valueOf(lookup.getId()));
			}});
			Map<String, String> taskParams = connector.taskConfigs(1).get(0);
			task.start(taskParams);

			List<Map<String, Object>> data = List.of(
					Map.of(
							"id", 1L,
							"city", "New York",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					),
					Map.of(
							"id", 2L,
							"city", "Boston",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					),
					Map.of(
							"id", 3L,
							"city", "Kyiv",
							"country", Map.of(
									"name", "Ukraine",
									"id", 5L
							)
					)
			);

			task.put(buildRecords(data, 1L));

			Set<Map<String, String>> expected = Set.of(
					Map.of(
							"id", "1",
							"city", "New York",
							"country.name", "USA",
							"country.id", "2"
					),
					Map.of(
							"id", "2",
							"city", "Boston",
							"country.name", "USA",
							"country.id", "2"
					),
					Map.of(
							"id", "3",
							"city", "Kyiv",
							"country.name", "Ukraine",
							"country.id", "5"
					)
			);

			MasterEntry masterEntry = lookup.getMasterEntry();
			assertEquals("1", masterEntry.currentVersion);
			assertEquals("1", masterEntry.nextVersion);
			assertEquals(data.size(), masterEntry.mapSize);
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);

			assertEquals(expected, Set.copyOf(lookup.getAllEntries()));
		} finally {
			lookup.deleteAsync().get();
		}
	}

	//@ParameterizedTest FIXME Check why it is happening intermittent errors.
	@MethodSource("modelInvariants")
	@SneakyThrows
	public void testSink_WithVersioning(MasterEntry.DataModelVersion dataModelVersion) {
		Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1011);
		try {
			lookup.save(emptyDataMap(lookup.getId(), dataModelVersion, true));
			connector.start(new HashMap<>(baseProperties) {{
				put(MAP_ID, String.valueOf(lookup.getId()));
			}});
			Map<String, String> taskParams = connector.taskConfigs(1).get(0);
			task.start(taskParams);

			List<Map<String, Object>> data = List.of(
					Map.of(
							"id", 1L,
							"city", "New York",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					),
					Map.of(
							"id", 2L,
							"city", "Boston",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					),
					Map.of(
							"id", 3L,
							"city", "Kyiv",
							"country", Map.of(
									"name", "Ukraine",
									"id", 5L
							)
					)
			);

			task.put(buildRecords(data, 1L));

			MasterEntry masterEntry = lookup.getMasterEntry();
			assertEquals("1", masterEntry.currentVersion);
			assertEquals("2", masterEntry.nextVersion);
			assertEquals(data.size(), masterEntry.mapSize);
			// update window is not closed, we are fetching old values
			assertEquals(0, lookup.getAllEntries().size());
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);

			// wait to enable flush
			Thread.sleep(FLUSH_INTERVAL_SECONDS * 1000);
			task.doFlush(ReadyToFlush.FLUSH);

			Set<Map<String, String>> expected = Set.of(
					Map.of(
							"id", "1",
							"city", "New York",
							"country.name", "USA",
							"country.id", "2"
					),
					Map.of(
							"id", "2",
							"city", "Boston",
							"country.name", "USA",
							"country.id", "2"
					),
					Map.of(
							"id", "3",
							"city", "Kyiv",
							"country.name", "Ukraine",
							"country.id", "5"
					)
			);

			masterEntry = lookup.getMasterEntry();
			assertEquals("2", masterEntry.currentVersion);
			assertEquals("3", masterEntry.nextVersion);
			assertEquals(0, masterEntry.mapSize);
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);
			assertEquals(expected, Set.copyOf(lookup.getAllEntries()));
		} finally {
			lookup.deleteAsync().get();
		}
	}

	//FIXME @ParameterizedTest - Fix NPE in redis logic
	@MethodSource("modelAndVersioningInvariants")
	@SneakyThrows
	public void testSinkWithMissingPK(MasterEntry.DataModelVersion dataModelVersion, boolean useVersioning) {
		Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1002);
		try {
			lookup.save(emptyDataMap(lookup.getId(), dataModelVersion, useVersioning));
			connector.start(new HashMap<>(baseProperties) {{
				put(MAP_ID, String.valueOf(lookup.getId()));
			}});
			Map<String, String> taskParams = connector.taskConfigs(1).get(0);
			task.start(taskParams);
			List<Map<String, Object>> data = List.of(
					new HashMap<>() {{
						put("id", null);
						put("city", "New York");
						put("country", Map.of(
								"name", "USA",
								"id", 2L
						));
					}});

			task.put(buildRecords(data, 1L));

			MasterEntry masterEntry = lookup.getMasterEntry();
			assertEquals("1", masterEntry.currentVersion);
			String expectedNextVersion = useVersioning ? "2" : "1";
			assertEquals(expectedNextVersion, masterEntry.nextVersion);
			assertEquals(0, masterEntry.mapSize);
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);

			assertTrue(JEDIS.keys(String.format("dm:entry:%d:*", lookup.getId())).isEmpty());
		} finally {
			lookup.deleteAsync().get();
		}
	}

	@ParameterizedTest
	@MethodSource("modelInvariants")
	@SneakyThrows
	public void testSinkWithMultipleRunIds_WithoutVersioning(MasterEntry.DataModelVersion dataModelVersion) {
		Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1003);
		try {
			lookup.save(emptyDataMap(lookup.getId(), dataModelVersion, false));
			connector.start(new HashMap<>(baseProperties) {{
				put(MAP_ID, String.valueOf(lookup.getId()));
			}});
			Map<String, String> taskParams = connector.taskConfigs(1).get(0);
			task.start(taskParams);

			List<Map<String, Object>> initialData = List.of(
					Map.of(
							"id", 1L,
							"city", "New York",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					),
					Map.of(
							"id", 2L,
							"city", "Boston",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					)
			);

			task.put(buildRecords(initialData, 1L));

			List<Map<String, Object>> updateData = List.of(
					Map.of(
							"id", 3L,
							"city", "Kyiv",
							"country", Map.of(
									"name", "Ukraine",
									"id", 5L
							)
					)
			);

			task.put(buildRecords(updateData, 2L));

			MasterEntry masterEntry = lookup.getMasterEntry();

			Set<Map<String, String>> expected = Set.of(
					Map.of(
							"id", "1",
							"city", "New York",
							"country.name", "USA",
							"country.id", "2"
					),
					Map.of(
							"id", "2",
							"city", "Boston",
							"country.name", "USA",
							"country.id", "2"
					),
					Map.of(
							"id", "3",
							"city", "Kyiv",
							"country.name", "Ukraine",
							"country.id", "5"
					)
			);
			assertEquals("1", masterEntry.nextVersion);
			assertEquals(expected.size(), masterEntry.mapSize);
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);

			assertEquals(expected, Set.copyOf(lookup.getAllEntries()));

		} finally {
			lookup.deleteAsync().get();
		}
	}

	//@ParameterizedTest FIXME Check why it is happening intermittent errors.
	@MethodSource("modelInvariants")
	@SneakyThrows
	public void testSinkWithMultipleRunIds_WithVersioning(MasterEntry.DataModelVersion dataModelVersion) {
		Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1013);
		try {
			lookup.save(emptyDataMap(lookup.getId(), dataModelVersion, true));
			connector.start(new HashMap<>(baseProperties) {{
				put(MAP_ID, String.valueOf(lookup.getId()));
			}});
			Map<String, String> taskParams = connector.taskConfigs(1).get(0);
			task.start(taskParams);

			// update lookup via redis sink with initial data on a first run
			List<Map<String, Object>> initialData = List.of(
					Map.of(
							"id", 1L,
							"city", "New York",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					),
					Map.of(
							"id", 2L,
							"city", "Boston",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					)
			);

			task.put(buildRecords(initialData, 1L));

			var masterEntry = lookup.getMasterEntry();
			assertEquals("1", masterEntry.currentVersion);
			assertEquals("2", masterEntry.nextVersion);
			assertEquals(2, masterEntry.mapSize);
			// update window is not closed, we are fetching old values
			assertEquals(0, lookup.getAllEntries().size());
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);

			// update lookup via redis sink with initial data on a second run
			List<Map<String, Object>> updateData = List.of(
					Map.of(
							"id", 3L,
							"city", "Kyiv",
							"country", Map.of(
									"name", "Ukraine",
									"id", 5L
							)
					)
			);
			task.put(buildRecords(updateData, 2L));

			Set<Map<String, String>> expected = Set.of(
					Map.of(
							"id", "1",
							"city", "New York",
							"country.name", "USA",
							"country.id", "2"
					),
					Map.of(
							"id", "2",
							"city", "Boston",
							"country.name", "USA",
							"country.id", "2"
					)
			);

			masterEntry = lookup.getMasterEntry();
			assertEquals("2", masterEntry.currentVersion);
			assertEquals("3", masterEntry.nextVersion);
			assertEquals(1, masterEntry.mapSize);
			// update window is not closed, we are fetching old values
			assertEquals(expected, Set.copyOf(lookup.getAllEntries()));
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);

			// wait to enable flush
			Thread.sleep(FLUSH_INTERVAL_SECONDS * 1000);
			task.doFlush(ReadyToFlush.FLUSH);

			masterEntry = lookup.getMasterEntry();
			expected = Set.of(
					Map.of(
							"id", "3",
							"city", "Kyiv",
							"country.name", "Ukraine",
							"country.id", "5"
					)
			);
			assertEquals("3", masterEntry.currentVersion);
			assertEquals("4", masterEntry.nextVersion);
			assertEquals(0, masterEntry.mapSize);
			assertEquals(expected, Set.copyOf(lookup.getAllEntries()));
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);

			assertEquals(expected, Set.copyOf(lookup.getAllEntries()));
		} finally {
			lookup.deleteAsync().get();
		}
	}

	@ParameterizedTest
	@MethodSource("modelInvariants")
	@SneakyThrows
	public void testSinkUpdateBetweenRunIds_WithoutVersioning(MasterEntry.DataModelVersion dataModelVersion) {
		Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1004);
		try {
			var dataMap = emptyDataMap(lookup.getId(), dataModelVersion, false);
			lookup.save(dataMap);
			connector.start(new HashMap<>(baseProperties) {{
				put(MAP_ID, String.valueOf(lookup.getId()));
			}});
			Map<String, String> taskParams = connector.taskConfigs(1).get(0);
			task.start(taskParams);

			// update lookup via redis sink with initial data on a first run
			List<Map<String, Object>> initialData = List.of(
					Map.of(
							"id", 1L,
							"city", "New York",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					),
					Map.of(
							"id", 2L,
							"city", "Boston",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					)
			);
			task.put(buildRecords(initialData, 1L));


			// update
			dataMap.setDataMap(List.of(Map.of(
							"id", "2",
							"city", "Boston",
							"country.name", "USA",
							"country.id", "2"
					)
			));
			lookup.save(dataMap);


			// update lookup via redis sink on a second run
			List<Map<String, Object>> updateData = List.of(
					Map.of(
							"id", 3L,
							"city", "Kyiv",
							"country", Map.of(
									"name", "Ukraine",
									"id", 5L
							)
					)
			);

			task.put(buildRecords(updateData, 2L));

			Set<Map<String, String>> expected = Set.of(
					Map.of(
							"id", "1",
							"city", "New York",
							"country.name", "USA",
							"country.id", "2"
					),
					Map.of(
							"id", "2",
							"city", "Boston",
							"country.name", "USA",
							"country.id", "2"
					),
					Map.of(
							"id", "3",
							"city", "Kyiv",
							"country.name", "Ukraine",
							"country.id", "5"
					)
			);
			var masterEntry = lookup.getMasterEntry();
			assertEquals("1", masterEntry.nextVersion);
			assertEquals(4, masterEntry.mapSize);
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);
			assertEquals(3, lookup.getAllEntries().size());

			assertEquals(expected, Set.copyOf(lookup.getAllEntries()));

		} finally {
			lookup.deleteAsync().get();
		}
	}

	@ParameterizedTest
	@MethodSource("modelInvariants")
	@SneakyThrows
	public void testSinkUpdateBetweenRunIds_WithVersioning(MasterEntry.DataModelVersion dataModelVersion) {
		Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1014);
		try {
			var dataMap = emptyDataMap(lookup.getId(), dataModelVersion, true);
			lookup.save(dataMap);
			connector.start(new HashMap<>(baseProperties) {{
				put(MAP_ID, String.valueOf(lookup.getId()));
			}});
			Map<String, String> taskParams = connector.taskConfigs(1).get(0);
			task.start(taskParams);

			// update lookup via redis sink with initial data on a first run
			List<Map<String, Object>> initialData = List.of(
					Map.of(
							"id", 1L,
							"city", "New York",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					),
					Map.of(
							"id", 2L,
							"city", "Boston",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					)
			);
			task.put(buildRecords(initialData, 1L));

			var masterEntry = lookup.getMasterEntry();
			assertEquals("1", masterEntry.currentVersion);
			assertEquals("2", masterEntry.nextVersion);
			assertEquals(2, masterEntry.mapSize);
			// update window is not closed, we are fetching old values
			assertEquals(0, lookup.getAllEntries().size());
			assertEquals(0, lookup.getPrimaryKeys().size());

			// update
			dataMap.setDataMap(List.of(Map.of(
							"id", "2",
							"city", "Boston",
							"country.name", "USA",
							"country.id", "2"
					)
			));
			lookup.save(dataMap);

			masterEntry = lookup.getMasterEntry();
			assertEquals("1", masterEntry.currentVersion);
			assertEquals("2", masterEntry.nextVersion);
			assertEquals(2, masterEntry.mapSize);
			// update window is not closed, we are fetching old values
			assertEquals(0, lookup.getAllEntries().size());
			assertEquals(0, lookup.getPrimaryKeys().size());

			// update lookup via redis sink on a second run
			List<Map<String, Object>> updateData = List.of(
					Map.of(
							"id", 3L,
							"city", "Kyiv",
							"country", Map.of(
									"name", "Ukraine",
									"id", 5L
							)
					)
			);

			task.put(buildRecords(updateData, 2L));

			masterEntry = lookup.getMasterEntry();

			assertEquals("2", masterEntry.currentVersion);
			assertEquals("3", masterEntry.nextVersion);
			assertEquals(1, masterEntry.mapSize);
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);
			// update window is not closed, we are fetching old values
			assertEquals(2, lookup.getAllEntries().size());
			assertEquals(2, lookup.getPrimaryKeys().size());

			Set<Map<String, String>> expected = Set.of(
					Map.of(
							"id", "1",
							"city", "New York",
							"country.name", "USA",
							"country.id", "2"
					),
					Map.of(
							"id", "2",
							"city", "Boston",
							"country.name", "USA",
							"country.id", "2"
					)
			);
			assertEquals(expected, Set.copyOf(lookup.getAllEntries()));
		} finally {
			lookup.deleteAsync().get();
		}
	}

	@ParameterizedTest
	@MethodSource("modelInvariants")
	@SneakyThrows
	public void testSinkUpdateMetadataWithinRunId_WithoutVersioning(MasterEntry.DataModelVersion dataModelVersion) {
		Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1010);
		try {
			var dataMap = emptyDataMap(lookup.getId(), dataModelVersion, false);
			lookup.save(dataMap);
			connector.start(new HashMap<>(baseProperties) {{
				put(MAP_ID, String.valueOf(lookup.getId()));
			}});
			Map<String, String> taskParams = connector.taskConfigs(1).get(0);
			task.start(taskParams);

			// process sink with initial data
			List<Map<String, Object>> initialData = List.of(
					Map.of(
							"id", 1L,
							"city", "New York",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					),
					Map.of(
							"id", 2L,
							"city", "Boston",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					)
			);
			task.put(buildRecords(initialData, 1L));

			// update lookup metadata (no entries)
			dataMap = emptyDataMap(lookup.getId(), dataModelVersion, false);
			dataMap.setDataDefaults(Map.of("name", "N/A"));
			lookup.save(dataMap);

			// process sink with updated data for the same run id
			List<Map<String, Object>> updateData = List.of(
					Map.of(
							"id", 3L,
							"city", "Kyiv",
							"country", Map.of(
									"name", "Ukraine",
									"id", 5L
							)
					)
			);
			task.put(buildRecords(updateData, 1L));

			MasterEntry masterEntry = lookup.getMasterEntry();
			Set<Map<String, String>> expected = Set.of(
					Map.of(
							"id", "1",
							"city", "New York",
							"country.name", "USA",
							"country.id", "2"
					),
					Map.of(
							"id", "2",
							"city", "Boston",
							"country.name", "USA",
							"country.id", "2"
					),
					Map.of(
							"id", "3",
							"city", "Kyiv",
							"country.name", "Ukraine",
							"country.id", "5"
					)
			);
			assertEquals("1", masterEntry.nextVersion);
			assertEquals(expected.size(), masterEntry.mapSize);
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);
			assertEquals(expected.size(), lookup.getAllEntries().size());

			assertEquals(expected, Set.copyOf(lookup.getAllEntries()));
		} finally {
			lookup.deleteAsync().get();
		}
	}

	@ParameterizedTest
	@MethodSource("modelInvariants")
	@SneakyThrows
	public void testSinkUpdateMetadataWithinRunId_WithVersioning(MasterEntry.DataModelVersion dataModelVersion) {
		Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1025);
		try {
			var dataMap = emptyDataMap(lookup.getId(), dataModelVersion, true);
			lookup.save(dataMap);
			connector.start(new HashMap<>(baseProperties) {{
				put(MAP_ID, String.valueOf(lookup.getId()));
			}});
			Map<String, String> taskParams = connector.taskConfigs(1).get(0);
			task.start(taskParams);

			// process sink with initial data
			List<Map<String, Object>> initialData = List.of(
					Map.of(
							"id", 1L,
							"city", "New York",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					),
					Map.of(
							"id", 2L,
							"city", "Boston",
							"country", Map.of(
									"name", "USA",
									"id", 2L
							)
					)
			);
			task.put(buildRecords(initialData, 1L));

			// update lookup
			dataMap = emptyDataMap(lookup.getId(), dataModelVersion, true);
			dataMap.setDataDefaults(Map.of("name", "N/A"));
			lookup.save(dataMap);

			var masterEntry = lookup.getMasterEntry();
			assertEquals("1", masterEntry.currentVersion);
			assertEquals("2", masterEntry.nextVersion);
			assertEquals(2, masterEntry.mapSize);
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);
			assertEquals(0, lookup.getAllEntries().size());

			// wait to enable flush
			Thread.sleep(FLUSH_INTERVAL_SECONDS * 1000);
			task.doFlush(ReadyToFlush.FLUSH);

			masterEntry = lookup.getMasterEntry();
			assertEquals("2", masterEntry.currentVersion);
			assertEquals("3", masterEntry.nextVersion);
			assertEquals(0, masterEntry.mapSize);
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);
			assertEquals(2, lookup.getAllEntries().size());
		} finally {
			lookup.deleteAsync().get();
		}
	}

	@ParameterizedTest
	@MethodSource("modelInvariants")
	@SneakyThrows
	public void testSinkWithLimitReached_WithoutVersioning(MasterEntry.DataModelVersion dataModelVersion) {
		Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1005);
		try {
			lookup.save(emptyDataMap(lookup.getId(), dataModelVersion, false));
			lookup.setMasterEntryField(MasterEntry.MAP_SIZE_LIMIT, "5");
			connector.start(new HashMap<>(baseProperties) {{
				put(MAP_ID, String.valueOf(lookup.getId()));
			}});
			Map<String, String> taskParams = connector.taskConfigs(1).get(0);
			task.start(taskParams);

			task.put(buildRecords(List.of(
					Map.of(
							"id", 1L,
							"letter", "A"
					),
					Map.of(
							"id", 2L,
							"letter", "B"
					)), 1L));

			task.put(buildRecords(List.of(
					Map.of(
							"id", 3L,
							"letter", "C"
					),
					Map.of(
							"id", 4L,
							"letter", "D"
					)), 2L));

			task.put(buildRecords(List.of(
					Map.of(
							"id", 5L,
							"letter", "E"
					),
					Map.of(
							"id", 6L,
							"letter", "F"
					)), 3L));

			task.put(buildRecords(List.of(
					Map.of(
							"id", 7L,
							"letter", "E"
					),
					Map.of(
							"id", 8L,
							"letter", "F"
					)), 4L));

			Set<Map<String, String>> expected = Set.of(
					Map.of(
							"id", "1",
							"letter", "A"
					),
					Map.of(
							"id", "2",
							"letter", "B"
					),
					Map.of(
							"id", "3",
							"letter", "C"
					),
					Map.of(
							"id", "4",
							"letter", "D"
					),
					Map.of(
							"id", "5",
							"letter", "E"
					),
					// over the limit, since it got into the last batch
					Map.of(
							"id", "6",
							"letter", "F"
					)
			);

			MasterEntry masterEntry = lookup.getMasterEntry();
			assertEquals("1", masterEntry.currentVersion);
			assertEquals("1", masterEntry.nextVersion);
			assertTrue(masterEntry.limitReached);
			assertEquals(expected.size(), masterEntry.mapSize);
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);
			assertEquals(expected.size(), lookup.getAllEntries().size());

			assertEquals(expected, Set.copyOf(lookup.getAllEntries()));
		} finally {
			lookup.deleteAsync().get();
		}
	}

	//@ParameterizedTest FIXME Check why it is happening intermittent errors
	@MethodSource("modelInvariants")
	@SneakyThrows
	public void testSinkWithLimitReached_WithVersioning(MasterEntry.DataModelVersion dataModelVersion) {
		Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1006);
		try {
			lookup.save(emptyDataMap(lookup.getId(), dataModelVersion, true));
			lookup.setMasterEntryField(MasterEntry.MAP_SIZE_LIMIT, "2");
			connector.start(new HashMap<>(baseProperties) {{
				put(MAP_ID, String.valueOf(lookup.getId()));
			}});
			Map<String, String> taskParams = connector.taskConfigs(1).get(0);
			task.start(taskParams);

			task.put(buildRecords(List.of(
					Map.of(
							"id", 1L,
							"letter", "A"
					),
					Map.of(
							"id", 2L,
							"letter", "B"
					),
					Map.of(
							"id", 3L,
							"letter", "C"
					),
					Map.of(
							"id", 4L,
							"letter", "D"
					)), 1L));

			MasterEntry masterEntry = lookup.getMasterEntry();
			assertFalse(masterEntry.limitReached);
			assertEquals(4, masterEntry.mapSize);
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);

			// wait to enable flush
			Thread.sleep(FLUSH_INTERVAL_SECONDS * 1000);
			task.doFlush(ReadyToFlush.FLUSH);

			Set<Map<String, String>> expected = Set.of(
					Map.of(
							"id", "1",
							"letter", "A"
					),
					Map.of(
							"id", "2",
							"letter", "B"
					),
					// over the defined limit, but it still stored. limit works only for not versioned lookups
					Map.of(
							"id", "3",
							"letter", "C"
					),
					Map.of(
							"id", "4",
							"letter", "D"
					)
			);

			masterEntry = lookup.getMasterEntry();
			assertEquals("2", masterEntry.currentVersion);
			assertEquals("3", masterEntry.nextVersion);
			assertFalse(masterEntry.limitReached);
			assertEquals(0, masterEntry.mapSize);
			assertEquals(dataModelVersion, masterEntry.dataModelVersion);

			assertEquals(expected, Set.copyOf(lookup.getAllEntries()));
		} finally {
			lookup.deleteAsync().get();
		}
	}

	private NexlaMetaData getNexlaMetaData(long runId) {
		NexlaMetaData nexlaMetaData = new NexlaMetaData();
		nexlaMetaData.setTrackerId(Tracker.parse("u5010:123:file.csv:123:5:1:1499194015000;7187:2:250;7188:2:750;4444:1:1010"));
		nexlaMetaData.setRunId(runId);
		nexlaMetaData.setIngestTime(nowUTC().getMillis());
		return nexlaMetaData;
	}

}